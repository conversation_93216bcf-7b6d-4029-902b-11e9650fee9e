export const SEARCH_CONFIG = [
  {
    title: 'IP地址',
    name: 'ip',
    type: 'string',
    "x-decorator": "FormItem",
    "x-decorator-props": {
      feedbackLayout: "none",
      labelWidth: 110
    },
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入IP地址',
    },
  },
  {
    title: '漏洞名称',
    name: 'vulName',
    type: 'string',
    "x-decorator": "FormItem",
    "x-decorator-props": {
      feedbackLayout: "none",
      labelWidth: 110
    },
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入漏洞名称',
    },
  },
  {
    title: 'CVE编号',
    name: 'cveNumber',
    type: 'string',
    "x-decorator": "FormItem",
    "x-decorator-props": {
      feedbackLayout: "none",
      labelWidth: 110
    },
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入CVE编号',
    },
  },
]


export const COLUMNS = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: 'IP地址',
    dataIndex: 'ip',
    key: 'ip'
  },
  {
    title: '漏洞名称',
    dataIndex: 'vulName',
    key: 'vulName'
  },
  {
    title: 'CVE编号',
    dataIndex: 'cveNumber',
    key: 'cveNumber'
  },
  {
    title: '风险评分',
    dataIndex: 'riskPoints',
    key: 'riskPoints'
  },
  {
    title: '发现日期',
    dataIndex: 'dateFound',
    key: 'dateFound'
  },
  {
    title: '修复状态',
    dataIndex: 'repairStatus',
    key: 'repairStatus',
    customRender: ({text}) => {
      const REPAIR_MAP = {
        0: '未修复', 1: '已修复', 2: '修复中',
      }
      return REPAIR_MAP[text]
    },
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: "200px"
  }
]

/** @type {*} 表单schema */

export const DETAIL_CONFIG = [
  {
    label: '漏洞名称',
    prop: 'vulName',
  },
  {
    label: '资产IP',
    prop: 'ip',
  },
  {
    label: 'CVE编号',
    prop: 'cveNumber',
  },
  {
    label: 'CNVD编号',
    prop: 'cnvdNumber',
  },
  {
    label: 'CNNVD编号',
    prop: 'cnnvdNumber',
  },
  {
    label: '应用类别',
    prop: 'applicationCategory',
  },
  {
    label: '威胁类型',
    prop: 'threatCategory',
  },
  {
    label: '发现日期',
    prop: 'dateFound',
  },
  {
    label: '风险评分',
    prop: 'riskPoints',
  },
  {
    label: '修复状态',
    prop: 'repairStatus',
    render(h, {prop, data}){
      const REPAIR_MAP = {
        0: '未修复', 1: '已修复', 2: '修复中',
      }
      return REPAIR_MAP[data.repairStatus]
    },
  },
  {
    label: '漏洞级别',
    prop: 'level',
    render(h, {prop, data}){
      const LEVEL_MAP = {
        1: '低级', 2: '中级', 3: '高级',
      }
      return LEVEL_MAP[data.level]
    },
    gridSpan:2
  },
  {
    label: '解决方法',
    prop: 'solution',
    gridSpan:2
  },
  {
    label: '描述',
    prop: 'description',
    gridSpan:2
  },
]


