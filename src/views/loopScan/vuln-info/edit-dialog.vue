<template>
  <a-modal
    v-model:visible="dialogVisible"
    title="漏洞详情"
    width="700px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <SocDetails :config="DETAIL_CONFIG" :colSpan="2" :data="defaultData" />
    </a-spin>
    <template #footer>
      <a-button type="default" @click="handleClose">关闭</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm } from '@formily/core'
import { DETAIL_CONFIG } from './config';
import { FormTab } from '@formily/antdv-x3'
import SocDetails from '@/views/workOrderManage/workOrderManage/custom-process-detail/_components_/soc-details.vue'

export default defineComponent({
name: 'EditList',
components: {
  SocDetails
},
props: {
  visible: {
    type: Boolean,
    default: false
  },
  defaultData: {
    type: Object,
    default: () => ({})
  }
},
emits: ['update:visible', 'on-success'],
setup (props, ctx) {
  const { proxy } = getCurrentInstance()
  const { visible, mode, type } = toRefs(props)
  const dialogVisible = computed(() => visible.value)

  const state = reactive({
    loading: false
  })

  const handleClose = async () => {
    ctx.emit('update:visible', false)
  }

  return {
    ...toRefs(state),
    dialogVisible,
    handleClose,
    DETAIL_CONFIG
  }
}
})
</script>
