<template>
  <div class="vuln-info-wrapper">
    <DynamicSearch style="border-radius: 6px;" :config="SEARCH_CONFIG" :operationConfig="{showExpandBtn: false}" @search="handleSearch" @reset="handleSearch" />
    <div class="vuln-info-content">
      <a-table
        :data-source="dataSource"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        :scroll="{ y: tableHeight }"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation'">
            <a-button type="link" @click="handleOpenDialog('view', record)"
              >详情 </a-button
            >
            <a-button type="link" @click="handleSubmitProcess(record)"
              > 发起流程</a-button
            >
          </template>
        </template>
      </a-table>
    </div>

    <EditDialog
      v-model:visible="dialogConfig.visible"
      :type="type"
      v-bind="dialogConfig"
      @on-success="handleSave"
    />
    <SubmitTicket ref="submitTicketRef" @submit="handleSubmit" />
  </div>
</template>

<script>
import DynamicSearch from "@/components/dynamic-search/index.vue";
import { SEARCH_CONFIG, COLUMNS } from "./config";
import { usePagination } from "vue-request";
import {
  computed,
  getCurrentInstance,
  reactive,
  toRefs,
  ref,
  onMounted,
} from "vue";
import EditDialog from "./edit-dialog.vue";
import { getInfoVulList, startLeakInfoFlow } from "@/request/api-process";
import SubmitTicket from "@/components/submit-ticket/index.vue";
import { message } from 'ant-design-vue';
import {transformIncludeFileFormData} from '@/utils/util.js'
import bpmnService from "@/request/processmanage";

export default {
  components: {
    DynamicSearch,
    EditDialog,
    SubmitTicket
  },
  setup() {
    const tableHeight = ref(500);
    const searchParams = ref({});

    onMounted(() => {
      tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 370;
    });

    const { proxy } = getCurrentInstance();
    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getInfoVulList, {
      manual: true,
      defaultParams: {
        ...searchParams.value
      },
      formatResult: ({data = {}}) => ({ items: data?.data ?? [], total: data?.total ?? 0 }),
      pagination: {
        currentKey: "page",
        pageSizeKey: "size",
        totalKey: "total",
      },
    });

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`,
    }));

    const dataSource = computed(() => {
      return data.value?.items || [];
    });

    /**
     * 处理表格分页变化
     * @param {Object} pag 分页参数
     */
    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        size: pag.pageSize,
        page: pag?.current,
        ...searchParams.value
      })
    }

    /**
     * 刷新表格数据
     * @param {boolean} isReload 是否重新加载第一页
     */
    const refreshTableData = (isReload = true) => {
      run({
        page: isReload ? 1 : pagination.value.current,
        size: pagination.value?.pageSize,
        ...searchParams.value
      })
    }

    // 搜索
    const handleSearch = (params = {}) => {
      searchParams.value = params
      refreshTableData()
    }

    const state = reactive({
      dialogConfig: {
        visible: false,
        mode: "add",
        defaultData: {},
      },
    });

    const submitTicketRef = ref(null);
    async function handleSubmitProcess(record){
      const { data } = await bpmnService.getBpmnPData({ pageNum: 1, pageSize: 10000 });
      const processId = data.list.find(i => i.processName === '漏洞管理流程').processId??'';
      submitTicketRef.value.open({
        title: '漏洞管理流程',
        data: {
          processId,
          form: {
            lstDataId: [record.uuid]
          }
        },
        type: 'leakManage',
      });
    }

    // 提交工单
    async function handleSubmit(values){
      const data = transformIncludeFileFormData(values)
      await startLeakInfoFlow(data);
      message.success('工单已提交');
    }

    // 打开弹窗
    const handleOpenDialog = (mode = "add", row = {}) => {
      state.dialogConfig.visible = true;
      state.dialogConfig.defaultData = row;
    };

    const columns = computed(() => {
      return COLUMNS.map(column => {
        if (column.customRender) {
          return {
            ...column,
            customRender: (args) => column.customRender(args, pagination.value)
          };
        }
        return column;
      });
    });

    return {
      submitTicketRef,
      handleSubmitProcess,
      SEARCH_CONFIG,
      columns,
      dataSource,
      pagination,
      ...toRefs(state),
      loading,
      handleOpenDialog,
      handleTableChange,
      handleSearch,
      handleSubmit,
      tableHeight
    };
  },
};
</script>

<style lang="less" scoped>
.vuln-info-wrapper {
  height: calc(100% - 48px);
  margin: 32px 16px 16px;

  ::v-deep .dynamic-search-wrapper .operator {
    margin-left: 70px
  }
}
.vuln-info-content {
  height: calc((100vh - 80px) - 140px);
  background-color: #fff;
  margin-top: 16px;
  padding: 20px;
  border-radius: 6px;
}

</style>
