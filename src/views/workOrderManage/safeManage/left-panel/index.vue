<template>
  <div class="leftTree">
    <div class="leftTree-operator">
      <a-input-search
        v-model:value.trim="treeSearchValue"
        enterButton
        placeholder="请输入设备分类"
        allow-clear
        :maxlength="50"
      >
      </a-input-search>
      <a-button type="primary" @click="handleAddDevice">
        <template #icon><PlusOutlined /></template>
      </a-button>
    </div>
    <div class="tree">
      <a-tree
        :tree-data="treeData"
        style="color: black"
        v-if="treeData.length"
        v-model:selectedKeys="selected"
        v-model:expandedKeys="expandedKeys"
        @select="handleSelect"
      >
        <template #title="{ label, totalCount, isDefault, value, data }">
          <div class="left-tree-item">
            <span class="left-tree-content" :title="label"> {{label}} </span> <span v-if="!data.children?.length">({{totalCount}})</span>
            <span class="op-icon">
              <FormOutlined
                style="color: #008dff"
                @click.prevent.stop="
                  handleEditDeviceType({ id: value, displayName: label })
                "
              />
              <DeleteOutlined
                v-show="![-2, -3].includes(value) && !isDefault"
                style="color: #ff4d4f"
                @click.prevent.stop="handleDeleteDeviceType({ value, totalCount })"
              />
            </span>
          </div>
        </template>
      </a-tree>
    </div>
    <AddDeviceModal ref="deviceRef" @submit="handleSubmit" />
  </div>
</template>

<script setup>
import {
  onMounted,
  ref,
  defineExpose,
  defineProps,
  computed,
  watch,
  defineEmits,
} from "vue";
import { Modal, message } from "ant-design-vue";
import { cloneDeep } from 'lodash';
import {
  addDeviceType,
  getDeviceGroup,
  deleteDeviceType,
  updateDeviceType,
} from "@/request/safe-device/index";
import {
  PlusOutlined,
  FormOutlined,
  DeleteOutlined,
} from "@ant-design/icons-vue";
import search from "@/components/bpmn/bpmn-js/lib/features/search";
import AddDeviceModal from "./add-device-type/index.vue";

// 首次加载标记
const isInit = ref(true);
const emits = defineEmits(["select", "updateTypeData"]);
const props = defineProps({
  selectedKeys: {
    type: Array,
    default: () => [],
  },
});

const selected = ref([]);
const expandedKeys = ref([]);
const loading = ref(false);
watch(
  () => props.selectedKeys,
  (newVal) => {
    selected.value = newVal;
  },
  { immediate: true }
);

const deviceRef = ref(null);
function handleAddDevice() {
  deviceRef.value.open({
    title: "新增安全设备分类",
  });
}
function handleEditDeviceType(data) {
  deviceRef.value.open({
    title: "编辑安全设备分类",
    data,
  });
}
async function handleDeleteDeviceType({ value, totalCount }) {
  if (totalCount > 0) {
    return message.error("该分类下存在设备，请先删除设备后再删除分类");
  }
  Modal.confirm({
    title: "删除安全设备分类",
    content: "确定删除该安全设备吗？",
    async onOk() {
      try {
        await deleteDeviceType({ id: value });
        message.success("删除成功");
        initData();
      } catch (err) {
        message.error("删除失败");
      }
    },
  });
}
async function handleSubmit(data) {
  try {
    if (data.id) {
      await updateDeviceType(data);
    } else {
      await addDeviceType(data);
    }
    message.success(data.id ? '编辑成功' : '新增成功');
    initData();
  } catch (err) {
    message.error(data.id ? '编辑失败' : '新增失败');
  }
}

const treeSearchValue = ref("");
const tree = ref([]);

const formatTreeData = (treeData = [], result = []) => {
  for (let node of treeData) {
    node = {...node, ...node.deviceTypeData};
    const item = {
      ...node,
      key: node?.id,
      value: node.id,
      label: node.displayName,
      deviceTypeKey: node.deviceTypeKey,
      isDefault: node.default,
      children: []
    }
    if (node.children?.length) {
      formatTreeData(node.children, item.children)
    }
    result.push(item);
  }
  return result;
}
async function initData() {
  try {
    loading.value = true;
    const { data = [] } = await getDeviceGroup();
    tree.value = formatTreeData(data);
    // 区分是否首次加载
    if (isInit.value) {
      expandedKeys.value = [-2, -3];
      isInit.value = false;
    }
    treeSearchValue.value = "";
    emits("updateTypeData", cloneDeep([...(tree.value?.[0]?.children ?? []), ...(tree.value?.[1]?.children ?? [])]));
  } finally {
    loading.value = false;
  }
}

function handleSelect(selectedKeys, ...rest) {
  console.log(selectedKeys, ...rest);
  emits("select", selectedKeys);
}

const treeData = computed(() => {
  if (treeSearchValue.value.trim() === "") return tree.value;

  const filterTree = (nodes) => {
    return nodes
      .map(node => {
        const filteredChildren = filterTree(node.children || []);
        const isMatched = node.label.includes(treeSearchValue.value.trim());

        if (isMatched || filteredChildren.length > 0) {
          return {
            ...node,
            children: filteredChildren,
            // 确保父节点保持打开状态
            expanded: true,
          };
        }
        return null;
      })
      .filter(node => node !== null);
  };

  return filterTree(tree.value);
});

onMounted(() => initData());
</script>

<style lang="less" scoped>
.leftTree {
  width: 20%;
  background: #fff;
  border-radius: 6px;
  height: 100%;
  margin-right: 16px;
  padding: 15px 20px 15px 20px;
  &-operator {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    margin-top: 10px;
    gap: 8px;
  }

  .button-groups {
    width: 100%;
    display: flex;
    justify-content: space-around;
    align-items: center;
    margin-bottom: 20px;
  }
  .op-icon {
    cursor: pointer;
    margin-left: 12px;
    display: inline-flex;
    align-items: center;
    gap: 4px;
  }

  .tree {
    width: 100%;
    height: calc(100% - 114px);
    overflow: auto;
    ::v-deep .ant-tree-treenode {
      width: 100%;
    }

    ::v-deep .ant-tree-node-content-wrapper {
      flex: 1;
    }
    .left-tree-item{
      display: flex;
      align-items: center;
    }
    .left-tree-content{
      display: inline-block;
      max-width: 120px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}
</style>
