export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 120
      },
      properties: {
        displayName: {
          type: 'string',
          title: '设备分类名称',
          'x-component': 'Input',
          'x-component-props': {
            'placeholder': '请输入设备分类名称'
          },
          'x-decorator': 'FormItem',
          'x-validator': [
            {
              required: true,
              message: '请输入设备分类名称'
            }
          ]
        },
      }
    },
  },
}

