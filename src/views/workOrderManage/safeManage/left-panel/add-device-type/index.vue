<template>
  <a-modal
    v-model:visible="visible"
    :title="modalTitle"
    :maskClosable="false"
    :keyboard="false"
    @ok="handleOk"
    width="560px"
  >
    <ProFom v-if="visible" :form="form" :schema="SCHEMA" />
  </a-modal>
</template>

<script setup>
import { createForm } from "@formily/vue";
import { ref, defineExpose, defineEmits, shallowRef, nextTick } from "vue";
import ProFom from "@/components/pro-form/index.vue";
import { SCHEMA } from "./config.jsx";

const emits = defineEmits(["submit"]);

const visible = ref(false);
const form = shallowRef(null);

async function handleOk() {
  await form.value.validate();
  visible.value = false;
  emits("submit", { ...form.value.values });
}

const modalTitle = ref("添加设备");

function open({ title, data }) {
  form.value = createForm({});
  form.value.setValues(data, "overwrite");
  visible.value = true;
  modalTitle.value = title;
}

defineExpose({
  open,
});
</script>
