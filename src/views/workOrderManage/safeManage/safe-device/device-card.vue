<template>
  <div class="device-card">
    <div
      :class="`device-card-item ${item.className}`"
      v-for="item in cardConfig"
      :key="item.value"
      @click="handleClickItem(item.value)"
    >
      <div class="left">
        <div class="label">{{ item.label }}</div>
        <div class="count">{{ item.count }}</div>
      </div>
      <div class="icon">
        <!-- <AreaChartOutlined /> -->
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed } from "vue";
import { AreaChartOutlined, BarcodeOutlined } from "@ant-design/icons-vue";

const props = defineProps({
  data: {
    type: Object,
    required: true,
  },
});

const emits = defineEmits(['update-device-status'])

const cardConfig = computed(() => {
  return [
    {
      label: "在线设备",
      value: 1,
      count: props.data?.['1']??0,
      className: "online",
    },
    {
      label: "离线设备",
      value: 2,
      count: props.data?.['2']??0,
      className: "offline",
    },
    {
      label: "异常设备",
      value: 3,
      count: props.data?.['3']??0,
      className: "fault",
    },
  ];
});

const handleClickItem = (status) => {
  emits('update-device-status', status)
}
</script>

<style lang="less" scoped>
.device-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
  padding: 12px;
  background: #bfbfbf3d;
  border-radius: 6px;
  &-item {
    flex: 1;
    height: 140px;
    box-shadow: 0 1px 3px 0 #d4d9e1;
    display: flex;
    justify-content: space-between;
    border-radius:8px;
    align-items: center;
    padding: 16px 32px;
    cursor: pointer;
    background-color: #fff;
    .left {
      display: flex;
      flex-direction: column;
      gap: 16px;
      margin-bottom: 8px;
    }
    .icon {
      font-size: 64px;
    }
    .count {
      font-size: 36px;
    }
    .label {
      font-size: 16px;
      margin-top: 4px;
      color: #0009;
    }
  }
  // .online {
  //   background: rgb(233, 250, 244);
  //   border-color: rgb(98, 171, 146);
  //   .icon {
  //     color: rgb(98, 171, 146);
  //   }
  // }
  // .offline {
  //   background: rgb(235, 245, 255);
  //   border-color: rgb(175, 192, 212);
  //   .icon {
  //     color: rgb(175, 192, 212);
  //   }
  // }
  // .fault {
  //   background: rgb(250, 233, 233);
  //   border-color: rgb(188, 158, 158);
  //   .icon {
  //     color: rgb(188, 158, 158);
  //   }
  // }
}
</style>
