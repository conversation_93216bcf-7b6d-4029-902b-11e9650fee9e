<template>
  <div>
    <DynamicSearch ref="dynamicSearchRef" :config="SEARCH_CONFIG" @search="handleSearch" @reset="handleSearch" />
    <DeviceCard :data="cardData" @update-device-status="handleUpdateDeviceStatus" />
    <div class="list-operator">
      <a-button type="primary" @click="handleAddDevice">新增</a-button>
    </div>
    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button
            type="link"
            @click="handleView(record)"
          >
            查看
          </a-button>
          <a-button
            type="link"
            :disabled="!record.deviceTypeData.default || [2].includes(record.connectionStatus)"
            @click="handleLogin(record)"
            >
            登录
          </a-button>
          <a-button
            type="link"
            :disabled="!record.deviceTypeData.default || [2].includes(record.connectionStatus)"
            @click="handleConfig(record)"
          >
            配置管理
          </a-button>
          <a-button
            type="link"
            @click="handleEdit(record)"
          >
            编辑
          </a-button>
          <a-button
            type="link"
            danger
            @click="handleDelete(record)"
          >
            删除
          </a-button>
        </div>
      </template>
    </CommonTable>
    <AddDevice
      ref="addDeviceRef"
      :typeData="typeData"
      @refresh="handleRefresh"
    />
    <DeviceConfig
      ref="deviceConfigRef"
      :typeId="typeId"
      :typeData="typeData"
      @refresh="handleRefresh"
    />
  </div>
</template>

<script setup>
import {
  defineComponent,
  defineProps,
  ref,
  defineEmits,
  computed,
  watch,
  onMounted,
} from "vue";
import { isString, filter, isNil } from 'lodash';
import DynamicSearch from "@/components/dynamic-search/index.vue";
import DeviceCard from "./device-card.vue";
import { SEARCH_CONFIG, getTableColumns } from "./config.jsx";
import CommonTable from "@/components/common-table/index.vue";
import {
  getDeviceList,
  getDeviceLogin,
  getDeviceStatus,
} from "@/request/safe-device";
import AddDevice from "./add-device/index.vue";
import DeviceConfig from "@/views/device/index.vue";
import { Modal, message } from "ant-design-vue";
import { deleteSafetyDevice } from "@/request/safe-device/index.js";

const emits = defineEmits(["deviceTotal"]);

const props = defineProps({
  typeId: {
    type: [String, Number],
    default: null,
  },
  typeData: {
    type: Array,
    default: () => [],
  },
});

const searchParams = ref({});
function handleSearch(params) {
  searchParams.value = params;
  handleRefresh();
}
function handleLogin(record) {
  if (!record?.deviceTypeData?.default) return;
  window.open(`/city/api/device-atomic/safety/sso?id=${record.id}`, "_blank");
}
function handleRefresh() {
  tableRef.value?.refresh();
   fetchCardData();
}
  const tableHeight = ref(500);
    onMounted(() => {
      tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
    });
const tableRef = ref(null);
const dynamicSearchRef = ref(null);

async function handleDelete(record) {
  Modal.confirm({
    title: "删除设备",
    content: "确定删除该设备吗？",
    onOk: async () => {
      try {
        await deleteSafetyDevice(record);
        message.success('删除成功');
        handleRefresh();
      } catch (err) {
        message.error('删除失败');
      }
    },
  });
}

function handleView(record) {
  addDeviceRef.value.open({
    title: "查看设备",
    data: { ...record },
    mode: "view",
  });
}

function handleEdit(record) {
  addDeviceRef.value.open({
    title: "编辑设备",
    data: { ...record },
    mode: "edit",
  });
}
const deviceConfigRef = ref(null);
function handleConfig(record) {
  if (!record?.deviceTypeData?.default) return;
  deviceConfigRef.value.open({ ...record });
}

const addDeviceRef = ref(null);
function handleAddDevice() {
  addDeviceRef.value.open({
    title: "新增设备",
    data: {
      deviceTypeId: props.typeId,
    },
    mode: "add",
  });
}

const deviceTypeIds = computed(() => {
  const deviceTypeIds = isString(props.typeId) ? props.typeId?.split(',') : [props.typeId];
  return filter(deviceTypeIds, item => !isNil(item) && item !== '')
});

async function fetchData(params) {

  const { data } = await getDeviceList({
    ...params,
    ...searchParams.value,
    deviceTypeIds: deviceTypeIds.value,
  });
  emits("deviceTotal", data.total);
  return { items: data.data, total: data.total };
}
const tableColumns = computed(() => getTableColumns(props.typeData, tableRef.value?.pagination ?? {}));

watch(
  () => props.typeId,
  () => handleRefresh(),
  { immediate: true }
);

const cardData = ref({});
async function fetchCardData() {
  const { data } = await getDeviceStatus({
    ...searchParams.value,
    deviceTypeIds: deviceTypeIds.value,
  });
  cardData.value = data;
}

const handleUpdateDeviceStatus = (status) => {
  dynamicSearchRef.value?.form?.setValuesIn('connectionStatus', status)
  handleRefresh()
}

</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}
.list-operator-column {
  display: flex;
  ::v-deep .ant-btn {
    padding: 4px 6px;
  }
}
</style>
