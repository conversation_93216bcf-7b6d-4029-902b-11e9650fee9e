import { getDeviceGroup } from "@/request/safe-device";
import { useAsyncDataSource } from "@/utils/util";

function composeSchema(extField, suffixFields, dataSource) {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          deviceName: {
            type: 'string',
            title: '设备名称',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入设备名称',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入设备名称'
              }
            ]
          },
          deviceVersion: {
            type: 'string',
            title: '版本型号',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入版本型号',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入版本型号'
              }
            ]
          },
          deviceTypeId: {
            type: 'number',
            default: null,
            title: '设备分类',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择设备分类',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择设备分类'
              }
            ],
            enum: dataSource
          },
          ...extField,
          staff: {
            type: 'string',
            title: '负责人',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入负责人',
            },
          },
          staffPhone: {
            type: 'string',
            title: '联系方式',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入联系方式',
            },
            'x-decorator': 'FormItem',
            'x-validator': (value) => {
              if (!value) return ''
              const regex = /^1[3-9]\d{9}$/;
              if (!regex.test(value)) return "请输入正确的联系方式"
            }
          },
          ...suffixFields,
        }
      },
    },
  }
}
function getHistorySchema(dataSource) {
  return composeSchema({
    url: {
      type: 'string',
      title: 'URL',
      'x-component': 'Input',
      'x-component-props': {
        placeholder: '请输入URL'
      },
      'x-decorator': 'FormItem',
      'x-validator': [
        {
          required: true,
          message: '请输入URL'
        },
        (value) => {
          if (!value) return '';
          const pattern = new RegExp(
            "^(https?|ftp|file)://[-A-Za-z0-9+&amp;@#/%?=~_|!:,.;]+[-A-Za-z0-9+&amp;@#/%=~_|]$"
          );
          if (!pattern.test(value)) return "请输入正确的URL"
        }
      ]
    },
    effectiveTime: {
      type: 'string',
      title: '有效期',
      'x-component': 'DatePicker',
      'x-decorator': 'FormItem',
      'x-component-props': {
        placeholder: '请选择有效期',
        valueFormat: 'YYYY-MM-DD HH:mm:ss',
        disabledDate: (current) => {
          const today = new Date();
          today.setHours(0, 0, 0, 0); // 将当前时间设为当天的零点
          return current < today; // 禁用今天以前的日期
        },
      },
      'x-validator': [
        {
          required: true,
          message: '请选择有效期'
        }
      ],
    },
  }, {
    deviceStatus: {
      type: 'number',
      default: 0,
      title: '设备状态',
      'x-component': 'Switch',
      'x-decorator': 'FormItem',
      enum: [
        { label: '启用', value: 1 },
        { label: '禁用', value: 0 },
      ],
      'x-validator': [
        {
          required: true,
          message: '请选择设备状态'
        }
      ]
    },
  }, dataSource)
}

function getCustomCommonSchema(lstField, dataSource) {
  const extFields = lstField.reduce((acc, cur) => {
    const { fieldKey, fieldName, fieldRequired } = cur;
    acc[fieldKey] = {
      type: 'string',
      title: fieldName,
      'x-component': fieldKey === 'password'? 'Password': 'Input',
      'x-component-props': {
        placeholder: `请输入${fieldName}`
      },
      'x-decorator': 'FormItem',
      'x-validator': [
        { required: fieldRequired, message: `请输入${fieldName}` }
      ]
    }
    return acc
  }, {});
  return composeSchema({
    deviceProtocol: {
      type: 'string',
      title: '通信协议',
      'x-component': 'Select',
      'x-decorator': 'FormItem',
      'x-component-props': {
        placeholder: '请选择通信协议',
      },
      enum: [
        { label: 'HTTPS', value: 'https' },
        { label: 'HTTP', value: 'http' },
      ],
      'x-validator': [
        {
          required: true,
          message: '请选择通信协议'
        }
      ]
    },
    deviceIp: {
      type: 'string',
      title: '设备IP',
      'x-component': 'Input',
      'x-component-props': {
        placeholder: '请输入设备IP'
      },
      'x-decorator': 'FormItem',
      'x-validator': [
        {
          required: true,
          message: '请输入设备IP'
        },
        (value) => {
          if (!value) return ''
          const regex = /^((?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))$/;
          if (!regex.test(value)) return "请输入正确的IP地址"
        }
      ]
    },
    devicePort: {
      type: 'number',
      title: '设备端口',
      'x-component': 'InputNumber',
      'x-component-props': {
        placeholder: '请输入设备端口'
      },
      'x-decorator': 'FormItem',
      'x-validator': [
        {
          required: true,
          message: '请输入设备端口'
        },
        (value) => {
          if (!value) return ''
          if (value < 1 || value > 65535) return "请输入正确的端口号"
        }
      ]
    },
    paramContent: {
      type: 'object',
      properties: {
        ...extFields,
      }
    }
  }, {}, dataSource)
}

export function getSchema(config, dataSource) {
  const { history = true, lstField } = config;
  if (history) return getHistorySchema(dataSource);
  return getCustomCommonSchema(lstField, dataSource)
}
