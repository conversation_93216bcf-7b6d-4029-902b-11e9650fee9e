<template>
  <a-modal v-model:visible="visible" :title="modalTitle" width="650px" :maskClosable="false" :keyboard="false">
    <ProFom :form="form" v-if="!loading" :schema="schema" />
    <template #footer>
      <div class="operator-group" :style="{ justifyContent: isView ? 'flex-end' : 'space-between' }">
        <a-button
          v-show="!isView"
          :loading="btnLoading"
          type="primary"
          @click="handleTest"
        >
          联通测试
        </a-button>
        <div class="operator-right">
          <a-button key="back" @click="handleCancel">{{ isView ? '关闭' : '取消' }}</a-button>
          <a-button
            :loading="btnLoading"
            v-show="!isView"
            key="submit"
            type="primary"
            @click="handleOk"
            >确定</a-button
          >
        </div>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { createForm } from "@formily/vue";
import { onFieldValueChange } from "@formily/core";
import {
  ref,
  defineExpose,
  defineEmits,
  shallowRef,
  defineProps,
  nextTick,
  computed,
} from "vue";
import ProFom from "@/components/pro-form/index.vue";
import { getSchema } from "./config.jsx";
import { postDeviceTest, updateSafetyDevice, addSafetyDevice } from "@/request/safe-device/index.js";
import { message } from "ant-design-vue";

const props = defineProps({
  typeData: {
    type: Array,
    default: () => [],
  },
});

const emits = defineEmits(["refresh"]);
const visible = ref(false);
const btnLoading = ref(false);

async function handleOk() {
  await form.value.validate();
  const payload = { ...form.value.values };
  btnLoading.value = true;
  try {
    switch (mode.value) {
      case "add":
        await addSafetyDevice(payload);
        message.success("新增成功");
        break;
      case "edit":
        await updateSafetyDevice(payload);
        message.success("编辑成功");
        break;
    }
    visible.value = false;
    emits("refresh");
  } finally {
    btnLoading.value = false;
  }
}

const form = shallowRef(null);
const schema = shallowRef(null);
const modalTitle = ref("添加设备");
const mode = ref("add");

const loading = ref(false);

const isView = computed(() => mode.value === "view");

function handleCancel() {
  visible.value = false;
}

function initForm(values) {
  const config =
    props.typeData.find((i) => i.value === values.deviceTypeId)
      ?.deviceTypeData ?? {};
  schema.value = getSchema(config, props.typeData);
  form.value = createForm({
    values: values,
    effects() {
      onFieldValueChange("deviceTypeId", () => {
        loading.value = true;
        setTimeout(() => {
          initForm({ ...form.value.values });
          loading.value = false;
        }, 10);
      });
    },
  });
}
function open({ title, data = {}, mode: modeType }) {
  initForm({ ...data });
  visible.value = true;
  mode.value = modeType;
  if (mode.value === "view") {
    setTimeout(() => {
      form.value.readPretty = true;
    }, 50);
  }
  modalTitle.value = title;
}

async function handleTest() {
  await form.value.validate();
  btnLoading.value = true;
  try {
    await postDeviceTest({...form.value.values });
    message.success("测试成功");
  } finally {
    btnLoading.value = false;
  }
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.operator-group {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
