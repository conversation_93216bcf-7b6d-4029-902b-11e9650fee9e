import { keyBy} from  'lodash'

const DEVICE_CONNECTION_STATUS_MAP = {
  0:'未探测',
  1:'在线',
  2:'离线',
  3:'异常'
}

export const SEARCH_CONFIG = [
  {
    title: '设备名称',
    name: 'deviceName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入设备名称',
    },
  },
  {
    title: '设备IP',
    name: 'deviceIp',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入设备IP',
    },
  },
  {
    title: '设备联通状态',
    name: 'connectionStatus',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择设备联通状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '在线', value: 1 },
      { label: '离线', value: 2 },
      { label: '异常', value: 3 },
    ],
  }
]

export function getTableColumns(typeData, pagination){
  const typeDataMap = keyBy(typeData, 'value');
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 80,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.pageSize * (pagination.current - 1) + index + 1;
      }
    },
    {
      title: "设备名称",
      dataIndex: "deviceName",
      width: 160,
      fixed: 'left',
      ellipsis: true,
    },
    {
      title: "版本型号",
      dataIndex: "deviceVersion",
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: "设备分类",
      dataIndex: "deviceTypeId",
      width: 120,
      align: "center",
      customRender({record = {}}){
        return record.deviceTypeData?.displayName ?? '-'
      },
      ellipsis: true,
    },
    {
      title: "设备IP",
      dataIndex: "deviceIp",
      width: 160,
      align: "center",
      ellipsis: true,
    },
    {
      title: "设备联通状态",
      dataIndex: "connectionStatus",
      width: 160,
      align: "center",
      customRender({text}){
        return DEVICE_CONNECTION_STATUS_MAP[text]
      }
    },
    {
      title: "健康状态",
      dataIndex: "healthStatus",
      width: 180,
      align: "center",
      ellipsis: true,
      customRender({text}){
        return <div>
          {text?.map(i => <div key={i.monitorKey}>
            <span>{i.monitorName}: </span>
            <span>{i.monitorValue}</span>
          </div>)}
        </div>
      }
    },
    {
      title: "负责人",
      dataIndex: "staff",
      width: 100,
      align: "center",
      ellipsis: true,
    },
    {
      title: "联系方式",
      dataIndex: "staffPhone",
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 260,
      fixed: 'right',
      align: "center",
    },
  ]
}

