<!-- 剧本流程配置弹窗 -->

<template>
  <el-dialog
    v-bind="$attrs"
    style="width: 82%; height: 900px; margin-top: 1%"
    :title="title"
    append-to-body
    :destroy-on-close="true"
    :close-on-press-escape="false"
    fullscreen
    @close="closeDialog"
  >
  <div class="d-flex j-center" style="width: 100%">
      <el-steps
        :active="activeSteps"
        finish-status="success"
        style="width: 80%"
      >
        <el-step title="填写流程信息" />
        <el-step title="流程绘制" />
      </el-steps>
    </div>
    <div class="mt-10">
      <el-form
        :model="form"
        :rules="rules"
        v-show="activeSteps == 0"
        ref="formRef"
        :label-width="120"
      >
        <el-row>
          <el-col :span="8">
            <el-form-item prop="process_name" label="流程名称">
              <el-input v-model.trim="form.process_name" clearable></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="type" label="流程类型">
              <el-select
                v-model="form.type"
                style="width: 100%"
                @change="selectType"
                clearable
              >
                <el-option
                  label="事件"
                  :value="1"
                  @click="thingClick"
                ></el-option>
                <el-option label="工单" :value="2"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="formKey" label="表单" v-if="form.type === 2">
              <el-select v-model="form.formKey" style="width: 100%" clearable>
                <el-option
                  v-for="key in formKeyList"
                  :key="key"
                  :label="key"
                  :value="key"
                ></el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col :span="8" v-if="form.type===1">
                        <el-form-item prop="rules" label="触发规则">
                            <el-select v-model="form.rules" placeholder=" " :disabled="title == '修改案例流程'"
                                style="width: 100%;" clearable>
                                <template v-for="item in ruleList" :key="item.value">
                                    <el-option v-if="title != '修改案例流程' && item.show" :label="item.label"
                                        :value="item.value" />
                                    <el-option
                                        v-else-if="(title == '修改案例流程' || title == '编辑流程') && form.rules == item.value ? true : item.show"
                                        :label="item.label" :value="item.value" />
                                    <el-option v-else-if="item.show" :label="item.label" :value="item.value" />
                                </template>
                            </el-select>
                        </el-form-item>
                    </el-col> -->
        </el-row>
        <!-- 案件 -->
        <!-- <el-row v-if="form.type === 1">
                    <el-col :span="8">
                        <el-form-item prop="caseType" label="案件类型">
                            <el-select v-model="form.caseType" filterable allow-create default-first-option
                                :reserve-keyword="false" placeholder="请选择案件类型或者输入新的案件类型" style="width:100%" clearable>
                                <el-option v-for="(item, index) in casesList" :key="index" :label="item" :value="item" />
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item prop="caseName" label="案件名称">
                            <el-input v-model.trim="form.caseName" clearable></el-input>
                        </el-form-item>
                    </el-col>
                    <el-col :span="8">
                        <el-form-item prop="caseDes" label="案件描述">
                            <el-input v-model.trim="form.caseDes" type="textarea"
                                :autosize="{ minRows: 1, maxRows: 5 }"></el-input>
                        </el-form-item>
                    </el-col>
                </el-row> -->
        <el-form-item prop="des" label="流程描述">
          <el-input
            type="textarea"
            :rows="18"
            placeholder="请输入内容"
            v-model.trim="form.des"
          >
          </el-input>
        </el-form-item>
      </el-form>
      <el-row v-loading="loadingProcess" element-loading-text="加载流程中……">
        <el-col :span="17">
          <ProcessDesigner
            ref="ProcessDesignerRef"
            :style="{ height: ContainerHeight }"
            @create="createBpmn"
            :showTools="true"
          />
        </el-col>
        <el-col :span="7">
          <div :style="{ height: ContainerHeight, overflow: 'auto' }">
            <h2 style="text-align: center">节点属性配置</h2>
            <component
              :is="Component"
              :processType="currentNode.type"
              :nodeId="nodeId"
              @save="saveNodeInfo"
              :nodeInfo="nodeInfo"
              :disabled="mode === 'view'"
            />
          </div>
        </el-col>
      </el-row>
    </div>
    <template #footer>
      <div style="text-align: center">
        <el-button @click="closeDialog">取消</el-button>
        <el-button v-if="mode === 'edit'" type="primary" @click="submit"
          >保存</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
/* eslint-disable */

import {
  ref,
  nextTick,
  shallowReactive,
  computed,
  watch,
  defineProps,
  reactive,
  shallowRef,
  defineEmits,
  onBeforeMount,
  onMounted,
} from "vue";

import ProcessDesigner from "../../../components/bpmn/process_designer.vue";

import elementHelper from "bpmn-js-properties-panel/lib/helper/ElementHelper";

import useTableHeight from "@/utils/util";

import CustomComponents from "./custom-components";

import ProcessMService from "@/request/processmanage";

import checkProcessValidity from "./process_test/index.js";

import { ElMessage } from "element-plus";

import convert from "xml-js";

const emit = defineEmits(["update:modelValue", "close"]);

const props = defineProps({
  mode: {
    type: String,
    require: true,
    default: "edit",
  },
  deployId: {
    type: String,
    default: "",
  },
  currentProcess: {
    type: Object,
    default: () => {},
  }
});

const ContainerHeight = useTableHeight(-20);

// 各类不同节点监听配置---触发器、事件名、fields
const listener = {
  // 开始
  startListener: {
    delegateExpression: "${startEvensListener}",
    event: "start",
    fields: [],
  },
  // 普通任务
  systemTaskListener: {
    delegateExpression: "${serviceTaskListener}",
    event: "start",
    fields: [],
  },
  // 用户任务-创建
  userTaskStartListener: {
    delegateExpression: "${userTaskStartListener}",
    event: "create",
    fields: [],
  },
  // 用户任务-完成
  userTaskListener: {
    delegateExpression: "${userTaskCompleteListener}",
    event: "complete",
    fields: [],
  },
  // 结束
  endListener: {
    delegateExpression: "${endEvensListener}",
    event: "start",
    fields: [],
  },
  exclusiveGatewayListener: {
    class: "cn.broadtech.soarcamunda.listener.ExclusiveGatewayListener",
    event: "start",
    fields: [],
  },
  parallelGatewayListener: {
    class: "cn.broadtech.soarcamunda.listener.ExclusiveGatewayListener",
    event: "start",
    fields: [],
  },
};

const Component = computed(() => {
  return CustomComponents[currentNode.type];
});

let bpmnModeler = null;

const ProcessDesignerRef = ref();

function createBpmn(modeler) {
  bpmnModeler = modeler;

  addModelerListener();

  updateProcessId();
}

function updateProcessId() {
  nextTick(() => {
    let e = [];
    tools.elementRegistry.filter((item) => {
      e.push(item);
    });
    //设置流程id和可运行参数
    tools.modeling.updateProperties(e[0], {
      id: "P-" + getUid().replace(/-/g, ""),
      isExecutable: true,
    });
  });
}

const tools = {
  eventBus: null,
  modeling: null,
  elementRegistry: null,
  bpmnFactory: null,
};

function addModelerListener() {
  tools.eventBus = bpmnModeler.get("eventBus");
  tools.modeling = bpmnModeler.get("modeling");
  tools.elementRegistry = bpmnModeler.get("elementRegistry");
  tools.bpmnFactory = bpmnModeler.get("bpmnFactory");

  tools.eventBus.on("element.click", eventHandler);

  tools.eventBus.on("shape.removed", (e) => {
    const { element } = e;
    if (element.id == currentNode.id) {
      currentNode.id = null;
      currentNode.type = "null";
      currentNode.e = null;
      currentNode.element = null;
      currentNode.shape = null;
    }
  });

  tools.eventBus.on("connection.removed", (e) => {
    const { element } = e;
    if (element.id == currentNode.id) {
      currentNode.id = null;
      currentNode.type = "null";
      currentNode.e = null;
      currentNode.element = null;
      currentNode.shape = null;
    }
  });

  tools.eventBus.on("shape.added", eventHandler);
}

const currentNode = shallowReactive({
  id: null,
  type: "null",
  e: null,
  element: null,
  shape: null,
});

/**
 * @description 判断是否为可编辑节点的附加条件
 */
function additionalConditions(element) {
  let source = null,
    sourceOutgoing = null;
  switch (element.type) {
    case "bpmn:SequenceFlow":
      source = element.source;
      sourceOutgoing = source.outgoing;
      if (
        source.type == "bpmn:ExclusiveGateway" ||
        source.type == "bpmn:ParallelGateway"
      ) {
        return sourceOutgoing.length >= 2;
      } else {
        return false;
      }
    default:
      return true;
  }
}

function eventHandler(e) {
  const { element, shape } = e;
  const types = Object.keys(CustomComponents);
  if (types.some((el) => el == element.type) && additionalConditions(element)) {
    currentNode.id = element.id;
    currentNode.e = e;
    currentNode.type = element.type;
    currentNode.element = element;
    currentNode.shape = shape;
    parseXmlDataToForm(element.type, element);
  } else {
    currentNode.type = "null";
  }
}
const nodeInfo = shallowRef({});
const nodeId = shallowRef(null);
function parseXmlDataToForm(type, element) {
  const { businessObject } = element;
  nodeInfo.value = {};
  let assign = {};
  let extensionElements, values, fields, label, conditionExpression, properties;
  switch (type) {
    case "bpmn:StartEvent":
    case "bpmn:EndEvent":
      assign.name = businessObject.name || "";
      nodeInfo.value = assign;
      break;
    case "bpmn:SequenceFlow":
      assign = {
        name: "",
        field: "",
      };
      label = element.label;
      conditionExpression = businessObject.conditionExpression;
      if (label && label.businessObject) {
        assign.name = label.businessObject.name;
      }
      if (conditionExpression && conditionExpression.body) {
        assign.field = conditionExpression.body;
      }
      nodeInfo.value = assign;
      break;
    case "bpmn:Task":
    case "bpmn:UserTask":
      nodeId.value = element.id;
      extensionElements = businessObject.extensionElements;
      if (extensionElements && extensionElements.values) {
        values = extensionElements.values;
        fields = values[0].fields;
        fields.forEach((field) => {
          assign[field.name] = field.string;
        });
      }
      nodeInfo.value = assign;
      break;
    case "bpmn:CallActivity":
      assign = {
        name: businessObject.name || "",
        calledElement: businessObject.calledElement || "",
      };

      nodeInfo.value = assign;
      break;
    case "bpmn:ExclusiveGateway":
    case "bpmn:ParallelGateway":
      nodeInfo.value = {
        name: businessObject.name,
      };
      break;
  }
}

function saveNodeInfo(form) {
  /* eslint-disable */
  let shape =
    (currentNode.element &&
      tools.elementRegistry.get(currentNode.element.id)) ||
    currentNode.shape ||
    currentNode.e;

  let extensionElements,
    executionListener,
    userTaskStartListener,
    userTaskListener,
    conditionExpression;

  switch (currentNode.type) {
    case "bpmn:StartEvent":
      extensionElements = elementHelper.createElement(
        "bpmn:ExtensionElements",
        { values: [] },
        currentNode.element.businessObject,
        tools.bpmnFactory
      );

      executionListener = elementHelper.createElement(
        "camunda:ExecutionListener",
        listener.startListener,
        currentNode.element.businessObject.extensionElements,
        tools.bpmnFactory
      );

      extensionElements.values.push(executionListener);
      tools.modeling.updateProperties(shape, {
        asyncAfter: true,
        name: form.name,
        extensionElements: extensionElements,
      });
      break;
    case "bpmn:ExclusiveGateway":
    case "bpmn:ParallelGateway":
      tools.modeling.updateProperties(shape, {
        asyncAfter: true,
        name: form.name,
      });
      break;
    case "bpmn:SequenceFlow":
      conditionExpression = bpmnModeler._moddle.create(
        "bpmn:FormalExpression",
        { body: form.field }
      );
      tools.modeling.updateProperties(shape, {
        name: form.name,
        conditionExpression: conditionExpression,
      });

      break;
    case "bpmn:Task":
      extensionElements = elementHelper.createElement(
        "bpmn:ExtensionElements",
        { values: [] },
        currentNode.element.businessObject,
        tools.bpmnFactory
      );

      executionListener = elementHelper.createElement(
        "camunda:ExecutionListener",
        listener.systemTaskListener,
        currentNode.element.businessObject.extensionElements,
        tools.bpmnFactory
      );

      extensionElements.values.push(executionListener);

      listener.systemTaskListener.fields = [];

      Object.entries(form).forEach((keyValue) => {
        let [key, value] = keyValue;
        executionListener.fields.push(
          elementHelper.createElement(
            "camunda:Field",
            {
              name: key,
              string: String(value),
            },
            executionListener,
            tools.bpmnFactory
          )
        );
      });

      tools.modeling.updateProperties(shape, {
        asyncAfter: true,
        name: form.name,
        extensionElements: extensionElements,
      });

      break;
    case "bpmn:UserTask":
      extensionElements = elementHelper.createElement(
        "bpmn:ExtensionElements",
        { values: [] },
        currentNode.element.businessObject,
        tools.bpmnFactory
      );

      userTaskStartListener = elementHelper.createElement(
        "camunda:TaskListener",
        listener.userTaskStartListener,
        currentNode.element.businessObject.extensionElements,
        tools.bpmnFactory
      );

      extensionElements.values.push(userTaskStartListener);

      listener.userTaskStartListener.fields = [];

      Object.entries(form).forEach((keyValue) => {
        let [key, value] = keyValue;

        userTaskStartListener.fields.push(
          elementHelper.createElement(
            "camunda:Field",
            {
              name: key,
              string: String(value),
            },
            userTaskStartListener,
            tools.bpmnFactory
          )
        );
      });

      //人工提交任务监听器--------------------------------------------
      userTaskListener = elementHelper.createElement(
        "camunda:TaskListener",
        listener.userTaskListener,
        currentNode.element.businessObject.extensionElements,
        tools.bpmnFactory
      );
      extensionElements.values.push(userTaskListener);
      //人工提交任务监听器--------------------------------------------

      tools.modeling.updateProperties(shape, {
        asyncAfter: true,
        name: form.name,
        extensionElements: extensionElements,
      });

      break;
    case "bpmn:CallActivity":
      extensionElements = elementHelper.createElement(
        "bpmn:ExtensionElements",
        { values: [] },
        currentNode.element.businessObject,
        tools.bpmnFactory
      );

      let inCamunda = elementHelper.createElement(
        "camunda:In",
        {
          source: "processParams",
          target: "processParams",
        },
        currentNode.element.businessObject.extensionElements,
        tools.bpmnFactory
      );

      let outCamunda = elementHelper.createElement(
        "camunda:Out",
        {
          source: "processParams",
          target: "processParams",
        },
        currentNode.element.businessObject.extensionElements,
        tools.bpmnFactory
      );

      extensionElements.values.push(inCamunda, outCamunda);

      tools.modeling.updateProperties(shape, {
        asyncAfter: true,
        name: form.name,
        calledElement: form.calledElement,
        extensionElements: extensionElements,
      });
      break;
    case "bpmn:EndEvent":
      extensionElements = elementHelper.createElement(
        "bpmn:ExtensionElements",
        { values: [] },
        currentNode.element.businessObject,
        tools.bpmnFactory
      );
      executionListener = elementHelper.createElement(
        "camunda:ExecutionListener",
        listener.endListener,
        currentNode.element.businessObject.extensionElements,
        tools.bpmnFactory
      );
      extensionElements.values.push(executionListener);
      tools.modeling.updateProperties(shape, {
        asyncAfter: true,
        name: form.name,
        extensionElements: extensionElements,
      });
      break;
  }
}

/**
 * @description 获取流程文件，并校验流程文件的合法性
 */
async function getProcessFile() {
  const { xml } = await bpmnModeler.saveXML();
  if (checkProcessValidity(convert.xml2js(xml), false)) {
    return new window.File([xml], getUid() + ".xml");
  }
  return Promise.reject(false);
}

function getUid() {
  let S4 = () => {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1);
  };
  return `${S4() + S4()}-${S4()}-${S4()}${S4()}${S4()}`;
}

// ======================================================   ============================

const formRef = ref();

/**
 * @description　手动校验流程节点是否填写正确
 */
function checkXmlValidity() {
  bpmnModeler.saveXML().then((res) => {
    checkProcessValidity(convert.xml2js(res.xml));
  });
}
const getText = (str) => {
  return str
    .replace(/<[^<p>]+>/g, "") // 将所有<p>标签 replace ''
    .replace(/<[</p>$]+>/g, "") // 将所有</p>标签 replace ''
    .replace(/&nbsp;/gi, "") // 将所有 空格 replace ''
    .replace(/<[^<br/>]+>/g, "") // 将所有 换行符 replace ''
    .replace(/\s+/g, "");
};

async function submit() {
  const file = await getProcessFile();
  const params = new FormData();
  params.append("file", file);
  params.append("processName", form.process_name);
  params.append("des", getText(form.des));
  params.append("type", form.type);
  params.append("formKey", form.formKey);
  params.append("processId", form.processId);
  params.append("reDeploy", 1);
  await ProcessMService.editProcess(params);
  closeDialog();
}

function closeDialog() {
  emit("update:modelValue", false);
  emit("close", false);
}

// ====================  编辑相关逻辑 =================

onMounted(() => getProcessXML());
const form = reactive({
  rules: "",
  process_name: "",
  type: null,

  caseType: "",
  caseName: "",
  caseDes: "",

  des: "",
  eventId: "",
  rootProcessInstanceId: "",
  deployId: "",
  formKey: "",
});
onBeforeMount(() => {
  nextTick(() => {
    form.des = props.currentProcess.des;
    form.process_name = props.currentProcess.processName;
    form.type = props.currentProcess.type;
    form.formKey = props.currentProcess.formKey;
    form.processId = props.currentProcess.processId;
    form.deployId = props.currentProcess.deployId;
  });
});

const title = computed(() => (props.mode == "edit" ? "编辑流程" : "新建流程"));

const loadingProcess = ref(false);
async function getProcessXML() {
  loadingProcess.value = true;
  const { data: xmlStr } = await ProcessMService.getBpmnXml(props.deployId);

  if (xmlStr && bpmnModeler) {
    bpmnModeler.importXML(xmlStr);
    loadingProcess.value = false;
  } else {
    loadingProcess.value = false;
    ElMessage.warning("流程文件存在问题");
  }
}
</script>

<style lang="less" scoped>
:deep(.bjs-powered-by) {
  display: none;
}

:deep(.bjs-container) {
  border: 2px solid #eee;
}

//新建流程流程描述边框颜色
:deep(.w-editor-border) {
  border: 1px solid #b0d1ff;
}

:deep(div[data-w-e-toolbar]) {
  border-bottom: 1px solid #b0d1ff !important;
}

</style>

<style lang="less">

</style>
