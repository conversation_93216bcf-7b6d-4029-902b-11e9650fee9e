<!-- 节点属性配置表单 -->

<template>
  <div class="task-item">
    <el-form
      :model="form"
      ref="formRef"
      :rules="rules"
      :label-width="120"
      :disabled="disabled"
    >
      <el-form-item prop="name" label="节点名称">
        <el-input v-model.trim="form.name"></el-input>
      </el-form-item>
      <el-form-item prop="assignedMode" label="指派类型">
        <el-radio-group v-model="form.assignedMode">
          <el-radio label="2">分配至角色</el-radio>
          <el-radio label="1">分配至账号</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        prop="assignedUser"
        label="指派账号"
        v-if="form.assignedMode === '1'"
      >
        <el-select
          v-model="form.assignedUser"
          multiple
          style="width: 100%"
          filterable
          allow-create
        >
          <el-option
            v-for="item in userList"
            :key="item.id"
            :label="item.userName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item
        prop="assignedUser"
        label="指派角色"
        v-if="form.assignedMode === '2'"
      >
        <el-select
          v-model="form.assignedUser"
          multiple
          style="width: 100%"
          filterable
          allow-create
        >
          <el-option
            v-for="item in roleList"
            :key="item.id"
            :label="item.roleName"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div class="operator" v-show="!disabled">
      <el-button @click="reset">取消</el-button>
      <el-button type="primary" @click="submit">保存</el-button>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  defineProps,
  defineEmits,
  watch,
  reactive,
  shallowRef,
  onMounted,
} from "vue";
import ProFrom from "@/components/pro-form/index.vue";
import { createForm } from "@formily/vue";
import ProcessMService from "@/request/processmanage";

const emits = defineEmits(["save"]);

const props = defineProps({
  nodeInfo: {
    type: Object,
    default: () => ({}),
  },
  disabled: {
    type: Boolean,
    default: false,
  }
});

console.log(props);

const form = reactive({
  name: "", // 节点名称
  assignedMode: "", // 指派类型
  assignedUser: [], // 指派账号
  acceptPattern: "2",
});
const rules = {
  name: [
    { required: true, message: "请输入节点名称" },
    { max: 30, message: "长度为1-30字符" },
    {
      message: "输入内容存在除数字,字母,下划线,中划线,汉字以外字符",
      pattern: /^[a-zA-Z0-9-_\u4e00-\u9fa5]+$/,
    },
  ],
  assignedMode: { required: true, message: "请选择指派类型" },
  assignedUser: { required: true },
};

watch(
  () => props.nodeInfo,
  (val) => {
    const { name, assignedMode = '',assignedUser = '' } = val;
    console.log(val);
    Object.assign(form, {
      name,
      assignedMode,
      assignedUser: assignedUser?.split(',')??[],
    });
  },
  { immediate: true }
);

const userList = shallowRef([]);
const roleList = shallowRef([]);
onMounted(async () => {
  const [userRes, roleRes] = await Promise.all([
    ProcessMService.getAlluser({}),
    ProcessMService.getAllrole({}),
  ]);
  userList.value = userRes.data;
  roleList.value = roleRes.data;
});

// 表单重置
function reset() {
  formRef.value.reset();
}

const formRef = ref(null);

// 提交
async function submit() {
  await formRef.value.validate();
  emits("save", { ...form });
}
</script>

<style lang="less" scoped>
.task-item {
  padding-left: 16px;
  .operator {
    float: right;
    margin-right: 16px;
  }
}
</style>
