// 节点组件映射

import NullNode from "../business_components/NullNode.vue";

import StartEvent from "../business_components/StartEvent.vue";

import Task from "./task.vue";

import CallActivity from "../business_components/CallActivity.vue"

import ExclusiveGateway from "../business_components/ExclusiveGateway.vue";

import ParallelGateway from "../business_components/ParallelGateway.vue";

import EndEvent from "../business_components/./EndEvent.vue";

import SequenceFlow from "../business_components/SequenceFlow.vue"

export default {
    'null': NullNode,
    'bpmn:StartEvent': StartEvent,
    'bpmn:Task': Task,
    'bpmn:UserTask': Task,
    'bpmn:CallActivity': CallActivity,
    'bpmn:ExclusiveGateway': ExclusiveGateway,
    'bpmn:ParallelGateway': ParallelGateway,
    'bpmn:EndEvent': EndEvent,
    'bpmn:SequenceFlow': SequenceFlow,
}
