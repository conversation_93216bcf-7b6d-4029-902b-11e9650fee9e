export const BASE_CONFIG = [
  {
    label: '工单名称',
    prop: 'name'
  },
  {
    label: '工单类型',
    prop: 'type'
  },
  {
    label: '流水号',
    prop: 'id'
  },
  {
    label: '发起人',
    prop: 'creator'
  },
  {
    label: '创建时间',
    prop: 'createTime'
  },
  {
    label: '工单状态',
    prop:'flowStatus',
    render: (h, { data }) => {
      const MAP = {
        1: '待审核',
        2: '待修改',
        8: '审核通过',
        9: '已关闭'
      }
      return <span>{MAP[data.flowStatus]}</span>
    }
  },
  {
    label: '当前所属节点',
    prop: 'nowNode'
  },
  {
    label: '当前节点处置人',
    prop: 'nowUser',
    gridSpan: 2
  },
]


// 漏洞管理流程
export const LEAK_MANAGE_CONFIG = [
  {
    label: '漏洞名称',
    prop: 'vulName'
  },
  {
    label: '资产IP',
    prop: 'ip'
  },
  {
    label: 'CVE编号',
    prop: 'cveNumber'
  },
  {
    label: '威胁类型',
    prop: 'threatCategory'
  },
  {
    label: '应用类别',
    prop: 'applicationCategory'
  },
  {
    label: 'CNCVD编号',
    prop: 'cnnvdNumber'
  },
  {
    label: '风险评分',
    prop: 'riskPoints'
  },
  {
    label: '发现日期',
    prop: 'dateFound'
  },
  {
    label: 'CNNVD编号',
    prop: 'cnnvdNumber'
  },
  {
    label: '漏洞级别',
    prop: 'level',
    render(h, {prop, data}){
      const LEVEL_MAP = {
        1: '低级', 2: '中级', 3: '高级',
      }
      return LEVEL_MAP[data.level]
    },
  },
  {
    label: '创建时间',
    prop: 'createTime'
  },
  {
    label: '修改时间',
    prop: 'updateTime'
  }
]


/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index',
  },
  {
    title: '漏洞名称',
    dataIndex: 'vulName',
    key: 'vulName'
  },
  {
    title: 'CVE编号',
    dataIndex: 'cveNumber',
    key: 'cveNumber'
  },
  {
    title: '危害等级',
    dataIndex: 'assetIp',
    key: 'assetIp'
  },
  {
    title: '影响资产数',
    dataIndex:'mac',
    key:'mac'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 180
  }
]
