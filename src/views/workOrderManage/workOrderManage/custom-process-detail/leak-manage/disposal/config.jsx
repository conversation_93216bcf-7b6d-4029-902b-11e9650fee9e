import { defineComponent, h } from "vue"

export const DISPOSAL_SCHEMA_MAP = {
  'Vul-Report': {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          desc: {
            type: 'string',
            title: '提报说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '根据提供漏洞存说明，现建议修复建议：{ 针对漏洞提出具体的修复建议，包括修复方案、修复步骤等 }',
            'x-validator': [
              { required: true, message: '请输入提报说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
  'Vul-Restore':{
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          disposeMsg: {
            type: 'string',
            title: '已经修复',
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            enum: [
              { label: '已修复', value: '已修复' },
              { label: '未修复', value: '未修复' },
            ],
            'x-reactions': field => {
              const flowStatus = field.value === '未修复' ? 2 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请选择是否已经修复' }
            ]
          },
          desc: {
            type: 'string',
            title: '漏洞修复说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '漏洞已成功识别并快速修复，系统已恢复安全状态。此次修复后有效提升了系统的整体安全防护能力，确保了业务稳定运行。',
            'x-validator': [
              { required: true, message: '请输入漏洞修复说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
  'Vul-Approval': {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          disposeMsg: {
            type: 'string',
            title: '审核结果',
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            enum: [
              { label: '通过', value: '通过' },
              { label: '不通过', value: '不通过' },
            ],
            'x-validator': [
              { required: true, message: '请选择审核结果' }
            ]
          },
          desc: {
            type: 'string',
            title: '审核结果说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            'x-reactions': field => {
              const flowStatus = field.value === '未修复' ? 2 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请输入审核结果说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
}
