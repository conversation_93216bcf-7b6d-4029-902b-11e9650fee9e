<!-- 工单详情弹窗入口 -->

<template>
  <component :is="componentName" v-bind="$attrs" @close="handleClose"  />
</template>

<script setup>
import { ref, defineProps, defineEmits, computed } from "vue";
import BusinessOnline from "./business-online/index.vue";
import LeakScan from "./leak-scan/index.vue";
import ThreatAlert from './threat-alert/index.vue'
import LeakManage from './leak-manage/index.vue'

const emit = defineEmits(["close"]);


const props = defineProps({
  processType: {
    type: String,
    required: true,
  },
});

const componentName = computed(() => {
  console.log(props.processType);
  switch (props.processType) {
    case "businessOnline":
      return BusinessOnline;
    case "leakScan":
      return LeakScan;
    case "threatAlert":
      return ThreatAlert
    case "leakManage":
      return LeakManage
    default:
      return BusinessOnline;
  }
});

// 关闭弹窗
function handleClose() {
  console.log("close");
  emit("close");
}
</script>
