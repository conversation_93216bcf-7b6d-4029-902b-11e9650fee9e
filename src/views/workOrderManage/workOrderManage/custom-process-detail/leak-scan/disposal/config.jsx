import { defineComponent, h } from "vue"

import { useAsyncDataSource } from "@/utils/util";

import { getTaskList } from "@/request/api-device-leak-scan";
import { getDeviceList } from "@/request/safe-device";

export const DISPOSAL_SCHEMA_MAP = {
  'Vul-Scan-Apply': {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          disposeMsg: {
            type: 'string',
            title: '审核结果',
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            enum: [
              { label: '通过', value: '通过' },
              { label: '不通过', value: '不通过' },
            ],
            'x-reactions': field => {
              const flowStatus = field.value === '不通过' ? 2 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请选择审核结果' }
            ]
          },
          desc: {
            type: 'string',
            title: '审核说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '经过仔细审阅您提交的扫描任务申请说明，我认为该申请充分展现了您对公司信息系统安全性的高度责任感和前瞻性，我认为本次扫描任务  {可以执行 /  不可以执行 }',
            'x-validator': [
              { required: true, message: '请输入审核说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
  'Vul-Scan-Approval': {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          disposeMsg: {
            type: 'string',
            title: '审核结果',
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            enum: [
              { label: '通过', value: '通过' },
              { label: '不通过', value: '不通过' },
            ],
            'x-reactions': field => {
              const flowStatus = field.value === '不通过' ? 9 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请选择审核结果' }
            ]
          },
          desc: {
            type: 'string',
            title: '审核说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '经过仔细审阅您提交的扫描任务申请说明，我认为该申请充分展现了您对公司信息系统安全性的高度责任感和前瞻性，我认为本次扫描任务  {可以执行 /  不可以执行 }',
            'x-validator': [
              { required: true, message: '请输入审核说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
  'Vul-Scan-Result-Report':{
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          'safetyId': {
            type: 'string',
            title: '扫描设备',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择扫描设备'
            },
            'x-reactions': useAsyncDataSource(async () => {
              const { data } = await getDeviceList({page: 1, size: 99999, deviceTypeId: 1});
              return data?.data?.map(i => ({ label: i.deviceName, value: i.id }))
            })
          },
          createTask: {
            type: 'void',
            'x-component': defineComponent({
              setup() {
                function handleClick() {
                  console.log('create task')
                }
                return () => <a-button type="primary" style="margin-left: 120px" onClick={handleClick}>发起扫描任务</a-button>
              }
            }),
            title: '',
            'x-decorator': 'FormItem',
            'x-decorator-props': {
              addonAfter: '需要人工创建扫描任务，并在扫描任务信息中选择已经创建的任务',
            },
          },
          'lstDataId': {
            type: 'string',
            title: '扫描任务',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              mode:"multiple",
              placeholder: '请选择扫描任务'
            },
            'x-reactions': async field => {
              const safetyId = field.query('.safetyId').value();
              if(!safetyId) return ;
              const { data } = await getTaskList({page: 1, size: 99999, deviceSafetyId: safetyId});
              const dataSource = data?.map(i => ({ label: i.taskName, value: i.taskID }))??[];
              field.setDataSource(dataSource)
            }
          },
          desc: {
            type: 'string',
            title: '扫描结果说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '经过本次扫描发现发现 { 安全漏洞、弱密码、未授权访问点 等发现的问题 }等问题。对扫描结果进行深入分析，识别问题的根源及潜在原因。评估问题对公司业务安全及稳定性的影响程度。并  { 做了那些操作 }，以防止风险发生，本次扫描结束，请审查。',
            'x-validator': [
              { required: true, message: '请输入扫描结果说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
  'Vul-Scan-Result-Approval':  {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          disposeMsg: {
            type: 'string',
            title: '审核结果',
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            enum: [
              { label: '通过', value: '通过' },
              { label: '不通过', value: '不通过' },
            ],
            'x-reactions': field => {
              const flowStatus = field.value === '不通过' ? 2 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请选择审核结果' }
            ]
          },
          desc: {
            type: 'string',
            title: '审核说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '经过对提交的扫描结果上报说明的详细审查，我认为该说明内容全面、结构清晰，有效地涵盖了扫描结果上报的各个关键环节，审核结果为  {通过 / 不通过，请继续整改}',
            'x-validator': [
              { required: true, message: '请输入审核说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
}
