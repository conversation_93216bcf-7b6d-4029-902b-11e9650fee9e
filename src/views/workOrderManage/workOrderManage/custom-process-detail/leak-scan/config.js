
export const BASE_CONFIG = [
  {
    label: '工单名称',
    prop: 'name'
  },
  {
    label: '工单类型',
    prop: 'type'
  },
  {
    label: '流水号',
    prop: 'id'
  },
  {
    label: '发起人',
    prop: 'creator'
  },
  {
    label: '创建时间',
    prop: 'createTime'
  },
  {
    label: '工单状态',
    prop: 'flowStatus',
    render: (h, { data }) => {
      const MAP = {
        1: '待审核',
        2: '待修改',
        8: '审核通过',
        9: '已关闭'
      }
      return <span>{MAP[data.flowStatus]}</span>
    }
  },
  {
    label: '当前所属节点',
    prop: 'nowNode'
  },
  {
    label: '当前节点处置人',
    prop: 'nowUser',
    gridSpan: 2
  },
]


/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: '任务名称',
    dataIndex: 'taskName',
    key: 'taskName'
  },
  {
    title: '扫描目标',
    dataIndex: 'assetType',
    key: 'assetType'
  },
  {
    title: '扫描进度',
    dataIndex: 'assetIp',
    key: 'assetIp'
  },
  {
    title: '开始时间',
    dataIndex:'mac',
    key:'mac'
  },
  {
    title: '任务类型',
    dataIndex: 'number',
    key: 'number'
  },
  {
    title: '任务状态',
    dataIndex: 'number',
    key: 'number'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 360
  }
]
