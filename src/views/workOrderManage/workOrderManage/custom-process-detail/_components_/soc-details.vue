<!-- 详情表格展示组件 -->

<template>
  <div class="soc-details" :style="`grid-template-columns: repeat(${colSpan}, ${(100/colSpan).toFixed(2)}%);`">
    <div
      class="soc-details__item"
      v-for="item in config"
      :key="item.prop"
      :style="{
        gridColumnStart: `span ${item.gridSpan ?? 1}`,
      }"
    >
      <div class="soc-details__item-title">
        <div class="title-label">
        {{ item.label }}
        </div>
      </div>
      <div class="soc-details__item-content">
        <component
          v-if="item.render"
          :is="() => item.render(h, { prop: item.prop, data: data })"
        />
        <div v-else>{{ data[item.prop] ?? "-" }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, defineProps } from "vue";

const props = defineProps({
  config: {
    type: Array,
    default: () => [],
  },
  data: {
    type: Object,
    default: () => ({}),
  },
  colSpan: {
    type: Number,
    default: 3,
  }
});
</script>

<style lang="less" scoped>
.soc-details {
  display: grid;
  border-left: 1px solid #ccc;
  border-top: 1px solid #ccc;
  .soc-details__item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-right: 1px solid #ccc;
    border-bottom: 1px solid #ccc;
    &-title {
      width: 180px;
      padding: 8px 16px;
      height: 100%;
      display: flex;
      align-items: center;
      border-right: 1px solid #ccc;
      background-color: #fafafa;
    }
    &-content {
      flex: 1;
      padding: 8px 16px;
    }
  }
}
</style>

