<!-- 工单详情-漏洞详情 -->

<template>
  <a-table
    :data-source="dataSource"
    :columns="COLUMNS"
    :loading="loading"
    :pagination="pagination"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'operation'">
        <a-button type="link" @click="handleViewDetails(record)">详情</a-button>
      </template>
    </template>
  </a-table>
  <CommonDetails ref="commonDetailsRef" />
</template>

<script setup>
import { usePagination } from "vue-request";

import {
  COLUMNS,
  TASK_DETAIlS_CONFIG,
} from "@/views/device/components/leak-scan/task-management/config";
import {
  computed,
  getCurrentInstance,
  reactive,
  defineProps,
  createVNode,
  toRefs,
  nextTick,
  ref,
  inject,
} from "vue";
import { getTaskList } from "@/request/api-device-leak-scan.js";
import CommonDetails from "@/components/common-details/index.vue";

const props = defineProps({
  safetyId: {
    type: String,
    required: true,
  },
  taskIds: {
    type: Array,
    required: true,
  },
});

const { data, run, loading, total, current, pageSize, reload } = usePagination(
  async (p) => {
    let { data } = await getTaskList({ ...p, deviceSafetyId: props.safetyId });
    console.log(data, props.taskIds, props.safetyId);
    data = data.filter((i) => props.taskIds.includes(i.taskID));
    return { data };
  },
  {
    manual: true,
    formatResult: (res) => ({
      items: res.data,
      total: res?.data?.length || 0,
    }),
    pagination: {
      currentKey: "page",
      pageSizeKey: "size",
      totalKey: "total",
    },
  }
);

const pagination = computed(() => ({
  total: total.value,
  current: current.value,
  pageSize: pageSize.value,
  showQuickJumper: true,
  showSizeChanger: true,
  showTotal: (total) => `共 ${total} 条`,
}));
const dataSource = computed(() => {
  return data.value?.items || [];
});

const refreshTableData = (isReload = true) => {
  run({
    page: isReload ? 1 : pagination.value.current,
    size: pagination.value.pageSize,
  });
};
const commonDetailsRef = ref(null);

refreshTableData();

function handleViewDetails(row) {
  commonDetailsRef.value.open({
    data: { ...row, deviceSafetyId: props.safetyId },
    config: TASK_DETAIlS_CONFIG,
  });
}
</script>

<style>
</style>
