<!-- 结束工单弹窗 -->

<template>
  <a-modal
    title="结束工单"
    width="720px"
    v-model:visible="visible"
    @ok="handleSubmit"
  >
    <FormProvider v-if="visible" :form="form">
      <SchemaField :schema="schema" />
    </FormProvider>
  </a-modal>
</template>

<script setup>
import { message } from "ant-design-vue";
import { ref, defineProps, defineEmits, defineExpose, shallowRef } from "vue";
import ProForm from "@/components/pro-form/index.vue";
import { createForm, createSchemaField } from "@formily/vue";
import { components } from "@/components/pro-form/utils";
import { startReportLeakInfo, startSelfCheck, startThreatWarn } from "@/request/api-process";

const emit = defineEmits(["refresh"]);

const form = shallowRef(null);
const schema = shallowRef({});
const visible = ref(false);

const data = ref({});
function open(baseInfo) {
  data.value = baseInfo;
  visible.value = true;
  form.value = createForm({});
  schema.value = {
    type: "object",
    properties: {
      layout: {
        type: "void",
        "x-component": "FormLayout",
        "x-component-props": {
          labelWidth: 120,
        },
        properties: {
          desc: {
            type: "string",
            title: "结束说明",
            "x-component": "Input.TextArea",
            "x-component-props": {
              rows: 4,
            },
            "x-decorator": "FormItem",
            'x-validator': [
              {required: true, message: '请输入结束说明'}
            ]
          },
        },
      },
    },
  };
}

async function handleSubmit() {
  await form.value.validate();
  const saveInterfaceMap = {
    businessOnline: startSelfCheck,
    threatAlert: startThreatWarn,
    leakManage: startReportLeakInfo
  }
  try {
    const saveInterface = saveInterfaceMap[data.value.processType];
    saveInterface && (await saveInterface({...data.value,...form.value.values, workOrderId: data.value.id, flowStatus: 9}))
    message.success('结束成功')
    emit("refresh");
    visible.value = false;
  } catch (err) {
    message.error('结束失败')
  }
}

defineExpose({
  open,
});
</script>

