<!-- 工单详情-处置记录 -->

<template>
  <a-timeline class="timeline-container">
    <a-timeline-item v-for="(item, index) in data" :key="index">
      <div class="node-operator" >
        <span class="node-node">操作节点：{{ item.nodeName }}</span>
        <span class="node-name">操作人：{{ item.userName }}</span>
        <span class="node-time">操作时间：{{ item.disposeTime }}</span>
        <span class="node-node">操作意见：{{ item.disposeMsg }}</span>
        <span  class="node-desc">描述：{{ item.desc }}</span>
        <div  class="node-attack" v-if="item.lstFileData">
          <div class="node-attach-title">
            附件：
          </div>
          <div class="content">
            <a-button type="link" v-for="attach in item.lstFileData" @click="handleDownload(attach)" :key="attach.fileId"> {{attach.srcName}}</a-button>
          </div>
        </div>
      </div>
    </a-timeline-item>
  </a-timeline>
</template>

<script setup>
import { defineProps } from 'vue';
import { downloadFile } from '@/utils/util'

const props = defineProps({
  data: {
    type: Array,
    default: () => []
  }
})

function handleDownload(attach) {
  downloadFile(`/soar/soarFile/download/${attach.fileId}`)
}


</script>

<style lang="less" scoped>
.timeline-container{
  margin-top: 16px;
}
.node-operator {
  display: flex;
  justify-content:flex-start;
  align-items: center;
  flex-wrap: wrap;
  padding: 12px;
  &>span{
    min-width: 30%;
  }
  &>div{
    min-width: 30%;
  }
  .node-attack{
    margin-top: 8px;
  }
}
</style>

