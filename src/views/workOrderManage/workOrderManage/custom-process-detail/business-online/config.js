
export const BASE_CONFIG = [
  {
    label: '工单名称',
    prop: 'name'
  },
  {
    label: '工单类型',
    prop: 'type'
  },
  {
    label: '流水号',
    prop: 'id'
  },
  {
    label: '发起人',
    prop: 'creator'
  },
  {
    label: '创建时间',
    prop: 'createTime'
  },
  {
    label: '工单状态',
    prop: 'flowStatus',
    render: (h, { data }) => {
      const MAP = {
        1: '待审核',
        2: '待修改',
        8: '审核通过',
        9: '已关闭'
      }
      return <span>{MAP[data.flowStatus]}</span>
    }
  },
  {
    label: '当前所属节点',
    prop: 'nowNode'
  },
  {
    label: '当前节点处置人',
    prop: 'nowUser',
    gridSpan: 2
  },
]

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: 'IP地址',
    dataIndex: 'bearerIp',
    key: 'bearerIp'
  },
  {
    title: '资产类型',
    dataIndex: 'assetType',
    key: 'assetType'
  },
  {
    title: '域名称',
    dataIndex: 'domainName',
    key: 'domainName'
  },
  // {
  //   title: '审批状态',
  //   dataIndex: 'approvalStatus',
  //   key: 'approvalStatus',
  //   customRender: ({record}) => {
  //     const data = {
  //       '-1': '未审批',
  //        0: '审批中',
  //        1: '审批通过',
  //        2: '审批未通过'
  //     }
  //     return <span>{data[record.approvalStatus]}</span>
  //   }
  // },
  {
    title: '所属业务系统',
    dataIndex: 'bizSystemName',
    key: 'bizSystemName'
  },
  {
    title: '资产负责人',
    dataIndex: 'responsiblePerson',
    key: 'responsiblePerson'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 180
  }
]
