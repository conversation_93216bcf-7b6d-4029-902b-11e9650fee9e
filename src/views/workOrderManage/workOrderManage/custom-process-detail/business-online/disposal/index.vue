<!-- 业务上线工单处置弹窗 -->

<template>
  <a-modal :title="data.nowNode"  width="720px"  v-model:visible="visible" @ok="handleSubmit">
   <FormProvider v-if="visible" :form="form">
    <SchemaField  :schema="schema"   />
  </FormProvider>
  </a-modal>
</template>

<script setup>
import { message } from "ant-design-vue";
import { ref, defineProps, defineEmits, defineExpose, shallowRef } from "vue";
import ProForm from "@/components/pro-form/index.vue";
import { createForm, createSchemaField } from "@formily/vue";
import { DISPOSAL_SCHEMA_MAP } from "./config";
import {components } from '@/components/pro-form/utils'
import { startSelfCheck } from "@/request/api-process";
import {transformIncludeFileFormData} from '@/utils/util.js'

const emit = defineEmits(['refresh'])

const form = shallowRef(null);
const schema = shallowRef({});
const visible = ref(false);

const data = ref({});

function open(baseInfo) {
  visible.value = true;
  data.value = baseInfo;
  form.value = createForm({});
  schema.value = DISPOSAL_SCHEMA_MAP[baseInfo.nodeKey];
}

// 提交数据
async function handleSubmit() {
  await form.value.validate();
  const values = form.value.values;
  if(values.safetyId) {
    values.form = {
      safetyId: values.safetyId,
      lstDataId: values.lstDataId,
    }
  }
  const payload = transformIncludeFileFormData({...data.value,...values, workOrderId: data.value.id})
  try {
    await startSelfCheck(payload);
    message.success('处置成功')
    emit('refresh')
    visible.value = false;
  } catch (err) {
    message.error('处置失败')
  }
}

defineExpose({
  open,
});
</script>

