

import { getTaskList } from "@/request/api-device-leak-scan";
import { getDeviceList } from "@/request/safe-device";
import { useAsyncDataSource } from "@/utils/util";
import { defineComponent, h } from "vue"

export const DISPOSAL_SCHEMA_MAP = {
  'Asset-Info-Update': {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          // onlineTime: {
          //   type: 'string',
          //   title: '期望上线时间',
          //   'x-component': 'DatePicker',
          //   'x-component-props': {
          //     format: 'YYYY-MM-DD HH:mm:ss'
          //   },
          //   'x-decorator': 'FormItem',
          //   required: true,
          // },
          desc: {
            type: 'string',
            title: '业务上线自检说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '业务系统上线前已完成全面自检，并完成了必要信息的填报，现申请进行上线自检审核，以确保系统稳定运行及符合安全规范。',
            'x-validator': [
              { required: true, message: '请输入业务上线自检说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
          disposeMsg: {
            type: 'string',
            title: '提交结论',
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            enum: [
              { label: '完成修改', value: '完成修改' },
              { label: '终止申请', value: '终止申请' },
            ],
            'x-reactions': field => {
              const flowStatus = field.value === '终止申请' ? 9 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请选择提交结论' }
            ]
          },
        },
      }
    }
  },
  "Asset-Info-Approval": {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          disposeMsg: {
            type: 'string',
            title: '审核结果',
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            enum: [
              { label: '通过', value: '通过' },
              { label: '不通过', value: '不通过' },
            ],
            'x-reactions': field => {
              const flowStatus = field.value === '不通过' ? 2 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请选择审核结果' }
            ]
          },
          desc: {
            type: 'string',
            title: '审核说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '审核业务系统上线自检申请信息确认无误，可以上线，请安全部门对系统安全性且符合安全标准后，即可完成上线操作.',
            'x-validator': [
              { required: true, message: '请输入审核说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
"Asset-Result-Report": {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          'safetyId': {
            type: 'string',
            title: '扫描设备',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择扫描设备'
            },
            'x-reactions': useAsyncDataSource(async () => {
              const { data } = await getDeviceList({page: 1, size: 99999, deviceTypeId: 1});
              return data?.data?.map(i => ({ label: i.deviceName, value: i.id }))
            })
          },
          createTask: {
            type: 'void',
            'x-component': defineComponent({
              setup() {
                function handleClick() {
                  console.log('create task')
                }
                return () => <a-button type="primary" style="margin-left: 120px" onClick={handleClick}>发起扫描任务</a-button>
              }
            }),
            title: '',
            'x-decorator': 'FormItem',
            'x-decorator-props': {
              addonAfter: '需要人工创建扫描任务，并在扫描任务信息中选择已经创建的任务',
            },
          },
          'lstDataId': {
            type: 'array',
            title: '扫描任务',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              mode:"multiple",
              placeholder: '请选择扫描任务'
            },
            'x-reactions': async field => {
              const safetyId = field.query('.safetyId').value();
              if(!safetyId) return ;
              const { data } = await getTaskList({page: 1, size: 99999, deviceSafetyId: safetyId});
              const dataSource = data?.map(i => ({ label: i.taskName, value: i.taskID }))??[];
              field.setDataSource(dataSource)
            }
          },
          disposeMsg: {
            type: 'string',
            title: '自检结果',
            enum: [
              { label: '自检通过', value: '自检通过' },
              { label: '自检不通过', value: '自检不通过' },
            ],
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            'x-reactions': field => {
              const flowStatus = field.value === '自检不通过' ? 2 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请选择自检结果' }
            ]
          },
          desc: {
            type: 'string',
            title: '自检结果说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '业务自检结果已全面汇总并上报，包括系统稳定性、安全性及功能完整性等方面，确保所有指标达标，申请进行下一步流程，请相关部门审核并确认。',
            'x-validator': [
              { required: true, message: '请输入自检结果说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
 "Asset-Result-Approval": {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          disposeMsg: {
            type: 'string',
            title: '审查结果',
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            enum: [
              { label: '通过', value: '通过' },
              { label: '不通过', value: '不通过' },
            ],
             'x-reactions': field => {
              const flowStatus = field.value === '不通过' ? 2 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请选择审查结果' }
            ]
          },
          desc: {
            type: 'string',
            title: '审查结果说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '已对业务自检结果进行详细审核，确认系统稳定性、安全性及功能完整性均达标，{ 符合上线要求，并完成上线操作 /  不符合上线要求，需要继续深度自检，自检完成后请重新上报} 。',
            'x-validator': [
              { required: true, message: '请输入审查结果说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
  "Asset-Online": {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          desc: {
            type: 'string',
            title: '业务上线说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            default: '业务系统上线前已完成全面自检，并完成了必要信息的填报，现申请进行上线自检审核，以确保系统稳定运行及符合安全规范。',
            'x-validator': [
              { required: true, message: '请输入业务上线说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
          },
        },
      }
    }
  },
}
