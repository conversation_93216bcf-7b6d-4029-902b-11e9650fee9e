<!-- 业务上线工单弹窗 -->

<template>
  <a-modal
    ref="modalRef"
    :visible="open"
    :wrap-style="{ overflow: 'hidden' }"
    :footer="null"
    width="90%"
    @cancel="handleClose"
  >
    <template #title>
      <div class="custom-modal-title">
        <div class="title">{{ baseInfo.name }}</div>
        <div class="operator">
          <a-button type="danger" v-if="showDisposal" ghost @click="handleEnd"> 结束</a-button>
          <a-button
            type="primary"
            v-if="showDisposal"
            ghost
            @click="handleDisposal"
            >处置</a-button
          >
          <a-button type="primary" ghost @click="handleViewProcess"
            >查看工单流程</a-button
          >
        </div>
      </div>
    </template>
    <div class="base-info">
      <div class="detail-title">基础信息</div>
      <SocDetails :config="BASE_CONFIG" :data="baseInfo" />
    </div>
    <div class="asset-info">
      <div class="detail-title">资产信息</div>
      <CommonTable
        ref="tableRef"
        :fetchData="fetchAssetData"
        :columns="columns"
      >
        <template #operation="{ record }">
          <a-button type="link" @click="handleView(record)">查看</a-button>
        </template>
      </CommonTable>
    </div>
    <div class="asset-info" v-if="safetyId">
      <div class="detail-title">漏扫任务</div>
      <LeakInfo :safetyId="safetyId" :taskIds="taskIds" />
    </div>
    <div class="disposal-record">
      <div class="detail-title">处置记录</div>
      <DisposalRecord :data="disposalRecord" />
    </div>
    <DisposalModal ref="disposalModalRef" @refresh="handleClose" />
    <CustomProcess
      v-if="processVisible"
      v-model="processVisible"
      mode="view"
      :deployId="baseInfo.deployId"
      @close="dialogClose"
    />
    <EndModal ref="endModalRef" @refresh="handleClose" />
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed, onMounted, watch } from "vue";
import ProForm from "@/components/pro-form/index.vue";
import { BASE_CONFIG, columns } from "./config";
import SocDetails from "../_components_/soc-details.vue";
import CommonTable from "@/components/common-table/index.vue";
import DisposalRecord from "../_components_/disposal-record.vue";
import DisposalModal from "./disposal/index.vue";
import {
  getWorkOrderDetail,
  getWorkOrderRelateData,
  getWorkOrderFlowRecord,
} from "@/request/api-process";
import router from "@/router";
import CustomProcess from "@/views/workOrderManage/processManage/custom-process-dialog.vue";
import EndModal from '../_components_/end-modal.vue'
import LeakInfo from '../_components_/leak-info.vue'

const emit = defineEmits(["update:visible", "close"]);

const props = defineProps({
  visible: {
    type: Boolean,
    default: true,
  },
  id: {
    type: String,
    default: "",
  },
  mode: {
    type: String,
    default: "edit",
  },
});

const open = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit("update:visible", value);
  },
});
const tableRef = ref(null);

const showDisposal = computed(() => {
 return props.mode !== "edit" || baseInfo.value.nodeKey !== 'end';
});

async function fetchAssetData(params) {
  const { data } = await getWorkOrderRelateData({
    ...params,
    workOrderId: props.id,
  });
  return { items: data.data, total: data.total };
}

function handleView(record) {
  const targetRoute = router.resolve({
    name: "assetlist",
    query: { ip: record.ip },
  }); // 替换为你的路由名称
  // 打开新标签页
  window.open(targetRoute.href, "_blank");
}
const disposalModalRef = ref(null);

function handleDisposal() {
  disposalModalRef.value.open(baseInfo.value);
}
const endModalRef = ref(null);
function handleEnd(){
  endModalRef.value.open({...baseInfo.value, processType: 'businessOnline'});
}

const safetyId = ref('');
const taskIds = ref([]);

const disposalRecord = ref([]);
async function fetchDisposalRecord() {
  const { data } = await getWorkOrderFlowRecord({ workOrderId: props.id });
  disposalRecord.value = data;
  const leakSanNodeInfo = data.find(item => item.variables&&item.variables.safetyId);
  if(leakSanNodeInfo){
    safetyId.value = leakSanNodeInfo.variables.safetyId;
    taskIds.value = leakSanNodeInfo.variables?.lstDataId??[];
  }
}

const baseInfo = ref({});
async function fetchWorOrderDetail() {
  const { data } = await getWorkOrderDetail({ workOrderId: props.id });
  baseInfo.value = data;
}

fetchWorOrderDetail();
fetchDisposalRecord();

const processVisible = ref(false);
function handleViewProcess() {
  processVisible.value = true;
}

function handleClose() {
  emit("close");
}

function handleRefresh() {
  fetchWorOrderDetail();
  fetchDisposalRecord();
  tableRef.value.refresh();
}
</script>

<style lang="less" scoped>
.base-info .detail-title {
  margin-top: 0;
}
.detail-title {
  position: relative;
  padding-left: 12px;
  font-size: 14px;
  margin: 8px 0;
  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    bottom: 20%;
    width: 2px;
    height: 60%;
    margin: auto;
    background-color: #008dff;
  }
}
.custom-modal-title {
  display: flex;
  justify-content: space-between;
  margin-right: 24px;
  align-content: center;
  height: 100%;
  .operator {
    display: flex;
    gap: 8px;
  }
}
</style>
