
export const BASE_CONFIG =[
  {
    label: '工单名称',
    prop: 'name'
  },
  {
    label: '工单类型',
    prop: 'type'
  },
  {
    label: '流水号',
    prop: 'id'
  },
  {
    label: '发起人',
    prop: 'creator'
  },
  {
    label: '创建时间',
    prop: 'createTime'
  },
  {
    label: '工单状态',
    prop:'flowStatus',
    render: (h, { data }) => {
      const MAP = {
        1: '待审核',
        2: '待修改',
        8: '审核通过',
        9: '已关闭'
      }
      return <span>{MAP[data.flowStatus]}</span>
    }
  },
  {
    label: '当前所属节点',
    prop: 'nowNode'
  },
  {
    label: '当前节点处置人',
    prop: 'nowUser',
    gridSpan: 2
  },
]


/** @type {*} 告警详情 */

export const ALERT_DETAIL_CONFIG = [
  {
    label: '事件名称',
    prop: 'eventTitle'
  },
  {
    label: '事件类型',
    prop: 'eventTypeName'
  },
  {
    label: '入库时间',
    prop: 'eventTime'
  },
  {
    label: '源IP',
    prop: 'eventDevIp'
  },
  {
    label: '目的IP',
    prop: 'eventTargetIp'
  },
  {
    label: '事件等级',
    prop:'eventLevel',
    render(h, {prop, data}){
      const MAP = {
        1:'低', 2:'中', 3:'高',
      }
      return MAP[data.eventLevel]
    },
  },
  {
    label: '事件描述',
    prop:'eventDescription',
    gridSpan: 3
  }
]

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: '序号',
    dataIndex: 'index',
    key: 'index'
  },
  {
    title: '事件名称',
    dataIndex: 'assetName',
    key: 'assetName'
  },
  {
    title: '事件类型',
    dataIndex: 'assetType',
    key: 'assetType'
  },
  {
    title: '入库时间',
    dataIndex: 'createTime',
    key: 'createTime'
  },
  {
    title: '源IP',
    dataIndex: 'assetIp',
    key: 'assetIp'
  },
  {
    title: '目的IP',
    dataIndex: 'destIp',
    key: 'destIp'
  },
  {
    title: '事件等级',
    dataIndex:'mac',
    key:'mac'
  },
  {
    title: '处置状态',
    dataIndex: 'number',
    key: 'number'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 180
  }
]
