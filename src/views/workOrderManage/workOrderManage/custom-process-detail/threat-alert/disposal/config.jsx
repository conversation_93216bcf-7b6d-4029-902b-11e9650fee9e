import { defineComponent, h } from "vue"

export const DISPOSAL_SCHEMA_MAP = {
  'Alarm-Report': {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          desc: {
            type: 'string',
            title: '提报说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            'x-validator': [
              { required: true, message: '请输入提报说明' }
            ]
          },
          referenceLink: {
            type: 'string',
            title: '参考链接',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
            'x-validator': [
              { required: true, message: '请选择附件' }
            ]
          },
        },
      }
    }
  },
  'Alarm-Dispose': {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          disposeMsg: {
            type: 'string',
            title: '处置状态',
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            enum: [
              { label: '已处置', value: '已处置' },
              { label: '未处置', value: '未处置' },
            ],
            'x-reactions': field => {
              const flowStatus = field.value === '未处置' ? 2 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请选择处置状态' }
            ]
          },
          desc: {
            type: 'string',
            title: '威胁处置说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            'x-validator': [
              { required: true, message: '请输入威胁处置说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
            'x-validator': [
              { required: true, message: '请选择附件' }
            ]
          },
        },
      }
    }
  },
  'Alarm-Approval': {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          disposeMsg: {
            type: 'string',
            title: '审核结果',
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            enum: [
              { label: '通过', value: '通过' },
              { label: '不通过', value: '不通过' },
            ],
             'x-reactions': field => {
              const flowStatus = field.value === '未处置' ? 2 : 1;
              field.form.setValuesIn('flowStatus', flowStatus);
            },
            'x-validator': [
              { required: true, message: '请选择审核结果' }
            ]
          },
          desc: {
            type: 'string',
            title: '审核结果说明',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 4
            },
            'x-decorator': 'FormItem',
            'x-validator': [
              { required: true, message: '请输入审核结果说明' }
            ]
          },
          lstFileId: {
            type: 'array',
            title: '附件',
            'x-decorator': 'FormItem',
            'x-component': 'DraggerUpload',
            'x-validator': [
              { required: true, message: '请选择附件' }
            ]
          },
        },
      }
    }
  },
}
