<!-- 威胁告警工单类型弹窗详情 -->

<template>
  <a-modal
    ref="modalRef"
    :visible="true"
    :wrap-style="{ overflow: 'hidden' }"
    :footer="null"
    width="90%"
    @cancel="handleClose"
  >
   <template #title>
      <div class="custom-modal-title">
        <div class="title">{{baseInfo.name}}</div>
        <div class="operator">
          <a-button type="danger" v-if="mode !== 'view'" ghost @click="handleEnd"> 结束</a-button>
          <a-button type="primary" v-if="mode !== 'view'" ghost @click="handleDisposal">处置</a-button>
          <a-button type="primary" ghost @click="handleViewProcess">查看工单流程</a-button>
        </div>
      </div>
    </template>
    <div class="base-info">
      <div class="detail-title">基础信息</div>
      <SocDetails :config="BASE_CONFIG" :data="baseInfo" />
    </div>
    <div class="asset-info">
      <div class="detail-title">威胁告警信息</div>
       <SocDetails :config="ALERT_DETAIL_CONFIG" :data="alertInfo" />
      <!-- <CommonTable
        ref="tableRef"
        :fetchData="fetchAlertInfo"
        :columns="columns"
      >
        <template #operation="{ record }">
          <a-button type="link" @click="handleView(record)">查看</a-button>
        </template>
      </CommonTable> -->
    </div>
  <div class="disposal-record">
      <div class="detail-title">处置记录</div>
      <DisposalRecord :data="disposalRecord" />
    </div>
    <EndModal ref="endModalRef" @refresh="handleClose" />
    <DisposalModal ref="disposalModalRef" @refresh="handleClose" />
     <CustomProcess
      v-if="processVisible"
      v-model="processVisible"
      mode="view"
      :deployId="baseInfo.deployId"
      @close="dialogClose"
    />
  </a-modal>
</template>

<script setup>
import { ref, defineProps, defineEmits, computed } from "vue";
import ProForm from "@/components/pro-form/index.vue";
import { BASE_CONFIG, columns, ALERT_DETAIL_CONFIG } from "./config";
import SocDetails from "../_components_/soc-details.vue";
import CommonTable from "@/components/common-table/index.vue";
import DisposalRecord from "../_components_/disposal-record.vue";
import DisposalModal from './disposal/index.vue'
import { getWorkOrderDetail, getWorkOrderRelateData, getWorkOrderFlowRecord } from "@/request/api-process";
import CustomProcess from '@/views/workOrderManage/processManage/custom-process-dialog.vue'
import EndModal from '../_components_/end-modal.vue'

const emit = defineEmits(["update:visible", 'close']);

const props = defineProps({
   visible: {
    type: Boolean,
    default: true,
  },
  id: {
    type: String,
    default: "",
  },
    mode: {
    type: String,
    default: "edit",
  }
});

const open = computed({
  get() {
    return props.visible;
  },
  set(value) {
    emit("update:visible", value);
  },
});
const tableRef = ref(null);

const alertInfo = ref({});
// 查询告警数据
async function fetchAlertInfo(params = {page: 1, size: 10}) {
  const { data} = await getWorkOrderRelateData({...params, workOrderId: props.id})
  alertInfo.value = data?.data?.[0]
 return { items: data.data, total: data.total};
}
function handleView(record) {
  console.log(record);
}
const disposalModalRef = ref(null);

// 打开处置弹窗
function handleDisposal(){
  disposalModalRef.value.open(baseInfo.value);
}
const endModalRef = ref(null);

// 打开结束工单弹窗
function handleEnd(){
  endModalRef.value.open({...baseInfo.value, processType: 'threatAlert'});
}

const disposalRecord = ref([]);

// 获取处置记录数据
async function fetchDisposalRecord() {
  const {data} = await getWorkOrderFlowRecord({ workOrderId: props.id });
  disposalRecord.value =data;
}

const baseInfo = ref({});
// 获取工单基础信息
async function fetchWorOrderDetail() {
  const {data} =  await getWorkOrderDetail({workOrderId: props.id});
  baseInfo.value = data;
}

const processVisible = ref(false);

// 打开查看流程弹窗
function handleViewProcess(){
  processVisible.value = true;
}

function handleClose() {
  emit("close");
}

// 刷新数据
function handleRefresh(){
  fetchWorOrderDetail();
  fetchDisposalRecord();
  fetchAlertInfo();
  tableRef.value?.refresh();
}
handleRefresh();
</script>

<style lang="less" scoped>
.base-info .detail-title {
  margin-top: 0;
}
.detail-title {
  position: relative;
  padding-left: 12px;
  font-size: 14px;
  margin: 8px 0;
  &::before {
    content: "";
    display: block;
    position: absolute;
    left: 0;
    bottom: 20%;
    width: 2px;
    height: 60%;
    margin: auto;
    background-color: #008dff;
  }
}
.custom-modal-title {
  display: flex;
  justify-content: space-between;
  margin-right: 24px;
  align-content: center;
  height: 100%;
  .operator {
    display: flex;
    gap: 8px;
  }
}
</style>
