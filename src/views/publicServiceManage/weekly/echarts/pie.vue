<template>
  <div class="sls">
    <div class="left">
      <div class="pieChart"></div>
    </div>
    <div class="right">
      <p v-for="(item, i) in data" :key="i">
        <span :class="`lv${i + 1}`">{{ item.name }}</span>
        <span>{{ item.value }}%</span>
        <span>{{ item.number }}</span>
      </p>
    </div>
  </div>
</template>

<script>
import * as echarts from 'echarts'
import { markRaw } from 'vue'
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      myChart: null,
      programmeArr: [],
      classArr: [],
      yData: []
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      const dom = document.getElementsByClassName('pieChart')[0]
      this.myChart = markRaw(echarts.init(dom))
      var color = ['#247dfd', '#f9ce41', '#5eebbd', '#fe857c']
      const option = {
        // backgroundColor: '#081736',
        color: color,
        tooltip: {
          show: true
          // trigger: 'item',
          // backgroundColor: 'rgba(0,0,0,0.5)',
          // color: '#fff'
        },
        series: [
          {
            name: '',
            type: 'pie',
            radius: ['50%', '88%'],
            center: ['50%', '50%'],
            avoidLabelOverlap: true,
            label: {
              show: false,
              position: 'inside',
              formatter: '{d}%',
              align: 'center',
              baseline: 'middle',
              fontSize: 16,
              fontWeight: '100',
              color: '#fff'
              // textStyle: {
              // }
              // normal: {
              // }
            },
            labelLine: {
              show: false
            },

            data: this.data
          }
        ]
      }

      this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    }
  }
}
</script>
<style lang="less" scoped>
.sls {
  display: flex;
  align-items: center;
  width: 730px;
  height: 260px;
  padding: 30px;
  margin: 20px auto;
  .left {
    width: 200px;
    height: 200px;
    background: url('../../../../assets/portalClient/weekly/pieBg.png') no-repeat;
    background-size: 100% 100%;
    margin-right: 100px;
    .pieChart {
      width: 100%;
      height: 100%;
    }
  }
  .right {
    p {
      display: flex;
      align-items: center;
      height: 30px;
      margin: 0;
      font-size: 14px;
      color: #333;
      // font-weight: 600;
      &:not(:first-child) {
        margin-top: 10px;
      }
      &:not(:last-child) {
        border-bottom: 1px solid #EDEDED;
      }
      span:first-child {
        margin-right: 160px;
      }
      span:nth-child(2) {
        // display: inline-block;
        width: 45px;
      }
      span:last-child {
        margin-left: 80px;
      }
    }
    .lv1::before,.lv2::before,.lv3::before,.lv4::before {
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 4px;
      background-color: #247dfd;
      content: '';
      margin-right: 10px;
    }
    .lv2::before {
      background-color: #f9ce41;
    }
    .lv3::before {
      background-color: #5eebbd;
    }
    .lv4::before {
      background-color: #fe857c;
    }
  }
}
</style>
