<template>
  <div class="horizontalBarChart"></div>
</template>

<script>
import * as echarts from 'echarts'
import { markRaw } from 'vue'
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  watch: {
    data: {
      handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          console.log(421445423)
          this.init()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    async init() {
      const dom = document.getElementsByClassName('horizontalBarChart')[0]
      this.myChart = markRaw(echarts.init(dom))
      var datas = [
        {
          value: 480,
          name: 'Chengdu'
        },
        {
          value: 296,
          name: 'Guangzhou'
        },
        {
          value: 589,
          name: 'Shanghai'
        },
        {
          value: 735,
          name: 'Beijing'
        },
        {
          value: 456,
          name: 'Hangzhou'
        },
        {
          value: 929,
          name: 'Shaoxing'
        }
      ]
      var maxArr = new Array(this.data.length).fill(100)
      const option = {
        // backgroundColor: '#000',
        grid: {
          left: 20,
          right: 40,
          bottom: 0,
          top: 40,
          containLabel: true
        },
        // tooltip: {
        //   trigger: 'axis',
        //   axisPointer: {
        //     type: 'none'
        //   },
        //   showContent: true,
        //   formatter: function (params) {
        //     return params.data.name + ' : ' + params.data.value
        //   }
        // },
        tooltip: {
          showContent: true,
          trigger: 'axis',
          backgroundColor: 'rgba(0,0,0,.7)',
          color: '#fff',
          textStyle: {
            color: '#fff'
          }
        },
        xAxis: {
          show: true,
          type: 'category',
          data: this.data.map((item) => {
            console.log('item', item)
            return item.name
          }),
          axisLabel: {
            show: true,
            fontSize: 14,
            color: '#333333'
            // textStyle: {
            // }
          },
          axisTick: {
            show: false
          }
        },
        yAxis: [
          {
            type: 'value',
            axisLabel: {
              show: true,
              margin: 15, // 右侧y轴数字的外边距
              fontSize: 14,
              color: '#333333',
              // textStyle: {
              // },
              formatter: (value) => {
                return value
              }
            },
            splitLine: {
              show: true,
              lineStyle: {
                color: '#E3E6EB',
                type: 'dashed'
              }
            },
            axisTick: {
              show: false
            },
            axisLine: {
              show: true
            }
          }
        ],
        series: [
          {
            name: '值',
            type: 'bar',
            zlevel: 1,
            showBackground: true,
            label: {
              show: true,
              fontSize: 14,
              position: 'top',
              color: '#666666',
              formatter: function (params) {
                if (params.value === 0) {
                  // 为0时不显示
                  return ''
                } else {
                  return params.value
                }
              }
              // normal: {
              // }
            },
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 1, 0, 0, [
                {
                  offset: 0,
                  color: '#B3DBFF'
                },
                {
                  offset: 1,
                  color: '#2468F3'
                }
              ])
            },
            barWidth: 28,
            data: this.data
          }
        ]
      }
      await this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    }
  }
}
</script>

<style lang="less" scoped>
.horizontalBarChart {
  width: 100%;
  height: 270px;
}
</style>
