<template>
  <div class="verticalBarChart" :style="{ minHeight: '200px',height: `${data.length * 80}px` }"></div>
</template>

<script>
import * as echarts from 'echarts'
import { markRaw } from 'vue'
export default {
  props: {
    data: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      myChart: null,
      programmeArr: [],
      classArr: [],
      yData: []
    }
  },
  watch: {
    data: {
      async handler(newVal, oldVal) {
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          await this.init()
          await this.myChart.resize()
        }
      },
      deep: true
    }
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      this.classArr = []
      this.yData = []
      this.programmeArr = []
      const dom = document.getElementsByClassName('verticalBarChart')[0]
      this.myChart = markRaw(echarts.init(dom))
      this.data.forEach((item) => {
        this.programmeArr.push(item.prodNum)
        this.classArr.push(item.courseNum)
        this.yData.push(item.name)
      })
      const option = {
        // backgroundColor: '#000',
        tooltip: {
          trigger: 'axis'
        },
        // legend: {
        //   top: '4%',
        //   left: '75%',
        //   itemWidth: 13,
        //   itemHeight: 13,
        //   itemStyle: {
        //     color: '#1870e6'
        //   },
        //   icon: 'rect',
        //   padding: 0,
        //   textStyle: {
        //     color: '#c0c3cd',
        //     fontSize: 13,
        //     padding: [2, 0, 0, 0]
        //   }
        // },
        legend: {
          top: '0%',
          left: '75%',
          itemWidth: 14,
          itemHeight: 14,
          icon: 'rect',
          textStyle: {
            fontSize: 14,
            color: '#333333'
          }
        },
        grid: {
          top: 50,
          left: 340,
          bottom: 30,
          right: 80
        },
        xAxis: {
          show: true,
          type: 'value',
          axisLine: {
            show: false,
            lineStyle: {
              color: '#333333',
              type: 'dashed'
            }
          },
          axisLabel: {
            show: false,
            color: '#333333'
            // textStyle: {
            // }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: true,
            lineStyle: {
              color: '#D1D6DE',
              type: 'dashed'
            }
          }
        },
        yAxis: {
          data: this.yData,
          axisLine: {
            lineStyle: {
              color: '#D1D6DE'
            }
          },
          axisLabel: {
            fontSize: 14,
            color: '#333333'
            // textStyle: {
            // }
          },
          axisTick: {
            show: false
          },
          splitLine: {
            show: false
          }
        },
        series: [
          {
            name: '解决方案数量',
            type: 'bar',
            barWidth: 14,
            zlevel: 2,
            itemStyle: {
              color: '#1870e6'
            },
            showBackground: true,
            label: {
              normal: {
                show: true,
                position: 'right',
                color: '#666666',
                fontSize: 14,
                formatter: function (params) {
                  if (params.value === 0) {
                    // 为0时不显示
                    return ''
                  } else {
                    return params.value
                  }
                }
              }
            },
            data: this.programmeArr
          },
          {
            name: '培训赋能数量',
            type: 'bar',
            barWidth: 14,
            zlevel: 2,
            itemStyle: {
              color: '#25cedd'
            },
            showBackground: true,
            label: {
              normal: {
                show: true,
                fontSize: 14,
                position: 'right',
                color: '#666666',
                formatter: function (params) {
                  if (params.value === 0) {
                    // 为0时不显示
                    return ''
                  } else {
                    return params.value
                  }
                }
              }
            },
            data: this.classArr
          }
        ]
      }
      this.myChart.setOption(option)
      window.addEventListener('resize', () => {
        this.myChart.resize()
      })
    }
  }
}
</script>

<style lang="less" scoped>
.verticalBarChart {
  margin: 20px 0;
  width: 100%;
}
</style>
