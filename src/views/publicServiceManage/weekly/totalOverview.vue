<template>
  <div class="totalOverview">
    <div class="item1-2">
      <div class="child child-1 overview" :class="type ? 'weekly-overview' : 'overview'">
        <div class="left flex-c">
          <p>{{ type ? '新开通用户数' : '平台用户总数' }}</p>
          <p class="set-color">{{ data.userNum || 0 }}<span>个</span></p>
        </div>
        <div class="right">
        </div>
      </div>
      <div class="child">
        <div class="left child-2">
          <p class="left-num">{{ type ? '新增平台资料总计' : '平台资料总计' }}: <span>{{ data.videoNum + data.pdfNum + data.excelNum + data.wordNum }}</span></p>
        </div>
        <div class="right">
          <div class="right-item flex-c">
            <p>视频</p>
            <p>{{ data.videoNum || 0 }}</p>
          </div>
          <div class="right-item flex-c">
            <p>PDF</p>
            <p>{{ data.pdfNum || 0 }}</p>
          </div>
          <div class="right-item flex-c">
            <p>EXCEL</p>
            <p>{{ data.excelNum || 0 }}</p>
          </div>
          <div class="right-item flex-c">
            <p>WORD</p>
            <p>{{ data.wordNum || 0 }}</p>
          </div>
        </div>
      </div>
      <div class="child">
        <div class="left child-3">
          <p class="left-num">{{ type ? '新增解决方案' : '解决方案' }}: <span>{{ data.prodNum }}</span></p>
        </div>
        <div class="right">
          <div class="right-item flex-c">
            <p>浏览量</p>
            <p>{{ data.psViewNum || 0 }}</p>
          </div>
          <div class="right-item flex-c">
            <p>收藏量</p>
            <p>{{ data.psCollectNum || 0 }}</p>
          </div>
          <div class="right-item flex-c">
            <p>点赞量</p>
            <p>{{ data.psLikeNum || 0 }}</p>
          </div>
          <div class="right-item flex-c">
            <p>精选量</p>
            <p>{{ data.psBoutiqueNum || 0 }}</p>
          </div>
        </div>
      </div>
      <div class="child">
        <div class="left child-4">
          <p class="left-num">{{ type ? '新增培训赋能' : '培训赋能' }}: <span>{{ data.courseNum }}</span></p>
        </div>
        <div class="right">
          <div class="right-item flex-c">
            <p>浏览量</p>
            <p>{{ data.courseViewNum || 0 }}</p>
          </div>
          <div class="right-item flex-c">
            <p>收藏量</p>
            <p>{{ data.courseCollectNum || 0 }}</p>
          </div>
          <div class="right-item flex-c">
            <p>点赞量</p>
            <p>{{ data.courseLikeNum || 0 }}</p>
          </div>
          <div class="right-item flex-c">
            <p>精选量</p>
            <p>{{ data.courseBoutiqueNum || 0 }}</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    data: {
      type: Object,
      default: () => {}
    },
    type: {
      type: Number,
      default: 0
    }
  }
}
</script>

<style lang="less" scoped>
.flex-c {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  p {
    margin: 0;
    &:first-child {
      font-size: 16px;
      color: #555;
    }
    &:last-child {
      font-size: 20px;
      font-weight: 700;
      color: #000;
      line-height: 1;
      &.set-color {
        font-size: 30px;
        color: #2468f3;
        span {
          font-weight: 400;
          font-size: 20px;
          color: #333;
        }
      }
    }
  }
}
.totalOverview {
  width: 100%;
  height: auto;
  .item1-1 {
    display: flex;
    justify-content: space-between;
    width: 100%;
    margin-bottom: 20px;
    .item1-1-item {
      width: 176px;
      height: 85px;
      background-color: #eff3ff;
      border-radius: 8px;
      padding: 16px;
    }
  }
  .item1-2 {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
    width: 100%;
    .child {
      display: flex;
      width: 24%;
      height: 197px;
      background-color: #eff3ff;
      border-radius: 8px;
      padding: 0 0 0 30px;
      // margin-bottom: 20px;
      .left {
        position: relative;
        width: 157px;
        height: 100%;
        .left-num {
          position: absolute;
          top: 20px;
          left: 50%;
          transform: translateX(-50%);
          white-space: nowrap;
          font-size: 22px;
          font-family: REEJI-PinboGB-Flash, REEJI-PinboGB;
          font-weight: 600;
          color: #333333;
          span {
            color: #0056e1;
          }
        }
      }
      .right {
        display: flex;
        flex-wrap: wrap;
        flex: 1;
        padding: 45px 10px 15px 55px;
        .right-item {
          align-items: center;
          width: 50%;
          height: 50%;
          &:nth-child(3),
          &:nth-child(4) {
            border-top: 1px solid #e3e9f8;
          }
          &:nth-child(2),
          &:nth-child(4) {
            border-left: 1px solid #e3e9f8;
          }
        }
      }
      &.child-1 {
        padding-left: 65px;
        &.overview {
          background: url('../../../assets/portalClient/weekly/overview-bg.png') no-repeat 65% 0;
          background-size: cover;
        }
        &.weekly-overview {
          background: url('../../../assets/portalClient/weekly/weekly-overview-bg.png') no-repeat 65% 0;
          background-size: cover;
        }
        .left {
          p:first-child {
            font-size: 22px;
            font-family: REEJI-PinboGB-Flash, REEJI-PinboGB;
            font-weight: 600;
            color: #333333;
          }
          p:last-child.set-color {
            font-size: 60px;
            font-family: YouSheBiaoTiHei;
            color: #2468F3;
            line-height: 1.2;
          }
        }
      }
      .child-2 {
        background: url('../../../assets/portalClient/weekly/child2.png') no-repeat;
        background-size: contain;
      }
      .child-3 {
        background: url('../../../assets/portalClient/weekly/child3.png') no-repeat;
        background-size: contain;
      }
      .child-4 {
        background: url('../../../assets/portalClient/weekly/child4.png') no-repeat;
        background-size: contain;
      }
    }
  }
}
</style>
