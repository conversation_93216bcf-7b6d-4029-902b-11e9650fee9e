<template>
  <div class="rankingList">
    <a-table :columns="columns" :data-source="data" :pagination="false">
      <template #num="{ text }">
        <!-- <div style="width: 50px;display: inline-flex;align-items: center;justify-content: center;"> -->
          <!-- <div class="table-num" :class="`table-num-${text}`"> -->
            {{ text }}
          <!-- </div> -->
        <!-- </div> -->
      </template>
    </a-table>
  </div>
</template>

<script>
export default {
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    data: {
      type: Array,
      default: () => []
    }
  }
}
</script>

<style lang="less" scoped>
.rankingList {
  margin-top: 25px;
  width: 100%;
  height: auto;

  :deep(.ant-table.ant-table-default.ant-table-empty .ant-table-placeholder) {
    background: transparent;
  }

  :deep(.ant-table-thead > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td), :deep(.ant-table-tbody > tr.ant-table-row-hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td), :deep(.ant-table-thead > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td), :deep(.ant-table-tbody > tr:hover:not(.ant-table-expanded-row):not(.ant-table-row-selected) > td) {
    background: #d2ddf3;
  }
  /* 表格斑马样式 **/
  // /deep/.ant-table-tbody tr:nth-child(2n) {
  //   background-color: #fafafa;
  // }
  :deep(.ant-table-thead > tr > th) {
    background: transparent;
    font-size: 16px;
    color: #111;
    border-bottom: 1px solid #EFEEF5;
    border-top: none;
  }
  :deep(.ant-table-tbody > tr > td) {
    padding: 8px 12px;
    font-size: 14px;
    color: #333;
    border-bottom: 1px solid #EFEEF5;
  }
  .table-num-1,.table-num-2,.table-num-3 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 34px;
  }
  .table-num-1 {
    background: url('../../../assets/portalClient/weekly/num1-icon.png') no-repeat 50% 50%;
    background-size: cover;
  }
  .table-num-2 {
    background: url('../../../assets/portalClient/weekly/num2-icon.png') no-repeat 50% 50%;
    background-size: cover;
  }
  .table-num-3 {
    background: url('../../../assets/portalClient/weekly/num3-icon.png') no-repeat 50% 50%;
    background-size: cover;
  }
  .table-num:not(.table-num-1,.table-num-2,.table-num-3) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 34px;
    background: url('../../../assets/portalClient/weekly/num-icon.png') no-repeat 50% 50%;
    background-size: cover;
  }
}
</style>
