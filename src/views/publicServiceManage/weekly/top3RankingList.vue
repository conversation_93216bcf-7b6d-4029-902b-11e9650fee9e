<template>
  <div class="list top3RankingList">
    <div class="top-3" v-if="data.length > 0">
      <div class="top-3-item top-3-flex" v-for="(item, i) in data.slice(0, 3)" :key="i">
        <p class="p1" :title="item.deptName">{{ item.deptName }}</p>
        <p class="p2">{{ title }}</p>
        <p class="p3">{{ item.value || 0 }}</p>
      </div>
    </div>
    <div class="top-3" v-else>
      <div class="top-3-item top-3-flex" v-for="item in 3" :key="item">
        <p class="p1" title="OBG-管理八部-中 移客户事业中心">暂未加载</p>
        <p class="p2">{{ title }}</p>
        <p class="p3">0</p>
      </div>
    </div>
    <div class="list-table" style="padding: 0 15px">
      <a-table :columns="columns" :data-source="data" :pagination="false">
        <template #num="{ text }">
          <div style="width: 58px;display: flex;align-items: center;justify-content: center;">
            <div class="table-num" :class="`table-num-${text}`">
              {{ text }}
            </div>
          </div>
        </template>
        <!-- <span slot="index" slot-scope="text" :class="`table-num-${text}`">Name</span> -->
        <!-- <span slot="index" slot-scope="text" :class="`table-num-${text}`">Name</span> -->
        <!-- <span slot="index" slot-scope="text" :class="`table-num-${text}`">Name</span> -->
      </a-table>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    columns: {
      type: Array,
      default: () => []
    },
    data: {
      type: Array,
      default: () => []
    },
    title: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="less" scoped>
.top3RankingList.list {
  width: 100%;
  height: auto;
  background: url('../../../assets/portalClient/weekly/list-bg.png') no-repeat;
  background-size: 100%;

  :deep(.ant-table.ant-table-default.ant-table-empty .ant-table-placeholder) {
    background: transparent;
  }
  :deep(.ant-table-tbody) > tr > td {
    padding: 8px 12px;
  }
  .table-num-1,.table-num-2,.table-num-3 {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 34px;
  }
  .table-num-1 {
    background: url('../../../assets/portalClient/weekly/num1-icon.png') no-repeat 50% 50%;
    background-size: cover;
  }
  .table-num-2 {
    background: url('../../../assets/portalClient/weekly/num2-icon.png') no-repeat 50% 50%;
    background-size: cover;
  }
  .table-num-3 {
    background: url('../../../assets/portalClient/weekly/num3-icon.png') no-repeat 50% 50%;
    background-size: cover;
  }
  .table-num:not(.table-num-1,.table-num-2,.table-num-3) {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 30px;
    height: 34px;
    background: url('../../../assets/portalClient/weekly/num-icon.png') no-repeat 50% 50%;
    background-size: cover;
  }
  .top-3 {
    display: flex;
    justify-content: flex-start;
    align-items: flex-end;
    padding-left: 178px;
    width: 100%;
    height: 315px;
    margin-bottom: 5px;

    .top-3-item {
      align-items: center;
      width: 248px;
      margin-right: 49px;

      p {
        margin: 0;

        &.p1 {
          padding: 0 12px;
          text-align: center;
          font-family: REEJI-PinboGB-Flash, REEJI-PinboGB;
          font-size: 24px;
          font-weight: 700;
          line-height: 1.2;
          word-break: break-all;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }

        &.p2 {
          font-family: REEJI-PinboGB-Flash, REEJI-PinboGB;
          font-size: 18px;
        }

        &.p3 {
          padding: 0 12px;
          text-align: center;
          font-family: YouSheBiaoTiHei;
          font-size: 26px;
          font-weight: 700;
          line-height: 1.2;
        }
      }

      &:first-child {
        height: 208px;

        p {
          color: #33606f;
        }

        .p1 {
          margin-bottom: 20px;
        }
      }

      &:nth-child(2) {
        height: 243px;

        p {
          color: #8a3c00;
        }

        .p1 {
          margin-bottom: 40px;
        }
      }

      &:last-child {
        height: 176px;

        p {
          color: #a9272f;
        }

        .p1 {
          margin-bottom: 10px;
        }
      }
    }
  }
}
.top-3-flex {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;

  p {
    margin: 0;

    &:first-child {
      font-size: 16px;
      color: #555;
    }

    &:nth-child(2) {
      font-size: 16px;
      color: #555;
    }

    &:last-child {
      font-size: 24px;
      font-weight: 700;
      color: #000;
      line-height: 1;
    }
  }
}
</style>
