<template>
  <div class="page">
    <!-- 搜索条件栏 -->
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">时间：</div>
        <div class="page-search-item-value time" style="display: flex">
          <a-range-picker
            v-model:value="searchData.time"
            style="width: 400px"
            :ranges="dataRange"
          />
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight, background: 'transparent' }">
      <div id="weekly" ref="addImage">
        <!-- <div class="rightMenu">
          <div class="downloadBtn" @click="generatorImage">
            <img
              src="../../../assets/portalClient/weekly/dowloadImg.png"
              alt=""
            />
          </div>
        </div> -->
        <!-- <div class="banner"></div> -->
        <div class="weekly" ref="capture">
          <div class="weekly-content">
            <div class="item1">
              <!-- <div class="item1-title item-title"></div> -->
              <TotalOverview
                :data="data.allData"
                :type="0"
                v-if="data.allData"
                key="weekly-item1"
              />
            </div>
            <div class="item2">
              <!-- <div class="item2-title item-title"></div> -->
              <!-- <div class="time">
            2023/10/30 — 2023/11
          </div> -->
              <div class="item2-bottom1">
                <TotalOverview
                  :data="data.weekData"
                  :type="1"
                  v-if="data.weekData"
                  key="weekly-item2"
                />
              </div>
              <div class="item2-bottom2">
                <div class="item2-1">
                  <div class="title">按照行业分布统计</div>
                  <horizontalBarChart
                    :data="data.industry"
                    v-if="data.industry"
                  ></horizontalBarChart>
                </div>
                <div class="item2-2">
                  <div class="title">按照部门分布统计</div>
                  <verticalBarChart
                    :data="data.dept"
                    v-if="data.dept && data.dept.length > 0"
                  ></verticalBarChart>
                  <div v-else class="default">
                    <span>暂无数据</span>
                  </div>
                </div>
              </div>
            </div>
            <div class="item3">
              <!-- <div class="item3-title item-title"></div> -->
              <div class="item3-bottom">
                <div class="item3-1">
                  <div class="title">方案上架数排行</div>
                  <div class="content">
                    <rankingList
                      :columns="columns1"
                      :data="data.showRanking"
                    ></rankingList>
                  </div>
                </div>
                <div class="item3-2">
                  <div class="title">浏览量排行</div>
                  <div class="content">
                    <rankingList
                      :columns="columns2"
                      :data="data.viewRanking"
                    ></rankingList>
                  </div>
                </div>
                <div class="item3-3">
                  <div class="title">点赞量排行</div>
                  <div class="content">
                    <rankingList
                      :columns="columns3"
                      :data="data.likeRanking"
                    ></rankingList>
                  </div>
                </div>
                <div class="item3-4">
                  <div class="title">收藏量排行</div>
                  <div class="content">
                    <rankingList
                      :columns="columns4"
                      :data="data.collectRanking"
                    ></rankingList>
                  </div>
                </div>
                <div class="item3-5">
                  <div class="title">方案级别统计</div>
                  <pieChart :data="data.level" v-if="data.level"></pieChart>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  ExclamationCircleOutlined,
  MailOutlined,
  ContainerOutlined,
  StarFilled,
  StarOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";
import TotalOverview from "./totalOverview.vue";
import top3RankingList from "./top3RankingList";
import rankingList from "./rankingList";
import {
  columns1,
  columns2,
  columns3,
  columns4,
} from "./table";
import { weekly } from "@/request/publicServiceManage/TProductSolutions";
import horizontalBarChart from "./echarts/horizontalBarChart";
import verticalBarChart from "./echarts/verticalBarChart";
import pieChart from "./echarts/pie";
import html2canvas from "html2canvas";
import { mapGetters } from "vuex";
import moment from "moment";
import dayjs from 'dayjs';

export default {
  components: {
    TotalOverview,
    top3RankingList,
    rankingList,
    horizontalBarChart,
    verticalBarChart,
    pieChart,
    SearchOutlined
  },
  data() {
    return {
      flexHeight: "0px",
      tableHeight: 0,
      searchData: {
        time: []
      },
      dataRange: {
        // 今天: [moment().startOf('day'), moment()],
        // 昨天: [moment().startOf('day').subtract(1, 'days'), moment().startOf('day').subtract(1, 'days')],
        '最近一周': [dayjs().startOf('week'), dayjs().endOf('week')],
        // 最近两周: [moment().startOf('day').subtract(2, 'weeks'), moment()],
        // 最近1个月: [moment().startOf('day').subtract(1, 'months'), moment()],
        // 最近3个月: [moment().startOf('day').subtract(3, 'months'), moment()],
        // 最近半年: [moment().startOf('day').subtract(6, 'months'), moment()],
        // 最近1年: [moment().startOf('day').subtract(1, 'years'), moment()]
      },
      data: {},
      columns1: columns1,
      columns2: columns2,
      columns3: columns3,
      columns4: columns4,
      time: null,
      params: {
        beginTime: null,
        endTime: null,
      },
    };
  },
  computed: {
    // ...mapGetters(['scrollInfo', 'backTop'])
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  mounted() {
    setTimeout(() => {
      this.flexHeight = `calc(100vh - ${this.searchHeight}px - 140px)`;
      this.tableHeight =
        (window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight) -
        336 -
        this.searchHeight;
    }, 100);
    const weekStart = dayjs().startOf('week').format("YYYY-MM-DD");
    // const weekEnd = moment().endOf('isoWeek').format('YYYY/MM/DD')
    const currentDate = dayjs().format("YYYY-MM-DD");
    this.searchData.time[0] = dayjs().startOf('week')
    this.searchData.time[1] = dayjs()
    this.params.beginTime = weekStart;
    this.params.endTime = currentDate;
    this.time = `${weekStart} - ${currentDate}`;
    this.init();
  },
  methods: {
    moment,
    beforeSearch() {
      this.init();
    },
    async init() {
      if (this.searchData.time && this.searchData.time.length > 0) {
        this.params.beginTime = dayjs(this.searchData.time[0]).format('YYYY-MM-DD');
        this.params.endTime = dayjs(this.searchData.time[1]).format('YYYY-MM-DD');
      }
      const res = await weekly(this.params)
      if (res.data.code === 0) {
        console.log(res);
        this.data = res.data.data;
      }
    },
    generatorImage() {
      html2canvas(this.$refs.capture).then((canvas) => {
        // const { height } = this.$refs.capture.getBoundingClientRect()
        canvas.style.display = "none";
        this.$refs.addImage.append(canvas);
        const link = document.createElement("a");
        link.href = canvas.toDataURL();
        link.setAttribute("download", `${this.params.beginTime}-${this.params.endTime}-运营周报.png`);
        link.style.display = "none";
        document.body.appendChild(link);
        link.click();
      });
    },
    onChange(value, dateString) {
      this.time = `${dateString[0]} - ${dateString[1]}`;
      this.params.beginTime = dateString[0];
      this.params.endTime = dateString[1];
      this.init();
    },
    onOk(value) {
      console.log("onOk: ", value);
    },
    // 设置不可选择的日期
    disabledDate(current) {
      // const weekOfday = parseInt(this.moment().format('d'))
      // 获取当前周的开始结束时间
      const weekStart = moment().startOf("isoWeek");
      const weekEnd = moment().endOf("isoWeek");
      // 设置区间之外的日期不可选
      return current < weekStart || current > weekEnd;
    },
  },
};
</script>

<style lang="less" scoped>
#weekly {
  position: relative;
  height: auto;
  min-width: 1260px;

  // .banner {
  //   position: absolute;
  //   top: 64px;
  //   width: 100%;
  //   height: 360px;
  //   background: url('../../../assets/portalClient/weekly/banner.png') no-repeat 50% 0;
  //   background-size: cover;
  // }

  // .footer-banner-new {
  //   position: absolute;
  //   bottom: 300px;
  //   width: 100%;
  //   height: 360px;
  //   background: url('../../../assets/portalClient/weekly/footer-banner.png') no-repeat 50% 0;
  //   background-size: cover;
  // }

  .weekly {
    width: 100%;
    height: auto;
    margin: 0 auto;
    .weekly-content {
      width: 100%;
      margin: 0 auto;

      .item-title {
        position: absolute;
        top: -11px;
        width: 380px;
        height: 54px;
      }

      .item1 {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: auto;
        background-color: #fff;
        border-radius: 8px;
        padding: 20px 20px 15px;
        margin-bottom: 20px;

        .item1-title {
          background: url("../../../assets/portalClient/weekly/item1-title.png")
            no-repeat;
          background-size: contain;
        }
      }

      .item2,
      .item3 {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 100%;
        height: auto;
        background-color: #fff;
        border-radius: 8px;
        padding: 20px 20px 15px;
        margin-bottom: 20px;

        .title {
          width: 100%;
          font-size: 18px;
          // font-weight: 600;
          color: #333;
          line-height: 1;

          &::before {
            display: inline-block;
            height: 18px;
            width: 3px;
            background-color: #2468f3;
            content: "";
            vertical-align: bottom;
            margin-right: 5px;
          }
        }

        .default {
          position: relative;
          width: 100%;
          height: 200px;
          background: url("../../../assets/portalClient/weekly/default.svg")
            no-repeat 50% 0;
          margin: 20px 0;
          transform: scale(0.65);
          span {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            text-align: center;
            color: #666666b4;
            font-size: 16px;
            font-weight: 600;
          }
        }

        .time {
          display: flex;
          justify-content: center;
          align-items: center;
          width: 660px;
          height: 54px;
          margin: 0 auto;
          background: url("../../../assets/portalClient/weekly/time-bg.png");
          background-size: contain;
          margin-bottom: 25px;
          font-size: 20px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #333333;
          .timeShow {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            cursor: pointer;
          }
        }

        .content {
          width: 100%;
          height: auto;
          background: linear-gradient(180deg, #eff5ff 0%, #ffffff 100%);
          padding: 0 15px;
          border-radius: 5px;
        }

        .item2-title {
          background: url("../../../assets/portalClient/weekly/item2-title.png")
            no-repeat;
          background-size: contain;
        }

        .item3-title {
          background: url("../../../assets/portalClient/weekly/item3-title.png")
            no-repeat;
          background-size: contain;
        }

        .item2-1,
        .item2-2,
        .item3-1,
        .item3-2,
        .item3-3,
        .item3-4,
        .item3-5 {
          margin-top: 40px;
          width: 100%;
        }
      }

      .item2 {
        padding: 0;
        background: transparent;
        .item2-bottom1 {
          width: 100%;
          padding: 20px;
          background: #fff;
          border-radius: 8px;
          margin-bottom: 20px;
        }
        .item2-bottom2 {
          display: flex;
          gap: 20px;
          justify-content: flex-start;
          align-items: center;
          width: 100%;
          height: 330px;
          .item2-1, .item2-2 {
            width: 49.45%;
            height: 100%;
            padding: 20px;
            background-color: #fff;
            margin: 0;
            border-radius: 8px;
          }
        }
      }
      .item3 {
        background: transparent;
        padding: 0;
        .item3-bottom {
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
          align-items: center;
          width: 100%;
          gap: 20px;
          .item3-1,
          .item3-2,
          .item3-3,
          .item3-4,
          .item3-5 {
            width: 49.45%;
            height: 336.14px;
            padding: 20px;
            background-color: #fff;
            margin: 0;
            border-radius: 8px;
            .content {
              padding: 0;
            }
          }
        }
      }
    }
  }
  .rightMenu {
    position: fixed;
    right: 40px;
    bottom: 8%;
    display: flex;
    flex-direction: column;
    justify-content: space-around;
    align-items: center;
    width: 64px;
    height: fit-content;
    box-shadow: 0 0 1px 3px #ffffff7e;
    background-color: #2468f3;
    border-radius: 8px;
    z-index: 3;
    padding: 5px;
    .line {
      // position: absolute;
      // top: 50%;
      display: inline-block;
      width: 70%;
      border-top: 1px solid #fff;
      margin: 5px 0;
    }
    .downloadBtn,
    .toTop {
      display: flex;
      justify-content: center;
      align-items: center;
      width: 48px;
      height: 80px;
      border-radius: 6px;
      transition: all 0.3s;
      cursor: pointer;
      &:hover {
        background-color: #3b8ef5;
      }
      img {
        width: 50%;
        object-fit: contain;
      }
    }
  }
}
</style>
