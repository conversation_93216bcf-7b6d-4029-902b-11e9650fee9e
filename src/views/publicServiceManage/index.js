export const buildTree = (list, arr, pid) => {
    list.forEach((item) => {
        if (item.parentId === pid) {
            var child = {
                ...item,
                // key: item.id,
                // value: item.id + '',
                // title: item.cname,
                children: []
            }
            buildTree(list, child.children, item.id)
            arr.push(child)
        }
    })
    return arr
}