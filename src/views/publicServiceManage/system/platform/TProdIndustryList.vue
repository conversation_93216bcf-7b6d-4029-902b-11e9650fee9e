<template>
  <a-card :bordered="false" style="height: 100%" class="system-card">
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">行业名称：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            placeholder="请输入行业名称"
            v-model:value="searchData.name"
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">行业编号：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            placeholder="请输入行业编号"
            v-model:value="searchData.code"
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">状态：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select allowClear placeholder="请选择"
                    style="width: 100%"
                    v-model:value="searchData.status">
            <a-select-option v-for="(b, index) in statusArray"
                             :key="index"
                             :value="b.dictValue">{{ b.dictLabel }}</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 15px">
          重置
        </a-button>
        <a-button type="primary" @click="handleAdd()" style="margin-left: 15px">
          <template #icon> <PlusOutlined /> </template>新增
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <a-row>
        <a-col :span="24">
          <a-table
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
            <template #num="{ index }">
              <span>{{
                (pagination.current - 1) * pagination.pageSize +
                Number(index) +
                1
              }}</span>
            </template>
            <template #status="{ text }">
              <span v-if="text === '0'">正常</span>
              <span v-if="text === '1'">停用</span>
            </template>
            <template #action="{ record }">
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleView(record)"
              >
                查看
              </a-button>
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleEdit(record)"
              >
                编辑
              </a-button>
              <a-button
                type="link"
                style="color: #fc5532"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    <tProdIndustry-modal ref="modal" @onOk="onOk" />
    <tProdIndustry-modal-view ref="viewModal" @onOk="onOk" />
  </a-card>
</template>

<script>
import {
  SearchOutlined,
  PlusOutlined
} from "@ant-design/icons-vue";
import { getTProdSpuList } from '@/request/publicServiceManage/tProdSpu'
import { delTProdIndustry, getTProdIndustryList } from '@/request/publicServiceManage/tProdIndustry'
import TProdIndustryModal from './modules/TProdIndustryModal.vue'
import TProdIndustryModalView from './modules/TProdIndustryModalView.vue'
import { getDictArray } from '@/utils/dict'
import { Modal, message } from 'ant-design-vue';

export default {
  name: "TableList",
  components: {
    TProdIndustryModal,
    TProdIndustryModalView,
    SearchOutlined,
    PlusOutlined
  },
  data() {
    return {
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      tableLoading: false,
      searchData: {
        title: "",
        status: null,
        operTime: [],
      },
      lastsearchData: {
        title: "",
        status: null,
        operTime: [],
      },
      tableColumns: [
      {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: '行业名称',
          dataIndex: 'name'
        },
        {
          title: '行业编号',
          dataIndex: 'code'
        },
        {
          title: '排序',
          dataIndex: 'sort'
        },
        {
          title: '状态',
          dataIndex: 'status',
          slots: { customRender: "status" },
        },
        {
          title: '操作',
          width: '240px',
          dataIndex: 'action',
          align: 'left',
          slots: { customRender: "action" },
        }
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
      statusArray: [],
      statusMap: {},
      flagChildArray: [],
      flagChildMap: {}
    };
  },
  created() {
    getDictArray('sys_normal_disable').then(data => {
      this.statusArray = data
      const statusMap = {}
      this.statusArray.map(d => {
        statusMap[d.dictValue] = { text: d.dictLabel }
      })
      this.statusMap = statusMap
    })
    getDictArray('t_prod_category_flag_child').then(data => {
      this.flagChildArray = data
      const flagChildMap = {}
      this.flagChildArray.map(d => {
        flagChildMap[d.dictValue] = { text: d.dictLabel }
      })
      this.flagChildMap = flagChildMap
    })
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  mounted() {
    setTimeout(() => {
      this.flexHeight = `calc(${
        document.getElementsByClassName("system-card")[0].clientHeight
        }px - ${this.searchHeight}px - 48px)`;
        this.tableHeight =
        document.getElementsByClassName("system-card")[0].clientHeight -
        this.searchHeight -
        160;
      }, 100);
    this.handleSearch(true)
  },
  methods: {
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    handleSearch(flag) {
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
      const params = {
        ...this.lastsearchData,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
      };
      this.tableLoading = true;
      getTProdIndustryList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            this.tableData = res.data.rows;
            this.pagination.total = res.data.total;
          } else {
            message.error(res.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {
      };
      this.searchData = {
      };
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    handleAdd() {
      this.$refs.modal.add();
    },
    handleView(record) {
      this.$refs.viewModal.view(record);
    },
    handleEdit(record) {
      this.$refs.modal.edit(record);
    },
    handleDelete(value) {
      Modal.confirm({
        title: "确定要删除选中内容吗?",
        okText: "确定",
        okType: "primary",
        cancelText: "取消",
        onOk: async () => {
            let flag = true
            const res = await getTProdSpuList({ prodCategoryId: value.id })
            if (res.data.code === 0 && res.data.rows.length > 0) {
              flag = false
              message.error('您选择分类下存在产品，删除失败！')
            }
            if (flag) {
              delTProdIndustry({ids: value.id}).then((res) => {
                if (res.data.code === 0) {
                  message.success('删除成功');
                  this.handleSearch();
                } else {
                  message.error(res.data.msg);
                }
              }).catch(error => {
                message.error(error.msg);
              });
            }
          },
          onCancel() {},
        });
    },
    onOk() {
      this.handleSearch(true);
    }
  },
  watch: {},
};
</script>
<style lang="less" scoped></style>
