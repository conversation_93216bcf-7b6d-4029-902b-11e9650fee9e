<template>
  <a-card :bordered="false" style="height: 100%" class="system-card">
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">类型名称：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            allowClear
            v-emoji
            v-model:value.trim="searchData.cname"
            placeholder="请输入类型名称"
          />
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 22px">
          重置
        </a-button>
        <a-button type="primary" @click="handleAdd()" style="margin-left: 15px">
          <template #icon> <PlusOutlined /> </template>新增
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <a-row>
        <a-col :span="24">
          <a-table
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
            <template #num="{ index }">
              <span>{{
                (pagination.current - 1) * pagination.pageSize +
                Number(index) +
                1
              }}</span>
            </template>
            <template #mark="{ record }">
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleView(record)"
              >
                查看
              </a-button>
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleEdit(record)"
              >
                编辑
              </a-button>
              <a-button
                type="link"
                style="color: #fc5532"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    <tTrainCourseType-modal ref="modal" @onOk="onOk" />
    <tTrainCourseType-modal-view ref="viewModal"/>
  </a-card>
</template>

<script>
import {
  SearchOutlined,
  PlusOutlined
} from "@ant-design/icons-vue";
import { delTTrainCourseType, getTTrainCourseTypeList } from '@/request/publicServiceManage/TTrainCourseType'
import TTrainCourseTypeModal from './modules/TTrainCourseTypeModal.vue'
import TTrainCourseTypeModalView from './modules/TTrainCourseTypeModalView.vue'
import { treeData } from '@/utils/treeutil'
import { getTTrainCourseList } from '@/request/publicServiceManage/TTrainCourse'
import { Modal, message } from 'ant-design-vue';

export default {
  name: "TableList",
  components: {
    TTrainCourseTypeModal,
    TTrainCourseTypeModalView,
    SearchOutlined,
    PlusOutlined
  },
  data() {
    return {
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      tableLoading: false,
      formData: {
        title: "",
        content: "",
        createTime: "",
        isRead: "",
        mark: "",
      },
      searchData: {
      },
      lastsearchData: {
      },
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: "类型名称",
          dataIndex: "cname",
        },
        {
          title: "排序",
          dataIndex: "sort",
        },
        {
          title: "操作",
          dataIndex: "mark",
          width: '240px',
          align: "center",
          slots: { customRender: "mark" },
        },
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
    };
  },
  filters: {},
  created() {},
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  mounted() {
    setTimeout(() => {
      this.flexHeight = `calc(${document.getElementsByClassName('system-card')[0].clientHeight}px - ${this.searchHeight}px - 48px)`;
      this.tableHeight = document.getElementsByClassName('system-card')[0].clientHeight - this.searchHeight - 160;
    }, 100);
    this.handleSearch(true)
  },
  methods: {
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    handleSearch(flag) {
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
      // this.pagination.total = this.tableData.length;
      const params = {
        ...this.lastsearchData,
        // pageSize: this.pagination.pageSize,
        // pageNum: this.pagination.current,
      };
      this.tableLoading = true;
      getTTrainCourseTypeList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            const rootId = '0'
            this.tableData = treeData(res.data.rows, 'id', 'parentId', 'children', rootId)
          } else {
            message.error(res.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
      console.log(this.tableData)
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {
        title: "",
        status: null,
        operTime: [],
      };
      this.searchData = {
        title: "",
        status: null,
        operTime: [],
      };
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    handleAdd() {
      this.$refs.modal.add();
    },
    handleEdit(record) {
      this.$refs.modal.edit(record);
    },
    handleView(record) {
      this.$refs.viewModal.view(record);
    },
    handleDelete(value) {
      Modal.confirm({
        title: "确定要删除选中内容吗?",
        okText: "确定",
        okType: "primary",
        cancelText: "取消",
        onOk: async () => {
          const res = await getTTrainCourseList({ courseTypeId: value.id })
          if (res.data.code === 0 && res.data.rows.length > 0) {
            message.error('分类下存在课程，删除失败！')
            return false
          }
          const res1 = await getTTrainCourseTypeList({ parentId: value.id })
          if (res1.data.code === 0 && res1.data.rows.length > 0) {
            message.error('分类下存在子分类，删除失败！')
            return false
          }
          delTTrainCourseType({ ids: value.id }).then((res) => {
            if (res.data.code === 0) {
              message.success('删除成功')
              this.handleSearch();
            } else {
              message.error(res.msg)
            }
          })
          },
          onCancel() {},
        });
    },
    onOk() {
      this.handleSearch(true);
    }
  },
  watch: {},
};
</script>
<style lang="less" scoped></style>
