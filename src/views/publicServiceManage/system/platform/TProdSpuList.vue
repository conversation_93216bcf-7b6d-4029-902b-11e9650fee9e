<template>
  <a-card :bordered="false" style="height: 100%" class="system-card">
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">安全能力：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            placeholder="请输入安全能力"
            v-model:value="searchData.name"
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">安全能力编码：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            placeholder="请输入安全能力编码"
            v-model:value="searchData.code"
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">安全能力行业：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select allowClear placeholder="请选择安全能力行业"
                    style="width: 100%"
                    v-model:value="searchData.prodIndustryId">
            <a-select-option v-for="(b, index) in industryArray"
                             :key="index"
                             :value="b.id">{{ b.name }}
            </a-select-option>
          </a-select>
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">安全能力状态：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select allowClear placeholder="请选择安全能力状态"
                    style="width: 100%"
                    v-model:value="searchData.status">
            <a-select-option v-for="(b, index) in statusArray"
                             :key="index"
                             :value="b.dictValue">{{ b.dictLabel }}
            </a-select-option>
          </a-select>
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 15px">
          重置
        </a-button>
        <a-button type="primary" @click="handleAdd()" style="margin-left: 15px">
          <template #icon> <PlusOutlined /> </template>新增
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <a-row>
        <a-col :span="24">
          <a-table
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
            <template #num="{ index }">
              <span>{{
                (pagination.current - 1) * pagination.pageSize +
                Number(index) +
                1
              }}</span>
            </template>
            <template #status="{ text }">
              <span v-if="text === '0'">正常</span>
              <span v-if="text === '1'">停用</span>
            </template>
            <template #action="{ record }">
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleView(record)"
              >
                查看
              </a-button>
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleEdit(record)"
              >
                编辑
              </a-button>
              <a-button
                type="link"
                style="color: #fc5532"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    <tProdSpu-modal ref="modal"
                    :category-array="categoryArray"
                    :status-array="statusArray"
                    :self-flag-array="selfFlagArray"
                    @onOk="onOk" />
    <tProdSpu-modal-view ref="viewModal"
                    :category-array="categoryArray"
                    :status-array="statusArray"
                    :self-flag-array="selfFlagArray"
                    @onOk="onOk" />
  </a-card>
</template>

<script>
import {
  SearchOutlined,
  PlusOutlined
} from "@ant-design/icons-vue";
import { delTProdSpu, getTProdSpuList } from '@/request/publicServiceManage/tProdSpu'
import { getTProductSolutionsList } from '@/request/publicServiceManage/TProductSolutions'
import TProdSpuModal from './modules/TProdSpuModal.vue'
import TProdSpuModalView from './modules/TProdSpuModalView.vue'
import { getDictArray } from '@/utils/dict'
import { getTProdCategoryList } from '@/request/publicServiceManage/tProdCategory'
import { delTProdIndustry, getTProdIndustryList } from '@/request/publicServiceManage/tProdIndustry'
import { treeSelectData } from '@/utils/treeutil'
import { Modal, message } from 'ant-design-vue';

export default {
  name: "TableList",
  components: {
    TProdSpuModal,
    TProdSpuModalView,
    SearchOutlined,
    PlusOutlined
  },
  data() {
    return {
      categoryArray: [],
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      tableLoading: false,
      searchData: {
        title: "",
        status: null,
        operTime: [],
      },
      lastsearchData: {
        title: "",
        status: null,
        operTime: [],
      },
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: '安全能力',
          dataIndex: 'name',
          width: '260px',
          slots: { customRender: 'name' }
        },
        {
          title: '安全能力编码',
          dataIndex: 'code'
        },
        {
          title: '产品行业',
          dataIndex: 'prodIndustryName',
          slots: { customRender: 'prodIndustryName' }
        },
        {
          title: '更新时间',
          dataIndex: 'updateTime'
        },
        {
          title: '状态',
          dataIndex: 'status',
          slots: { customRender: "status" },
        },
        {
          title: '操作',
          width: '240px',
          dataIndex: 'action',
          align: 'left',
          slots: { customRender: "action" },
        }
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
      statusArray: [],
      statusMap: {},
      selfFlagArray: [],
      selfFlagMap: {},
      ategoryArray: [],
      industryArray: [],
      categoryMap: {},
      industryMap: {},
    };
  },
  created() {
    getDictArray('sys_normal_disable').then(data => {
      this.statusArray = data
      const statusMap = {}
      this.statusArray.map(d => {
        statusMap[d.dictValue] = { text: d.dictLabel }
      })
      this.statusMap = statusMap
    })
    getDictArray('psp_spu_self_flag').then(data => {
      this.selfFlagArray = data
      const selfFlagMap = {}
      this.selfFlagArray.forEach(d => {
        selfFlagMap[d.dictValue] = { text: d.dictLabel }
      })
      this.selfFlagMap = selfFlagMap
    })
    getTProdCategoryList().then(res => {
      if (res.data.code === 0) {
        const rootId = '0'
        this.categoryArray = treeSelectData(res.data.rows, false)
        const categoryMap = {}
        res.data.rows.forEach(d => {
          categoryMap[d.id] = { text: d.name }
        })
        this.categoryMap = categoryMap
      }
    })
    getTProdIndustryList().then(res => {
      if (res.data.code === 0) {
        this.industryArray = res.data.rows
        const industryMap = {}
        res.data.rows.forEach(d => {
          industryMap[d.id] = { text: d.name }
        })
        this.industryMap = industryMap
      }
    })
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  mounted() {
    setTimeout(() => {
      this.flexHeight = `calc(${
        document.getElementsByClassName("system-card")[0].clientHeight
        }px - ${this.searchHeight}px - 48px)`;
        this.tableHeight =
        document.getElementsByClassName("system-card")[0].clientHeight -
        this.searchHeight -
        160;
      }, 100);
    this.handleSearch(true)
  },
  methods: {
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    handleSearch(flag) {
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
      const params = {
        ...this.lastsearchData,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
      };
      this.tableLoading = true;
      getTProdSpuList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            this.tableData = res.data.rows;
            this.pagination.total = res.data.total;
          } else {
            message.error(res.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {
      };
      this.searchData = {
      };
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    handleAdd() {
      this.$refs.modal.add();
    },
    handleEdit(record) {
      this.$refs.modal.edit(record);
    },
    handleView(record) {
      this.$refs.viewModal.view(record);
    },
    handleDelete(value) {
      Modal.confirm({
        title: "确定要删除选中内容吗?",
        okText: "确定",
        okType: "primary",
        cancelText: "取消",
        onOk: async () => {
            let flag = true
            const res = await getTProductSolutionsList({ productSpuId: value.id })
            if (res.data.code === 0 && res.data.rows.length > 0) {
              flag = false
              message.error('您选择产品下存在解决方案，删除失败！')
            }
            if (flag) {
              delTProdSpu({ ids: value.id }).then((res) => {
                if (res.data.code === 0) {
                  message.success('删除成功');
                  this.handleSearch();
                } else {
                  message.error(res.data.msg);
                }
              }).catch(error => {
                message.error(error.msg);
              });
            }
          },
          onCancel() {},
        });
    },    
    onOk() {
      this.handleSearch(true);
    }
  },
  watch: {},
};
</script>
<style lang="less" scoped></style>
