<template>
  <a-modal title="查看行业"
           style="top: 20px;"
           :width="450"
           v-model:visible="visible"
           @cancel="handleClose">
    <a-descriptions :column="1" size="middle">
      <!-- 隐藏域改为显示 -->
      <!-- <a-descriptions-item label="行业ID" :span="24">
        {{ form.id || '无' }}
      </a-descriptions-item> -->
      
      <a-descriptions-item label="行业名称" :span="24">
        {{ form.name || '未填写' }}
      </a-descriptions-item>

      <a-descriptions-item label="状态" :span="24">
        {{ statusMap[form.status]?.text || '未知' }}
      </a-descriptions-item>

      <a-descriptions-item label="排序" :span="24">
        {{ form.sort || 0 }}
      </a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button type="primary" @click="handleClose">
        关闭
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { getDictArray } from '@/utils/dict'
import { message } from 'ant-design-vue'

export default {
  name: 'TProdIndustryViewModal',
  props: {},
  components: {},
  data() {
    return {
      visible: false,
      form: {},
      statusMap: {} // 状态映射表
    }
  },
  created() {
    // 初始化状态映射（仅加载一次）
    getDictArray('sys_normal_disable').then(data => {
      this.statusMap = data.reduce((acc, cur) => {
        acc[cur.dictValue] = { text: cur.dictLabel }
        return acc
      }, {})
    })
  },
  methods: {
    /**
     * 查看方法（替代原edit）
     * @param {Object} record 行数据
     */
    view(record) {
      this.form = {
        id: record.id || 0,
        name: record.name || '',
        status: record.status || '0',
        sort: record.sort || 0
      }
      this.visible = true
    },

    handleClose() {
      this.visible = false
      this.form = {} // 清空数据
    }
  }
}
</script>

<style scoped lang="less">
/* 优化描述列表间距 */
:deep(.ant-descriptions-item-label) {
  padding-right: 16px;
  min-width: 80px;
  font-weight: 500;
}
</style>