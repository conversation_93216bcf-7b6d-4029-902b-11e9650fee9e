<template>
  <a-modal title="查看行业"
           style="top: 20px;"
           :width="450"
           v-model:visible="visible"
           @cancel="handleClose">
    <a-descriptions :column="1" size="middle">
      <!-- 隐藏域改为显示 -->
      <a-descriptions-item label="解决方案" :span="24">
        {{ prodInfo[form.id]?.text || '无' }}
      </a-descriptions-item>

      <a-descriptions-item label="审核状态" :span="24">
        {{ statusMap[form.status]?.text || '未知' }}
      </a-descriptions-item>

      <a-descriptions-item label="申请时间" :span="24">
        {{ form.createDate || '未知' }}
      </a-descriptions-item>

      <a-descriptions-item label="申请人" :span="24">
        {{ form.useUserName || 0 }}
      </a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button type="primary" @click="handleClose">
        关闭
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { getDictArray } from '@/utils/dict'
import { message } from 'ant-design-vue'
import { getTProductSolutionsList } from '@/request/publicServiceManage/TProductSolutions'

export default {
  name: 'TProdIndustryViewModal',
  props: {},
  components: {},
  data() {
    return {
      visible: false,
      prodInfo: {},
      form: {},
      statusMap: {} // 状态映射表
    }
  },
  created() {
    // 初始化状态映射（仅加载一次）
    getTProductSolutionsList().then((res) => {
      if (res.data.code !== 0) {
        message.error(res.data.msg)
      }
      console.log(res.data.rows)
      this.prodInfo = {}
      res.data.rows.forEach(item => {
        // this.prodInfo.set(item.id, { text: item.sname });
        this.prodInfo[item.id] = { text: item.sname };
      })
    })
    getDictArray('sys_normal_disable').then(data => {
      this.statusMap = data.reduce((acc, cur) => {
        acc[cur.dictValue] = { text: cur.dictLabel }
        return acc
      }, {})
    })
  },
  methods: {
    /**
     * 查看方法（替代原edit）
     * @param {Object} record 行数据
     */
    view(record) {
      this.form = {
        id: record.productSolutionId || 0,
        status: record.status || '0',
        createDate: record.createDate || '暂无',
        useUserName: record.useUserName || '暂无'
      }
      this.visible = true
    },

    handleClose() {
      this.visible = false
      this.form = {} // 清空数据
    }
  }
}
</script>

<style scoped lang="less">
/* 优化描述列表间距 */
:deep(.ant-descriptions-item-label) {
  padding-right: 16px;
  min-width: 80px;
  font-weight: 500;
}
</style>