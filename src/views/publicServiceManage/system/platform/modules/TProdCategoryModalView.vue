<template>
  <a-modal
    title="分类详情"
    style="top: 20px"
    :width="500"
    v-model:visible="visible"
    @cancel="handleClose"
  >
    <a-descriptions
      :column="1"
      size="middle"
      :colon="false"
      class="view-description"
    >
      <!-- 隐藏ID字段（如需展示可取消display:none） -->


      <!-- 上级分类（树结构名称展示） -->
      <a-descriptions-item label="上级分类">
        {{ getParentName(form.pid) || '顶级分类' }}
        <a-tooltip :title="getParentPath(form.pid)">
          <template #icon>
            <a-icon type="info-circle" theme="twoTone" two-tone-color="#1890FF" />
          </template>
        </a-tooltip>
      </a-descriptions-item>

      <!-- 分类名称 -->
      <a-descriptions-item label="分类名称">
        {{ form.name || '未命名' }}
      </a-descriptions-item>

      <!-- 状态（字典映射） -->
      <a-descriptions-item label="状态">
        {{ statusMap[form.status] || '未知' }}
      </a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button type="primary" @click="handleClose">
        关闭
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { getTProdCategoryList } from '@/request/publicServiceManage/tProdCategory'
import { getDictArray } from '@/utils/dict'
import { message } from 'ant-design-vue'

export default {
  name: 'TProdCategoryModal',
  props: {},
  data() {
    return {
      visible: false,
      form: {},
      prodCategoryTree: [], // 分类树
      categoryMap: {},      // ID -> 名称映射
      statusMap: {},        // 状态字典
      pathCache: {}         // 路径缓存（优化性能）
    }
  },
  created() {
    // 初始化数据映射（只加载一次）
    this.initData()
  },
  methods: {
    // 初始化数据（分类树+状态字典）
    async initData() {
      // 加载分类树
      const categoryRes = await getTProdCategoryList()
      this.prodCategoryTree = categoryRes.data.rows || []
      this.buildCategoryMap(this.prodCategoryTree)

      // 加载状态字典
      const statusRes = await getDictArray('sys_normal_disable')
      this.statusMap = statusRes.reduce((acc, cur) => {
        acc[cur.dictValue] = cur.dictLabel
        return acc
      }, {})
    },

    // 构建分类映射及路径缓存
    buildCategoryMap(nodes, parentPath = []) {
      nodes.forEach(node => {
        const fullPath = [...parentPath, node.name].join(' / ')
        this.categoryMap[node.id] = node.name
        this.pathCache[node.id] = fullPath
        if (node.children) {
          this.buildCategoryMap(node.children, [...parentPath, node.name])
        }
      })
    },

    // 获取上级分类名称
    getParentName(pid) {
      if (pid === 0) return '' // 顶级分类无上级
      return this.categoryMap[pid] || '无'
    },

    // 获取完整路径（用于tooltip）
    getParentPath(pid) {
      return this.pathCache[pid] || `ID:${pid}（路径不存在）`
    },

    // 保持父组件调用方式不变（原edit方法）
    async view(record) {
      // 兼容历史调用方式（原edit方法）
      this.form = {
        id: record.id || 0,
        name: record.name || '',
        pid: record.pid || 0,
        status: record.status || '0'
      }
      this.visible = true
    },

    handleClose() {
      this.visible = false
      this.form = {} // 清空数据
    }
  }
}
</script>

<style scoped lang="less">
.view-description {
  padding: 24px;

  .ant-descriptions-item {
    padding: 12px 0;

    &-label {
      min-width: 80px;
      font-weight: 500;
      color: #333;
    }

    &-content {
      color: #666;
      word-break: break-all;

      /* 状态颜色区分 */
      &:contains('正常') { color: #52c41a; }
      &:contains('停用') { color: #ff4d4f; }
    }
  }

  .ant-tooltip {
    margin-left: 8px;
    font-size: 12px;
    color: #999;
  }
}
</style>