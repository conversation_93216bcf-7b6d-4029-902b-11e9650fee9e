<template>
  <a-modal
    title="查看课程分类信息"
    style="top: 20px"
    :width="400"
    v-model:visible="visible"
    @cancel="handleClose"
  >
    <!-- 使用 a-descriptions 展示信息 -->
    <a-descriptions :column="1">
      <!-- 上级分类展示 -->
      <a-descriptions-item label="上级分类">
        {{ getParentCategoryName(form.parentId) || '无' }}
      </a-descriptions-item>
      <!-- 分类名展示 -->
      <a-descriptions-item label="分类名">
        {{ form.cname || '无' }}
      </a-descriptions-item>
      <!-- 排序展示 -->
      <a-descriptions-item label="排序">
        {{ form.sort || '无' }}
      </a-descriptions-item>
    </a-descriptions>
    <!-- 仅保留关闭按钮 -->
    <template #footer>
      <a-button @click="handleClose">关闭</a-button>
    </template>
  </a-modal>
</template>

<script>
import { getTTrainCourseTypeList } from "@/request/publicServiceManage/TTrainCourseType";
import { message } from "ant-design-vue";

export default {
  name: "TTrainCourseTypeModal",
  props: {},
  components: {},
  data() {
    return {
      visible: false,
      form: {},
      TProdCourseType: [],
    };
  },
  beforeCreate() {},
  created() {
    this.loadTree();
  },
  methods: {
    // 加载课程分类树数据
    loadTree() {
      getTTrainCourseTypeList().then((res) => {
        if (res.data.code === 0) {
          this.TProdCourseType = [{ key: "0", value: "0", title: "无" }];
          this.buildTree(res.data.rows, this.TProdCourseType, "0");
        }
      });
    },
    // 构建树形结构
    buildTree(list, arr, pid) {
      list.forEach((item) => {
        if (item.parentId === pid) {
          var child = {
            key: item.id,
            value: item.id + "",
            title: item.cname,
            children: [],
          };
          this.buildTree(list, child.children, item.id);
          arr.push(child);
        }
      });
    },
    // 根据 parentId 获取上级分类名称
    getParentCategoryName(parentId) {
      const findCategory = (nodes) => {
        for (const node of nodes) {
          if (node.key === parentId) {
            return node.title;
          }
          if (node.children) {
            const result = findCategory(node.children);
            if (result) {
              return result;
            }
          }
        }
        return null;
      };
      return findCategory(this.TProdCourseType);
    },
    // 查看方法，接收记录并显示弹窗
    async view(record) {
      await this.loadTree();
      this.form = {
        id: record.id,
        parentId: record.parentId,
        cname: record.cname,
        sort: record.sort,
      };
      this.visible = true;
    },
    // 关闭弹窗并清空表单数据
    handleClose() {
      this.form = {};
      this.visible = false;
    },
  },
};
</script>

<style scoped lang="less">
/* 样式可以根据需要调整 */
</style>