<template>
  <a-modal
    title="查看产品信息"
    style="top: 20px"
    :width="600"
    v-model:visible="visible"
    @cancel="handleClose"
  >
    <a-descriptions :column="1" size="middle">
      <!-- 基础信息 -->
      <!-- <a-descriptions-item label="产品ID" :span="24">
        {{ form.id || '无' }}
      </a-descriptions-item> -->

      <a-descriptions-item label="安全能力" :span="24">
        {{ form.name || '未填写' }}
      </a-descriptions-item>

      <a-descriptions-item label="产品类型" :span="24">
        {{ getCategoryName(form.prodCategoryId) || '未选择' }}
      </a-descriptions-item>

      <!-- 扩展信息 -->
      <a-descriptions-item label="产品别名" :span="24">
        {{ form.alias || '无' }}
      </a-descriptions-item>

      <a-descriptions-item label="计量单位" :span="24">
        {{ form.unit || '个' }}
      </a-descriptions-item>

      <a-descriptions-item label="产品状态" :span="24">
        {{ getStatusText(form.status) || '未知' }}
      </a-descriptions-item>

      <a-descriptions-item label="所属行业" :span="24">
        {{ getIndustryName(form.prodIndustryId) || '未关联' }}
      </a-descriptions-item>

      <a-descriptions-item label="产品介绍" :span="24">
        <div v-if="form.remark">{{ form.remark }}</div>
        <div v-else>暂无介绍</div>
      </a-descriptions-item>
    </a-descriptions>

    <template #footer>
      <a-button type="primary" @click="handleClose">
        关闭
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { getDictArray } from '@/utils/dict'
import { message } from 'ant-design-vue'

export default {
  name: 'TProdSpuViewModal',
  props: {
    categoryArray: {  // 从父组件接收的产品类型树
      type: Array,
      default: () => []
    },
    statusArray: {    // 状态字典
      type: Array,
      default: () => []
    },
    industrys: {      // 行业列表
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      visible: false,
      form: {},
      // 预生成映射表
      categoryMap: {},
      statusMap: {},
      industryMap: {}
    }
  },
  created() {
    // 初始化映射关系（只执行一次）
    this.categoryMap = this.categoryArray.reduce((acc, item) => {
      acc[item.key] = item.title
      return acc
    }, {})
    this.statusMap = this.statusArray.reduce((acc, item) => {
      acc[item.dictValue] = item.dictLabel
      return acc
    }, {})
    this.industryMap = this.industrys.reduce((acc, item) => {
      acc[item.id] = item.name
      return acc
    }, {})
  },
  methods: {
    /**
     * 查看方法（替代原edit）
     * @param {Object} record 行数据
     */
    view(record) {
      this.form = {
        id: record.id || 0,
        name: record.name || '',
        prodCategoryId: record.prodCategoryId || '',
        prodIndustryId: record.prodIndustryId || '',
        alias: record.alias || '',
        unit: record.unit || '',
        status: record.status || '0',
        remark: record.remark || ''
      }
      this.visible = true
    },

    // 辅助方法：获取分类名称
    getCategoryName(id) {
      return this.categoryMap[id] || this.categoryArray.find(item => item.key === id)?.title
    },

    // 辅助方法：获取状态文本
    getStatusText(value) {
      return this.statusMap[value] || this.statusArray.find(item => item.dictValue === value)?.dictLabel
    },

    // 辅助方法：获取行业名称
    getIndustryName(id) {
      return this.industryMap[id] || this.industrys.find(item => item.id === id)?.name
    },

    handleClose() {
      this.visible = false
      this.form = {} // 清空数据
    }
  }
}
</script>

<style scoped lang="less">
:deep(.ant-descriptions-item-label) {
  min-width: 100px;
  font-weight: 500;
  color: #2c3e50;
}

:deep(.ant-descriptions-item-content) {
  word-break: break-all;
  white-space: pre-wrap;
}

/* 优化长文本显示 */
:deep(.ant-descriptions-item-content div) {
  max-height: 100px;
  overflow-y: auto;
  padding: 8px;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-top: 4px;
}
</style>