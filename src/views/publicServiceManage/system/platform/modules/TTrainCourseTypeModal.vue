<template>
  <a-modal
    title="操作"
    style="top: 20px"
    :width="500"
    v-model:visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleClose"
    @ok="handleSubmit"
  >
    <a-form :model="form" ref="form">
      <a-form-item style="display: none">
        <a-input v-model:value="form.id" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="上级分类"
        name="parentId"
        :rules="formValidatorRules.parentId"
      >
        <a-tree-select
          v-model:value="form.parentId"
          :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
          :treeData="TProdCourseType"
          placeholder="上级分类"
          treeDefaultExpandAll
        ></a-tree-select>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="分类名"
        name="cname"
        :rules="{ required: true, message: '请输入分类名' }"
      >
        <a-input placeholder="分类名" v-model:value="form.cname" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="排序"
        name="sort"
      >
        <a-input-number placeholder="排序" :min="1" v-model:value="form.sort" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import {
  saveTTrainCourseType,
  getTTrainCourseTypeList,
} from "@/request/publicServiceManage/TTrainCourseType";
import { message } from "ant-design-vue";
import { getDictArray } from "@/utils/dict";

export default {
  name: "TTrainCourseTypeModal",
  props: {},
  components: {},
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      form: {},
      confirmLoading: false,
      mdl: {},
      TProdCourseType: [],
      formValidatorRules: {
        parentId: [{ required: true, message: "请选择上级分类" }],
        cname: [{ required: true, message: "请输入名称" }],
      },
      statusArray: [],
      statusMap: {},
      flagChildArray: [],
      flagChildMap: {},
    };
  },
  beforeCreate() {},
  created() {
    this.init();
  },
  methods: {
    init() {
      getDictArray("sys_normal_disable").then((data) => {
        this.statusArray = data;
        const statusMap = {};
        this.statusArray.map((d) => {
          statusMap[d.dictValue] = { text: d.dictLabel };
        });
        this.statusMap = statusMap;
      });
      getDictArray("t_prod_category_flag_child").then((data) => {
        this.flagChildArray = data;
        const flagChildMap = {};
        this.flagChildArray.map((d) => {
          flagChildMap[d.dictValue] = { text: d.dictLabel };
        });
        this.flagChildMap = flagChildMap;
      });
    },
    add(parentId) {
      this.edit({ parentId: parentId || 0, id: 0 });
    },
    async edit(record) {
      await this.loadTree();
      this.mdl = Object.assign(record);
      this.form = {
        id: this.mdl.id,
        parentId: this.mdl.parentId,
        cname: this.mdl.cname,
        sort: this.mdl.sort,
      };
      this.visible = true;
    },
    buildTree(list, arr, pid) {
      list.forEach((item) => {
        if (item.parentId === pid) {
          var child = {
            key: item.id,
            value: item.id + "",
            title: item.cname,
            children: [],
          };
          this.buildTree(list, child.children, item.id);
          arr.push(child);
        }
      });
    },
    loadTree() {
      getTTrainCourseTypeList().then((res) => {
        if (res.data.code === 0) {
          this.TProdCourseType = [{ key: "0", value: "0", title: "无" }];
          this.buildTree(res.data.rows, this.TProdCourseType, "0");
        }
      });
    },
    handleSubmit() {
      this.$refs["form"]
        .validateFields()
        .then((value) => {
          saveTTrainCourseType(this.form)
            .then((res) => {
              if (res.data.code === 0) {
                message.success("保存成功");
                this.handleClose();
                this.$emit("onOk");
              } else {
                message.error(res.msg);
              }
            })
            .catch(() => {
              message.error("系统错误，请稍后再试");
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        })
        .catch((err) => {
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    handleClose() {
      this.$refs["form"].resetFields();
      this.visible = false;
    },
  },
  watch: {
    /*
              'selectedRows': function (selectedRows) {
                this.needTotalList = this.needTotalList.map(item => {
                  return {
                    ...item,
                    total: selectedRows.reduce( (sum, val) => {
                      return sum + val[item.dataIndex]
                    }, 0)
                  }
                })
              }
              */
  },
};
</script>
<style scoped lang="less">
:deep(.ant-input-number) {
  width: 100%;
}
</style>
