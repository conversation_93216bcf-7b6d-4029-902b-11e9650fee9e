<template>
  <a-modal
    title="审核"
    style="top: 20px"
    :width="600"
    v-model:visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    @cancel="handleClose"
  >
    <a-form :model="form" ref="form">
      <a-form-item style="display: none">
        <a-input v-model:value="form.id" />
      </a-form-item>
      <a-form-item
        v-show="false"
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        name="type"
        label="审核类型"
      >
        <a-select v-model:value="form.type">
          <a-select-option
            v-for="(b, index) in typeArray"
            :key="index"
            :value="b.dictValue"
            >{{ b.dictLabel }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        v-show="false"
        name="tableId"
        :wrapperCol="wrapperCol"
        label="关联表ID"
      >
        <a-input v-model:value="form.tableId" placeholder="关联表ID" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        name="auditAdvice"
        label="审核意见"
      >
        <a-textarea
          v-model:value="form.auditAdvice"
          placeholder="审核意见"
          :rows="4"
        />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button type="primary" @click="handleSubmit(3)">驳回</a-button>
      <a-button type="primary" @click="handleSubmit(2)">通过</a-button>
    </template>
  </a-modal>
</template>
<script>
import { saveAuditLog } from "@/request/publicServiceManage/AuditLog";
import { getDictArray } from "@/utils/dict";
import { saveTProductSolutions } from "@/request/publicServiceManage/TProductSolutions";
import { saveTTrainCourse } from "@/request/publicServiceManage/TTrainCourse";
import { saveTPsEmpowerApply } from "@/request/publicServiceManage/TPsEmpowerApply";
import { message } from "ant-design-vue";
import { pick } from 'lodash'

export default {
  name: "AuditLogModal",
  props: {
    tableId: {
      type: String,
    },
    type: {
      type: String,
    },
  },
  components: {},
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 4 },
      },
      typeArray: [],
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 18 },
      },
      confirmLoading: false,
      mdl: {},
      form: {},
    };
  },
  beforeCreate() {},
  created() {
    this.init();
  },
  methods: {
    init() {},
    add() {
      this.edit({
        id: 0,
      });
    },
    edit(record) {
      this.mdl = Object.assign({}, record);
      this.form = pick(this.mdl, 'id', 'type', 'tableId', 'status', 'auditUserId');
      this.visible = true;
    },
    handleClose() {
      this.mdl = Object.assign({});
      this.$refs["form"].resetFields();
      this.visible = false;
    },
    handleSubmit(status) {
      let values = {
        status: status,
        tableId: this.tableId,
        type: this.type,
        id: 0,
        auditAdvice: this.form.auditAdvice
      };
      this.confirmLoading = true;
      saveAuditLog(values)
        .then((res) => {
          if (res.data.code === 0) {
            message.success("审核成功");
            this.$emit("onOk");
            this.handleClose()
          } else {
            message.error(res.data.msg);
          }
        })
        .catch(() => {
          message.error("系统错误，请稍后再试");
        })
        .finally(() => {
          this.confirmLoading = false;
        });
      const param = { id: this.tableId, auditStatus: status };
      if (this.type === "prod") {
        saveTProductSolutions(param);
      } else if (this.type === "course") {
        saveTTrainCourse(param);
      } else if (this.type === "apply") {
        const param2 = { id: this.tableId, status: status };
        saveTPsEmpowerApply(param2);
      }
    },
  },
  watch: {
    /*
      'selectedRows': function (selectedRows) {
        this.needTotalList = this.needTotalList.map(item => {
          return {
            ...item,
            total: selectedRows.reduce( (sum, val) => {
              return sum + val[item.dataIndex]
            }, 0)
          }
        })
      }
      */
  },
};
</script>
