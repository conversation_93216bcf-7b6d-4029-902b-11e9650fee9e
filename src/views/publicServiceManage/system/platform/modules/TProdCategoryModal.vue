<template>
  <a-modal title="操作"
           style="top: 20px;"
           :width="500"
           v-model:visible="visible"
           :confirmLoading="confirmLoading"
           @cancel="handleClose"
           @ok="handleSubmit">
    <a-form :model="form" ref="form">
      <a-form-item style="display:none">
        <a-input v-model:value="form.id" />
      </a-form-item>
      <a-form-item :labelCol="labelCol"
                   :wrapperCol="wrapperCol"
                   name="pid"
                   :rules="formValidatorRules.pid"
                   label="上级分类">
        <a-tree-select allowClear v-model:value="form.pid"
                       :dropdownStyle="{ maxHeight: '400px', overflow: 'auto' }"
                       :treeData="TProdCategoryes"
                       placeholder="上级分类"
                       treeDefaultExpandAll></a-tree-select>
      </a-form-item>
      <a-form-item :labelCol="labelCol"
                   :wrapperCol="wrapperCol"
                   name="name"
                   :rules="formValidatorRules.name"
                   label="名称">
        <a-input
            placeholder="名称"
            v-model:value="form.name" />
      </a-form-item>

      <a-form-item :labelCol="labelCol"
                   :wrapperCol="wrapperCol"
                   name="status"
                   :rules="{ required: true, message: '请选择状态' }"
                   label="状态">
        <a-select allowClear v-model:value="form.status">
          <a-select-option v-for="(b, index) in statusArray"
                           :key="index"
                           :value="b.dictValue">{{ b.dictLabel }}</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { getTProdCategoryList, saveTProdCategory, uniqueTProdCategory } from '@/request/publicServiceManage/tProdCategory'
// import pick from 'lodash.pick'
import { getDictArray } from '@/utils/dict'
import { message } from 'ant-design-vue'

const statusMap = {}
export default {
  name: 'TProdCategoryModal',
  props: {},
  components: {},
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      TProdCategoryes: [],
      confirmLoading: false,
      mdl: {},
      form: {},
      formValidatorRules: {
        pid: [{ required: true, message: '请选择上级分类' }],
        name: [{ required: true, message: '请输入名称' },
        //  { validator: this.validatorName }
        ]
      },
      statusArray: []
    }
  },
  beforeCreate() { },
  created() {
  },
  methods: {
    init() {
      getDictArray('sys_normal_disable').then(data => {
        this.statusArray = data
        this.statusArray.map(d => {
          statusMap[d.dictValue] = { text: d.dictLabel }
        })
      })
    },
    add(pid) {
      this.edit({ pid: pid || 0, id: 0 })
    },
    async edit(record) {
      await this.loadtree()
      await this.init()
      this.mdl = Object.assign(record)
      this.form = {
        id: this.mdl.id,
        name: this.mdl.name,
        pid: this.mdl.pid,
        status: this.mdl.status
      }
      this.visible = true
    },
    loadtree() {
      getTProdCategoryList().then(res => {
        this.TProdCategoryes = [{ key: '0', value: '0', title: '无' }]
        this.buildtree(res.data.rows, this.TProdCategoryes, '0')
      })
    },
    buildtree(list, arr, pid) {
      list.forEach(item => {
        if (item.pid === pid) {
          var child = {
            key: item.id,
            value: item.id + '',
            title: item.name,
            children: []
          }
          this.buildtree(list, child.children, item.id)
          arr.push(child)
        }
      })
    },
    handleSubmit() {
      this.$refs["form"]
        .validateFields()
        .then((value) => {
          saveTProdCategory(this.form)
            .then(res => {
              if (res.data.code === 0) {
                message.success("保存成功");
                this.handleClose();
                this.$emit("onOk");
              } else {
                message.error(res.data.msg)
              }
            })
            .catch(() => {
              message.error('系统错误，请稍后再试')
            })
            .finally(() => {
              this.confirmLoading = false
            })
        })
        .catch((err) => {
          console.log(7777, err);
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    handleClose() {
      this.$refs["form"].resetFields();
      this.visible = false;
    },
  },
  watch: {

  }
}
</script>
