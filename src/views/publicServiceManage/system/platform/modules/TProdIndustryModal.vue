<template>
  <a-modal title="操作"
           style="top: 20px;"
           :width="600"
           v-model:visible="visible"
           :confirmLoading="confirmLoading"
           @cancel="handleClose"
           @ok="handleSubmit">
    <a-form :model="form" ref="form">
      <a-form-item style="display:none">
        <a-input v-model:value="form.id" />
      </a-form-item>
      <a-form-item :labelCol="labelCol"
                   :wrapperCol="wrapperCol"
                   name="name"
                   :rules="formValidatorRules.name"
                   label="行业名称">
        <a-input placeholder="行业名称" v-model:value="form.name" />
      </a-form-item>

      <a-form-item :labelCol="labelCol"
                   :wrapperCol="wrapperCol"
                   name="status"
                   :rules="{ required: true, message: '请选择状态' }"
                   label="状态">
        <a-select allowClear v-model:value="form.status">
          <a-select-option v-for="(b, index) in statusArray"
                           :key="index"
                           :value="b.dictValue">{{ b.dictLabel }}</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item :labelCol="labelCol"
                   :wrapperCol="wrapperCol"
                   name="sort"
                   label="排序">
        <a-input-number placeholder="排序"
                 :min="1"
                 v-model:value="form.sort" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { saveTProdIndustry, uniqueTProdIndustry } from '@/request/publicServiceManage/tProdIndustry'
import { message } from 'ant-design-vue'
import { getDictArray } from '@/utils/dict'

const statusMap = {}
export default {
  name: 'TProdIndustryModal',
  props: {},
  components: {},
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      mdl: {},
      form: {},
      formValidatorRules: {
        name: [{ required: true, message: '请输入名称' }, 
        // { validator: this.validatorName }
      ]
      },
      statusArray: []
    }
  },
  beforeCreate() {
  },
  created() {
  },
  methods: {
    init() {
      getDictArray('sys_normal_disable').then(data => {
        this.statusArray = data
        this.statusArray.map(d => {
          statusMap[d.dictValue] = { text: d.dictLabel }
        })
      })
    },
    add() {
      this.edit({ id: 0 })
    },
    async edit(record) {
      await this.init()
      this.mdl = Object.assign(record)
      this.form = {
        id: this.mdl.id,
        name: this.mdl.name,
        status: this.mdl.status,
        sort: this.mdl.sort
      }
      this.visible = true
    },
    handleSubmit() {
      this.$refs["form"]
        .validateFields()
        .then(async (value) => {
          saveTProdIndustry(this.form)
            .then(res => {
              if (res.data.code === 0) {
                message.success("保存成功");
                this.handleClose();
                this.$emit("onOk");
              } else {
                message.error(res.data.msg)
              }
            })
            .catch(() => {
              message.error('系统错误，请稍后再试')
            })
            .finally(() => {
              this.confirmLoading = false
            })
        })
        .catch((err) => {
          console.log(7777, err);
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    // validatorName(rule, value, callback) {
    //   const id = this.form.getFieldValue('id')
    //   const errors = []
    //   if (value && value !== '') {
    //     this.confirmLoading = true
    //     uniqueTProdIndustry({ idValue: id, colName: 'name', colValue: value })
    //       .then(res => {
    //         if (res.code !== 0) {
    //           errors.push(value + '已存在')
    //         }
    //         callback(errors)
    //       })
    //       .catch(() => {
    //         this.$message.error('系统错误，请稍后再试')
    //         errors.push('系统错误，请稍后再试')
    //         callback(errors)
    //       })
    //       .finally(() => {
    //         this.confirmLoading = false
    //       })
    //   } else {
    //     callback()
    //   }
    // },
    handleClose() {
      this.$refs["form"].resetFields();
      this.visible = false;
    },
  },
  watch: {
    /*
      'selectedRows': function (selectedRows) {
        this.needTotalList = this.needTotalList.map(item => {
          return {
            ...item,
            total: selectedRows.reduce( (sum, val) => {
              return sum + val[item.dataIndex]
            }, 0)
          }
        })
      }
      */
  }
}
</script>
<style scoped lang="less">
:deep(.ant-input-number) {
  width: 100%;
}
</style>
