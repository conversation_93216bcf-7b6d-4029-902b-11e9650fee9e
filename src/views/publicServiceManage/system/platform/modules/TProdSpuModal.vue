<template>
  <a-modal
    title="操作"
    style="top: 20px"
    :width="700"
    v-model:visible="visible"
    @cancel="handleClose"
    @ok="handleSubmit"
  >
    <a-form :model="form" ref="form">
      <a-form-item style="display: none">
        <a-input v-model:value="form.id" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        name="name"
        :rules="[
          { required: true, message: '请输入安全能力' },
          { max: 200, message: '安全能力不能超过200个字符' },
        ]"
        label="安全能力"
      >
        <a-input placeholder="安全能力" v-model:value="form.name" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        name="prodCategoryId"
        :rules="{ required: false, message: '请选择产品类型' }"
        label="产品类型"
      >
        <a-tree-select
          allowClear
          v-model:value="form.prodCategoryId"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          :tree-data="categoryArray"
          placeholder="请选择产品类型"
          @change="handleChangeCategory"
          tree-default-expand-all
        >
        </a-tree-select>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        name="alias"
        :rules="[
          { required: false, message: '请输入产品别名' },
          { max: 200, message: '安全能力不能超过200个字符' },
        ]"
        label="产品别名"
      >
        <a-input placeholder="产品别名" v-model:value="form.alias" />
      </a-form-item>

      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        name="unit"
        :rules="[
          { required: true, message: '请输入计量单位' },
          { max: 10, message: '计量单位不能超过10个字符' },
        ]"
        label="计量单位"
      >
        <a-input placeholder="计量单位" v-model:value="form.unit" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        name="status"
        :rules="{ required: true, message: '请选择状态' }"
        label="状态"
      >
        <a-select allowClear v-model:value="form.status">
          <a-select-option
            v-for="(b, index) in statusArray"
            :key="index"
            :value="b.dictValue"
            >{{ b.dictLabel }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        name="prodIndustryId"
        :rules="[{ required: true, message: '请选择产品行业' }]"
        label="产品行业"
      >
        <a-select allowClear v-model:value="form.prodIndustryId">
          <a-select-option
            v-for="(b, index) in industrys"
            :key="index"
            :value="b.id"
            >{{ b.name }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        name="remark"
        :rules="[
          { required: false, message: '请输入产品介绍' },
          { max: 1000, message: '产品介绍不能超过1000个字符' },
        ]"
        label="产品介绍"
      >
        <a-textarea :rows="4" placeholder="产品介绍" v-model:value="form.remark" />
      </a-form-item>
    </a-form>
    <!-- <spu-prop-modal ref="propModal" @ok="addPropOk"></spu-prop-modal> -->
  </a-modal>
</template>
<script>
import {
  saveTProdSpu,
  addGetProp,
  editGetProp,
  unique,
} from "@/request/publicServiceManage/tProdSpu";
import { getTProdIndustryList } from "@/request/publicServiceManage/tProdIndustry";
import { message } from 'ant-design-vue'

export default {
  name: "TProdSpuModal",
  props: {
    categoryArray: {
      type: Array,
      default: () => [],
    },
    statusArray: {
      type: Array,
      default: () => [],
    },
    selfFlagArray: {
      type: Array,
      default: () => [],
    },
  },
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      industrys: [],
      confirmLoading: false,
      propLoading: false,
      mdl: {},
      prodPropList: [],
      form: {},
    };
  },
  beforeCreate() {},
  created() {
    this.init();
  },
  methods: {
    init() {
      // getDictArray('sys_normal_disable').then(data => {
      //   this.statusArry = data
      // })
      getTProdIndustryList(null).then((res) => {
        this.industrys = res.data.rows;
      });
    },
    add() {
      this.edit({
        id: 0,
      });
    },
    async edit(record) {
      await this.init()
      this.mdl = Object.assign(record);
      this.prodPropList = [];
      this.form = {
        id: this.mdl.id,
        name: this.mdl.name,
        prodCategoryId: this.mdl.prodCategoryId,
        prodIndustryId: this.mdl.prodIndustryId,
        alias: this.mdl.alias,
        prodManufacturerId: this.mdl.prodManufacturerId,
        status: this.mdl.status,
        unit: this.mdl.unit,
        selfFlag: this.mdl.selfFlag,
        remark: this.mdl.remark
      }
      this.visible = true;
    },
    handleClose() {
      this.$refs["form"].resetFields();
      this.visible = false;
    },
    handleSubmit() {
      this.$refs["form"]
        .validateFields()
        .then((value) => {
          saveTProdSpu(this.form)
            .then((res) => {
              if (res.data.code === 0) {
                  message.success("保存成功");
                  this.handleClose(true);
                  this.$emit("onOk");
              } else {
                message.error(res.data.msg);
              }
            })
            .catch(() => {
              message.error("系统错误，请稍后再试");
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        })
        .catch((err) => {
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    handleChangeCategory(value, label, extra) {},
  },
  watch: {
    /*
      'selectedRows': function (selectedRows) {
        this.needTotalList = this.needTotalList.map(item => {
          return {
            ...item,
            total: selectedRows.reduce( (sum, val) => {
              return sum + val[item.dataIndex]
            }, 0)
          }
        })
      }
      */
  },
};
</script>
<style lang="less" scoped>
.active {
  box-shadow: 0px 10px 25px 0px rgba(13, 57, 233, 0.418);
  border: 1px solid #b4bdda;
}
.imageDiv {
  margin-left: 35px;
  background: #f1f2f5;
  border: 1px solid transparent;
  &:hover {
    cursor: pointer;
    border: 1px solid #b4bdda;
    box-shadow: 0px 2px 16px 0px rgba(7, 27, 103, 0.16);
  }
}
.steps-box {
  padding: 0 25%;
  margin-top: 16px;
  padding-bottom: 40px;
  border-bottom: 1px solid #556ee61c;
  margin-bottom: 40px;
}
.dynamic-button {
  font-size: 14px;
  margin: 0px;
}
</style>
<style lang="less">
.quick-form-item {
  width: calc(100% - 48px) !important;
  margin-right: 8px;
}
.spu-title {
  width: 100%;
  height: 36px;
  line-height: 36px;
  background: #556ee61c;
  padding: 0px 12px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
}
</style>
