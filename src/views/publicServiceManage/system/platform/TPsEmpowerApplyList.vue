<template>
  <a-card :bordered="false" style="height: 100%" class="system-card">
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">解决方案：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            allowClear
            show-search
            placeholder="请选择解决方案"
            style="width: 100%"
            :filter-option="filterOption"
            v-model:value="searchData.productSolutionId"
          >
            <a-select-option
              v-for="info in prodInfo"
              :key="info.id.toString()"
              :value="info.id"
              >{{ info.sname }}</a-select-option
            >
          </a-select>
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">审核状态：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            allowClear
            placeholder="请选择审核状态"
            style="width: 100%"
            v-model:value="searchData.status"
          >
            <a-select-option
              v-for="(b, index) in auditStatusArray"
              :key="index"
              :value="b.dictValue"
              >{{ b.dictLabel }}</a-select-option
            >
          </a-select>
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 15px">
          重置
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <a-row>
        <a-col :span="24">
          <a-table
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
            <template #num="{ index }">
              <span>{{
                (pagination.current - 1) * pagination.pageSize +
                Number(index) +
                1
              }}</span>
            </template>
            <template #prodSlot="{ text }">
              {{ getCurrentProd(text) }}
            </template>
            <template #statusSlot="{ text }">
              {{ statusFilter(text) }}
            </template>
            <template #action="{ record }">
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleView(record)"
              >
                查看
              </a-button>
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleAudit(record)"
                v-if="record.status === 1"
              >
                授权
              </a-button>
              <a-button
                type="link"
                :disabled="true"
                v-if="record.status === 2"
              >
                已授权
              </a-button>
              <a-button
                type="link"
                :disabled="true"
                v-if="record.status === 3"
              >
                驳回
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    <audit-log-modal
      ref="auditModal"
      :table-id="id"
      :type="type"
      @onOk="onOk"
      />
    <applyListModal ref="applyListModal" />
  </a-card>
</template>

<script>
import {
  SearchOutlined,
  PlusOutlined
} from "@ant-design/icons-vue";
import { delTPsEmpowerApply, getTPsEmpowerApplyList } from '@/request/publicServiceManage/TPsEmpowerApply'
import { getDictArray } from '@/utils/dict'
import { delTProductSolutions, getTProductSolutionsList, saveTProductSolutions } from '@/request/publicServiceManage/TProductSolutions'
import AuditLogModal from './modules/AuditLogModal.vue'
import applyListModal from './modules/applyListModal.vue'
import { message } from "ant-design-vue";

export default {
  name: "TableList",
  components: {
    AuditLogModal,
    applyListModal,
    SearchOutlined,
    PlusOutlined
  },
  data() {
    return {
      prodInfo: [],
      id: null,
      type: 'apply',
      categoryArray: [],
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      tableLoading: false,
      searchData: {
      },
      lastsearchData: {
      },
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: "解决方案",
          dataIndex: "productSolutionId",
          slots: { customRender: "prodSlot" },
        },
        {
          title: "审核状态",
          dataIndex: "status",
          slots: { customRender: "statusSlot" },
        },
        {
          title: "申请时间",
          dataIndex: "createDate",
        },
        {
          title: "申请人",
          dataIndex: "useUserName",
        },
        {
          title: "操作",
          width: "200px",
          dataIndex: "action",
          align: "left",
          slots: { customRender: "action" },
        },
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
      statusArray: [],
      statusMap: {},
      selfFlagArray: [],
      selfFlagMap: {},
      ategoryArray: [],
      industryArray: [],
      categoryMap: {},
      industryMap: {},
      auditStatusMap: {},
      auditStatusArray: []
    };
  },
  created() {
    getTProductSolutionsList().then((res) => {
      if (res.data.code !== 0) {
        message.error(res.data.msg)
      }
      this.prodInfo = res.data.rows
    })
    getDictArray('audit_status').then(data => {
      this.auditStatusArray = data
      const auditStatusMap = {}
      this.auditStatusArray.forEach(d => {
        auditStatusMap[d.dictValue] = { text: d.dictLabel }
      })
      this.auditStatusMap = auditStatusMap
    })
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  mounted() {
    setTimeout(() => {
      this.flexHeight = `calc(${
        document.getElementsByClassName("system-card")[0].clientHeight
      }px - ${this.searchHeight}px - 48px)`;
      this.tableHeight =
        document.getElementsByClassName("system-card")[0].clientHeight -
        this.searchHeight -
        160;
    }, 100);
    this.handleSearch(true);
  },
  methods: {
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    handleSearch(flag) {
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
      const params = {
        ...this.lastsearchData,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
      };
      this.tableLoading = true;
      getTPsEmpowerApplyList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            this.tableData = res.data.rows;
            this.pagination.total = res.data.total;
          } else {
            message.error(res.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {};
      this.searchData = {};
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    handleAudit(record) {
      this.id = record.id
      this.$refs.auditModal.edit(record)
    },
    handleView(record) {
      this.$refs.applyListModal.view(record)
    },
    getCurrentPName(id) {
      console.log(id)
      if (this.userInfo) {
        const currentItem = this.userInfo.filter((item) => {
          return item.userId === id
        })[0]
        if (currentItem && currentItem.userName) {
          return `${currentItem.userName}-${currentItem.phonenumber}`
        } else {
          return '暂无信息'
        }
      }
    },
    getCurrentProd(id) {
      console.log(id)
      if (this.prodInfo) {
        const currentItem = this.prodInfo.filter((item) => {
          return item.id === id
        })[0]
        if (currentItem && currentItem.sname) {
          return `${currentItem.sname}`
        } else {
          return '暂无信息'
        }
      }
    },
    onOk() {
      this.handleSearch(true);
    },
    filterOption(input, option) {
      const userId = 1;
      const result = this.prodInfo.find(item => item.id === option.value.toLowerCase())?.sname;
      return result.indexOf(input.toLowerCase()) >= 0
    },
    statusFilter(status) {
      return this.auditStatusMap[status] && this.auditStatusMap[status].text
    }
  },
  watch: {},
};
</script>
<style lang="less" scoped></style>
