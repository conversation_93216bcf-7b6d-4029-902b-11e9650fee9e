<template>
  <a-card :bordered="false" style="height: 100%" class="system-card">
    <!-- 搜索条件栏 -->
    <div class="page-search" id="datalist_search">
      <!-- <div class="page-search-item">
        <div class="page-search-item-label">课程分类：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input allowClear v-emoji v-model:value.trim="searchData.title" placeholder="请输入方案名称" />
        </div>
      </div> -->
      <div class="page-search-item">
        <div class="page-search-item-label">课程名称：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input allowClear v-emoji v-model:value.trim="searchData.title" placeholder="请输入方案名称" />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">审核状态：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select allowClear v-model:value="searchData.auditStatus"
                    :getPopupContainer="(node) => node.parentNode || document.body" placeholder="请选择审核状态" style="width: 100%">
            <a-select-option :value="0">未提交</a-select-option>
            <a-select-option :value="1">审核中</a-select-option>
            <a-select-option :value="2">通过</a-select-option>
            <a-select-option :value="3">驳回</a-select-option>
          </a-select>
        </div>
      </div>


      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon>
            <SearchOutlined />
          </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 22px">
          重置
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <a-row>
        <a-col :span="24">
          <a-table style="padding-top: 10px; padding-left: 10px; padding-right: 10px" :columns="tableColumns"
                   :data-source="tableData" :loading="tableLoading" :pagination="pagination" :row-class-name="(record, index) => (index % 2 === 1 ? 'table-striped' : null)
            " :scroll="{ y: tableHeight }" @change="handleTableChange">
            <template #num="{ index }">
              <span>{{
                  (pagination.current - 1) * pagination.pageSize +
                  Number(index) +
                  1
                }}</span>
            </template>
            <template #trainCourseType="{ text }">
              {{ text.cname }}
            </template>
            <template #isFineWork="{ text }">
              {{ text ? "是" : "否" }}
            </template>
            <template #isRequired="{ text }">
              {{ text ? "必修" : "选修" }}
            </template>
            <template #auditStatus="{ text, record }">
              <div v-if="text === 0" class="text-wrapper_8 flex-col">
                <span class="text_20">审核未提交</span>
              </div>
              <div disabled v-if="text === 1" class="text-wrapper_8 flex-col btnDisabled">
                <span class="text_20">审核中</span>
              </div>
              <div disabled v-else-if="text === 2" class="text-wrapper_8 flex-col btnDisabled">
                <span class="text_20">审核通过</span>
              </div>
              <div
                  class="text-wrapper_8 flex-col btnDisabled isError"
                  v-else-if="text === 3"
              >
                <a-popover title="审核意见" placement="top">
                  <template #content>
                    <p style="max-width: 100%; height: auto; white-space: wrap">
                      {{ record.auditAdvice }}
                    </p>
                  </template>
                  <exclamation-circle-outlined style="color: #fc5532;margin-right: 5px;"/>
                </a-popover>
                <span class="text_20"
                >驳回</span>
              </div>
            </template>
            <template #mark="{ record }">
              <a-button
                  type="link"
                  @click="$refs.dataModalChapters.show(record)"
              >
                查看
              </a-button>
              <a-button
                  type="link"
                  :disabled="record.auditStatus !== 1"
                  @click="handleEdit(record)"
              >
                审核
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    <audit-log-modal
        ref="modal"
        @onOk="onOk"
        :table-id="id"
        :type="type"/>
    <TTrainCourseChaptersList ref="dataModalChapters" />
  </a-card>
</template>

<script>
import {
  ExclamationCircleOutlined,
  MailOutlined,
  ContainerOutlined,
  StarFilled,
  StarOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";
import { createVNode } from "vue";
import { Modal } from "ant-design-vue";
import {getTTrainCourseList} from "@/request/publicServiceManage/TTrainCourse";
import AuditLogModal from '@/views/publicServiceManage/system/platform/modules/AuditLogModal.vue'
import TTrainCourseChaptersList from './modules/TTrainCourseChaptersList.vue'

export default {
  components: {
    MailOutlined,
    ContainerOutlined,
    StarFilled,
    StarOutlined,
    SearchOutlined,
    ExclamationCircleOutlined,
    AuditLogModal,
    TTrainCourseChaptersList
  },
  data() {
    return {
      userInfo: [],
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      newVisible: false,
      tableLoading: false,
      formData: {
        title: "",
        content: "",
        createTime: "",
        isRead: "",
        mark: "",
      },
      searchData: {
      },
      lastsearchData: {
      },
      type: 'course',
      id: '',
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: "课程名称",
          dataIndex: "title",
          align: "center",
          ellipsis: true,
          slots: {
            customRender: "title",
          },
        },
        {
          title: "分类",
          dataIndex: "trainCourseType",
          align: "center",
          slots: {
            customRender: "trainCourseType",
          },
        },
        {
          title: "是否精选",
          dataIndex: "isFineWork",
          align: "center",
          slots: {
            customRender: "isFineWork",
          },
        },
        {
          title: "学习要求",
          dataIndex: "isRequired",
          align: "center",
          slots: {
            customRender: "isRequired",
          },
        },
        {
          title: "简介",
          dataIndex: "shortDesc",
          align: "center",
          ellipsis: true,
          slots: {
            customRender: "shortDesc",
          },
        },
        {
          title: "审核状态",
          dataIndex: "auditStatus",
          align: "center",
          slots: {
            customRender: "auditStatus",
          },
        },
        {
          title: "操作",
          dataIndex: "mark",
          width: "240px",
          align: "center",
          slots: {
            customRender: "mark",
          },
        },
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
    };
  },
  mounted() {
    this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    setTimeout(() => {
      this.flexHeight = `calc(100vh - ${this.searchHeight}px - 140px)`;
      this.tableHeight =
          (window.innerHeight ||
              document.documentElement.clientHeight ||
              document.body.clientHeight) -
          336 -
          this.searchHeight;
    }, 100);
  },
  computed: {
    rowSelection() {
      const { selectedRowKeys } = this;
      return {
        selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRowKeys = selectedRowKeys;
        },
        getCheckboxProps: (record) => ({
          disabled: record.isRead, // 根据isRead字段判断是否禁用该行的复选框
        }),
      };
    },
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  methods: {
    handleClose() {
      this.newVisible = false;
    },
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    async handleSearch(flag) {
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
      this.tableLoading = true;
      const params = {
        searchValue: 'audit',
        auditStatus: this.lastsearchData.auditStatus,
        title: this.lastsearchData.title,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
      }
      await getTTrainCourseList(params).then((data) => {
        console.log(data)
        if (data.status === 200) {
          this.tableData = data.data.rows
          this.pagination.total = data.data.total;

        } else {
          this.tableData = []
          this.pagination.total = 0
        }
      })
      this.tableLoading = false;
    },
    onOk() {
      this.handleSearch(true);
    },
    handleEdit(record) {
      this.id = record.id
      this.$refs.modal.edit(record)
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {
      };
      this.searchData = {
      };
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    handleSubmit(value) {
      this.$refs.submitModal.edit(value);
    },
    handleDelete(value) {
      if (value.auditStatus === 1) {
        this.$message.error("正在审核中，请等待审核完成再进行编辑操作！");
        return;
      }
      Modal.confirm({
        title: "确定要删除选中内容吗?",
        okText: "确定",
        okType: "primary",
        cancelText: "取消",
        onOk() { },
        onCancel() { },
      });
    }
  },
};
</script>

<style scoped lang="less">
.form-color {

  //已办样式
  .ant-input[disabled] {
    color: black;
    background-color: white;
    border: none;
    cursor: default;
  }

  :deep(.ant-select-disabled.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
    color: black;
    background: white;
    cursor: default;
  }

  :deep(.ant-select-selector, .ant-select-selector:focus) {
    border: none;
    box-shadow: none;
    background-color: transparent;
  }

  :deep(.ant-select-selector .ant-select-selection-search) {
    padding-right: 0;
  }

  :deep(.ant-select-arrow) {
    display: none;
  }

  :deep(.ant-form-item-label > label.ant-form-item-required:not(.ant-form-item-required-mark-optional)::before) {
    color: white;
  }
}
.disabled-btn {
  color: #c3c0c0 !important;
}
</style>
