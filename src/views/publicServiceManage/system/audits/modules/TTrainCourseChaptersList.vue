<template>
  <a-modal :title="mdl.title" style="top: 20px;height: 100%" :width="800" v-model:visible="visible" :ok-button-props="{ style: { display: 'none' } }">
    <a-card :bordered="false">
      <div class="top">
        <img :src="mdl.thumb" alt="" width="300" height="200" class="top-left" />
        <div class="top-right">
          <h2>简介</h2>
          <p class="detail" :title="mdl.shortDesc">
            {{mdl.shortDesc}}
          </p>
          <div class="tips">
            <span>是否必修: {{isRequiredFilter(mdl.isRequired)}}</span>
            <span>课程类型: {{ mdl.trainCourseType ? mdl.trainCourseType.cname : '' }}</span>
            <!-- <span>总课时: {{ mdl.classHour }}</span> -->
          </div>
        </div>
      </div>
      <div class="bottom" style="margin-top: 15px">
        <a-collapse  expandIconPosition="right">
          <a-collapse-panel :header="`${i + 1}  ${item.cname}`" v-for="(item, i) in data" :key="`${i + 1}`">
            <div v-for="(iItem, ii) in item.children" :key="ii" class="collapse-content">
              <div class="text">
                <span>{{ iItem.title }}</span
                ><span :class="`span${iItem.resources.ftype}`" @click="previewFile(iItem.resources)">
                  {{ iItem.resources.fname }}
                  <a class="character">点击预览</a>
                </span>
              </div>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
      <videoDom v-if="showVideo" :url="videoUrl" @close="videoClose"></videoDom>
    </a-card>
  </a-modal>
</template>

<script>
import { delTTrainCourseChapters, getTTrainCourseChaptersList, getTree } from '@/request/publicServiceManage/TTrainCourseChapters'
import videoDom from '@/components/filesPreview/videoDom'


export default {
  name: 'TableList',
  components: {
    videoDom
  },
  data() {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      pagination: false,
      loading: false,
      form: {},
      mdl: {},
      data: [],
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      // 表头
      columns: [
        // {
        //   title: '培训课程ID',
        //   dataIndex: 'trainCourseId'
        // },
        {
          title: '课程章节标题',
          dataIndex: 'cname'
        },
        {
          title: '排序',
          dataIndex: 'sort'
        },
        {
          title: '创建时间',
          dataIndex: 'createDate',
        },
        {
          title: '操作',
          width: '120px',
          dataIndex: 'action',
          align: 'center',
          slots: { customRender: 'action' }
        }
      ],
      visible: false,
      selectedRowKeys: [],
      selectedRows: [],
      addEnable: true,
      editEnabel: true,
      removeEnable: true,
      videoUrl: '',
      showVideo: false
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      getTree(Object.assign(this.queryParam)).then((res) => {
        if (res.data.code === 0) {
          this.data = res.data.data
        }
      })
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    show(record) {
      this.visible = true
      this.mdl = Object.assign({}, record)
      this.mdl.thumb = this.getPath(this.mdl.thumb)
      this.queryParam.trainCourseId = record.id
      this.init()
    },
    getPath(filePath) {
      return process.env.VUE_APP_File_URL + filePath
    },
    handleAdd() {
      this.$refs.modal.add(this.queryParam.trainCourseId)
    },
    handleEdit(record) {
      this.$refs.modal.edit(record)
    },
    handleOk() {
      this.init()
    },
    isRequiredFilter(value) {
      if ((value === 1) | (value === '1')) {
        return '必修'
      } else if ((value === '0') | (value === 0)) {
        return '选修'
      }
    },
    delByIds(ids) {
      delTTrainCourseChapters({ ids: ids.join(',') }).then((res) => {
        if (res.code === 0) {
          this.$message.success('删除成功')
          this.handleOk()
        } else {
          this.$message.error(res.msg)
        }
        this.$refs.table.clearSelected()
      })
    },
    previewFile(value) {
      if (value.ftype === 1) {
        this.videoUrl = value.url
        this.showVideo = true
        return
      }
      let typeName = ''
      switch (value.ftype) {
        case 2:
          typeName = 'previewPdf'
          break
        case 3:
          typeName = 'previewExcel'
          break
        case 4:
          typeName = 'previewWord'
          break
      }
      const routeData = this.$router.resolve({
        path: '/preview',
        query: {
          type: typeName,
          id: value.id
        }
      })
      window.open(routeData.href, '_blank')
    },
    videoClose() {
      this.showVideo = false
    }
  }
}
</script>
<style lang="less" scoped>
:deep(.ant-card-body) {
  padding: 0px !important;
}
.top {
  display: flex;
  height: 200px;
  .top-left {
    object-fit: contain;
    border: 1px solid #f1f1f1;
  }
  .top-right {
    position: relative;
    flex: 1;
    height: 100%;
    padding: 20px;
    p {
      max-height: 63px;
    }
    .tips {
      position: absolute;
      left: 20px;
      bottom: 20px;
      span {
        margin-right: 25px;
      }
    }
  }
}
.bottom {
  .collapse-content .text {
    display: flex;
    justify-content: space-between;
    align-items: center;
    span {
      width: 50%;
      // border: 1px solid #3c6fea;
      padding: 1px 4px;
      // color: #3c6fea;
      // cursor: pointer;
      // transition: all 0.3s;
      // &:hover {
      //   background-color: #8cd7f581;
      // }
      &:last-child {
        width: fit-content;
        max-width: 50%;
        position: relative;
        text-align: right;
        padding-left: 25px;
        &::before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          margin: auto;
          display: inline-block;
          width: 20px;
          height: 20px;
          content: '';
        }
        .character {
          font-size: 12px;
          color: #3c6fea;
          cursor: pointer;
        }
      }
    }
    .span1::before {
      background: url('@/assets/admin/1.png');
      background-size: 100% 100%;
    }
    .span2::before {
      background: url('@/assets/admin/2.png');
      background-size: 100% 100%;
    }
    .span3::before {
      background: url('@/assets/admin/3.png');
      background-size: 100% 100%;
    }
    .span4::before {
      background: url('@/assets/admin/4.png');
      background-size: 100% 100%;
    }
  }
  :deep(.ant-collapse.ant-collapse-icon-position-right) {
    border: none;
    background-color: #fff;
    & > .ant-collapse-item {
      background-color: #f4f6fa;
      border: none;
      margin-bottom: 5px;
      // .ant-collapse-content {
      // }
      .ant-collapse-header {
        padding: 8px 16px;
      }
      .ant-collapse-content {
        border: none;
      }

      .ant-collapse-content > .ant-collapse-content-box {
        padding: 0px;
        .collapse-content {
          padding: 8px 20px;
        }
        .collapse-content:not(:last-child) {
          border-bottom: 1px solid #f1f1f1;
        }
      }
    }
  }
}
.detail{
  word-break: break-all;
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 12;
  -webkit-box-orient: vertical;
}
</style>
