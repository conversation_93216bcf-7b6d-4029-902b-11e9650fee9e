<template>
  <a-modal :title="mdl.sname" style="top: 20px" :width="800" v-model:visible="visible" :ok-button-props="{ style: { display: 'none' } }">
    <a-card :bordered="false">
      <div class="top">
        <img :src="mdl.thumb" alt="" width="300" height="200" class="top-left" />
        <div class="top-right">
          <h2>简介</h2>
          <p :title="mdl.solutionIntroduce" class="desc">
            {{mdl.solutionIntroduce}}
          </p>
          <div class="preview">
            附件(点击预览):
            <span v-for="(item, i) in resources" :key="i" @click="previewFile(item.resources)">
              <img width="24" v-if="item.resources.ftype === 1" src="@/assets/admin/1.png" alt="视频" title="视频"/>
              <img width="24" v-if="item.resources.ftype === 2" src="@/assets/admin/2.png" alt="pdf" title="pdf"/>
              <img width="24" v-if="item.resources.ftype === 3" src="@/assets/admin/3.png" alt="excel" title="excel"/>
              <img width="24" v-if="item.resources.ftype === 4" src="@/assets/admin/4.png" alt="word" title="word"/>
            </span>
          </div>
          <div class="tips">
            <span>安全能力: {{ mdl.productSpuName }}</span>
            <span>方案级别: {{ securityLevelFilter(mdl.securityLevel,securityLevelMap) }}</span>
          </div>
        </div>
      </div>
      <div class="bottom" style="margin-top: 15px">
        <a-collapse>
          <a-collapse-panel header="行业需求：">
            <div v-for="(item, i) in data.industryDemandForm" :key="i" class="collapse-content">
              <div class="text industryDemand">
                <p class="title">{{ item.title }}</p>
                <p class="context">{{ item.context }}</p>
              </div>
            </div>
          </a-collapse-panel>
        </a-collapse>
        <a-collapse>
          <a-collapse-panel header="痛点分析：">
            <div v-for="(item, i) in data.solutionForm" :key="i" class="collapse-content">
              <div class="text solution">
                <p class="title">{{ item.title }}</p>
                <p class="context">{{ item.context }}</p>
              </div>
            </div>
          </a-collapse-panel>
        </a-collapse>
        <a-collapse>
          <a-collapse-panel header="典型客户：">
            <div v-for="(item, i) in data.customersForm" :key="i" class="collapse-content">
              <div class="text customers">
                <p class="title">客户名称: {{ item.title }}</p>
                <p class="context">客户项目名称: {{ item.context }}</p>
              </div>
            </div>
          </a-collapse-panel>
        </a-collapse>
      </div>
      <videoDom v-if="showVideo" :url="videoUrl" @close="videoClose"></videoDom>
    </a-card>
  </a-modal>
</template>

<script>
import videoDom from '@/components/filesPreview/videoDom'
import { getTProductSolutionsItem } from '@/request/publicServiceManage/TProductSolutions'

export default {
  name: 'TProductSolutionDetail',
  components: {
    videoDom
  },
  props: {
    securityLevelMap: {
      type: Object
    }
  },
  data() {
    return {
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      pagination: false,
      loading: false,
      form: {},
      mdl: {},
      data: {},
      // 高级搜索 展开/关闭
      advanced: false,
      // 查询参数
      queryParam: {},
      resources: [],
      visible: false,
      selectedRowKeys: [],
      selectedRows: [],
      addEnable: true,
      editEnabel: true,
      removeEnable: true,
      videoUrl: '',
      showVideo: false
    }
  },
  created() {
  },
  methods: {
    // init() {
    //   getTree(Object.assign(this.queryParam)).then((res) => {
    //     if (res.code === 0) {
    //       this.data = res.data
    //     }
    //   })
    // },
    async init() {
      const res = await getTProductSolutionsItem(this.queryParam.solutionId)
      if (res.data.code === 0) {
        this.data = res.data.data
        this.splitAttributes(res.data.data.attributes)
        this.resources = res.data.data.chapters[0].children
      }
    },
    splitAttributes(value) {
      this.data.industryDemandForm = []
      this.data.solutionForm = []
      this.data.customersForm = []
      value.concat([]).filter((item, i) => {
        if (item.type === 1) {
          this.data.industryDemandForm.push(item)
        } else if (item.type === 2) {
          this.data.solutionForm.push(item)
        } else {
          this.data.customersForm.push(item)
        }
      })
      this.data.attributes = []
    },
    securityLevelFilter(securityLevel, securityLevelMap) {
      return securityLevelMap[securityLevel] && securityLevelMap[securityLevel].text
    },
    onSelectChange(selectedRowKeys, selectedRows) {
      this.selectedRowKeys = selectedRowKeys
      this.selectedRows = selectedRows
    },
    show(record) {
      this.visible = true
      this.mdl = Object.assign({}, record)
      this.mdl.thumb = this.getPath(this.mdl.thumb)
      console.log(this.mdl.thumb)
      this.queryParam.solutionId = record.id
      this.init()
    },
    getPath(filePath) {
      return process.env.VUE_APP_File_URL + filePath
    },
    handleAdd() {
      this.$refs.modal.add(this.queryParam.trainCourseId)
    },
    handleEdit(record) {
      this.$refs.modal.edit(record)
    },
    handleOk() {
      this.init()
    },
    previewFile(value) {
      console.log(value)
      if (value.ftype === 1) {
        this.videoUrl = value.url
        this.showVideo = true
        return
      }
      let typeName = ''
      switch (value.ftype) {
        case 2:
          typeName = 'previewPdf'
          break
        case 3:
          typeName = 'previewExcel'
          break
        case 4:
          typeName = 'previewWord'
          break
      }
      const routeData = this.$router.resolve({
        path: '/preview',
        query: {
          type: typeName,
          id: value.id
        }
      })
      window.open(routeData.href, '_blank')
    },
    videoClose() {
      this.showVideo = false
    }
  }
}
</script>
<style lang="less" scoped>
:deep(.ant-card-body)  {
  padding: 0px !important;
}
.top {
  display: flex;
  height: 200px;
  .top-left {
    object-fit: contain;
    border: 1px solid #f1f1f1;
  }
  .top-right {
    position: relative;
    flex: 1;
    height: 100%;
    padding: 20px;
    padding-bottom: 15px;
    p {
      max-height: 63px;
    }
    .desc {
      word-break: break-all;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
    }
    .tips {
      // position: absolute;
      // left: 20px;
      // bottom: 20px;
      span {
        margin-right: 25px;
      }
    }
    .preview {
      margin-bottom: 5px;
      span {
        cursor: pointer;
        margin-left: 4px;
      }
      //img {
      //  width: 24px;
      //}
    }
  }
}
.bottom {
  .collapse-content .text {
    display: flex;
    justify-content: space-between;
    span {
      width: 50%;
      // border: 1px solid #3c6fea;
      padding: 1px 4px;
      // color: #3c6fea;
      // cursor: pointer;
      // transition: all 0.3s;
      // &:hover {
      //   background-color: #8cd7f581;
      // }
      &:last-child {
        width: fit-content;
        max-width: 50%;
        position: relative;
        text-align: right;
        padding-left: 25px;
        &::before {
          position: absolute;
          top: 0;
          bottom: 0;
          left: 0;
          margin: auto;
          display: inline-block;
          width: 20px;
          height: 20px;
          content: '';
        }
        .character {
          font-size: 12px;
          color: #3c6fea;
          cursor: pointer;
        }
      }
    }
  }
  .industryDemand, .solution{
    flex-direction: column;
    align-items: flex-start;
    padding: 0 30px;
    .title {
      margin-bottom: 5px;
      color: #333;
      font-weight: 600;
    }
    .context {
      width: 100%;
    }
  }
  .customers {
    padding: 0 30px;
    align-items: center;
    justify-content: flex-start !important;
    p {
      margin: 0;
    }
    p:first-child {
      margin-right: 30px;
    }
  }
  :deep(.ant-collapse.ant-collapse-icon-position-left) {
    border: none;
    background-color: #fff;
    & > .ant-collapse-item {
      background-color: #f4f6fa;
      border: none;
      margin-bottom: 5px;
      // .ant-collapse-content {
      // }
      .ant-collapse-header {
        padding: 8px 16px 8px 40px;
      }
      .ant-collapse-content {
        border: none;
      }

      .ant-collapse-content > .ant-collapse-content-box {
        padding: 0px;
        .collapse-content {
          padding: 8px 16px;
        }
        .collapse-content:not(:last-child) {
          border-bottom: 1px solid #f1f1f1;
        }
      }
    }
  }
}
</style>
