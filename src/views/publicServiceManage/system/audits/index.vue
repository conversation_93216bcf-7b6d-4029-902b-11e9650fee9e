<template>
  <div class="page">
    <div class="page-left">
      <div v-if="typeListData.length > 0" class="menuList">
        <div
            class="listItem"
            v-for="(item, i) in typeListData"
            :key="i"
            @click="clickCategory(item.id)"
            :class="currentTypeIndex === item.id ? 'active' : ''"
            :title="item.name"
        >
          {{ item.name }}
        </div>
      </div>
      <div class="menuList" v-else>
        <div
            style="
              font-size: 16px;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            "
        >
          暂无内容
        </div>
      </div>
    </div>
    <div class="page-right" style="width: calc(100% - 180px);">
      <component :is='componentName' style="height: 100%;"></component>
    </div>
  </div>
</template>

<script>

import { InboxOutlined, UploadOutlined, SearchOutlined } from "@ant-design/icons-vue";
import TProductSolutionsList from './TProductSolutionsList.vue'
import TrainCourseList from './TrainCourseList.vue'


export default {
  components: { InboxOutlined, UploadOutlined, SearchOutlined, TProductSolutionsList, TrainCourseList },
  data() {
    return {
      componentName: null,
      currentTypeIndex: null,
      typeListData: [
        {
          id: "TProductSolutionsList",
          name: "方案审核",
        },
        {
          id: "TrainCourseList",
          name: "课程审核",
        }
      ],
    };
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  created() {
    this.currentTypeIndex = this.typeListData[0].id
    this.componentName = this.currentTypeIndex
  },
  methods: {
    clickCategory(index) {
      this.currentTypeIndex = index;
      this.componentName = index
    },
  },
};
</script>

<style scoped lang='less'>
.ant-card {
  margin-bottom: 1px;
}
.page {
  display: flex;
  align-items: flex-start;
  margin-top: 15px;
  gap: 15px;
  .page-left {
    overflow: hidden;
    width: 180px;
    background: #fff;
    border-radius: 5px;
  }
  .page-left, .page-right {
    height: calc(100vh - 180px);
  }
}

.menuList {
  height: 605px;
  width: 100%;
  padding: 0;
  overflow-y: overlay;

  .listItem {
    font-size: 16px;
    font-family: PingFangSC-Regular, PingFang SC;
    font-weight: 600;
    color: #3c404c;
    padding: 16px 20px;
    cursor: pointer;
    // transition: all 0.3s;
    text-align: center;
    text-overflow: ellipsis;
    overflow: hidden;
    white-space: nowrap;

    &:hover {
      background: #0f79e9;
      color: #fff;
    }
  }

  //   .listItem:not(:first-child) {
  //     margin: 10px 0;
  //   }
  .active {
    background: #0f79e9;
    color: #fff;
  }
}
</style>
