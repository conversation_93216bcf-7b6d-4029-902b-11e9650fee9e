<template>
  <div class="page">
    <!-- 搜索条件栏 -->
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">方案级别：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            allowClear
            :getPopupContainer="(node) => node.parentNode || document.body"
            placeholder="请选择方案级别"
            v-model:value="searchData.securityLevel"
            style="width: 100%"
          >
            <a-select-option
              v-for="(b, index) in securityLevelArray"
              :key="index"
              :value="b.dictValue"
            >
              {{ b.dictLabel }}
            </a-select-option>
          </a-select>
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">审核状态：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            allowClear
            v-model:value="searchData.auditStatus"
            :getPopupContainer="(node) => node.parentNode || document.body"
            placeholder="请选择审核状态"
            style="width: 100%"
          >
            <a-select-option :value="0">未提交</a-select-option>
            <a-select-option :value="1">审核中</a-select-option>
            <a-select-option :value="2">通过</a-select-option>
            <a-select-option :value="3">驳回</a-select-option>
          </a-select>
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">方案名称：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            allowClear
            v-emoji
            v-model:value.trim="searchData.sname"
            placeholder="请输入方案名称"
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">是否上架：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            allowClear
            v-model:value="searchData.isShow"
            :getPopupContainer="(node) => node.parentNode || document.body"
            placeholder="请选择审核状态"
            style="width: 100%"
          >
            <a-select-option :value="1">是</a-select-option>
            <a-select-option :value="0">否</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 22px">
          重置
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <a-row justify="end">
        <a-col :span="3">
          <div class="page-content-buttons">
            <a-button type="primary" class="page-content-buttons-item" @click="handleAdd()">
              新增
            </a-button>
          </div>
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="24">
          <a-table
            style="padding-top: 10px; padding-left: 10px; padding-right: 10px"
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :row-class-name="
              (record, index) => (index % 2 === 1 ? 'table-striped' : null)
            "
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
            <template #num="{ index }">
              <span>{{
                (pagination.current - 1) * pagination.pageSize +
                Number(index) +
                1
              }}</span>
            </template>
            <template #securityLevel="{ text }">
              <span v-if="text === '0'">企业商密</span>
              <span v-else-if="text === '1'">内部商密</span>
            </template>
            <template #isShow="{ text, record }">
              <a-switch
                :checked="text === 1"
                :disabled="record.auditStatus !== 2"
                checked-children="是"
                un-checked-children="否"
                @change="updowncircle(record)"
              />
            </template>
            <template #version="{ text }"> v{{ text }} </template>
            <template #auditStatus="{ text, record }">
              <div v-if="text === 0" class="text-wrapper_8 flex-col">
                <span class="text_20">审核未提交</span>
              </div>
              <div
                disabled
                v-if="text === 1"
                class="text-wrapper_8 flex-col btnDisabled"
              >
                <span class="text_20">审核中</span>
              </div>
              <div
                disabled
                v-else-if="text === 2"
                class="text-wrapper_8 flex-col btnDisabled"
              >
                <span class="text_20">审核通过</span>
              </div>
              <div
                class="text-wrapper_8 flex-col btnDisabled isError"
                v-else-if="text === 3"
              >
                <a-popover title="审核意见" placement="top">
                  <template #content>
                    <p style="max-width: 100%; height: auto; white-space: pre-wrap; word-break: break-all;">
                      {{ record.auditAdvice || '暂无驳回意见' }}
                    </p>
                  </template>
                  <exclamation-circle-outlined style="color: #fc5532;margin-right: 5px;"/>
                </a-popover>
                <span class="text_20"
                  >驳回</span>
              </div>
            </template>
            <template #mark="{ record }">
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleView(record)"
              >
                查看
              </a-button>
              <a-button
                type="link"
                :disabled="record.auditStatus === 1 || record.isShow === 1"
                :class="record.auditStatus === 1 || record.isShow === 1 ? 'disabled-btn' : ''"
                style="color: #387ff1"
                @click="handleEdit(record)"
              >
                编辑
              </a-button>
              <a-button
                type="link"
                :disabled="record.auditStatus === 1 || record.auditStatus === 2"
                :class="record.auditStatus === 1 || record.auditStatus === 2 ? 'disabled-btn' : ''"
                style="color: #387ff1"
                @click="handleSubmit(record)"
              >
                提交审核
              </a-button>
              <a-button
                type="link"
                :disabled="record.auditStatus === 1 || record.isShow === 1"
                :class="record.auditStatus === 1 || record.isShow === 1 ? 'disabled-btn' : ''"
                style="color: #fc5532"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
  </div>
  <programmeView ref="programmeView" @onOk="onOk"/>
  <programmeEdit ref="programmeEdit" @onOk="onOk"/>
  <submitModal ref="submitModal" :user-info="userInfo" @onOk="onOk" type="programme"/>
</template>

<script>
import {
  postMessagePages,
  postMessageMark,
  postMessageRead,
} from "@/request/messagenotification_messagelist";
import { message } from 'ant-design-vue';
import {
  ExclamationCircleOutlined,
  MailOutlined,
  ContainerOutlined,
  StarFilled,
  StarOutlined,
  SearchOutlined,
  QuestionCircleOutlined
} from "@ant-design/icons-vue";
import { getDictArray } from "@/utils/dict";
import { Modal } from "ant-design-vue";
import programmeEdit from "./modules/programmeEdit.vue";
import programmeView from "./modules/programmeView.vue";
import submitModal from "../modules/submitModal.vue";
import { getTProductSolutionsList, saveTProductSolutions, delTProductSolutions } from '@/request/publicServiceManage/TProductSolutions'

export default {
  components: {
    MailOutlined,
    ContainerOutlined,
    StarFilled,
    StarOutlined,
    SearchOutlined,
    programmeEdit,
    programmeView,
    submitModal,
    ExclamationCircleOutlined,
    QuestionCircleOutlined
  },
  data() {
    return {
      userInfo: [],
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      newVisible: false,
      tableLoading: false,
      formData: {
        title: "",
        content: "",
        createTime: "",
        isRead: "",
        mark: "",
      },
      searchData: {
        title: "",
        status: null,
        operTime: [],
      },
      lastsearchData: {
        title: "",
        status: null,
        operTime: [],
      },
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: "方案名称",
          dataIndex: "sname",
          align: "center",
          ellipsis: true,
          slots: {
            customRender: "sname",
          },
        },
        {
          title: "所属行业",
          dataIndex: "industryName",
          align: "center",
        },
        {
          title: "方案级别",
          dataIndex: "securityLevel",
          align: "center",
          slots: {
            customRender: "securityLevel",
          },
        },
        {
          title: "安全能力",
          dataIndex: "productSpuName",
          align: "center",
          slots: {
            customRender: "productSpuName",
          },
        },
        {
          title: "版本号",
          dataIndex: "version",
          align: "center",
          slots: {
            customRender: "version",
          },
        },
        {
          title: "是否上架",
          dataIndex: "isShow",
          align: "center",
          slots: {
            customRender: "isShow",
          },
        },
        {
          title: "审核状态",
          dataIndex: "auditStatus",
          align: "center",
          slots: {
            customRender: "auditStatus",
          },
        },
        {
          title: "操作",
          dataIndex: "mark",
          width: "320px",
          align: "center",
          slots: {
            customRender: "mark",
          },
        },
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
      securityLevelArray: []
    };
  },
  created() {
    getDictArray("security_level").then((data) => {
      this.securityLevelArray = data;
      const securityLevelMap = {};
      this.securityLevelArray.forEach((d) => {
        securityLevelMap[d.dictValue] = { text: d.dictLabel };
      });
      this.securityLevelMap = securityLevelMap;
      console.log(this.securityLevelMap);
    });
  },
  mounted() {
    this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    setTimeout(() => {
      this.flexHeight = `calc(100vh - ${this.searchHeight}px - 140px)`;
      this.tableHeight =
        (window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight) -
        336 -
        this.searchHeight;
    }, 100);
  },
  computed: {
    rowSelection() {
      const { selectedRowKeys } = this;
      return {
        selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRowKeys = selectedRowKeys;
        },
        getCheckboxProps: (record) => ({
          disabled: record.isRead, // 根据isRead字段判断是否禁用该行的复选框
        }),
      };
    },
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  methods: {
    handleClose() {
      this.newVisible = false;
    },
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    handleSearch(flag) {
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
        const params = {
          searchValue: 'manage',
          auditStatus: this.lastsearchData.auditStatus,
          securityLevel: this.lastsearchData.securityLevel,
          sname: this.lastsearchData.sname,
          isShow: this.lastsearchData.isShow,
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.current,
        }
        this.tableLoading = true;
        getTProductSolutionsList(params).then((res) => {
            if (res.data.code === 0) {
                this.tableData = [];
                this.tableData = res.data.rows;
                this.pagination.total = res.data.total;
            } else {
              message.error(res.data.msg);
            }
            this.tableLoading = false;
        }).catch(error => {
          message.error(error.msg);
            this.tableLoading = false;
        });
    },
    handleView(record) {
      this.$refs.programmeView.open(record.id);
    },
    handleEdit(record) {
      if (record.auditStatus === 1) {
        message.error("正在审核中，请等待审核完成再进行编辑操作！");
        return;
      }
      this.$refs.programmeEdit.open(record.id);
    },
    handleAdd() {
      this.$refs.programmeEdit.open();
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {
        title: "",
        status: null,
        operTime: [],
      };
      this.searchData = {
        title: "",
        status: null,
        operTime: [],
      };
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    handleSubmit(value) {
      this.$refs.submitModal.edit(value);
    },
    handleDelete(value) {
      if (value.auditStatus === 1) {
        message.error("正在审核中，请等待审核完成再进行编辑操作！");
        return;
      }
      Modal.confirm({
        title: "确定要删除选中内容吗?",
        okText: "确定",
        okType: "primary",
        cancelText: "取消",
        onOk: () => {
          delTProductSolutions({ids: value.id}).then((res) => {
            if (res.data.code === 0) {
              message.success('删除成功');
              this.handleSearch();
            } else {
              message.error(res.data.msg);
            }
          }).catch(error => {
            message.error(error.msg);
          });
        },
        onCancel() {},
      });
    },
    updowncircle(row) {
      const rowNew = {
        id: row.id,
        isShow: row.isShow,
        auditStatus: row.auditStatus
      }
      rowNew.isShow = rowNew.isShow !== 0 ? 0 : 1;
      saveTProductSolutions(rowNew).then((res) => {
        if (res.data.code === 0) {
          message.success("操作成功");
          this.handleSearch();
        } else {
          message.error(res.data.msg);
        }
      });
    },
    onOk() {
      this.handleSearch(true);
    }
  },
};
</script>

<style scoped lang="less">
.form-color {
  //已办样式
  .ant-input[disabled] {
    color: black;
    background-color: white;
    border: none;
    cursor: default;
  }

  :deep(
      .ant-select-disabled.ant-select:not(.ant-select-customize-input)
        .ant-select-selector
    ) {
    color: black;
    background: white;
    cursor: default;
  }

  :deep(.ant-select-selector, .ant-select-selector:focus) {
    border: none;
    box-shadow: none;
    background-color: transparent;
  }

  :deep(.ant-select-selector .ant-select-selection-search) {
    padding-right: 0;
  }

  :deep(.ant-select-arrow) {
    display: none;
  }

  :deep(
      .ant-form-item-label
        > label.ant-form-item-required:not(
          .ant-form-item-required-mark-optional
        )::before
    ) {
    color: white;
  }
}

.disabled-btn {
  color: #c3c0c0 !important;
}
</style>
