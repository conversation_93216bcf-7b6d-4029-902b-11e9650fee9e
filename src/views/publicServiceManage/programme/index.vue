<template>
  <div class="page">
    <!-- 搜索条件栏 -->
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">所属行业：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            allowClear
            v-model:value="searchData.industryId"
            :getPopupContainer="(node) => node.parentNode || document.body"
            placeholder="请选择所属行业"
            style="width: 100%"
          >
            <a-select-option v-for="item in prodIndustryList" :key="item.id">{{
              item.name
            }}</a-select-option>
          </a-select>
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">安全能力：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            :disabled="searchData.industryId ? false : true"
            allowClear
            v-model:value="searchData.productSpuId"
            :getPopupContainer="(node) => node.parentNode || document.body"
            placeholder="请选择安全能力"
            style="width: 100%"
          >
            <a-select-option v-for="item in prodSpuList" :key="item.id">{{
              item.name
            }}</a-select-option>
          </a-select>
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">方案级别：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            allowClear
            v-model:value="searchData.securityLevel"
            :getPopupContainer="(node) => node.parentNode || document.body"
            placeholder="请选择方案级别"
            style="width: 100%"
          >
            <a-select-option
              v-for="item in securityLevelArray"
              :key="item.dictValue"
              >{{ item.dictLabel }}</a-select-option
            >
          </a-select>
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">是否精选：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            allowClear
            v-model:value="searchData.isBoutique"
            :getPopupContainer="(node) => node.parentNode || document.body"
            placeholder="请选择精选状态"
            style="width: 100%"
          >
            <a-select-option :value="1">是</a-select-option>
            <a-select-option :value="0">否</a-select-option>
          </a-select>
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">是否收藏：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            allowClear
            v-model:value="searchData.collectFlag"
            :getPopupContainer="(node) => node.parentNode || document.body"
            placeholder="请选择收藏状态"
            style="width: 100%"
          >
            <a-select-option :value="1">是</a-select-option>
            <a-select-option :value="0">否</a-select-option>
          </a-select>
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 22px">
          重置
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <a-row>
        <a-col :span="24">
          <a-table
            style="padding-top: 10px; padding-left: 10px; padding-right: 10px"
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
            <template #num="{ index }">
              <span>{{
                (pagination.current - 1) * pagination.pageSize +
                Number(index) +
                1
              }}</span>
            </template>
            <!-- <template #thumb="{ record }">
              <a-image :width="60" :src="getPath(record.thumb)" />
            </template> -->
            <template #isBoutique="{ text }">
              {{ text ? "是" : "否" }}
            </template>
            <template #securityLevel="{ text }">
              <span v-if="text === '0'">企业商密</span>
              <span v-else-if="text === '1'">内部商密</span>
              <!-- <span v-else-if="text === '2'">三星</span>
              <span v-else-if="text === '3'">四星</span> -->
            </template>
            <template #likeFlag="{ text, record }">
              <span class="like" :class="text === 0 || !text ? '' : 'is-actived'" @click="clickLike(record)">
                {{ record.likeNum || 0 }}
              </span>
            </template>
            <template #collectFlag="{ text, record }">
              <span class="collect" :class="text === 0 || !text ? '' : 'is-actived'" @click="clickCollect(record)">
                {{ record.collectNum || 0 }}
              </span>
            </template>
            <template #mark="{ record }">
              <span v-if="record.securityLevel !== '0'">
                <a-button
                  type="link"
                  style="color: #387ff1"
                  @click="apply(record.id)"
                  v-if="record.applyStatus === 0"
                >
                  授权申请
                </a-button>
                <a-button
                  type="link"
                  :disabled="true"
                  v-if="record.applyStatus === 1"
                >
                  授权申请中
                </a-button>
                <a-button
                  type="link"
                  :disabled="true"

                  v-if="record.applyStatus === 2"
                >
                  已授权
                </a-button>
                <a-button
                  type="link"
                  style="color: #fc5532;"
                  v-if="record.applyStatus === 3"
                  @click="apply(record.id)"
                >
                  <!-- <a-popover title="审核意见" placement="top">
                    <template #content>
                      <p style="max-width: 100%; height: auto; white-space: pre-wrap; word-break: break-all;">
                        {{ record.auditAdvice || '暂无驳回意见' }}
                      </p>
                    </template>
                    <exclamation-circle-outlined style="color: #fc5532;"/>
                  </a-popover> -->
                  授权失败
                </a-button>
              </span>
              <span>
                <a-button
                  type="link"
                  style="color: #387ff1"
                  @click="handleDetail(record)"
                  v-if="record.securityLevel === '0' || (record.securityLevel !== '0' && record.applyStatus === 2)"
                >
                  查看
                </a-button>
                <a-button
                  type="link"
                  :disabled="true"
                  v-else
                >
                  查看
                </a-button>
              </span>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script>
import {
  ExclamationCircleOutlined,
  MailOutlined,
  ContainerOutlined,
  StarFilled,
  StarOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";
import { createVNode } from "vue";
import { Modal, message } from "ant-design-vue";
import dayjs from "dayjs";
import { getTProductSolutionsList } from "@/request/publicServiceManage/TProductSolutions";
import { getTProdIndustryTree } from "@/request/publicServiceManage/tProdIndustry.js";
import { getTProdSpuDict } from "@/request/publicServiceManage/tProdSpu.js";
import { getDictArray } from "@/utils/dict";
import { saveHandleLike, removeHandleLike, saveHandleCollect, removeHandleCollect } from '@/request/publicServiceManage/tHandleLike'
import { saveTPsEmpowerApply } from '@/request/publicServiceManage/TPsEmpowerApply'
import { saveTUserViewLog } from '@/request/publicServiceManage/TUserViewLog'

export default {
  components: {
    MailOutlined,
    ContainerOutlined,
    StarFilled,
    StarOutlined,
    SearchOutlined,
    ExclamationCircleOutlined
  },
  data() {
    return {
      prodIndustryList: [],
      prodSpuList: [],
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      newVisible: false,
      tableLoading: false,
      formData: {
        title: "",
        content: "",
        createTime: "",
        isRead: "",
        mark: "",
      },
      searchData: {},
      lastsearchData: {
      },
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        // {
        //   title: "封面",
        //   dataIndex: "thumb",
        //   slots: {
        //     customRender: "thumb",
        //   },
        //   width: "10%",
        //   align: "center",
        // },
        {
          title: "标题",
          dataIndex: "sname",
          align: "center",
          ellipsis: true,
          slots: {
            customRender: "sname",
          },
        },
        {
          title: "所属行业",
          dataIndex: "industryName",
          align: "center",
        },
        {
          title: "安全能力",
          dataIndex: "productSpuName",
          align: "center",
          slots: {
            customRender: "productSpuName",
          },
        },
        {
          title: "方案级别",
          dataIndex: "securityLevel",
          align: "center",
          slots: {
            customRender: "securityLevel",
          },
        },
        {
          title: "是否精选",
          dataIndex: "isBoutique",
          align: "center",
          slots: {
            customRender: "isBoutique",
          },
        },
        {
          title: "浏览量",
          dataIndex: "viewNum",
          align: "center",
          slots: {
            customRender: "viewNum",
          },
        },
        {
          title: "点赞",
          dataIndex: "likeFlag",
          align: "center",
          slots: {
            customRender: "likeFlag",
          },
        },
        {
          title: "收藏",
          dataIndex: "collectFlag",
          align: "center",
          slots: {
            customRender: "collectFlag",
          },
        },
        {
          title: "操作",
          dataIndex: "mark",
          width: "12%",
          align: "center",
          slots: {
            customRender: "mark",
          },
        },
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
      securityLevelArray: [],
      securityLevelMap: {},
      currentLike: false,
      currentCollect: false
    };
  },
  computed: {
    rowSelection() {
      const { selectedRowKeys } = this;
      return {
        selectedRowKeys,
        onChange: (selectedRowKeys, selectedRows) => {
          this.selectedRowKeys = selectedRowKeys;
        },
        getCheckboxProps: (record) => ({
          disabled: record.isRead, // 根据isRead字段判断是否禁用该行的复选框
        }),
      };
    },
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
    currentIndustryName() {
      return this.searchData.industryId;
    },
  },
  watch: {
    currentIndustryName(newVal) {
      this.filterBox1Click(newVal);
    },
  },
  created() {
    getDictArray("security_level").then((data) => {
      this.securityLevelArray.push(...data);
      const securityLevelMap = {};
      this.securityLevelArray.forEach((d) => {
        securityLevelMap[d.dictValue] = { text: d.dictLabel };
      });
      this.securityLevelMap = securityLevelMap;
      console.log(this.securityLevelMap);
    });
  },
  mounted() {
    this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    setTimeout(() => {
      this.flexHeight = `calc(100vh - ${this.searchHeight}px - 140px)`;
      this.tableHeight =
        (window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight) -
        336 -
        this.searchHeight;
    }, 100);
    this.initIndustry();
  },
  methods: {
    handleClose() {
      this.newVisible = false;
    },
    getPath: function (filePath) {
      return process.env.VUE_APP_File_URL + filePath;
    },
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    showImg(event) {},
    handleSearch(flag) {
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
      const params = {
        isShow: 1,
        ...this.lastsearchData,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
      };
      this.tableLoading = true;
      getTProductSolutionsList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            this.tableData = res.data.rows;
            this.pagination.total = res.data.total;
          } else {
            message.error(res.data.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
    },
    async initIndustry() {
      const res = await getTProdIndustryTree();
      if (res.data.code === 0) {
        this.prodIndustryList.push(...res.data.rows);
        this.initProdSpuList(null);
      }
    },
    async initProdSpuList(industryId) {
      let params;
      if (industryId) {
        params = { prodIndustryId: industryId };
      } else {
        params = {};
      }
      const res = await getTProdSpuDict(params);
      if (res.data.code === 0) {
        this.prodSpuList.push(...res.data.rows);
      }
    },
    async filterBox1Click(item) {
      this.prodSpuList = [];
      this.searchData.industryId = item;
      this.searchData.productSpuId = null;
      await this.initProdSpuList(item);
    },
    handleDetail(record) {
      const routeData = this.$router.resolve({
        path: `/publicServiceManage/programmeDetail/${record.id}`,
        query: {},
      });
      window.open(routeData.href, "_blank");
      this.saveView(record)
      //查看
    },
    saveView(item) {
      const param = { tableId: item.id, type: 1 }
      saveTUserViewLog(param).then(res => {
        console.log(res)
      })
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {
      };
      this.searchData = {
      };
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    async clickLike(item) {
      if (this.currentLike) {
        return
      }
      this.currentLike = true
      const param = { type: 2, tableId: item.id }
      if (item.likeFlag === 0 || !item.likeFlag) {
        await saveHandleLike(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('点赞成功')
            } else {
              message.error('系统错误，请稍后再试')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentLike = false
          })
      } else {
        await removeHandleLike(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('取消点赞成功')
            } else {
              message.error('系统错误，请稍后再试')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentLike = false
          })
      }
      this.handleSearch();
    },
    async clickCollect(item) {
      if (this.currentCollect) {
        return
      }
      this.currentCollect = true
      const param = { type: 2, tableId: item.id }
      if (item.collectFlag === 0 || !item.collectFlag) {
        await saveHandleCollect(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('收藏成功')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentCollect = false
          })
      } else {
        await removeHandleCollect(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('取消收藏成功')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentCollect = false
          })
        }
      this.handleSearch();
    },
    async apply(id) {
      const res = await saveTPsEmpowerApply({ id: 0, productSolutionId: id })
      if (res.data.code === 0) {
        message.success('授权申请已提交')
        this.handleSearch();
      } else {
        message.error('授权申请失败')
      }
    },
  },
};
</script>

<style scoped lang="less">
.form-color {
  //已办样式
  .ant-input[disabled] {
    color: black;
    background-color: white;
    border: none;
    cursor: default;
  }

  :deep(
      .ant-select-disabled.ant-select:not(.ant-select-customize-input)
        .ant-select-selector
    ) {
    color: black;
    background: white;
    cursor: default;
  }

  :deep(.ant-select-selector, .ant-select-selector:focus) {
    border: none;
    box-shadow: none;
    background-color: transparent;
  }

  :deep(.ant-select-selector .ant-select-selection-search) {
    padding-right: 0;
  }

  :deep(.ant-select-arrow) {
    display: none;
  }

  :deep(
      .ant-form-item-label
        > label.ant-form-item-required:not(
          .ant-form-item-required-mark-optional
        )::before
    ) {
    color: white;
  }
}

.collect,.like {
  display: inline-block;
  width: fit-content;
  padding-left: 25px;
  cursor: pointer;
}
.collect {
  background: url('@/assets/portalClient/nocollect.png') no-repeat;
  background-size: 20px 20px;
  &.is-actived {
    background: url('@/assets/portalClient/collect.png') no-repeat;
    background-size: 20px 20px;
  }
}

.like {
  background: url('@/assets/portalClient/nolike.png') no-repeat;
  background-size: 20px 20px;
  &.is-actived {
    background: url('@/assets/portalClient/like.png') no-repeat;
    background-size: 20px 20px;
  }
}
</style>
