<template>
  <Header style="position: fixed;top: 0;left: 0;z-index: 999;"></Header>
  <div id="programmeDetail">
    <!-- <div class="banner">
      <div class="mask"></div>
      <img :src="getPath(topData.thumb)" alt="" />
      <div class="banner-content">
        <p>{{ topData.sname }}</p>
        <p class="desc1" :title="topData.solutionIntroduce">
          {{ topData.solutionIntroduce }}
        </p>
      </div>
    </div> -->
    <div class="programmeDetailBody">
      <div class="item1">
        <div class="item1-content">
          <div class="left">
            <!-- <img
              src="../../../assets/portalClient/programmeDetail/cover.png"
              alt=""
            /> -->
            <img :src="getPath(topData.thumb)" alt="" />
            <!-- <div
              class="play"
              @click="previewFile(resources[0].resources)"
            ></div> -->
          </div>
          <div class="right">
            <div class="name">
              <p>{{ topData.sname }}</p>
              <div class="tag">
                <span v-if="topData.isBoutique === '1'">精选</span>
                <span
                  class="lv"
                  v-if="topData.securityLevel && topData.securityLevel !== '0'"
                  >{{ securityLevelFilter(topData.securityLevel, securityLevelMap) }}</span
                >
              </div>
            </div>
            <p class="desc" :title="topData.solutionIntroduce">
              {{ topData.solutionIntroduce }}
            </p>
            <div class="preview">
              预览:
              <span
                v-for="(item, i) in resources"
                :key="i"
                @click="previewFile(item.resources)"
              >
                <img
                  v-if="item.resources.ftype === 1"
                  src="../../../assets/portalClient/programmeDetail/video.png"
                  alt=""
                />
                <img
                  v-if="item.resources.ftype === 2"
                  src="../../../assets/portalClient/programmeDetail/pdf.png"
                  alt=""
                />
                <img
                  v-if="item.resources.ftype === 3"
                  src="../../../assets/portalClient/programmeDetail/excel.png"
                  alt=""
                />
                <img
                  v-if="item.resources.ftype === 4"
                  src="../../../assets/portalClient/programmeDetail/word.png"
                  alt=""
                />
              </span>
            </div>

            <div style="cursor: pointer;font-size: 24px;text-align: right;" @click="clickCollect()">
              <star-filled style="color: #fff;" v-if="topData.collectFlag"/>
              <star-outlined style="color: #fff;" v-else/>
            </div>
          </div>
        </div>
      </div>
      <div class="item2" v-if="topData.industryDemandForm && topData.industryDemandForm.length > 4">
        <div class="item2-content">
          <div class="title">行业需求</div>
          <div style="width: 1200px; overflow: hidden" class="swiper">
            <div class="left" @click="leftClick"></div>
            <div class="right" @click="rightClick"></div>
            <div class="item2-body" ref="item2Body">
              <div
                class="item"
                v-for="item in topData.industryDemandForm"
                :key="item"
              >
                <img :src="iconList[item.thumb]" alt="" />
                <p class="name">{{ item.title }}</p>
                <p class="desc" :title="item.context">{{ item.context }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="item2" v-else>
        <div class="item2-content">
          <div class="title">行业需求</div>
          <div
            style="
              width: 1200px;
              overflow: hidden;
              display: flex;
              justify-content: center;
            "
            class="swiper"
          >
            <div class="item2-body">
              <div
                class="item"
                v-for="item in topData.industryDemandForm"
                :key="item"
              >
                <img :src="iconList[item.thumb]" alt="" />
                <p class="name">{{ item.title }}</p>
                <p class="desc" :title="item.context">{{ item.context }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="item3">
        <div class="item3-content">
          <div class="title">解决方案</div>
          <div class="item3-1-body">
            <div
              ref="itemBodyItem"
              class="item3-1-body-item"
              :class="`item${i}`"
              v-for="(item, i) in topData.solutionForm"
              :key="item"
            >
              <div class="mask"></div>
              <div class="content">
                <div class="num">{{ (i + 1).toString().padStart(2, "0") }}</div>
                <p class="name">{{ item.title }}</p>
                <p class="desc" :title="item.context">{{ item.context }}</p>
              </div>
            </div>
          </div>
          <div class="title">典型客户</div>
          <div class="item3-2-body">
            <div
              class="item3-2-body-item"
              v-for="item in topData.customersForm"
              :key="item"
            >
              <p>{{ item.title }}</p>
              <p>{{ item.context }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
    <videoDom v-if="showVideo" :url="videoUrl" @close="videoClose"></videoDom>
  </div>
</template>

<script>
import { getTree } from "@/request/publicServiceManage/TSolutionChapter";
import { getTProductSolutionsItem } from "@/request/publicServiceManage/TProductSolutions";
// import { mixinDevice } from "@/utils/mixin";
import { getDictArray } from "@/utils/dict";
import { mapGetters } from "vuex";
import videoDom from "@/components/filesPreview/videoDom";
import Header from "@/components/Header";
import {
  StarOutlined,
  StarFilled
} from "@ant-design/icons-vue";
import { message } from "ant-design-vue";
import { saveHandleCollect, removeHandleCollect } from '@/request/publicServiceManage/tHandleLike'

export default {
  name: "programmeDetail",
  components: {
    videoDom,
    Header,
    StarOutlined,
    StarFilled
  },
  // mixins: [mixinDevice],
  data() {
    return {
      currentCollect: false,
      num: 0,
      activeKey: ["1"],
      videoUrl: "",
      showVideo: false,
      data: [],
      topData: {},
      currentIndex: 0,
      cardGridDomWidth: 0,
      fileList: {
        file1: require("../../../assets/portalClient/programmeDetail/file1.png"),
        file2: require("../../../assets/portalClient/programmeDetail/file2.png"),
        file3: require("../../../assets/portalClient/programmeDetail/file3.png"),
        file4: require("../../../assets/portalClient/programmeDetail/file4.png"),
      },
      iconList: [
        require("@/assets/portalClient/icon/icon1.png"),
        require("@/assets/portalClient/icon/icon2.png"),
        require("@/assets/portalClient/icon/icon3.png"),
        require("@/assets/portalClient/icon/icon4.png"),
        require("@/assets/portalClient/icon/icon5.png"),
        require("@/assets/portalClient/icon/icon6.png"),
        require("@/assets/portalClient/icon/icon7.png"),
        require("@/assets/portalClient/icon/icon8.png"),
        require("@/assets/portalClient/icon/icon9.png"),
        require("@/assets/portalClient/icon/icon10.png"),
        require("@/assets/portalClient/icon/icon11.png"),
        require("@/assets/portalClient/icon/icon12.png"),
        require("@/assets/portalClient/icon/icon13.png"),
        require("@/assets/portalClient/icon/icon14.png"),
        require("@/assets/portalClient/icon/icon15.png"),
        require("@/assets/portalClient/icon/icon16.png"),
        require("@/assets/portalClient/icon/icon17.png"),
      ],
      resources: [],
      securityLevelArray: [],
      securityLevelMap: {}
    };
  },
  // filters: {
    // securityLevelFilter(securityLevel, securityLevelMap) {
    //   return (
    //     securityLevelMap[securityLevel] && securityLevelMap[securityLevel].text
    //   );
    // },
  // },
  computed: {
    ...mapGetters(['getUserId'])
  },
  created() {
    getDictArray("security_level").then((data) => {
      this.securityLevelArray = data;
      const securityLevelMap = {};
      this.securityLevelArray.forEach((d) => {
        securityLevelMap[d.dictValue] = { text: d.dictLabel };
      });
      this.securityLevelMap = securityLevelMap;
      console.log(this.securityLevelMap, "45454545");
    });
    this.init();
    // await this.initList()
  },
  mounted() {
    setTimeout(() => {
      document
        .getElementsByClassName("item3-1-body-item")[0]
        ?.classList.add("isHover");
      document
        .getElementsByClassName("item3-1-body")[0]
        .addEventListener("mouseover", (e) => {
          const itemList = document.getElementsByClassName("item3-1-body-item");
          Array.from(itemList).forEach((item) => {
            if (e.target === item || item.contains(e.target)) {
              item.classList.add("isHover");
            } else {
              item.classList.remove("isHover");
            }
          });
        });
    }, 500);
  },
  methods: {
    throttled(fn, delay) {
      let oldtime = Date.now();
      return function (...args) {
        const newtime = Date.now();
        if (newtime - oldtime >= delay) {
          fn.apply(null, args);
          oldtime = Date.now();
        }
      };
    },
    getPath: function (filePath) {
      return process.env.VUE_APP_File_URL + filePath;
    },
    // openItem(value) {
    //   const itemList = document.getElementsByClassName('item3-1-body-item')
    //   Array.from(itemList).forEach((item, i) => {
    //     item.classList.remove('isHover')
    //     if (i === value) {
    //       item.classList.add('isHover')
    //     }
    //   })
    // },
    async init() {
      const res = await getTProductSolutionsItem(this.$route.params.id)
      if (res.data.code === 0) {
        this.topData = res.data.data
        // this.splitAttributes(res.data.data.attributes)
        // this.resources = res.data.data.chapters[0].children
      }
      this.splitAttributes(this.topData.attributes);
      this.resources = this.topData.chapters[0].children;
    },
    splitAttributes(value) {
      this.topData.industryDemandForm = [];
      this.topData.solutionForm = [];
      this.topData.customersForm = [];
      value.concat([]).filter((item, i) => {
        if (item.type === 1) {
          this.topData.industryDemandForm.push(item);
        } else if (item.type === 2) {
          this.topData.solutionForm.push(item);
        } else {
          this.topData.customersForm.push(item);
        }
      });
      this.topData.attributes = [];
    },
    securityLevelFilter(securityLevel, securityLevelMap) {
      return securityLevelMap[securityLevel] && securityLevelMap[securityLevel].text;
    },
    leftClick() {
      if (this.num === 0) {
        return;
      }
      this.num -= 1;
      this.$refs.item2Body.style.transform = `translateX(${this.num * -296}px)`;
    },
    rightClick() {
      if (this.topData.industryDemandForm.length - this.num === 4) {
        return;
      }
      this.num += 1;
      this.$refs.item2Body.style.transform = `translateX(${this.num * -296}px)`;
    },
    previewFile(value) {
      if (this.getUserId) {
        if (value.ftype === 1) {
          this.videoUrl = value.url;
          this.showVideo = true;
          return;
        }
        let typeName = "";
        switch (value.ftype) {
          case 2:
            typeName = "previewPdf";
            break;
          case 3:
            typeName = "previewExcel";
            break;
          case 4:
            typeName = "previewWord";
            break;
        }
        const routeData = this.$router.resolve({
          path: "/preview",
          query: {
            type: typeName,
            id: value.id,
          },
        });
        window.open(routeData.href, "_blank");
      } else {
        const routeData = this.$router.resolve({
          path: "/login",
        });
        window.open(routeData.href, "_blank");
      }
    },
    videoClose() {
      this.showVideo = false;
    },
    async clickCollect() {
      if (this.currentCollect) {
        return
      }
      this.currentCollect = true
      const param = { type: 2, tableId: this.topData.id }
      if (this.topData.collectFlag === 0 || !this.topData.collectFlag) {
        await saveHandleCollect(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('收藏成功')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentCollect = false
          })
      } else {
        await removeHandleCollect(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('取消收藏成功')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentCollect = false
          })
        }
        this.init();
    }
  },
};
</script>

<style lang="less" scoped>
#programmeDetail {
  background-color: #f3f5f9;
  min-width: 1200px;
  padding: 20px;
  padding-top: 64px;
  .banner {
    position: relative;
    width: 100%;
    height: 420px;
    .mask {
      position: absolute;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      background: linear-gradient(to right, #0040c4 20%, #0041c421 100%);
      z-index: 1;
    }
    img {
      width: 80%;
      height: 100%;
      object-fit: cover;
      float: right;
    }
    .banner-content {
      position: absolute;
      inset: 0;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: flex-start;
      width: 1200px;
      margin: auto;
      z-index: 2;
      p {
        margin: 0;
      }

      p:first-child {
        font-size: 48px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        margin-bottom: 5px;
      }
      p:last-child {
        width: 500px;
        height: auto;
        font-size: 18px;
        font-family: PingFangSC-Regular, PingFang SC;
        font-weight: 400;
        color: #ffffff;
        word-wrap: break-word;
      }
    }
  }
  .programmeDetailBody {
    width: 100%;
    .title {
      width: 100%;
      text-align: center;
      font-size: 36px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: #333333;
      margin: 0 0 36px;
      padding-top: 57px;
    }
    .item1 {
      width: 100%;
      height: 490px;
      background: #ffffff;
      .item1-content {
        position: relative;
        width: 1200px;
        height: 100%;
        margin: 0 auto;
        .left {
          position: absolute;
          left: 0px;
          top: 60px;
          width: 685px;
          height: 370px;
          z-index: 1;
          img {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
          .play {
            position: absolute;
            inset: 0;
            margin: auto;
            width: 90px;
            height: 90px;
            background: url("../../../assets/portalClient/programmeDetail/play.png");
            background-size: cover;
            cursor: pointer;
            transition: all 0.5s;
            &:hover {
              filter: contrast(0.5);
            }
          }
        }
        .right {
          position: absolute;
          top: 122px;
          right: 0;
          width: 543px;
          height: 246px;
          background: url("../../../assets/portalClient/programmeDetail/item1-right.png")
            no-repeat;
          background-size: 100% 100%;
          padding: 50px 40px 50px 70px;
          .tag {
            width: fit-content;
            white-space: nowrap;
            span {
              display: inline-block;
              width: fit-content;
              height: 26px;
              line-height: 26px;
              background: linear-gradient(270deg, #ff6965 0%, #f33e3e 100%);
              border-radius: 4px;
              margin-right: 8px;
              padding: 0 10px;
              color: #fff;
              &.lv {
                background: linear-gradient(270deg, #24e3ff 0%, #0092ff 100%);
              }
            }
          }
          p {
            margin: 0;
          }

          .name {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
            max-width: 415px;
            p {
              font-size: 20px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #ffffff;
              margin-right: 5px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
          .desc {
            height: 65px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #ffffff;
            margin-bottom: 20px;
            word-break: break-all;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
          }
          .preview {
            width: 100%;
            font-size: 14px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #ffffff;
            img {
              width: 23px;
              height: 22px;
              object-fit: contain;
              margin-left: 20px;
              cursor: pointer;
              transition: all 0.3s;
              &:hover {
                filter: brightness(1.2);
              }
            }
          }
        }
      }
    }
    .item2 {
      width: 100%;
      background: #f3f5f9;
      .item2-content {
        height: 532px;
        width: 1200px;
        margin: 0 auto;
        .swiper {
          position: relative;
          .left,
          .right {
            position: absolute;
            width: 100px;
            height: 100px;
            opacity: 0.8;
            z-index: 3;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s;
            background-color: #cccccc;
            &:hover {
              background-color: #1f60e7;
            }
            &:after {
              position: absolute;
              top: 0;
              bottom: 0;
              margin: auto;
              content: "";
              display: inline-block;
              width: 20px;
              height: 20px;
              border: 2px solid #fff;
            }
          }
          .left {
            top: 0;
            bottom: 0;
            left: -50px;
            margin: auto;
            &:after {
              right: 13px;
              transform: rotate(-135deg);
              border-left: none;
              border-bottom: none;
            }
          }
          .right {
            top: 0;
            bottom: 0;
            right: -50px;
            margin: auto;
            &:after {
              left: 13px;
              transform: rotate(45deg);
              border-left: none;
              border-bottom: none;
            }
          }
        }
        .item2-body {
          display: flex;
          justify-content: space-between;
          align-items: center;
          width: fit-content;
          transition: all 0.5s;
          .item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            width: 282px;
            height: 330px;
            margin: 0 8px;
            background: #fff;
            padding: 0 28px 20px;
            cursor: pointer;
            img {
              width: 160px;
              height: 150px;
            }
            p {
              margin: 0;
            }
            .name {
              width: 100%;
              font-size: 24px;
              font-family: PingFangSC-Medium, PingFang SC;
              font-weight: 500;
              color: #333;
              margin-bottom: 10px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
              text-align: center;
            }
            .desc {
              height: 96px;
              font-size: 16px;
              font-family: PingFangSC-Regular, PingFang SC;
              font-weight: 400;
              color: #666;
              word-break: break-all;
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 4;
              -webkit-box-orient: vertical;
            }
            &:hover {
              background: url("../../../assets/portalClient/programmeDetail/item2-itembg.png")
                no-repeat;
              background-size: cover;
              .name {
                color: #ffffff;
              }
              .desc {
                color: #ffffff;
              }
            }
          }
        }
      }
    }
    .item3 {
      width: 100%;
      background: #fff;
      .item3-content {
        height: fit-content;
        width: 1200px;
        margin: 0 auto;
        .item3-1-body {
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 392px;
          width: 100%;
          .item3-1-body-item {
            position: relative;
            // width: 205px;
            flex: 1;
            height: 100%;
            cursor: pointer;
            transition: flex 0.5s ease-in-out;
            .mask {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              padding: 40px;
              background: linear-gradient(
                135deg,
                #0054ff 0%,
                rgba(0, 84, 255, 0.1) 100%
              );
              opacity: 0;
              transition: all 0.5s;
              z-index: 1;
            }
            .content {
              position: absolute;
              left: 0;
              top: 0;
              width: 100%;
              height: 100%;
              padding: 40px;
              z-index: 2;
              .num {
                font-size: 48px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #ffffff;
              }
              p {
                max-width: 400px;
                margin: 0;
              }
              .name {
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                font-size: 24px;
                font-family: PingFangSC-Medium, PingFang SC;
                font-weight: 500;
                color: #ffffff;
                margin-bottom: 10px;
              }
              .desc {
                opacity: 0;
                max-height: 190px;
                font-size: 14px;
                font-family: PingFangSC-Regular, PingFang SC;
                font-weight: 400;
                color: #ffffff;
                word-break: break-all;
                overflow: hidden;
                display: -webkit-box;
                -webkit-line-clamp: 9;
                -webkit-box-orient: vertical;
                transition: opacity 0.3s 0.1s ease;
              }
            }
          }
          .item0 {
            background: url("../../../assets/portalClient/programmeDetail/item3-1-1.png")
              no-repeat;
            background-size: cover;
          }
          .item1 {
            background: url("../../../assets/portalClient/programmeDetail/item3-1-2.png")
              no-repeat;
            background-size: cover;
          }
          .item2 {
            background: url("../../../assets/portalClient/programmeDetail/item3-1-3.png")
              no-repeat;
            background-size: cover;
          }
          .isHover {
            flex: 3.85;
            background-size: cover;
            .mask {
              opacity: 1;
            }
            .content {
              .desc {
                opacity: 1;
              }
            }
          }
        }
        .item3-2-body {
          width: 1200px;
          margin: 0 auto;
          display: flex;
          flex-wrap: wrap;
          justify-content: flex-start;
          align-items: center;
          .item3-2-body-item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            width: 380px;
            height: 114px;
            background: url("../../../assets/portalClient/programmeDetail/item3-1-2-itembg.png")
              no-repeat;
            background-size: cover;
            font-size: 20px;
            font-family: PingFangSC-Medium, PingFang SC;
            font-weight: 500;
            color: #333333;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            margin-bottom: 30px;
            p {
              width: 100%;
              text-align: center;
              margin: 0;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
            p:first-child {
              font-size: 18px;
              font-weight: 600;
              margin-bottom: 5px;
            }
            &:not(:nth-child(3n)) {
              margin-right: 30px;
            }
          }
        }
      }
    }
  }
  .desc1 {
    word-break: break-all;
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 4;
    -webkit-box-orient: vertical;
  }
  .programmeDetailContent {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    position: relative;
    top: -55px;
    width: 1200px;
    margin: auto;
    min-height: 450px;
    height: auto;
    background: #ffffff;
    box-shadow: 0px 1px 8px 0px rgba(1, 43, 121, 0.08);
    border-radius: 4px;
    padding: 30px;
    .noContent {
      margin: auto;
      padding: 20px 0 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        width: 240px;
      }
      span {
        font-size: 16px;
      }
    }
    .detailCollapse-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 50px;
      width: 100%;
      &:not(:last-child) {
        border-bottom: 1px solid #e2e2e2;
      }
      .left {
        .title {
          max-width: 660px;
          font-size: 18px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #292e3d;
          margin-bottom: 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .fileName {
          display: flex;
          align-items: center;
          img {
            width: 18px;
          }
          span {
            margin-left: 5px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
          }
        }
      }
      .right {
        .showBtn {
          display: flex;
          align-items: center;
          padding: 0 20px;
          width: auto;
          height: 36px;
          border-radius: 18px;
          border: 1px solid #2468f3;
          cursor: pointer;
          transition: all 0.3s;
          &:hover {
            background: #2468f3;
            color: #fff;
          }
        }
      }
    }
  }
}
</style>
