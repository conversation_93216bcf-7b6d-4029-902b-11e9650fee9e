<template>
  <a-modal
    title="资源上传"
    style="top: 20px"
    :width="450"
    v-if="visible"
    v-model:visible="visible"
    :keyboard="false"
    :maskClosable="false"
    :destroyOnClose="true"
    :closable="false"
    :confirmLoading="confirmLoading"
  >
    <template #footer>
      <a-button class="btn" @click="closeUploadModal()"> 取 消 </a-button>
      <a-button class="btn" @click="submit"> 确 定 </a-button>
    </template>
    <div>
      <a-form ref="resources" :model="resources" layout="horizontal">
        <a-form-item
          :labelCol="labelCol"
          :wrapperCol="wrapperCol"
          label="上传资源"
          name="url"
          :rules="[{ required: true, message: '请上传正确的附件' }]"
        >
          <a-input
            placeholder="资源名称"
            v-model:value="resources.url"
            style="display: none"
            :disabled="disabled"
          />
          <a-form-item-rest>
            <a-upload
              name="file"
              :accept="fileType"
              :multiple="true"
              :beforeUpload="beforeUpload"
              :customRequest="customRequest"
              @change="uploadChange"
              :file-list="fileList"
            >
              <a-button :disabled="disabled" style="margin-bottom: 10px">
                <upload-outlined />
                点击上传
              </a-button>
            </a-upload>
          </a-form-item-rest>
        </a-form-item>
      </a-form>
      <a-alert message="支持文件类型 （docx、xlsx、pdf、mp4）" banner />
      <a-alert message="上传资源请勿超过500MB" banner />
      <!-- <a-upload-dragger
        name="file"
        :multiple="true"
        :customRequest="customRequest"
        @change="uploadChange"
        :accept="fileType"
        :beforeUpload="beforeUpload"
      >
        <p class="ant-upload-drag-icon">
          <a-icon type="inbox" />
        </p>
        <p class="ant-upload-text">请将视频拖拽到此处上传</p>
        <p class="ant-upload-hint">支持一次上传多个 / 仅支持 mp4 格式视频以及 docx、xlsx、pdf 格式的文件</p>
      </a-upload-dragger> -->
    </div>
  </a-modal>
</template>
<script>
// import pick from 'lodash.pick'
// import { getArea } from '@/request/system.js'
import { uploadTResources } from "@/request/publicServiceManage/TResources";
import { message } from "ant-design-vue";
import { UploadOutlined } from "@ant-design/icons-vue";

export default {
  name: "trainEditUploadFileModel",
  props: {
    fTypeList: {
      type: Array,
      default: () => [],
    },
  },
  components: {
    UploadOutlined,
  },
  data() {
    return {
      confirmLoading: false,
      disabled: false,
      visible: false,
      fileType: ".docx,.xlsx,.pdf,.mp4",
      acceptType: ["xlsx", "docx", "pdf", "mp4"],
      uploadUrl: "",
      loading: false,
      uploadMap: new Map(),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      fileList: [],
      resources: {
        url: "",
      },
    };
  },
  beforeCreate() {},
  created() {},
  methods: {
    add() {
      this.visible = true;
      this.resources = {};
      this.fileList = [];
    },
    closeUploadModal() {
      if (this.uploadMap.size === 0) {
        this.visible = false;
      } else {
        message.warning("上传未完成,无法关闭当前窗口");
      }
    },
    uploadChange(info) {
      console.log(info, 123123)
      let fileList = [...info.fileList];
      // 上传状态处理
      if (info.file.status === 'error') {
        message.error(info.file.error.message);
        this.fileList = []
      } else if (info.file.status === 'uploading') {
        this.loading = true;
      } else if (info.file.status === 'done') {
        this.fileList = []
        // 只保留最后一个文件
        fileList = fileList.slice(-1);
        // message.success(info.file.response.message);
        fileList = fileList.map(file => {
          if (file.response) {
            // 上传完成后的处理
            message.success(file.response.message);
            file.url = file.response.url
          }
          return file;
        });
        this.$emit("ok");
      } else {
        this.resources = {};
        this.fileList = [];
      }

      this.fileList = fileList;
    },
    beforeUpload(file, fileList) {
      this.resetForm();
    },
    /**
     * 自定义上传请求
     * @param {*} param
     */
    async customRequest({ onSuccess, onError, file, onProgress }) {
      new Promise((resolve, reject) => {
        // 检查当前文件类型是否已上传,避免重复上传相同类型文件
        if (
          this.fTypeList.includes(
            file.name.split(".")[file.name.split(".").length - 1]
          )
        ) {
          console.log(file.name, 'file.name')
          reject("当前文件类型的附件已上传,请上传其他类型附件");
          return;
        }

        // 校验文件名是否为空
        if (!file.name && file.name === "") {
          reject("文件名不能为空!");
          return;
        }

        // 获取文件扩展名并校验文件类型
        const fix = file.name.split(".")[file.name.split(".").length - 1];
        const isAcceptType = this.typeMatch(fix);
        if (!isAcceptType) {
          reject("文件类型限制为 docx、xlsx、pdf、mp4");
          return;
        }

        // 校验文件大小是否超过500MB
        if (file.size > 500 * 1024 * 1024) {
          reject("上传资源请勿超过500MB");
          return;
        }
        resolve();
      })
        .then(async () => {
          const formData = new FormData();
          formData.append("files", file);
          formData.append("categoriesId", this.categoriesId);
          formData.append("deptId", "1");
          try {
            const res = await uploadTResources({
              formData: formData,
              onUploadProgress: (ev) => {
                // ev.loaded 当前已上传内容的大小，ev.total - 本次上传请求内容总大小
                console.log(ev);
                this.uploadMap.set(file.name, file.size);
                const percent = (ev.loaded / ev.total) * 100;
                console.log(percent);
                // 计算出上传进度，调用组件进度条方法
                onProgress({ percent });
              },
            });
            if (res.data.code === 0) {
              this.convertFileType(res.data.data);
              onSuccess({ code: 0, message: `${file.name}上传成功` }, file);
            } else {
              onError({ code: 500, message: `${file.name}上传失败` }, file);
            }
            this.uploadMap.delete(file.name);
          } catch (error) {
            this.uploadMap.delete(file.name);
            onError({ code: 500, message: `${file.name}上传失败` }, file);
          }
        })
        .catch((error) => {
          onError({ code: 500, message: error }, file);
        });
    },
    typeMatch(fileType) {
      if (this.acceptType.includes(fileType)) {
        return true;
      }
      return false;
    },
    convertFileType(value) {
      switch (value.ftype) {
        case 1:
          value.ftypeName = "mp4";
          break;
        case 2:
          value.ftypeName = "pdf";
          break;
        case 3:
          value.ftypeName = "xlsx";
          break;
        case 4:
          value.ftypeName = "docx";
          break;
      }
      this.resources = value;
      this.resources.url = value.url;
    },
    submit() {
      this.$refs.resources
        .validateFields()
        .then((value) => {
          this.confirmLoading = true;
          this.$emit("setFileType", this.resources.ftypeName);
          this.$emit("setNewChildren", this.resources);
          this.visible = false;
        })
        .catch(() => {
          console.log("error submit!!");
          return false;
        })
        .finally(() => {
          this.resources = { url: "" };
          this.confirmLoading = false;
        });
    },
    resetForm() {
      this.$refs.resources.resetFields();
    },
  },
};
</script>
<style lang="less" scoped>
.ant-select.ant-select-enabled {
  width: 100%;
  margin-bottom: 20px;
}

.btn {
  border-radius: 0;

  &:last-child {
    border: none;
    border-radius: 0;
    background: #246df3;
    color: #fff;

    &:hover {
      filter: brightness(1.2);
    }
  }
}
</style>
