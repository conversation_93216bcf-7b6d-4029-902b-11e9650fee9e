<template>
  <a-modal :maskClosable="false" v-model:visible="newVisible" :title="title" :width="1048" @cancel="handleClose"
    :confirmLoading="confirmLoading" @ok="handleSubmit" :destroyOnClose="true"
    :bodyStyle="{ height: '680px', overflowY: 'auto', overflowX: 'hidden' }">
    <div id="programmeEdit">
      <div class="programmeEditBody">
        <a-form ref="programmeForm" :model="programmeForm">
          <div class="item1">
            <div class="title">
              <span>01 基本信息</span>
            </div>
            <div class="item1-content">
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="所属行业" :name="'industryId'"
                    :rules="{ required: true, message: '请输入所属行业' }">
                    <a-select :getPopupContainer="(trigger) => trigger.parentNode" placeholder="所属行业"
                      v-model:value="programmeForm.industryId" :disabled="isDisabled" @change="industryIdChange">
                      <a-select-option v-for="(b, index) in prodIndustryList" :key="index" :value="b.id">
                        {{ b.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="方案名称" :name="'sname'"
                    :rules="{ required: true, message: '请输入方案名称' }">
                    <a-input placeholder="方案名称" v-model:value="programmeForm.sname" :disabled="isDisabled" />
                  </a-form-item>
                  <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="方案级别" :name="'securityLevel'"
                    :rules="{ required: true, message: '请选择方案级别' }">
                    <a-select :getPopupContainer="(trigger) => trigger.parentNode" placeholder="方案级别"
                      v-model:value="programmeForm.securityLevel">
                      <a-select-option v-for="(b, index) in securityLevelArray" :key="index" :value="b.dictValue">
                        {{ b.dictLabel }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="精选要求">
                    <a-radio-group v-model:value="programmeForm.isBoutique" name="radioGroup1">
                      <a-radio :value="0"> 否</a-radio>
                      <a-radio :value="1"> 是</a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="方案介绍" :name="'solutionIntroduce'"
                    :rules="[
    { required: true, message: '请输入方案介绍' },
    { max: 800, message: '解决方案不能超过800个字符' },
  ]">
                    <a-textarea placeholder="方案介绍" style="height: 128px"
                      v-model:value="programmeForm.solutionIntroduce" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="安全能力" :name="'productSpuId'"
                    :rules="{ required: true, message: '请输入安全能力' }">
                    <a-select :getPopupContainer="(trigger) => trigger.parentNode" placeholder="安全能力"
                      v-model:value="programmeForm.productSpuId">
                      <a-select-option v-for="(b, index) in prodSpuList" :key="index" :value="b.id">
                        {{ b.name }}
                      </a-select-option>
                    </a-select>
                  </a-form-item>
                  <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="方案版本" :name="'version'"
                    :rules="{ required: true, message: '请输入方案版本' }">
                    <a-input-number placeholder="方案版本" :disabled="isDisabled" :precision="2" :min="0"
                      v-model:value="programmeForm.version" style="width: 100%" />
                  </a-form-item>
                  <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="方案封面">
                    <a-input placeholder="方案封面" style="display: none" v-model:value="programmeForm.thumb" />
                    <a-upload list-type="picture-card" :file-list="fileList" @preview="handlePreview" :multiple="true"
                      :beforeUpload="beforeUpload" :customRequest="customRequest" @change="handleChange"
                      style="margin-top: 5px">
                      <div v-if="fileList.length < 1">
                        <plus-outlined />
                        <div class="ant-upload-text">上传封面</div>
                      </div>
                    </a-upload>
                    <a-modal :visible="previewVisible" :footer="null" @cancel="handleCancel">
                      <img alt="example" style="width: 100%" :src="getPath(programmeForm.thumb)" />
                    </a-modal>
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </div>
          <div class="item2">
            <div class="title">
              <span>02 行业需求</span>
            </div>
            <div class="item2-content">
              <div class="item2-content-item" v-for="(item, i) in programmeForm.industryDemandForm" :key="i">
                <a-row>
                  <a-col :span="12">
                    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" :label="`行业需求${i + 1}`"
                      :name="['industryDemandForm', i, 'title']" :rules="{ required: true, message: '请输入行业需求' }">
                      <a-input placeholder="请输入行业需求" v-model:value="item.title" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="11">
                    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="描述"
                      :name="['industryDemandForm', i, 'context']" :rules="{ required: true, message: '请输入描述' }">
                      <a-textarea placeholder="描述" style="height: 115px" v-model:value="item.context" />
                    </a-form-item>
                  </a-col>
                  <a-col :span="1">
                    <MinusCircleOutlined class="delete-button" v-if="programmeForm.industryDemandForm.length > 3"
                      @click="removeIndustry(i)" />
                  </a-col>
                </a-row>
                <a-row>
                  <a-col style="margin-top: -7%" :span="12">
                    <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="选择图标"
                      :name="['industryDemandForm', i, 'thumb']" :rules="{ required: true, message: '请选择图标' }">
                      <a-input placeholder="选择图标" v-model:value="item.thumb" style="display: none" />
                      <img :src="iconList[item.thumb]" alt="" style="width: 100px" @click="selectIcon(i)" />
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>
            </div>
            <div class="add" @click="addInstury">
              <div class="btn">+ 新增行业需求</div>
            </div>
          </div>
          <div class="item3">
            <div class="title">
              <span>03 解决方案</span>
            </div>
            <div class="item3-content">
              <div class="item3-content-item" v-for="(item, i) in programmeForm.solutionForm" :key="i">
                <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" :label="item.title"
                  :name="['solutionForm', i, 'context']" :rules="{ required: true, message: '请输入描述' }">
                  <a-textarea placeholder="请输入描述" style="height: 115px" v-model:value="item.context" />
                </a-form-item>
              </div>
            </div>
          </div>
          <div class="item4">
            <div class="title">
              <span>04 典型客户</span>
            </div>
            <div class="item4-content">
              <a-row :gutter="24" v-for="(item, i) in programmeForm.customersForm" :key="i"
                style="margin-bottom: 5px; position: relative">
                <a-col :span="12">
                  <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="客户名称"
                    :name="['customersForm', i, 'title']" :rules="{
    required: i === 0 ? true : false,
    message: '请输入客户名称',
  }">
                    <a-input placeholder="请输入客户名称" v-model:value="item.title" />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="项目名称"
                    :name="['customersForm', i, 'context']" :rules="{
    required: i === 0 ? true : false,
    message: '请输入项目名称',
  }">
                    <a-input placeholder="请输入项目名称" v-model:value="item.context" />
                  </a-form-item>
                </a-col>
                <MinusCircleOutlined class="delete-button" v-if="programmeForm.customersForm.length > 1"
                  @click="removeCustomers(i)" />
              </a-row>
              <div class="add" @click="addCustomers">
                <div class="btn">+ 新增典型客户</div>
              </div>
            </div>
          </div>
          <div class="item5" v-for="(item, i) in programmeForm.chapters" :key="i">
            <div class="title">
              <span>05 附件</span>
            </div>
            <div class="item5-content">
              <div class="top">
                <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="资源主题" :name="['chapters', i, 'title']"
                  :rules="{ required: true, message: '请输入产品资源介绍' }">
                  <a-textarea placeholder="请输入产品资源介绍" style="height: 115px" v-model:value="item.title" />
                </a-form-item>
              </div>
              <div class="bottom">
                <div style="margin-bottom: 20px">
                  <a-button class="upload" @click="() => $refs.fileModal.add()">+ 上传资源</a-button>
                  <span style="margin-left: 20px; color: #246df3">附件请遵循四个1: 1视频,1Pdf,1Excel,1Word</span>
                </div>
                <a-table size="default" ref="table" :columns="columns" :data-source="item.children" :pagination="false">
                  <template #fname="{ record }">
                    {{ record.resources.fname }}
                  </template>
                  <template #ftype="{ record }">
                    <span v-if="record.resources.ftype === 1">{{ "MP4" }}</span>
                    <span v-else-if="record.resources.ftype === 2">{{
    "PDF"
  }}</span>
                    <span v-else-if="record.resources.ftype === 3">{{
    "XLSX"
  }}</span>
                    <span v-else-if="record.resources.ftype === 4">{{
    "DOCX"
  }}</span>
                  </template>
                  <template #action="{ record, index }">
                    <span @click="previewFile(record.resources)" style="
                        cursor: pointer;
                        color: #387ff1;
                        margin-right: 10px;
                      ">预览</span>
                    <a-popconfirm title="确认删除?" @confirm="() => delByIds(i, record, index)">
                      <span style="color: #fc5532; cursor: pointer">删除</span>
                    </a-popconfirm>
                  </template>
                </a-table>
              </div>
            </div>
          </div>
        </a-form>
        <!-- <div class="save">
          <a-button class="btn" @click="handleSubmit">保存</a-button>
        </div> -->
      </div>
      <videoDom v-if="showVideo" :url="videoUrl" @close="videoClose"></videoDom>
      <programmeUploadFileModel ref="fileModal" :fTypeList="ftypeList" @setFileType="setFileType"
        @setNewChildren="setNewChildren" />
      <IconSelector @iconSelected="iconSelected" ref="icon"></IconSelector>
    </div>
  </a-modal>
</template>

<script>
import { message } from "ant-design-vue";
// import { uploadFile } from '@/request/publicServiceManage/MinioUtils'
import { imageUpload } from "@/request/publicServiceManage/TResources";
import {
  saveSolution,
  getSolutionDetail,
  updateSolution,
} from "@/request/publicServiceManage/TProductSolutions";
import videoDom from "@/components/filesPreview/videoDom";
import { getDictArray } from "@/utils/dict";
import programmeUploadFileModel from "./programmeUploadFileModel.vue";
import { getTProdSpuList } from "@/request/publicServiceManage/tProdSpu";
import { getTProdIndustryTree } from "@/request/publicServiceManage/tProdIndustry";

// import { getUserList } from '@/request/system'
import IconSelector from "../../modules/IconSelector.vue";
import { mapGetters } from "vuex";
import {
  MinusCircleOutlined,
  EyeOutlined,
  DeleteOutlined,
  PlusOutlined,
} from "@ant-design/icons-vue";
export default {
  name: "programmeEdit",
  components: {
    EyeOutlined,
    DeleteOutlined,
    PlusOutlined,
    videoDom,
    programmeUploadFileModel,
    MinusCircleOutlined,
    IconSelector,
  },
  data() {
    return {
      confirmLoading: false,
      newVisible: false,
      ftypeList: [],
      iconList: [
        require("@/assets/portalClient/icon/icon1.png"),
        require("@/assets/portalClient/icon/icon2.png"),
        require("@/assets/portalClient/icon/icon3.png"),
        require("@/assets/portalClient/icon/icon4.png"),
        require("@/assets/portalClient/icon/icon5.png"),
        require("@/assets/portalClient/icon/icon6.png"),
        require("@/assets/portalClient/icon/icon7.png"),
        require("@/assets/portalClient/icon/icon8.png"),
        require("@/assets/portalClient/icon/icon9.png"),
        require("@/assets/portalClient/icon/icon10.png"),
        require("@/assets/portalClient/icon/icon11.png"),
        require("@/assets/portalClient/icon/icon12.png"),
        require("@/assets/portalClient/icon/icon13.png"),
        require("@/assets/portalClient/icon/icon14.png"),
        require("@/assets/portalClient/icon/icon15.png"),
        require("@/assets/portalClient/icon/icon16.png"),
        require("@/assets/portalClient/icon/icon17.png"),
      ],
      prodSpuList: [],
      prodIndustryList: [],
      securityLevelArray: [],
      securityLevelMap: {},
      videoUrl: "",
      showVideo: false,
      currentIndex: 0,
      courseTreeArray: [],
      thumb: "",
      previewVisible: false,
      showUploadList: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 17 },
      },
      fileList: [],
      programmeForm: {
        isBoutique: 0,
        industryDemandForm: [
          { type: 1, thumb: 0, sort: 1 },
          { type: 1, thumb: 0, sort: 2 },
          { type: 1, thumb: 0, sort: 3 },
        ],
        solutionForm: [
          { title: "痛点分析", type: 2, sort: 1 },
          { title: "解决方案", type: 2, sort: 2 },
          { title: "方案价值", type: 2, sort: 3 },
        ],
        customersForm: [{ type: 3, sort: 1 }],
        industryId: undefined,
        productSpuId: undefined,
        chapters: [
          {
            children: [],
          },
        ],
        thumb: "",
        applyUsers: [
          '684b4e34ce7645309d3972d57227d9f6'
        ],
      },
      applyUsersList: [],
      columns: [
        {
          title: "文件名称",
          // 使用 scopedSlots 进行自定义渲染
          slots: { customRender: "fname" },
        },
        {
          title: "文件类型",
          // 使用 scopedSlots 进行自定义渲染
          slots: { customRender: "ftype" },
        },
        {
          title: "操作",
          width: "120px",
          dataIndex: "action",
          align: "left",
          slots: {
            customRender: "action",
          },
        },
      ],
      title: "新增解决方案",
      eTitle: "New Solution",
      // resourceTypeArray: [],
      // resourceTypeMap: {},
      isDisabled: false,
      currentId: null,
    };
  },
  // filters: {
  //   resourceTypeFilter(resourceType, resourceTypeMap) {
  //     return resourceTypeMap[resourceType] && resourceTypeMap[resourceType].text
  //   }
  // },
  async created() {
    // getDictArray('resource_file_type').then(data => {
    //   this.resourceTypeArray = data
    //   const resourceTypeMap = {}
    //   this.resourceTypeArray.forEach(d => {
    //     resourceTypeMap[d.dictValue] = { text: d.dictLabel }
    //   })
    //   this.resourceTypeMap = resourceTypeMap
    // })
    getDictArray("security_level").then((data) => {
      this.securityLevelArray = data;
      const securityLevelMap = {};
      this.securityLevelArray.forEach((d) => {
        securityLevelMap[d.dictValue] = { text: d.dictLabel };
      });
      this.securityLevelMap = securityLevelMap;
      console.log(this.securityLevelMap);
    });
    const res2 = await getTProdIndustryTree();
    if (res2.data.code === 0) {
      this.prodIndustryList = res2.data.rows;
    }
    if (this.currentId) {
      this.isDisabled = true;
      this.title = "编辑解决方案";
      this.eTitle = "Update Solution";
      this.init();
    }
  },
  mounted() {
    // this.scrollToView()
    // this.initUserList()
  },
  methods: {
    getPath: function (filePath) {
      return process.env.VUE_APP_File_URL + filePath;
    },
    // scrollToView() {
    //   const panel = document.getElementsByClassName('__panel')[0]
    //   panel.scrollTo(0, 0)
    // },
    // async initUserList() {
    //   const res = await getUserList()
    //   if (res.code === 0) {
    //     this.applyUsersList = res.rows
    //   }
    // },
    /**
     * 初始化方法
     * 获取解决方案详情并初始化表单数据
     */
    async init() {
      // 获取解决方案详情
      const res = await getSolutionDetail(this.currentId);
      if (res.data.code === 0) {
        // 初始化产品SPU列表
        await this.initProdSpuList(res.data.data.industryId)
        // 设置表单数据
        this.programmeForm = res.data.data;
        // 拆分属性数据
        this.splitAttributes(this.programmeForm.attributes);
        // 设置缩略图文件列表
        this.fileList = [
          {
            uid: "-1",
            name: "image.png",
            status: "done",
            url: this.getPath(this.programmeForm.thumb),
          },
        ];
        // 获取文件类型列表
        this.ftypeList = this.programmeForm.chapters[0].children.map((item) => {
          return item.resources.fname.split(".")[
            item.resources.fname.split(".").length - 1
          ];
        });
      }
    },
    splitAttributes(value) {
      // this.$set(this.programmeForm, 'industryDemandForm', [])
      // this.$set(this.programmeForm, 'solutionForm', [])
      // this.$set(this.programmeForm, 'customersForm', [])
      this.programmeForm.industryDemandForm = [];
      this.programmeForm.solutionForm = [];
      this.programmeForm.customersForm = [];
      value.concat([]).filter((item, i) => {
        if (item.type === 1) {
          this.programmeForm.industryDemandForm.push(item);
        } else if (item.type === 2) {
          this.programmeForm.solutionForm.push(item);
        } else {
          this.programmeForm.customersForm.push(item);
        }
      });
      this.programmeForm.attributes = [];
    },
    async initProdSpuList(industryId) {
      const param = { prodIndustryId: industryId };
      const res1 = await getTProdSpuList(param);
      if (res1.data.code === 0) {
        this.prodSpuList = res1.data.rows;
        if (this.prodSpuList.length > 0) {
          this.programmeForm.productSpuId = this.prodSpuList[0].id;
        } else {
          this.programmeForm.productSpuId = undefined;
        }
      }
    },
    addCustomers() {
      console.log(this.programmeForm.customersForm, 123132);
      this.programmeForm.customersForm.push({
        type: 3,
        sort: this.programmeForm.customersForm.length,
      });
    },
    addInstury() {
      this.programmeForm.industryDemandForm.push({
        type: 1,
        thumb: 0,
        sort: this.programmeForm.industryDemandForm.length,
      });
    },
    removeCustomers(value) {
      console.log(value);
      this.programmeForm.customersForm.splice(value, 1);
    },
    removeIndustry(value) {
      console.log(value);
      this.programmeForm.industryDemandForm.splice(value, 1);
    },
    handleCancel() {
      this.previewVisible = false;
    },
    getBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
      });
    },
    async handlePreview(file) {
      if (!file.url && !file.preview) {
        file.preview = await this.getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },
    async customRequest({ onSuccess, onError, file, onProgress }) {
      new Promise((resolve, reject) => {
        // 检查文件大小是否超过3MB
        if (file.size > 3 * 1024 * 1024) {
          reject('文件大小不能超过3MB');
          return;
        }
        // 检查文件格式是否为png或jpeg
        if (!['image/png', 'image/jpeg'].includes(file.type)) {
          reject('只支持PNG和JPEG格式的图片');
          return;
        }
        resolve()
      }).then(() => {
        const formData = new FormData();
        formData.append("file", file);
        imageUpload(formData).then((res) => {
          if (res.data.code === 0) {
            this.programmeForm.thumb = res.data.fileName;
            onSuccess({ code: 0, message: "上传成功" }, file); 
          } else {
            onError({ code: 500, message: "上传失败" }, file);
          }
        });
      }).catch((error) => {
        onError({ code: 500, message: error }, file)
      })
    },
    handleChange(info) {
      console.log(info, 123123)
      // 检查文件列表
      let fileList = [...info.fileList];
      // 处理上传错误
      if (info.file.status === 'error') {
        message.error(info.file.error.message);
        this.fileList = []
      } else if (info.file.status === 'done') {
        this.fileList = []
        // 只保留最后一个文件
        fileList = fileList.slice(-1);
        // 更新文件状态
        fileList = fileList.map(file => {
          if (file.response) {
            // 上传完成后的处理
            message.success(file.response.message);
            file.url = this.getPath(this.programmeForm.thumb);
          }
          return file;
        });
      }
      this.fileList = fileList;
    },
    handleSubmit(e) {
      this.$refs["programmeForm"]
        .validateFields()
        .then(async (value) => {
          try {
            this.confirmLoading = true
            if (
              this.programmeForm.chapters[0] &&
              this.programmeForm.chapters[0].children.length < 4
            ) {
              message.error("附件不满足要求,无法保存");
              return;
            }
            this.programmeForm.attributes = [
              ...this.programmeForm.industryDemandForm,
              ...this.programmeForm.solutionForm,
              ...this.programmeForm.customersForm,
            ];
            if (!this.currentId) {
              const res = await saveSolution(this.programmeForm);
              console.log(res);
              if (res.code === 0) {
                this.init();
              }
            } else {
              const res = await updateSolution(this.programmeForm);
              console.log(res);
              if (res.code === 0) {
                this.init();
              }
            }
            message.success("保存成功");
            this.handleClose();
            this.$emit("onOk");
          } catch (error) {
            console.log(error)
            message.success(error);
          } finally {
            this.confirmLoading = false
          }
        })
        .catch((err) => {
          console.log(7777, err);
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    delByIds(pIndex, record, index) {
      console.log(
        record,
        this.ftypeList.indexOf(record.resources.fname.split(".")[1])
      );
      this.ftypeList.splice(
        this.ftypeList.indexOf(record.resources.fname.split(".")[1]),
        1
      );
      this.programmeForm.chapters[pIndex].children.splice(index, 1);
    },
    setNewChildren(value) {
      this.programmeForm.chapters[0].children.push({ resources: value });
    },
    industryIdChange(value) {
      if (value) {
        this.initProdSpuList(value);
      }
    },
    previewFile(value) {
      if (this.getUserId) {
        if (value.ftype === 1) {
          this.videoUrl = value.url;
          this.showVideo = true;
          return;
        }
        let typeName = "";
        switch (value.ftype) {
          case 2:
            typeName = "previewPdf";
            break;
          case 3:
            typeName = "previewExcel";
            break;
          case 4:
            typeName = "previewWord";
            break;
        }
        const routeData = this.$router.resolve({
          path: "/preview",
          query: {
            type: typeName,
            id: value.id,
          },
        });
        window.open(routeData.href, "_blank");
      } else {
        const routeData = this.$router.resolve({
          path: "/login",
        });
        window.open(routeData.href, "_blank");
      }
    },
    videoClose() {
      this.showVideo = false;
    },
    selectIcon(index) {
      this.currentIndex = index;
      this.$refs.icon.handlerSelectIcon();
    },
    iconSelected(index) {
      this.$nextTick(() => {
        this.programmeForm.industryDemandForm[this.currentIndex].thumb = index;
      });
    },
    setFileType(value) {
      this.ftypeList.push(value);
    },
    applyUsersChange(value) {
      this.programmeForm.applyUsers = value;
    },
    open(value) {
      if (value) {
        this.currentId = value;
        this.init();
      }
      this.newVisible = true;
    },
    handleClose() {
      this.currentId = null
      this.$refs["programmeForm"].resetFields();
      this.programmeForm = {
        isBoutique: 0,
        industryDemandForm: [
          { type: 1, thumb: 0, sort: 1 },
          { type: 1, thumb: 0, sort: 2 },
          { type: 1, thumb: 0, sort: 3 },
        ],
        solutionForm: [
          { title: "痛点分析", type: 2, sort: 1 },
          { title: "解决方案", type: 2, sort: 2 },
          { title: "方案价值", type: 2, sort: 3 },
        ],
        customersForm: [{ type: 3, sort: 1 }],
        industryId: undefined,
        productSpuId: undefined,
        chapters: [
          {
            children: [],
          },
        ],
        thumb: "",
        applyUsers: ['684b4e34ce7645309d3972d57227d9f6'],
      };
      this.fileList = [];
      this.ftypeList = [];
      this.newVisible = false;
    },
  },
  computed: {
    ...mapGetters(["getUserId"]),
    industryId11() {
      return this.programmeForm.industryId;
    },
  },
  watch: {
    // async industryId11(newVal, oldVal) {
    //   if (newVal) {
    //     console.log(newVal, oldVal);
    //     // if (newVal !== oldVal) {
    //     //   await this.initProdSpuList(newVal);
    //     // }
    //     this.industryIdChange(newVal);
    //   }
    // },
  },
};
</script>

<style lang="less" scoped>
#programmeEdit {
  background-color: #f3f5f9;
  min-width: 1000px;

  // background: url('../../../assets/portalClient/trainManage/editBg.png') no-repeat;
  // background-size: contain;
  .programmeEditBody {
    width: 1000px;
    height: 80%;
    background: #fff;

    .banner {
      position: relative;
      width: 100%;
      height: auto;
      margin-bottom: 30px;

      img {
        border-radius: 5px;
        width: 100%;
      }

      .banner-text {
        padding-left: 130px;
      }

      .programmeEditIllustrate {
        width: fit-content;
        font-size: 42px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
        margin-bottom: 0px;
      }

      .illustrate {
        width: fit-content;
        font-size: 24px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #ffffff;
      }
    }

    .title {
      width: 100%;
      height: 58px;
      // background: url('../../../assets/portalClient/trainManage/editTitle.png');
      padding: 0 25px;
      display: flex;
      align-items: center;

      span {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #000;
      }
    }

    .item1,
    .item2,
    .item3,
    .item4,
    .item5 {
      width: 100%;
      height: fit-content;
      background: #ffffff;

      .item1-content {
        width: 100%;
        padding: 25px 80px;
      }
    }

    .item2,
    .item3,
    .item4,
    .item5 {
      margin-top: 30px;
    }

    .item2 {
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        color: #000;

        .delete {
          font-size: 22px;
          color: #f0f0f0;
          cursor: pointer;
          transition: all 0.3s;

          &:hover {
            color: #fff;
          }
        }
      }

      .item2-content {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        padding: 25px 60px;

        .item2-content-item {
          width: 100%;
          margin-bottom: 10px;

          img {
            cursor: pointer;
          }

          .delete-button {
            position: absolute;
            top: 6px;
            right: 30px;
            height: fit-content;
            font-size: 18px;
            transition: all 0.3s;
            color: rgb(245, 49, 49);

            &:hover {
              color: rgb(245, 12, 12);
              filter: drop-shadow(0 0 0.5px rgb(241, 33, 33));
            }
          }
        }

        .bottom {}
      }
    }

    .item3 {
      .item3-content {
        display: flex;
        flex-wrap: wrap;
        width: 100%;
        padding: 25px 60px;

        .item3-content-item {
          width: 50%;
          margin-bottom: 5px;
        }
      }
    }

    .item4 {
      .item4-content {
        width: 100%;
        padding: 25px 100px;

        .delete-button {
          position: absolute;
          top: 6px;
          right: 20px;
          height: fit-content;
          font-size: 18px;
          transition: all 0.3s;
          color: rgb(245, 49, 49);

          &:hover {
            color: rgb(245, 12, 12);
            filter: drop-shadow(0 0 0.5px rgb(241, 33, 33));
          }
        }
      }
    }

    .item5 {
      .item5-content {
        width: 100%;
        padding: 25px 60px;

        .top {
          display: flex;
          width: 100%;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;

          :deep(.ant-row.ant-form-item) {
            width: 900px;
            margin: 0;

            .ant-form-item-label {
              width: 80px;
            }

            .ant-form-item- control-wrapper {
              width: 800px;
            }
          }
        }

        .bottom {
          .upload {
            border: none;
            border-radius: 0;
            background: #246df3;
            color: #fff;

            &:hover {
              filter: brightness(1.2);
            }
          }
        }
      }
    }

    .add {
      width: 100%;
      // height: 40px;
      text-align: center;
      color: #246df3;
      padding: 0px 60px 20px;
      background-color: #fff;

      .btn {
        width: 100%;
        height: 30px;
        line-height: 30px;
        border: 1px solid #246df3;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background-color: #246df3;
          color: #fff;
        }
      }
    }

    .save {
      width: 100%;
      height: 48px;
      margin-top: 30px;
      text-align: center;

      .btn {
        width: 222px;
        height: 100%;
        background-color: #246df3;
        color: #fff;
        border-radius: 0;

        &:hover {
          filter: brightness(1.2);
        }
      }
    }
  }
}

:deep(.ant-upload-list-picture-card-container) {
  width: 100%;
  height: 175px;

  .ant-upload-list-item.ant-upload-list-item-done.ant-upload-list-item-list-type-picture-card {
    width: 100%;
    height: 100%;
  }

  .ant-upload-list-item.ant-upload-list-item-uploading.ant-upload-list-item-list-type-picture-card {
    width: 100%;
    height: 100%;
    display: flex;
    background-color: #fff;
    align-items: center;
  }
  .ant-upload-list-item-thumbnail img {
    object-fit: cover;
  }
}

:deep(.ant-upload-picture-card-wrapper) {
  display: inline-block;
  height: 100%;
}

:deep(.ant-upload.ant-upload-select.ant-upload-select-picture-card) {
  width: 100%;
  height: 175px;
  margin: 0;
  background-color: #fff;
}

:deep(.ant-select-dropdown) {
  z-index: 98;
}
</style>
