<template>
  <div class="page">
    <!-- 搜索条件栏 -->
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">课程名称：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            allowClear
            v-emoji
            v-model:value.trim="searchData.title"
            placeholder="请输入课程名称"
          />
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 22px">
          重置
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div style="display: flex; gap: 15px">
      <div
        class="page-content"
        :style="{ minHeight: flexHeight, width: '180px', overflow: 'hidden' }"
      >
        <div v-if="deptTree.length>0" class="productDetailList">
          <a-tree
              v-if="deptTree.length > 0"
              :treeData="deptTree"
              @expand="onExpand"
              :expandedKeys="expandedKeys"
              :autoExpandParent="autoExpandParent"
              :selectedKeys="selectedKeys_"
              :default-selected-keys="[deptTree[0].key]"
              @select="handleSelect"
          >
            <!-- <a-icon v-if="!expanded"  type="plus-square" />
            <a-icon v-if="expanded"  type="minus-square" /> -->
          </a-tree>
        </div>
        <div class="productDetailList" v-else>
          <div
            style="
              font-size: 16px;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            "
          >
            暂无内容
          </div>
        </div>
      </div>
      <div
        class="page-content"
        :style="{ minHeight: flexHeight, width: 'calc(100% - 180px)' }"
      >
        <a-row>
          <a-col :span="24">
            <a-table
              style="padding-top: 10px; padding-left: 10px; padding-right: 10px"
              :columns="tableColumns"
              :data-source="tableData"
              :loading="tableLoading"
              :pagination="pagination"
              :row-class-name="
                (record, index) => (index % 2 === 1 ? 'table-striped' : null)
              "
              :scroll="{ y: tableHeight }"
              @change="handleTableChange"
            >
              <template #num="{ index }">
                <span>{{
                  (pagination.current - 1) * pagination.pageSize +
                  Number(index) +
                  1
                }}</span>
              </template>
              <template #thumb="{ record }">
                <a-image :width="60" :src="getPath(record.thumb)" />
              </template>
              <template #courseTypeId="{ text }">
                {{ text }}
              </template>
              <template #isFineWork="{ text }">
                {{ text ? "是" : "否" }}
              </template>
              <template #isRequired="{ text }">
                {{ text ? "是" : "否" }}
              </template>
              <template #likeFlag="{ text, record }">
                <span class="like" :class="text === 0 || !text ? '' : 'is-actived'" @click="handleLike(record)">
                  {{ record.likeNum || 0 }}
                </span>
              </template>
              <template #collectFlag="{ text, record }">
                <span class="collect" :class="text === 0 || !text ? '' : 'is-actived'" @click="handleCollect(record)">
                  {{ record.collectNum || 0 }}
                </span>
              </template>
              <template #mark="{ record }">
                <a-button
                  type="link"
                  style="color: #387ff1"
                  @click="handleDetail(record)"
                >
                  查看
                </a-button>
              </template>
            </a-table>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>
</template>

<script>
import {
  postMessagePages,
  postMessageMark,
  postMessageRead,
} from "@/request/messagenotification_messagelist";
import {
  ExclamationCircleOutlined,
  MailOutlined,
  ContainerOutlined,
  StarFilled,
  StarOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";
import { Modal, message } from "ant-design-vue";
import {getTTrainCourseList} from "@/request/publicServiceManage/TTrainCourse";
import {getTTrainCourseTypeList} from "@/request/publicServiceManage/TTrainCourseType";
import { saveHandleLike, removeHandleLike, saveHandleCollect, removeHandleCollect } from '@/request/publicServiceManage/tHandleLike'
import { saveTUserViewLog } from '@/request/publicServiceManage/TUserViewLog'

export default {
  components: {
    MailOutlined,
    ContainerOutlined,
    StarFilled,
    StarOutlined,
    SearchOutlined,
  },
  data() {
    return {
      currentTypeIndex: "all",
      deptTree: [
        {
          title: '全部课程',
          key: '',
          scoped : { title: 'custom' }
        },
        {
          title: '我的收藏',
          key: 'collect',
          scopedSlots: { title: 'custom' }
        }
      ],
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      newVisible: false,
      tableLoading: false,
      formData: {
        title: "",
        content: "",
        createTime: "",
        isRead: "",
        mark: "",
      },
      searchData: {
      },
      autoExpandParent: true,
      expanded: true,
      expandedKeys: [],
      lastsearchData: {
      },
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: "封面",
          dataIndex: "thumb",
          slots: {
            customRender: "thumb",
          },
          width: "10%",
          align: "center",
        },
        {
          title: "标题",
          dataIndex: "title",
          align: "center",
          ellipsis: true,
          slots: {
            customRender: "title",
          },
        },
        {
          title: "分类",
          width: "15%",
          dataIndex: "courseTypeName",
          align: "center",
        },
        {
          title: "是否精选",
          width: "9%",
          dataIndex: "isFineWork",
          align: "center",
          slots: {
            customRender: "isFineWork",
          },
        },
        {
          title: "是否必修",
          width: "9%",
          dataIndex: "isRequired",
          align: "center",
          slots: {
            customRender: "isRequired",
          },
        },
        {
          title: "浏览量",
          width: "11%",
          dataIndex: "viewNum",
          align: "center",
          slots: {
            customRender: "viewNum",
          },
        },
        {
          title: "点赞",
          width: "11%",
          dataIndex: "likeFlag",
          align: "center",
          slots: {
            customRender: "likeFlag",
          },
        },
        {
          title: "收藏",
          width: "11%",
          dataIndex: "collectFlag",
          align: "center",
          slots: {
            customRender: "collectFlag",
          },
        },
        {
          title: "操作",
          dataIndex: "mark",
          width: "8%",
          align: "center",
          slots: {
            customRender: "mark",
          },
        },
      ],
      selectedKeys_: ['all'],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
      currentLike: false,
      currentCollect: false
    };
  },
  mounted() {
    this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    setTimeout(() => {
      this.flexHeight = `calc(100vh - ${this.searchHeight}px - 140px)`;
      this.tableHeight =
        (window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight) -
        336 -
        this.searchHeight;
    }, 100);
    this.initTrainType()
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  methods: {
    getPath: function (filePath) {
      return process.env.VUE_APP_File_URL + filePath;
    },
    handleClose() {
      this.newVisible = false;
    },
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    async initTrainType() {
      const res = await getTTrainCourseTypeList()
      console.log(res)
      if (res.status === 200) {
        const data = res.data.rows
        await this.buildtree(data, this.deptTree, '0')
        console.log(this.deptTree)
        this.expandedKeys = data.map((m) => m.parentId)
        this.dataList = data.map((m) => {
          return { key: m.id, title: m.cname }
        })
        this.queryParam = {
          courseTypeId: this.deptTree[0].key
        }
        this.selectedKeys_ = [this.deptTree[0].key]
      }
    },
    buildtree(list, arr, pid) {
      list.forEach((item) => {
        if (item.parentId === pid) {
          var child = {
            key: item.id,
            value: item.id, // value是给modal的select用的，2者属性不一样
            title: item.cname,
            // scopedSlots: { title: 'title' },
            children: []
          }
          this.buildtree(list, child.children, item.id)
          if (child.children.length === 0) {
            delete child.children
          }
          arr.push(child)
        }
      })
    },
    handleSelect(selectedKeys, info) {
      // eslint-disable-next-line no-empty
      if (selectedKeys.length === 0) {
      } else {
        if (selectedKeys[0] === 'all') {
          this.queryParam = {}
        } else if (selectedKeys[0] === 'collect') {
          this.queryParam = { collectFlag: 1 }
        } else {
          this.queryParam = {
            courseTypeId: selectedKeys[0]
          }
        }
        this.selectedKeys_ = [selectedKeys[0]]
      }

      this.handleSearch(true)
    },
    onExpand(expandedKeys, info) {
      this.expanded = info.expanded
      this.expandedKeys = expandedKeys
      this.autoExpandParent = false
    },
    async handleSearch(flag) {
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
      this.tableLoading = true;
      const param = {
        title: this.lastsearchData.title,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.pageNum
      }
      await getTTrainCourseList(Object.assign({isShow: 1}, param, this.queryParam)).then((data) => {
        console.log(data)
        if (data.status === 200) {
          this.tableData = data.data.rows
          this.pagination.total = data.data.total;

        } else {
          this.tableData = []
          this.pagination.total = 0
        }
      })
      this.tableLoading = false;
    },
    handleDetail(record) {
      const routeData = this.$router.resolve({
        path: `/publicServiceManage/trainDetail/${record.id}`,
        query: {},
      });
      window.open(routeData.href, "_blank");
      this.saveView(record)
    },
    saveView(item) {
      const param = { tableId: item.id, type: 2 }
      saveTUserViewLog(param).then(res => {
        console.log(res)
      })
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {
      };
      this.searchData = {
      };
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    clickCategory(index) {
      this.currentTypeIndex = index;
      this.pagination.pageNum = 1;
      this.handleSearch(true);
    },
    async handleLike(train) {
      if (this.currentLike) {
        return
      }
      this.currentLike = true
      const param = { type: 3, tableId: train.id }
      if (train.likeFlag === 0 || !train.likeFlag) {
        await saveHandleLike(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('点赞成功')
            } else {
              message.error('系统错误，请稍后再试')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentLike = false
          })
      } else {
        await removeHandleLike(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('取消点赞成功')
            } else {
              message.error('系统错误，请稍后再试')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentLike = false
          })
      }
      this.handleSearch();
    },
    async handleCollect(train) {
      if (this.currentCollect) {
        return
      }
      this.currentCollect = true
      const param = { type: 3, tableId: train.id }
      if (train.collectFlag === 0 || !train.collectFlag) {
        await saveHandleCollect(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('收藏成功')
            } else {
              message.error('系统错误，请稍后再试')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentCollect = false
          })
      } else {
        await removeHandleCollect(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('取消收藏成功')
            } else {
              message.error('系统错误，请稍后再试')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentCollect = false
          })
      }
      this.handleSearch();
    },
  },
};
</script>

<style scoped lang="less">
.productDetailList {
  height: 605px;
  width: 100%;
  padding: 0;
  overflow-y: overlay;
  .active {
    background: #0f79e9;
    color: #fff;
  }
  :deep(.ant-tree) {
    .ant-tree-list {
      .ant-tree-list-holder {
        .ant-tree-list-holder-inner {
          .ant-tree-treenode {
            width: 100%;
            height: 50px;
            padding: 0;
            position: relative;
            &.ant-tree-treenode-selected {
              color: #fff;
              background: #0f79e9;
            }
            .ant-tree-switcher {
              position: absolute;
              left: 5px;
              top: 0;
              bottom: 0;
              margin: auto;
              z-index: 1;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .ant-tree-indent {
              display: none;
            }
            .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-open,
            .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-normal {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              width: 100%;
              height: 100%;
              font-size: 16px;
              padding-left: 30px;
              &.ant-tree-node-selected {
                color: #fff;
                background: #0f79e9;
              }
            }
            &.ant-tree-treenode-switcher-close.ant-tree-treenode-leaf-last {
              .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-close {
                display: flex;
                justify-content: flex-start;
                align-items: center;
                width: 100%;
                height: 100%;
                font-size: 16px;
                padding-left: 30px;
                &.ant-tree-node-selected {
                  color: #fff;
                  background: #0f79e9;
                }
              }
              .ant-tree-node-content-wrapper.ant-tree-node-content-wrapper-normal {
                padding-left: 45px;
                font-size: 14px;
              }
            }
            .ant-tree-node-content-wrapper {
              .ant-tree-title {
                display: inline-block;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
                width: 100%;
              }
            }
          }
        }
      }
    }
  }
}
.collect,.like {
  display: inline-block;
  width: fit-content;
  padding-left: 25px;
  cursor: pointer;
}
.collect {
  background: url('@/assets/portalClient/nocollect.png') no-repeat;
  background-size: 20px 20px;
  &.is-actived {
    background: url('@/assets/portalClient/collect.png') no-repeat;
    background-size: 20px 20px;
  }
}

.like {
  background: url('@/assets/portalClient/nolike.png') no-repeat;
  background-size: 20px 20px;
  &.is-actived {
    background: url('@/assets/portalClient/like.png') no-repeat;
    background-size: 20px 20px;
  }
}
</style>
