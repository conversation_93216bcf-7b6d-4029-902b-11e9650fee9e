<template>
  <a-modal
    :maskClosable="false"
    v-model:visible="newVisible"
    :title="title"
    :width="1048"
    @cancel="handleClose"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
    :destroyOnClose="true"
    :bodyStyle="{ height: '680px', overflowY: 'auto', overflowX: 'hidden' }"
  >
    <div id="trainEdit">
      <div class="trainEditBody">
        <a-form ref="courseForm" :model="courseForm">
          <div class="item1">
            <div class="title">
              <span>01 基本信息</span>
            </div>
            <div class="item1-content">
              <a-row :gutter="24">
                <a-col :span="12">
                  <a-form-item
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    label="课程标题"
                    name="title"
                    :rules="{ required: true, message: '请输入课程标题' }"
                  >
                    <a-input
                      placeholder="课程标题"
                      :disabled="disabled"
                      v-model:value="courseForm.title"
                    />
                  </a-form-item>
                  <a-form-item
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    label="学习要求"
                  >
                    <a-radio-group
                      v-model:value="courseForm.isRequired"
                      name="radioGroup1"
                      :disabled="disabled"
                    >
                      <a-radio :value="0"> 选修</a-radio>
                      <a-radio :value="1"> 必修</a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    label="课程封面"
                  >
                    <a-input
                      placeholder="课程封面"
                      :disabled="disabled"
                      style="display: none"
                      v-model:value="courseForm.thumb"
                    />
                    <a-upload
                      list-type="picture-card"
                      :file-list="fileList"
                      @preview="handlePreview"
                      :multiple="true"
                      :customRequest="customRequest"
                      @change="handleChange"
                    >
                      <div v-if="fileList.length < 1">
                        <plus-outlined />
                        <div class="ant-upload-text">上传封面</div>
                      </div>
                    </a-upload>
                    <a-modal
                      :visible="previewVisible"
                      :footer="null"
                      @cancel="handleCancel"
                    >
                      <img
                        alt="example"
                        style="width: 100%"
                        :src="getPath(courseForm.thumb)"
                      />
                    </a-modal>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    label="课程类型"
                    name="courseTypeId"
                    :rules="{ required: true, message: '请选择课程类型' }"
                  >
                    <a-tree-select
                      v-model:value="courseForm.courseTypeId"
                      :getPopupContainer="(trigger) => trigger.parentNode"
                      :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
                      :treeData="courseTreeArray"
                      placeholder="课程类型"
                      :disabled="disabled"
                      tree-default-expand-all
                    >
                    </a-tree-select>
                  </a-form-item>
                  <a-form-item
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    label="精选要求"
                  >
                    <a-radio-group
                      v-model:value="courseForm.isFineWork"
                      name="radioGroup2"
                      :disabled="disabled"
                    >
                      <a-radio :value="0"> 非精选</a-radio>
                      <a-radio :value="1"> 精选</a-radio>
                    </a-radio-group>
                  </a-form-item>
                  <a-form-item
                    :labelCol="labelCol"
                    :wrapperCol="wrapperCol"
                    label="简介"
                    name="shortDesc"
                    :rules="{ required: true, message: '请输入简介' }"
                  >
                    <a-textarea
                      :rows="8"
                      :disabled="disabled"
                      placeholder="简介"
                      :auto-size="{ minRows: 7.5 }"
                      v-model:value="courseForm.shortDesc"
                    />
                  </a-form-item>
                </a-col>
              </a-row>
            </div>
          </div>
          <div class="item2" v-for="(item, i) in courseForm.chapters" :key="i">
            <div class="title">
              <span>{{ `${2 + i}`.padStart(2, "0") }} 章节信息</span>
              <a-tooltip>
                <template #title>删除章节</template>
                <MinusCircleOutlined
                  class="delete"
                  v-if="i > 0"
                  type="delete"
                  @click="deletechapters(i)"
                />
              </a-tooltip>
            </div>
            <div class="item2-content">
              <div class="top">
                <a-form-item
                  :labelCol="labelCol"
                  :wrapperCol="wrapperCol"
                  label="章节标题"
                  :name="['chapters', i, 'cname']"
                  :rules="{ required: true, message: '请输入章节标题' }"
                >
                  <a-input
                    placeholder="章节标题"
                    :disabled="disabled"
                    v-model:value="item.cname"
                  />
                </a-form-item>
                <a-button class="addClassHourBtn" @click="addClassHour(i)"
                  >+ 新增课时</a-button
                >
              </div>
              <div class="bottom">
                <a-table
                  size="default"
                  ref="table"
                  :columns="columns"
                  :data-source="item.children"
                  :pagination="false"
                >
                  <template #fname="{ record }">
                    {{ record.resources.fname }}
                  </template>
                  <template #ftype="{ record }">
                    <span v-if="record.resources.ftype === 1">{{ "MP4" }}</span>
                    <span v-else-if="record.resources.ftype === 2">{{
                      "PDF"
                    }}</span>
                    <span v-else-if="record.resources.ftype === 3">{{
                      "XLSX"
                    }}</span>
                    <span v-else-if="record.resources.ftype === 4">{{
                      "DOCX"
                    }}</span>
                  </template>
                  <template #duration="{ record }">
                    <span>{{ record.resources.duration }}</span>
                  </template>
                  <template #action="{ record, index }">
                    <span
                      @click="handleEdit(i, record, index)"
                      style="
                        cursor: pointer;
                        color: #387ff1;
                        margin-right: 10px;
                      "
                      >编辑</span
                    >
                    <span
                      @click="previewFile(record.resources)"
                      style="
                        cursor: pointer;
                        color: #387ff1;
                        margin-right: 10px;
                      "
                      >预览</span
                    >
                    <a-popconfirm
                      title="确认删除?"
                      @confirm="() => delByIds(i, record, index)"
                    >
                      <span style="color: #fc5532; cursor: pointer">删除</span>
                    </a-popconfirm>
                  </template>
                </a-table>
              </div>
            </div>
          </div>
          <div class="add" @click="addchapters">
            <div class="btn">+ 新增章节</div>
          </div>
        </a-form>
        <!-- <div class="save">
          <a-button class="btn" @click="handleSubmit">保存</a-button>
        </div> -->
      </div>
      <trainEditUploadFileModel
        ref="trainEditUploadFileModel"
        :type="modelType"
        @setNewChildren="setNewChildren"
        @updateChildren="updateChildren"
      ></trainEditUploadFileModel>
      <videoDom v-if="showVideo" :url="videoUrl" @close="videoClose"></videoDom>
    </div>
  </a-modal>
</template>

<script>
import { message } from "ant-design-vue";
import { imageUpload } from "@/request/publicServiceManage/TResources";
import { getTTrainCourseTypeList } from "@/request/publicServiceManage/TTrainCourseType";
import {
  saveTrain,
  updateTrain,
  getTrainDetail,
} from "@/request/publicServiceManage/TTrainCourse";
import { mapGetters } from "vuex";
import trainEditUploadFileModel from "./trainEditUploadFileModel.vue";
import videoDom from "@/components/filesPreview/videoDom";
import { getDictArray } from "@/utils/dict";
import {
  MinusCircleOutlined,
  EyeOutlined,
  DeleteOutlined,
  PlusOutlined,
} from "@ant-design/icons-vue";

export default {
  name: "trainEdit",
  components: {
    trainEditUploadFileModel,
    videoDom,
    PlusOutlined,
    MinusCircleOutlined,
  },
  data() {
    return {
      newVisible: false,
      videoUrl: "",
      showVideo: false,
      modelType: "add",
      currentIndex: 0,
      currentClassHourIndex: 0,
      disabled: false,
      title: "新增课程",
      eTitle: "New courses",
      courseTreeArray: [],
      previewVisible: false,
      showUploadList: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      fileList: [],
      courseForm: {
        isRequired: 0,
        isFineWork: 0,
        thumb: "",
        chapters: [
          {
            cname: "",
            children: [],
            sort: 0,
          },
        ],
      },
      columns: [
        {
          title: "课时名称",
          dataIndex: "title",
          ellipsis: true,
          // width: '180px'
        },
        {
          title: "文件名称",
          slots: { customRender: "fname" },
          ellipsis: true,
          // width: '180px'
        },
        {
          title: "文件类型",
          slots: { customRender: "ftype" },
        },
        {
          title: "时长",
          slots: { customRender: "duration" },
          // width: '100px'
        },
        {
          title: "操作",
          width: "150px",
          dataIndex: "action",
          align: "left",
          slots: { customRender: "action" },
        },
      ],
      resourceTypeArray: [],
      resourceTypeMap: {},
      currentId: null,
      confirmLoading: false
    };
  },
  filters: {
    resourceTypeFilter(resourceType, resourceTypeMap) {
      return (
        resourceTypeMap[resourceType] && resourceTypeMap[resourceType].text
      );
    },
  },
  computed: {
    ...mapGetters(["getUserId"]),
  },
  created() {
    getDictArray("resource_file_type").then((data) => {
      this.resourceTypeArray = data;
      const resourceTypeMap = {};
      this.resourceTypeArray.forEach((d) => {
        resourceTypeMap[d.dictValue] = { text: d.dictLabel };
      });
      this.resourceTypeMap = resourceTypeMap;
    });
    this.initTrainCourseTypeList();
    if (this.currentId) {
      this.title = "编辑课程";
      this.eTitle = "Update Courses";
      this.init();
    }
  },
  mounted() {
    // this.scrollToView()
  },
  methods: {
    getPath: function (filePath) {
      return process.env.VUE_APP_File_URL + filePath;
    },
    // scrollToView() {
    //   const panel = document.getElementsByClassName('__panel')[0]
    //   panel.scrollTo(0, 0)
    // },
    async init() {
      const res = await getTrainDetail(this.currentId);
      if (res.data.code === 0) {
        this.courseForm = res.data.data;
        this.fileList = [
          {
            uid: "-1",
            name: "image.png",
            status: "done",
            url: this.getPath(this.courseForm.thumb),
          },
        ];
      }
    },
    async initTrainCourseTypeList() {
      const res = await getTTrainCourseTypeList();
      this.buildTree(res.data.rows, this.courseTreeArray, "0");
      console.log(this.courseTreeArray);
    },
    buildTree(list, arr, pid) {
      list.forEach((item) => {
        if (item.parentId === pid) {
          var child = {
            key: item.id,
            value: item.id + "",
            title: item.cname,
            children: [],
          };
          this.buildTree(list, child.children, item.id);
          arr.push(child);
        }
      });
    },
    addchapters() {
      this.courseForm.chapters.push({
        cname: "",
        children: [],
        sort: this.courseForm.chapters.length,
      });
    },
    deletechapters(value) {
      this.courseForm.chapters.splice(value, 1);
    },
    handleCancel() {
      this.previewVisible = false;
    },
    getBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.readAsDataURL(file);
        reader.onload = () => resolve(reader.result);
        reader.onerror = (error) => reject(error);
      });
    },
    async handlePreview(file) {
      console.log(file);
      if (!file.url && !file.preview) {
        file.preview = await this.getBase64(file.originFileObj);
      }
      this.previewImage = file.url || file.preview;
      this.previewVisible = true;
    },
    async customRequest({ onSuccess, onError, file, onProgress }) {
      new Promise((resolve, reject) => {
        // 检查文件大小是否超过3MB
        if (file.size > 3 * 1024 * 1024) {
          reject('文件大小不能超过3MB');
          return;
        }
        // 检查文件格式是否为png或jpeg
        if (!['image/png', 'image/jpeg'].includes(file.type)) {
          reject('只支持PNG和JPEG格式的图片');
          return;
        }
        resolve()
      }).then(() => {
        const formData = new FormData();
        formData.append("file", file);
        imageUpload(formData).then((res) => {
          if (res.data.code === 0) {
            this.courseForm.thumb = res.data.fileName;
            onSuccess({ code: 0, message: "上传成功" }, file);
          } else {
            onError({ code: 500, message: "上传失败" }, file);
          }
        });
      }).catch((error) => {
        onError({ code: 500, message: error }, file)
      })
    },
    handleChange(info) {
      console.log(info, 123123)
      // 检查文件列表
      let fileList = [...info.fileList];
      // 处理上传错误
      if (info.file.status === 'error') {
        message.error(info.file.error.message);
        this.fileList = []
      } else if (info.file.status === 'done') {
        this.fileList = []
        // 只保留最后一个文件
        fileList = fileList.slice(-1);
        // 更新文件状态
        fileList = fileList.map(file => {
          if (file.response) {
            // 上传完成后的处理
            message.success(file.response.message);
            file.url = this.getPath(this.courseForm.thumb);
          }
          return file;
        });
      }
      this.fileList = fileList;
    },
    handleSubmit(e) {
      this.$refs["courseForm"]
        .validateFields()
        .then(async () => {
          this.confirmLoading = true
          try {
            if (!this.currentId) {
              const res = await saveTrain(this.courseForm);
              console.log(res);
              if (res.code === 0) {
                this.init();
              }
            } else {
              const res = await updateTrain(this.courseForm);
              console.log(res);
              if (res.code === 0) {
                this.init();
              }
            }
            message.success("保存成功");
            this.handleClose();
            this.$emit("onOk");
          } catch (error) {
            console.log(error);
            message.success(error);
          } finally{
            this.confirmLoading = false
          }
        })
        .catch(() => {
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    handleEdit(pIndex, record, index) {
      this.modelType = "edit";
      this.currentIndex = pIndex;
      this.currentClassHourIndex = index;
      this.$refs.trainEditUploadFileModel.edit(record);
    },
    delByIds(pIndex, record, index) {
      this.courseForm.chapters[pIndex].children.splice(index, 1);
    },
    addClassHour(index) {
      this.modelType = "add";
      this.$refs.trainEditUploadFileModel.add();
      this.currentIndex = index;
    },
    setNewChildren(value) {
      value.sort = this.courseForm.chapters[this.currentIndex].children.length;
      this.courseForm.chapters[this.currentIndex].children.push(value);
    },
    updateChildren(value) {
      value.sort = this.currentClassHourIndex;
      this.courseForm.chapters[this.currentIndex].children.splice(
        this.currentClassHourIndex,
        1,
        value
      );
    },
    previewFile(value) {
      if (this.getUserId) {
        if (value.ftype === 1) {
          this.videoUrl = value.url;
          this.showVideo = true;
          return;
        }
        let typeName = "";
        switch (value.ftype) {
          case 2:
            typeName = "previewPdf";
            break;
          case 3:
            typeName = "previewExcel";
            break;
          case 4:
            typeName = "previewWord";
            break;
        }
        const routeData = this.$router.resolve({
          path: "/preview",
          query: {
            type: typeName,
            id: value.id,
          },
        });
        window.open(routeData.href, "_blank");
      } else {
        const routeData = this.$router.resolve({
          path: "/login",
        });
        window.open(routeData.href, "_blank");
      }
    },
    videoClose() {
      this.showVideo = false;
    },
    open(value) {
      if (value) {
        this.currentId = value;
        this.init();
      }
      this.newVisible = true;
    },
    handleClose() {
      this.currentId = null
      this.fileList = []
      this.courseForm = {
        isRequired: 0,
        isFineWork: 0,
        thumb: "",
        chapters: [
          {
            cname: "",
            children: [],
            sort: 0,
          },
        ],
      }
      this.$refs["courseForm"].resetFields();
      this.newVisible = false;
    },
  },
};
</script>

<style lang="less" scoped>
#trainEdit {
  background-color: #f3f5f9;
  min-width: 1000px;
  // background: url('../../../assets/portalClient/trainManage/editBg.png') no-repeat;
  // background-size: contain;
  .trainEditBody {
    width: 1000px;
    background: #fff;
    // margin: 40px auto;

    .title {
      width: 100%;
      height: 58px;
      // background: url('../../../assets/portalClient/trainManage/editTitle.png');
      padding: 0 25px;
      display: flex;
      align-items: center;

      span {
        font-size: 18px;
        font-family: PingFangSC-Medium, PingFang SC;
        font-weight: 500;
        color: #000;
      }
    }

    .item1,
    .item2 {
      width: 100%;
      height: fit-content;
      background: #ffffff;

      .item1-content {
        width: 100%;
        padding: 25px 80px 0;
      }
    }

    .item2 {
      margin-top: 30px;
    }

    .item2 {
      .title {
        display: flex;
        justify-content: space-between;
        align-items: center;
        .delete {
          font-size: 18px;
          transition: all 0.3s;
          color: rgb(245, 49, 49);
          &:hover {
            color: rgb(245, 12, 12);
            filter: drop-shadow(0 0 0.5px rgb(241, 33, 33));
          }
        }
      }

      .item2-content {
        width: 100%;
        padding: 25px 60px;

        .top {
          display: flex;
          width: 100%;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;

          .ant-row.ant-form-item {
            width: 415px;
            margin: 0;
          }

          .addClassHourBtn {
            border-radius: 0;
            background-color: #246df3;
            color: #fff;
            border: none;

            &:hover {
              filter: brightness(1.2);
            }
          }
        }

        .bottom {
        }
      }
    }

    .add {
      width: 100%;
      // height: 40px;
      text-align: center;
      color: #246df3;
      padding: 0px 60px 20px;
      background-color: #fff;

      .btn {
        width: 100%;
        height: 30px;
        line-height: 30px;
        border: 1px solid #246df3;
        cursor: pointer;
        transition: all 0.3s;

        &:hover {
          background-color: #246df3;
          color: #fff;
        }
      }
    }

    .save {
      width: 100%;
      height: 48px;
      margin-top: 30px;
      text-align: center;

      .btn {
        width: 222px;
        height: 100%;
        background-color: #246df3;
        color: #fff;
        border-radius: 0;

        &:hover {
          filter: brightness(1.2);
        }
      }
    }
  }
}

:deep(.ant-upload-list-picture-card-container) {
  width: 100%;
  height: 175px;

  .ant-upload-list-item.ant-upload-list-item-done.ant-upload-list-item-list-type-picture-card {
    width: 100%;
    height: 100%;
  }

  .ant-upload-list-item.ant-upload-list-item-uploading.ant-upload-list-item-list-type-picture-card {
    width: 100%;
    height: 100%;
    display: flex;
    background-color: #fff;
    align-items: center;
  }
  .ant-upload-list-item-thumbnail img {
    object-fit: cover;
  }
}

:deep(.ant-upload-picture-card-wrapper) {
  display: inline-block;
  height: 100%;
}

:deep(.ant-upload.ant-upload-select.ant-upload-select-picture-card) {
  width: 100%;
  height: 175px;
  margin: 0;
  background-color: #fff;
}
:deep(.ant-select-dropdown) {
  z-index: 98;
}
</style>
