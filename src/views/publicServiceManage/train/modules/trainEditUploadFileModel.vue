<template>
  <a-modal title="新增课时" style="top: 20px" :width="450" v-if="visible" v-model:visible="visible"
    :confirmLoading="confirmLoading" :keyboard="false" :closable="false" :maskClosable="false" :destroyOnClose="true">
    <template #footer>
      <a-button key="back" @click="closeUploadModal()"> 取 消 </a-button>
      <a-button type="primary" @click="submit"> 确 定 </a-button>
    </template>
    <div>
      <a-form ref="infoForm" :model="infoForm" layout="horizontal">
        <!-- <a-form-model-item style="display: none">
          <a-input v-decorator="['id']" />
        </a-form-model-item> -->
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="课时名称"
          :rules="{ required: true, message: '请输入课时名称' }" :name="'title'">
          <a-input placeholder="课时名称" :disabled="disabled" v-model:value="infoForm.title" />
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="上传附件"
          :rules="[{ required: true, message: '请上传正确的附件' }]" :name="'url'">
          <a-input placeholder="课时名称" v-model:value="infoForm.url" style="display: none" :disabled="disabled" />
          <a-form-item-rest>
            <a-upload name="file" :accept="fileType" :multiple="true" :beforeUpload="beforeUpload"
              :customRequest="customRequest" @change="uploadChange" :file-list="fileList">
              <a-button :disabled="disabled" style="margin-bottom: 10px">
                <upload-outlined />
                点击上传
              </a-button>
            </a-upload>
          </a-form-item-rest>
        </a-form-item>
      </a-form>
      <a-alert message="支持文件类型 （docx、xlsx、pdf、mp4）" banner />
      <a-alert message="上传资源请勿超过500MB" banner />
    </div>
  </a-modal>
</template>
<script>
import { uploadTResources } from "@/request/publicServiceManage/TResources";
import { UploadOutlined } from "@ant-design/icons-vue";
import { message } from "ant-design-vue";

export default {
  name: "trainEditUploadFileModel",
  props: {
    type: {
      type: String,
      default: "add",
    },
  },
  components: {
    UploadOutlined,
  },
  data() {
    return {
      confirmLoading: false,
      disabled: false,
      visible: false,
      fileType: ".docx,.xlsx,.pdf,.mp4",
      acceptType: ["xlsx", "docx", "pdf", "mp4"],
      uploadUrl: "",
      loading: false,
      uploadMap: new Map(),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      fileList: [
        {
          uid: "1",
          name: "xxx.png",
          status: "done",
          url: "http://www.baidu.com/xxx.png",
        },
      ],
      infoForm: {
        title: "",
        resources: {
          url: "",
        },
        url: null,
      },
      title: "",
    };
  },
  beforeCreate() { },
  created() { },
  methods: {
    add() {
      this.visible = true;
      this.infoForm = {
        title: "",
        resources: {
          url: "",
        },
        url: null,
      };
      this.fileList = [];
    },
    edit(value) {
      this.visible = true;
      this.infoForm = Object.assign({}, value);
      this.fileList = [
        {
          uid: value.resources.id,
          name: value.resources.fname,
          status: "done",
          url: value.resources.url,
        },
      ];
    },
    closeUploadModal() {
      if (this.uploadMap.size === 0) {
        this.visible = false;
      } else {
        this.$message.warning("上传未完成,无法关闭当前窗口");
      }
    },
    uploadChange(info) {
      console.log(info, 123123);
      let fileList = [...info.fileList];
      // 上传状态处理
      if (info.file.status === "error") {
        message.error(info.file.error.message);
        this.fileList = [];
      } else if (info.file.status === "uploading") {
        this.loading = true;
      } else if (info.file.status === "done") {
        this.fileList = [];
        // 只保留最后一个文件
        fileList = fileList.slice(-1);
        // message.success(info.file.response.message);
        fileList = fileList.map((file) => {
          if (file.response) {
            // 上传完成后的处理
            message.success(file.response.message);
            file.url = file.response.url;
          }
          return file;
        });
        this.$emit("ok");
      } else {
        this.infoForm.resources = {};
        this.fileList = [];
      }

      this.fileList = fileList;
    },
    beforeUpload(file, fileList) {
      this.resetForm();
    },
    /**
     * 自定义上传请求
     * @param {*} param
     */
    async customRequest({ onSuccess, onError, file, onProgress }) {
      new Promise((resolve, reject) => {
        // 校验文件名是否为空
        if (!file.name && file.name === "") {
          this.$message.error(`文件名不能为空!`);
          reject(`文件名不能为空!`);
          return;
        }
        // 获取文件扩展名
        const fix = file.name.split(".")[file.name.split(".").length - 1];
        // 校验文件类型是否符合要求
        const isAcceptType = this.typeMatch(fix);
        if (!isAcceptType) {
          reject(`文件类型限制为 docx、xlsx、pdf、mp4`);
          return;
        }

        if (file.size > 500 * 1024 * 1024) {
          reject("上传资源请勿超过500MB");
          return;
        }
        resolve();
      })
        .then(async () => {
          const formData = new FormData();
          formData.append("files", file);
          formData.append("categoriesId", this.categoriesId);
          formData.append("deptId", "1");
          try {
            const res = await uploadTResources({
              formData: formData,
              onUploadProgress: (ev) => {
                // ev.loaded 当前已上传内容的大小，ev.total - 本次上传请求内容总大小
                this.uploadMap.set(file.name, file.size);
                const percent = (ev.loaded / ev.total) * 100;
                // 计算出上传进度，调用组件进度条方法
                onProgress({ percent });
              },
            });
            if (res.data.code === 0 && res.data.data.url) {
              this.infoForm.resources = res.data.data;
              this.infoForm.url = res.data.data.url;
              onSuccess({ code: 0, message: `${file.name}上传成功` }, file);
            } else {
              onError({ code: 500, message: `${file.name}上传失败` }, file);
            }
            this.uploadMap.delete(file.name);
          } catch (error) {
            this.uploadMap.delete(file.name);
            onError({ code: 500, message: `${file.name}上传失败` }, file);
          }
        })
        .catch((err) => {
          onError({ code: 500, message: err }, file);
        });
    },
    typeMatch(fileType) {
      if (this.acceptType.includes(fileType)) {
        return true;
      }
      return false;
    },
    submit() {
      this.$refs.infoForm
        .validateFields()
        .then((value) => {
          this.confirmLoading = true;
          if (this.type === "edit") {
            this.$emit("updateChildren", this.infoForm);
          } else {
            this.$emit("setNewChildren", this.infoForm);
          }
          this.visible = false;
        })
        .catch(() => {
          console.log("error submit!!");
          return false;
        })
        .finally(() => {
          this.confirmLoading = false;
        });
    },
    async resetForm() {
      this.title = this.infoForm.title;
      await this.$refs.infoForm.resetFields();
      this.infoForm.title = this.title;
    },
  },
};
</script>
<style lang="less" scoped>
.ant-select.ant-select-enabled {
  width: 100%;
  margin-bottom: 20px;
}
</style>
