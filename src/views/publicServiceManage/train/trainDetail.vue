<template>
  <Header style="position: fixed;top: 0;left: 0;z-index: 999;"></Header>
  <div id="trainDetail">
    <div class="trainDetail">

      <!-- <portalHeader></portalHeader> -->
      <div class="banner">
        <!-- <img src="../../assets/portalClient/programmeDetail/banner.png" alt="" /> -->
        <div class="banner-content">
          <div class="left">
            <img :src="getPath(topData.thumb)" alt="" />
            <!-- <div class="tag">
              <span class="text_15" v-if="topData.isFineWork == 1">精选</span>
              <span class="lv" v-if="topData.isRequired == 1">必修</span>
            </div> -->
          </div>
          <div class="right">
            <p>{{ topData.title }}</p>
            <p :title="topData.shortDesc">{{ topData.shortDesc }}</p>
            <p
              class="lastTime"
              v-if="lastTimeData && lastTimeData.chapter && lastTimeData.hour"
            >
              上次学到:
              <span><strong>章节</strong> {{ lastTimeData.chapter }},</span
              ><span><strong>课时</strong> {{ lastTimeData.hour }}</span>
            </p>
            <p class="lastTime" v-else>暂无学习记录</p>
            <p class="last-p">
              <span>{{ topData.chapterNum || 0 }}<sub>个章节</sub></span>
              <!-- <span>{{ topData.durationNum || 0 }}<sub>个课时</sub></span> -->
              <span>{{ topData.likeNum || 0 }}<sub>个点赞</sub></span>
              <span>{{ topData.collectNum || 0 }}<sub>个收藏</sub></span>
            </p>
            <div style="position: absolute;right: 40px; bottom: 20px; cursor: pointer;font-size: 28px;text-align: right;" @click="handleCollect()">
              <star-filled style="color: #246cf2;" v-if="topData.collectFlag"/>
              <star-outlined style="color: #246cf2;" v-else/>
            </div>
          </div>
        </div>
      </div>
      <div class="trainDetailContent detailCollapse">
        <div class="title">课程章节</div>
        <a-collapse
          v-model="activeKey"
          expandIconPosition="right"
          v-if="data.length > 0"
        >
          <a-collapse-panel
            :header="`${(i + 1).toString().padStart(2, '0')}  ${item.title}`"
            v-for="(item, i) in data"
            :key="`${i + 1}`"
          >
            <div
              v-for="(iItem, ii) in item.children"
              :key="ii"
              class="detailCollapse-content"
            >
              <div class="left">
                <div class="title">{{ `${ii + 1}.${iItem.title}` }}</div>
                <div class="fileName" v-if="iItem.resources">
                  <img :src="fileList[`file${iItem.resources.ftype}`]" alt="" />
                  <span>{{ iItem.resources.fname }}</span>
                </div>
              </div>
              <div class="right">
                <span
                  class="showBtn"
                  v-if="iItem.resources && iItem.resources.ftype === 1"
                  @click="previewFile(iItem, iItem.resources)"
                  >点击观看</span
                >
                <span
                  class="showBtn"
                  v-else
                  @click="previewFile(iItem, iItem.resources)"
                  >点击查看</span
                >
              </div>
            </div>
          </a-collapse-panel>
        </a-collapse>
        <div class="noContent" v-else>
          <!-- <img src="../../assets/portalClient/noContent.png" alt="" /> -->
          <span>暂无内容</span>
        </div>
      </div>
    </div>
    <!-- <portalFooterDom></portalFooterDom> -->
    <videoDom v-if="showVideo" :url="videoUrl" @close="videoClose"></videoDom>
  </div>
</template>

<script>
import { getTree } from "@/request/publicServiceManage/TTrainCourseChapters";
import {
  getTTrainCourseItem,
  saveCourseHour,
  getCourseHour,
} from "@/request/publicServiceManage/TTrainCourse";
// import { mixinDevice } from "@/utils/mixin";
import { mapGetters } from "vuex";
import videoDom from "@/components/filesPreview/videoDom";
import Header from "@/components/Header";
import {
  StarOutlined,
  StarFilled
} from "@ant-design/icons-vue";
import { message } from "ant-design-vue";
import { saveHandleLike, removeHandleLike, saveHandleCollect, removeHandleCollect } from '@/request/publicServiceManage/tHandleLike'

export default {
  name: "trainDetail",
  components: {
    videoDom,
    Header,
    StarOutlined,
    StarFilled
  },
  // mixins: [mixinDevice],
  data() {
    return {
      currentCollect: false,
      activeKey: ["1"],
      videoUrl: "",
      showVideo: false,
      data: [],
      topData: {},
      fileList: {
        file1: require("../../../assets/portalClient/programmeDetail/file1.png"),
        file2: require("../../../assets/portalClient/programmeDetail/file2.png"),
        file3: require("../../../assets/portalClient/programmeDetail/file3.png"),
        file4: require("../../../assets/portalClient/programmeDetail/file4.png"),
      },
      lastTimeData: {},
    };
  },
  computed: {
    ...mapGetters(['getUserId'])
  },
  async created() {
    await this.init();
    await this.initLastTime();
    await this.initList();
  },
  mounted() {
    console.log(this.$route);
    if (this.$route.params.activeKey) {
      this.activeKey = [`${this.$route.params.activeKey}`];
    }
  },
  methods: {
    getPath: function (filePath) {
      return process.env.VUE_APP_File_URL + filePath;
    },
    async init() {
      const res = await getTTrainCourseItem(this.$route.params.id);
      if (res.data.code === 0) {
        this.topData = res.data.data;
      }
    },
    async initLastTime() {
      this.lastTimeData = null
      const res = await getCourseHour({ id: this.$route.params.id });
      if (res.data.code === 0 && res.data.data !== null) {
        this.lastTimeData = res.data.data;
      }
    },
    initList() {
      getTree({ trainCourseId: this.$route.params.id }).then((res) => {
        if (res.data.code === 0) {
          this.data = res.data.data;
        }
      });
    },
    async previewFile(item, value) {
      if (this.getUserId) {
        if (value.ftype === 1) {
          this.videoUrl = value.url;
          this.showVideo = true;
          await saveCourseHour({ id: item.id });
          return;
        }
        let typeName = "";
        switch (value.ftype) {
          case 2:
            typeName = "previewPdf";
            break;
          case 3:
            typeName = "previewExcel";
            break;
          case 4:
            typeName = "previewWord";
            break;
        }
        const routeData = this.$router.resolve({
          path: "/preview",
          query: {
            type: typeName,
            id: value.id,
          },
        });
        window.open(routeData.href, "_blank");
        await saveCourseHour({ id: item.id });
      } else {
        const routeData = this.$router.resolve({
          path: "/login",
        });
        window.open(routeData.href, "_blank");
      }
    },
    videoClose() {
      this.showVideo = false;
    },
    async handleCollect() {
      if (this.currentCollect) {
        return
      }
      this.currentCollect = true
      const param = { type: 3, tableId: this.topData.id }
      if (this.topData.collectFlag === 0 || !this.topData.collectFlag) {
        await saveHandleCollect(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('收藏成功')
            } else {
              message.error('系统错误，请稍后再试')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentCollect = false
          })
      } else {
        await removeHandleCollect(param)
          .then((res) => {
            if (res.data.code === 0) {
              message.success('取消收藏成功')
            } else {
              message.error('系统错误，请稍后再试')
            }
          })
          .catch(() => {
            message.error('系统错误，请稍后再试')
          }).finally(() => {
            this.currentCollect = false
          })
      }
      this.init();
    }
  },
};
</script>

<style lang="less" scoped>
#trainDetail {
  background-color: #f3f5f9;
  width: 100%;
  height: fit-content;
  // min-height: 100%;
  padding: 20px;
  padding-top: 80px;
  .trainDetail {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: center;
    width: 100%;
    min-height: calc(100vh - 105px);
    height: fit-content;
    background: #fff;
  }
  .banner {
    // position: relative;
    width: 1200px;
    height: auto;
    padding-top: 20px;
    margin-bottom: 54px;
    .banner-content {
      // position: absolute;
      // inset: 0;
      display: flex;
      align-items: center;
      width: 1200px;
      margin: auto;
      .left {
        width: 510px;
        height: 325px;
        background: #fff;
        border-radius: 5px;
        overflow: hidden;
        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
        .tag {
          position: absolute;
          top: 20%;
          left: 20px;
          width: 100%;
          span {
            display: inline-block;
            width: fit-content;
            height: 26px;
            line-height: 26px;
            background: linear-gradient(270deg, #ff6965 0%, #f33e3e 100%);
            border-radius: 4px;
            margin-right: 8px;
            padding: 0 10px;
            color: #fff;
            &.lv {
              background: linear-gradient(270deg, #24e3ff 0%, #0092ff 100%);
            }
          }
        }
      }
      .right {
        position: relative;
        flex: 1;
        margin-left: 30px;
        p {
          margin-bottom: 25px;
        }
        p:first-child {
          max-width: 600px;
          font-size: 38px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 600;
          color: #292e3d;
          line-height: 60px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        p:first-child + p {
          font-size: 18px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #292e3d;
          word-break: break-all;
          line-height: 1.5;
          overflow: hidden;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
        }
        .lastTime {
          display: flex;
          align-items: center;
          font-size: 16px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #292e3d;
          span {
            margin-left: 15px;
          }
          &::before {
            display: inline-block;
            width: 24px;
            height: 20px;
            background: url("../../../assets/portalClient/trainDetail/lastTime.png")
              no-repeat;
            background-size: contain;
            margin-right: 5px;
            content: "";
          }
        }
        .last-p {
          span {
            font-size: 26px;
            color: #292e3d;
            margin-right: 20px;
            sub {
              margin-left: 5px;
              font-size: 16px;
              bottom: 0.05em;
            }
          }
        }
      }
    }
  }
  .trainDetailContent {
    border-top: 10px solid #246cf2;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    // position: relative;
    // top: -55px;
    width: 1200px;
    min-height: 395px;
    margin: auto;
    height: auto;
    // background: #ffffff;
    // box-shadow: 0px 1px 8px 0px rgba(1, 43, 121, 0.08);
    // border-radius: 4px;
    padding: 30px;
    margin-bottom: 60px;
    .title {
      font-size: 30px;
      font-weight: 600;
      margin-bottom: 30px;
    }
    .noContent {
      margin: auto;
      padding: 20px 0 10px;
      display: flex;
      flex-direction: column;
      align-items: center;
      img {
        width: 240px;
      }
      span {
        font-size: 16px;
      }
    }
    .detailCollapse-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 50px;
      width: 100%;
      &:not(:last-child) {
        border-bottom: 1px solid #e2e2e2;
      }
      .left {
        .title {
          max-width: 660px;
          font-size: 18px;
          font-family: PingFangSC-Regular, PingFang SC;
          font-weight: 400;
          color: #292e3d;
          margin-bottom: 5px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .fileName {
          display: flex;
          align-items: center;
          img {
            width: 18px;
          }
          span {
            margin-left: 5px;
            font-size: 14px;
            font-family: PingFangSC-Regular, PingFang SC;
            font-weight: 400;
            color: #666666;
          }
        }
      }
      .right {
        .showBtn {
          display: flex;
          align-items: center;
          padding: 0 20px;
          width: auto;
          height: 36px;
          border-radius: 18px;
          border: 1px solid #2468f3;
          color: #2468f3;
          cursor: pointer;
          transition: all 0.3s;
          &:hover {
            background: #2468f3;
            color: #fff;
          }
        }
      }
    }
  }
}

:deep(.ant-collapse.ant-collapse-icon-position-right) {
  width: 100%;
  border: none;
  background: none;
  .ant-collapse-header, .ant-collapse-content, .ant-collapse-item, .ant-collapse-content-box {
    border: none;
  }
  .ant-collapse-item {
    margin-bottom: 15px;
    .ant-collapse-header {
      background: #f5f6fa;
      font-size: 18px;
      .anticon.anticon-right.ant-collapse-arrow {
        font-size: 18px;
      }
    }
    .ant-collapse-content.ant-collapse-content-active {
      .ant-collapse-content-box:not(:first-child) {
        border-top: 1px solid #e6e6e6;
      }
    }
  }
}
</style>
