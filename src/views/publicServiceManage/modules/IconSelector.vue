<template>
  <a-modal
    style="top: 20px"
    :width="900"
    :ok-button-props="{ style: { display: 'none' } }"
    :bodyStyle="{ height: '66vh',padding: 30 }"
    v-model:visible="visible">
    <div>
      <h2>图标选择</h2>
      <div class="icon-list">
        <a
          v-for="(icon, index) in iconList"
          :key="index"
          class="icon-item"
          @click="selectIcon(index)"
        >
          <img :src="icon" alt="Icon"/>
        </a>
      </div>
    </div>
  </a-modal>
</template>

<script>
export default {
  name: 'iconSelector',
  data() {
    return {
      iconList: Array.from({ length: 17 }, (_, index) => require(`@/assets/portalClient/icon/icon${index + 1}.png`)),
      visible: false
    }
  },
  methods: {
    selectIcon(index) {
      this.$emit('iconSelected', index)
      this.visible = false
    },
    handlerSelectIcon() {
      this.visible = true
    }
  }
}
</script>

<style scoped>
.icon-list {
  display: flex;
  flex-wrap: wrap;
}
.icon-item:hover{
  border: 2px solid #14bee8;
}
.icon-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80px;
  height: 80px;
  margin: 10px;
  border: 1px solid #ccc;
  cursor: pointer;
}

.icon-item img {
  max-width: 100%;
  max-height: 100%;
}

</style>
