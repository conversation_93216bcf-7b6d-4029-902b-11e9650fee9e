<template>
  <a-modal
    title="提交审核"
    style="top: 20px"
    :width="450"
    v-model:visible="visible"
    :destroyOnClose="true"
    :confirmLoading="confirmLoading"
    @cancel="handleClose"
    @ok="handleSubmit"
  >
    <a-form ref="form" :model="form">
      <a-form-item label="审核人" :rules="[{ required: true, message: '请选择审核人' }]" prop="userId">
        <a-select
          v-model:value="form.userId"
        >
          <a-select-option v-for="info in userInfo" :key="info.id.toString()" :value="info.id">{{
            info.userName
          }}</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
// import { auditTTrainCourse, saveTTrainCourse } from '@/request/publicServiceManage/TTrainCourse'
// import pick from 'lodash.pick'
// import { getDictArray } from '@/utils/dict'
// import { saveAuditLog } from '@/request/publicServiceManage/AuditLog'
// import { sendAuditMsg } from '@/request/publicServiceManage/MsgLog'
import { selectListUserByRole } from '@/request/publicServiceManage/system'
import { message } from 'ant-design-vue';
import { saveTProductSolutions } from '@/request/publicServiceManage/TProductSolutions'
import { saveTTrainCourse } from '@/request/publicServiceManage/TTrainCourse'

export default {
  name: 'SubmitModal',
  components: {},
  props: {
    type: {
      type: String
    }
  },
  data() {
    return {
      visible: false,
      userInfo: [],
      confirmLoading: false,
      mdl: {},
      form: {
        userId: null
      },
      record: {}
    }
  },
  created() {
    selectListUserByRole({ roleKey: '999' }).then((res) => {
      if (res.data.code !== '00000') {
        message.error(res.data.msg)
      }
      this.userInfo = res.data.data
    })
  },
  methods: {
    add(record) {
      this.edit({ id: 0 })
      this.record = record
    },
    edit(record) {
      console.log(4546666)
      this.mdl = Object.assign({}, record)
      this.visible = true
    },
    handleClose() {
      this.$refs['form'].resetFields()
      this.visible = false
    },
    handleSubmit() {
      this.$refs.form.validateFields().then((value) => {
        this.confirmLoading = true
        if (this.type === 'programme') {
          const parameter = { auditStatus: 1, id: this.mdl.id, auditId: value.userId }
          saveTProductSolutions(parameter)
            .then((res) => {
              if (res.data.code === 0) {
                message.success('提交审核成功')
                this.$emit('onOk')
                this.handleClose()
              } else {
                message.error(res.data.msg)
              }
            })
            .catch(() => {
              message.error('系统错误，请稍后再试')
            })
            .finally(() => {
              this.confirmLoading = false
            })
        } else {
          const parameter = { auditStatus: 1, id: this.mdl.id, auditUserId: value.userId }
          saveTTrainCourse(parameter)
            .then((res) => {
              if (res.data.code === 0) {
                message.success('提交审核成功')
                this.$emit('onOk')
                this.handleClose()
              } else {
                message.error(res.data.msg)
              }
            })
            .catch(() => {
              message.error('系统错误，请稍后再试')
            })
            .finally(() => {
              this.confirmLoading = false
            })
        }
      }).catch(() => {
        console.log('error submit!!')
        return false
      })
    }
  },
  watch: {
    /*
              'selectedRows': function (selectedRows) {
                this.needTotalList = this.needTotalList.map(item => {
                  return {
                    ...item,
                    total: selectedRows.reduce( (sum, val) => {
                      return sum + val[item.dataIndex]
                    }, 0)
                  }
                })
              }
              */
  }
}
</script>
<style lang="less">
.ant-modal-wrap.approveUser {
  .ant-modal-header {
    // background: url('~@/assets/portalClient/dialogHeaderBg.png') no-repeat;
    background-size: 100% 100%;
    .ant-modal-title {
      color: #fff;
      font-size: 18px;
      font-weight: 600;
    }
  }
  .ant-modal-close {
    color: #fff;
  }
  .ant-modal-body {
    padding: 30px;

    .ant-form.ant-form-horizontal {
      padding: 30px;

      .ant-row {
        .modal-divider-text {
          // border-bottom: 1px solid #d9d9d9;
          padding: 0 36px;
          font-weight: 700;
        }

        .ant-input-number {
          width: 100%;
        }

        .ant-col.left,
        .ant-col.text {
          .ant-form-item {
            // .ant-form-item-label {
            //   width: 172px;
            // }
          }
        }

        .ant-col.text {
          .ant-form-item {
            .ant-form-item-control-wrapper {
              padding-right: 22px !important;
              width: calc(100% - 172px);
            }
          }
        }

        .ant-col.right {
          padding-right: 0px !important;

          .ant-form-item {
            .ant-form-item-label {
              width: 150px;
            }

            .ant-calendar-picker {
              width: 100%;
            }
          }
        }
      }
    }
  }

  .ant-modal-footer {
    .ant-btn {
      border-radius: 4px;
    }
  }
}
</style>
