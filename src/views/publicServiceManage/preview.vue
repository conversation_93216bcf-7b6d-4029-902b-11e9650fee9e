<template>
  <div>
    <component :is="currentComponent" :url="url"></component>
  </div>
</template>

<script>
import previewWord from '@/components/filesPreview/docx.vue'
import previewExcel from '@/components/filesPreview/xlsx.vue'
import previewPdf from '@/components/filesPreview/pdf.vue'
import { getTResourcesList } from '@/request/publicServiceManage/TResources'

export default {
  components: {
    previewWord,
    previewExcel,
    previewPdf
  },
  data() {
    return {
      currentComponent: '',
      url: ''
    }
  },
  async created() {
    await this.init(this.$route.query.id)
  },
  mounted() {
    console.log(this.$route.query.type, 'dsdasdasds')
    this.$nextTick(() => {
      this.currentComponent = this.$route.query.type
    })
  },
  methods: {
    async init(id) {
      const res = await getTResourcesList({ id: id })
      if (res.data.code === 0) {
        this.url = res.data.rows[0].url
      }
    }
  }
}
</script>

<style lang="scss" scoped></style>
