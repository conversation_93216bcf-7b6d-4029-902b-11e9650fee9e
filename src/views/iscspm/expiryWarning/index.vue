<template>
    <div class="page">
      <div class="count">
        <div class="left">
          <span>未备案</span>
          <span>{{ countData.not || 0 }}</span>
        </div>
        <div class="center">
          <div class="chartItem item1">
            <span>{{ countData.year || 0 }}</span>
            <span>6-12个月</span>
          </div>
          <div class="chartItem item2">
            <span>{{ countData.hYear || 0 }}</span>
            <span>3-6个月</span>
          </div>
          <div class="chartItem item3">
            <span>{{ countData.threeMonth || 0 }}</span>
            <span>1-3个月</span>
          </div>
          <div class="chartItem item4">
            <span>{{ countData.month || 0 }}</span>
            <span>1个月内</span>
          </div>
        </div>
        <div class="right">
          <span>已备案</span>
          <span>{{ countData.have || 0 }}</span>
        </div>
      </div>
      <!-- 搜索条件栏 -->
      <div class="page-search" id="datalist_search">
        <div class="page-search-item">
          <div class="page-search-item-label">系统名称：</div>
          <div class="page-search-item-value" style="display: flex">
            <a-input
              v-model:value.trim="searchData.systemName"
              placeholder="请输入系统名称"
              allow-clear
              v-emoji
            />
          </div>
        </div>
        <div class="page-search-item">
          <div class="page-search-item-label">公司名称：</div>
          <div class="page-search-item-value" style="display: flex">
            <a-input
              v-model:value.trim="searchData.deptName"
              placeholder="请输入公司名称"
              allow-clear
              v-emoji
            />
          </div>
        </div>
        <div class="page-search-item">
          <div class="page-search-item-label">等保等级：</div>
          <div class="page-search-item-value" style="display: flex">
            <a-select placeholder="等保等级" allowClear v-model:value="searchData.protectionLevel" style="width: 100%;">
                <a-select-option v-for="(b, index) in protectionArr" :key="index" :value="b.value">{{
                  b.label
                }}</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="page-search-item">
          <div class="page-search-item-label">联系人：</div>
          <div class="page-search-item-value" style="display: flex">
            <a-input
              v-model:value.trim="searchData.contactPerson"
              placeholder="请输入联系人"
              allow-clear
              v-emoji
            />
          </div>
        </div>
        <div class="page-search-item">
          <div class="page-search-item-label">联系电话：</div>
          <div class="page-search-item-value" style="display: flex">
            <a-input
              v-model:value.trim="searchData.contactPhone"
              placeholder="请输入联系电话"
              allow-clear
              v-emoji
            />
          </div>
        </div>
        <div class="page-search-item">
          <div class="page-search-item-label">备案状态：</div>
          <div class="page-search-item-value" style="display: flex">
            <a-select placeholder="备案状态" allowClear v-model:value="searchData.expireState" style="width: 100%;">
              <a-select-option v-for="(b, index) in expireArr" :key="index" :value="b.value">{{
                b.label
              }}</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="page-search-item">
          <div class="page-search-item-label">备案地点：</div>
          <div class="page-search-item-value" style="display: flex">
            <a-input
              v-model:value.trim="searchData.filingAddr"
              placeholder="请输入备案地点"
              allow-clear
              v-emoji
            />
          </div>
        </div>
        <div class="page-search-item">
          <div class="page-search-item-label">系统类型：</div>
          <div class="page-search-item-value" style="display: flex">
            <a-select placeholder="系统类型" allowClear v-model:value="searchData.systemType" style="width: 100%;">
              <a-select-option value="1">内部</a-select-option>
              <a-select-option value="2">外部</a-select-option>
            </a-select>
          </div>
        </div>
        <div class="page-search-item">
          <div class="page-search-item-label">到期天数：</div>
          <div class="page-search-item-value" style="display: flex">
            <a-select placeholder="系统类型" allowClear v-model:value="searchData.expireDayRange" style="width: 100%;">
              <a-select-option v-for="(b, index) in expireDayArr" :key="index" :value="b.value">{{
                b.label
              }}</a-select-option>
            </a-select>
          </div>
        </div>

        <div class="page-search-buttons">
          <a-button type="primary" @click="beforeSearch(true)">
            <template #icon>
              <SearchOutlined />
            </template>
            查询
          </a-button>
          <a-button @click="handleReset(true)" style="margin-left: 22px">
            重置
          </a-button>
        </div>
      </div>
      <!-- 表格展示栏 -->
      <div class="page-content" :style="{ minHeight: flexHeight }">
        <!-- 工具栏 -->
        <a-row>
          <a-col :span="24">
            <a-table
              style="padding-top: 10px; padding-left: 10px; padding-right: 10px"
              :columns="tableColumns"
              :data-source="tableData"
              :loading="tableLoading"
              :pagination="pagination"
              :scroll="{ y: tableHeight }"
              @change="handleTableChange"
            >
              <!-- #00ff00 -->
              <template #num="{ index }">
                <span>{{
                  (pagination.current - 1) * pagination.pageSize +
                  Number(index) +
                  1
                }}</span>
              </template>
              <template #systemType="{ text }">
                {{ switchValue(text, 'systemType') }}
              </template>
              <template #protectionLevel="{ text }" >
                <span :class="`protectionLevel_${text}`" style="display: inline-block;width: 70px;text-align: left;">
                  <img v-if="text === '0'" src="../../../assets/portalClient/protectionLevel_0.png" alt="" width="16" style="padding-bottom:1px">
                  <img v-if="text === '1'" src="../../../assets/portalClient/protectionLevel_1.png" alt="" width="16" style="padding-bottom:1px">
                  <img v-if="text === '2'" src="../../../assets/portalClient/protectionLevel_2.png" alt="" width="16" style="padding-bottom:1px">
                  <img v-if="text === '3'" src="../../../assets/portalClient/protectionLevel_3.png" alt="" width="16" style="padding-bottom:1px">
                  <img v-if="text === '4'" src="../../../assets/portalClient/protectionLevel_4.png" alt="" width="16" style="padding-bottom:1px">
                  {{ switchValue(text, 'protectionLevel') }}
                </span>
              </template>
              <template #expireState="{ text }">
                <span :class="`expireState_${text}`" style="display: inline-block;width: 70px;text-align: left;">
                  <img v-if="text === '1'" src="../../../assets/portalClient/expireState1.png" alt="" width="16" style="padding-bottom:1px">
                  <img v-if="text === '2'" src="../../../assets/portalClient/expireState2.png" alt="" width="16" style="padding-bottom:1px">
                  <img v-if="text === '3'" src="../../../assets/portalClient/expireState3.png" alt="" width="16" style="padding-bottom:1px">
                  <img v-if="text === '4'" src="../../../assets/portalClient/expireState4.png" alt="" width="16" style="padding-bottom:1px">
                  {{ switchValue(text, 'expireState') }}
                </span>
              </template>
              <template #action="{ record }">
                <a-button
                    type="link"
                    style="color: #387ff1"
                    @click="handleDetail(record)"
                >
                  查看
                </a-button>
              </template>
            </a-table>
          </a-col>
        </a-row>
      </div>
      <!-- 导入框 -->
      <SystemRecordDetail ref="detail" />
    </div>
  </template>

  <script>
  import {
    InboxOutlined,
    UploadOutlined,
    SearchOutlined,
  } from "@ant-design/icons-vue";
  // import { delTClassifiedProtectionInfo, getTClassifiedProtectionInfoList } from '@/request/iscspm/TClassifiedProtectionInfo'
  import { getDictArray } from '@/utils/dict'
  import { getTClassifiedProtectionInfoList, getcountProtection } from '@/request/iscspm/TClassifiedProtectionInfo.js'
  import dayjs from "dayjs";
  import { Modal, message } from "ant-design-vue";
  import SystemRecordDetail from "@/views/iscspm/filingInfo/modules/SystemRecordDetail.vue";

  export default {
    components: {SystemRecordDetail, InboxOutlined, UploadOutlined, SearchOutlined },
    data() {
      return {
        countData: {},
        expireDayArr: [
        {
          value: '10-20',
          label: '10-20'
        },
        {
          value: '30-50',
          label: '30-50'
        },
        {
          value: '120-180',
          label: '120-180'
        },
        {
          value: '180-365',
          label: '180-365'
        }
      ],
        tableLoading: false,
        flexHeight: "0px",
        tableHeight: 0,
        searchData: {},
        lastsearchData: {},
        tableColumns: [
          {
            title: "序号",
            dataIndex: "num",
            width: "5%",
            align: "center",
            slots: {
              customRender: "num",
            },
          },
          {
            title: '系统名称',
            dataIndex: 'systemName',
            ellipsis: true,
            align: "center",
            width: '180px'
          },
          {
            title: '公司名称',
            dataIndex: 'deptName',
            ellipsis: true,
            align: "center",
          },
          {
            title: '系统类型',
            dataIndex: 'systemType',
            align: "center",
            slots: { customRender: 'systemType' }
          },
          {
            title: '等保等级',
            dataIndex: 'protectionLevel',
            align: "center",
            slots: { customRender: 'protectionLevel' },
            width: '135px'
            // sorter: (a, b) => { console.log(4564456); return a.protectionLevel - b.protectionLevel }
          },
          {
            title: '备案时间',
            align: "center",
            dataIndex: 'filingTime',
            width: '110px'
          },
          {
            title: '备案地点',
            dataIndex: 'filingAddr',
            align: "center",
            ellipsis: true,
            width: '120px'
          },
          {
            title: '联系人',
            align: "center",
            dataIndex: 'contactPerson'
          },
          {
            title: '联系电话',
            align: "center",
            dataIndex: 'contactPhone'
          },
          {
            title: '备案状态',
            dataIndex: 'expireState',
            align: "center",
            slots: { customRender: 'expireState' }
          },
          {
            title: '到期时间',
            dataIndex: 'expireDate',
            align: "center",
            width: '110px'
            // sorter: (a, b) => new Date(a.expireDate) - new Date(b.expireDate)
          },
          {
            title: '距到期限天数',
            dataIndex: 'expireDay',
            align: "center",
            width: '130px'
            // sorter: (a, b) => a.expireDay - b.expireDay
          },
          {
            title: '操作',
            dataIndex: 'action',
            align: 'center',
            slots: { customRender: "action" },
          }
        ],
        tableData: [],
        pagination: {
          current: 1,
          pageSize: 10,
          total: 0,
          showTotal: (total) => `共${total}项`,
          showQuickJumper: true,
          showSizeChanger: true,
          pageSizeOptions: ["10", "20", "50"],
        },
        expireMap: {},
        expireArr: [],
        protectionArr: [],
        protectionMap: {},
        systemTypeArr: [],
        systemTypeMap: {}
      };
    },
    computed: {
      searchHeight() {
        return document.getElementById("datalist_search").offsetHeight;
      },
    },
    async created() {
      const expireArr = await getDictArray('t_expire_state')
      this.expireArr = expireArr.map((d) => {
        this.expireMap[d.dictValue] = { text: d.dictLabel }
        return {
          value: d.dictValue,
          label: d.dictLabel
        }
      })
      const protectionArr = await getDictArray('t_protection_level')
      this.protectionArr = protectionArr.map((d) => {
        this.protectionMap[d.dictValue] = { text: d.dictLabel }
        return {
          value: d.dictValue,
          label: d.dictLabel
        }
      })
      const arr = await getDictArray('t_system_type')
      this.systemTypeArr = arr.map((d) => {
        this.systemTypeMap[d.dictValue] = { text: d.dictLabel }
        return {
          value: d.dictValue,
          label: d.dictLabel
        }
      })
      this.initProtection()
      this.handleSearch();
    },
    mounted() {
      setTimeout(() => {
        this.flexHeight = `calc(100vh - ${this.searchHeight}px - 292px)`;
        this.tableHeight =
        (window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight) -
        336 -
        this.searchHeight;
      }, 100);
    },
    methods: {
      switchValue(value, key) {
        if (key === 'protectionLevel') {
          return this.protectionMap[value]?.text
        } else if (key === 'expireState') {
          console.log(this.expireMap[value], value)
          return value ? this.expireMap[value]?.text : '--'
        } else if (key === 'systemType') {
          return value ? this.systemTypeMap[value]?.text : '--'
        }
      },
      handleDetail(record) {
        this.$refs.detail.open(record);
      },
      async initProtection() {
        const res = await getcountProtection({ systemType: this.systemType })
        console.log(res)
        if (res.data.code === 0) {
          this.countData = res.data.data
        }
      },
      beforeSearch(flag) {
        this.lastsearchData = { ...this.searchData };
        this.handleSearch(flag);
      },
      handleSearch(flag) {
        if (flag) {
          // 如果是点击查询按钮，需要从第一页开始查询
          this.pagination.current = 1;
        }
        const params = {
          ...this.lastsearchData,
          pageSize: this.pagination.pageSize,
          pageNum: this.pagination.current,
        };
        this.tableLoading = true;
        getTClassifiedProtectionInfoList(params)
          .then((res) => {
            if (res.data.code === 0) {
              this.tableData = [];
              this.tableData = res.data.rows.map(item => {
                item.filingTime = item.filingTime && dayjs(item.filingTime).format('YYYY-MM-DD')
                item.expireDate = item.expireDate && dayjs(item.expireDate).format('YYYY-MM-DD')
                return item
              })
              this.pagination.total = res.data.total;
            } else {
              message.error(res.data.msg);
            }
            this.tableLoading = false;
          })
          .catch((error) => {
            message.error(error.msg);
            this.tableLoading = false;
          });
      },
      handleTableChange(pagination) {
        this.searchData.pageNum = pagination.current;
        this.searchData.pageSize = pagination.pageSize;
        this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
        this.handleSearch();
      },
      handleReset() {
        this.searchData = {};
        this.lastsearchData = {};
        this.handleSearch(true);
      },
    },
  };
  </script>

  <style scoped lang='less'>
  .count {
    display: flex;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    height: 137px;
    border-radius: 8px;
    overflow: hidden;
    margin-bottom: 15px;
    .left,.right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 400px;
      height: 100%;
      background: #fff;
      border-radius: 8px;
      span {
        font-weight: 600;
        &:first-child {
          width: 40%;
          text-align: center;
          font-size: 22px;
        }
        &:last-child {
          width: 60%;
          text-align: right;
          padding-right: 30px;
          font-size: 46px;
          color: #0f79e9;
        }
      }
    }
    .center {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: calc(100% - 400px - 400px - 15px - 15px);
      height: 100%;
      .chartItem {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        width: 247px;
        height: 100%;
        border-radius: 8px;
      }
      .item1,.item2,.item3,.item4 {
        line-height: 24px;
        font-weight: 600;
        span:first-child {
          font-size: 30px;
          margin-top: 10px;
        }
        span:last-child {
          margin-top: 20px;
          font-size: 16px;
          color: #000;
        }
    }
    .item1 {
      border: 1px solid #0075fe;
      background: linear-gradient(to bottom, #81bbfd3b, #fff);
      color: #0075fe;
    }
    .item2 {
      border: 1px solid #1fc59f;
      color: #1fc59f;
      background: linear-gradient(to bottom, #82ffe23b, #fff);
    }
    .item3 {
      border: 1px solid #ff6d05;
      color: #ff6d05;
      background: linear-gradient(to bottom, #fcb37e3b, #fff);
    }
    .item4 {
      border: 1px solid #ff0000;
      color: #ff0000;
      background: linear-gradient(to bottom, #fd82823b, #fff);
    }
    }
  }
  .ant-card {
    margin-bottom: 1px;
  }
  </style>
