<template>
  <a-modal
      title="系统备案详情"
      style="top: 20px"
      :width="800"
      v-model:visible="visible"
      :footer="null"
      :destroyOnClose="true"
  >
    <template #footer>
      <a-button key="submit" type="primary" @click="handleClose">关闭</a-button>
    </template>
    <a-descriptions :column="2" >
      <!-- 第一列 -->

        <!-- 系统信息 -->
        <a-descriptions-item label="系统名称" :span="2">
          {{ data.systemName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="公司名称">
          {{ data.deptName || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="系统类型">
          {{ getDictLabel(systemTypeDict, data.systemType) }}
        </a-descriptions-item>
        <a-descriptions-item label="等保等级">
          {{ getDictLabel(protectionLevelDict, data.protectionLevel) }}
        </a-descriptions-item>


      <!-- 第二列 -->

        <!-- 备案信息 -->
        <a-descriptions-item label="备案时间" :span="2">
          {{ formatDate(data.filingTime) }}
        </a-descriptions-item>
        <a-descriptions-item label="备案地点">
          {{ data.filingAddr || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="联系人">
          {{ data.contactPerson || '-' }}
        </a-descriptions-item>
        <a-descriptions-item label="联系电话">
          {{ data.contactPhone }}
        </a-descriptions-item>


      <!-- 状态信息 -->
      <a-descriptions-item label="备案状态" :span="2">
        <a-tag :color="getStatusColor(data.expireState)">
          {{ getDictLabel(statusDict, data.expireState) }}
        </a-tag>
      </a-descriptions-item>
      <a-descriptions-item label="到期时间">
        {{ formatDate(data.expireDate) }}
      </a-descriptions-item>
      <a-descriptions-item label="剩余天数">
        <span :style="{ color: data.expireDay > 0 ? '#52c41a' : '#ff4d4f' }">
          {{ data.expireDay || 0 }} 天
        </span>
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
import { getDictArray } from '@/utils/dict'
import dayjs from "dayjs";

export default {
  name: "SystemRecordDetail",
  data() {
    return {
      visible: false,
      data: {},
      // 字典数据示例（需根据实际字典配置）
      systemTypeDict: [],
      protectionLevelDict: [],
      statusDict: []
    }
  },
  async created() {
    // 初始化字典
    this.systemTypeDict = await getDictArray('t_system_type')
    this.protectionLevelDict = await getDictArray('t_protection_level')
    this.statusDict = await getDictArray('t_expire_state')
  },
  methods: {
    // 打开弹窗
    open(record) {
      this.data = { ...record }
      this.visible = true
    },

    // 字典值转标签
    getDictLabel(dict, value) {
      console.log(dict)
      console.log(value)
      const item = dict.find(d => d.dictValue === value)
      return item?.dictLabel || '-'
    },

    // 状态颜色映射
    getStatusColor(status) {
      const colorMap = {
        '1': 'green',   // 正常
        '2': 'orange',  // 即将到期
        '3': 'red'      // 已过期
      }
      return colorMap[status] || 'default'
    },

    // 日期格式化
    formatDate(date) {
      return date ? dayjs(date).format('YYYY-MM-DD') : '-'
    },

    // 电话脱敏
    formatPhone(phone) {
      return phone?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') || '-'
    }
  }
}
</script>

<style scoped>
/* 两列布局样式 */
.ant-descriptions-item-content {
  min-width: 160px;
  padding: 12px 24px;
}
.ant-descriptions-item-label {
  width: 100px;
  text-align: center;
  background: #fafafa;
}
</style>
