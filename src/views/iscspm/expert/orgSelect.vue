<template>
  <div class="page">
    <!-- 搜索条件栏 -->
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">机构名称：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            v-model:value.trim="searchData.organizationName"
            placeholder="请输入机构名称"
            allow-clear
            v-emoji
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">地址：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            v-model:value.trim="searchData.address"
            placeholder="请输入地址"
            allow-clear
            v-emoji
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">联系人：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            v-model:value.trim="searchData.contactPerson"
            placeholder="请输入联系人"
            allow-clear
            v-emoji
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">联系电话：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            v-model:value.trim="searchData.contactPhone"
            placeholder="请输入联系电话"
            allow-clear
            v-emoji
          />
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch(true)">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="handleReset(true)" style="margin-left: 22px">
          重置
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <!-- 工具栏 -->
      <a-row>
        <a-col :span="24">
          <a-table
            style="padding-top: 10px; padding-left: 10px; padding-right: 10px"
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
            <!-- #00ff00 -->
            <template #num="{ index }">
              <span>{{
                (pagination.current - 1) * pagination.pageSize +
                Number(index) +
                1
              }}</span>
            </template>
            <template #action="{ record }">
              <a-button
                  type="link"
                  style="color: #387ff1"
                  @click="handleDetail(record)"
              >
                查看
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    <!-- 导入框 -->
    <OrgView ref="detail" />
  </div>
</template>

<script>
import { getDictArray } from "@/utils/dict";
import { getTClassifiedProtectionOrganizationList } from "@/request/iscspm/TClassifiedProtectionOrganization";
import {
  InboxOutlined,
  UploadOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";
import dayjs from "dayjs";
import { Modal, message } from "ant-design-vue";
import OrgView from "@/views/iscspm/expert/modules/orgView.vue";

export default {
  components: {OrgView, InboxOutlined, UploadOutlined, SearchOutlined },
  data() {
    return {
      tableLoading: false,
      flexHeight: "0px",
      tableHeight: 0,
      searchData: {},
      lastsearchData: {},
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: "机构名称",
          dataIndex: "organizationName",
          ellipsis: true,
          // width: '240px'
        },
        {
          title: "地址",
          dataIndex: "address",
          ellipsis: true,
          // width: '180px'
        },
        {
          title: "联系人",
          dataIndex: "contactPerson",
          // ellipsis: true
        },
        {
          title: "联系电话",
          dataIndex: "contactPhone",
          // width: '180px'
        },
        {
          title: "联系邮箱",
          dataIndex: "contactEmail",
          // width: '200px'
        },
        {
          title: '操作',
          width: '100px',
          dataIndex: 'action',
          align: 'center',
          slots: { customRender: "action" },
        }
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
    };
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  mounted() {
    this.handleSearch();
    setTimeout(() => {
      this.flexHeight = `calc(100vh - ${this.searchHeight}px - 180px)`;
      this.tableHeight =
        (window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight) -
        378 -
        this.searchHeight;
    }, 100);
  },
  methods: {
    beforeSearch(flag) {
      this.lastsearchData = { ...this.searchData };
      this.handleSearch(flag);
    },
    handleDetail(record) {
      this.$refs.detail.open(record);
    },
    handleSearch(flag) {
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
      }
      const params = {
        ...this.lastsearchData,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
      };
      this.tableLoading = true;
      getTClassifiedProtectionOrganizationList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            this.tableData = res.data.rows;
            this.pagination.total = res.data.total;
          } else {
            message.error(res.data.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
    },
    handleTableChange(pagination) {
      this.searchData.pageNum = pagination.current;
      this.searchData.pageSize = pagination.pageSize;
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.searchData = {};
      this.lastsearchData = {};
      this.handleSearch(true);
    },
  },
};
</script>

<style scoped lang='less'>
.ant-card {
  margin-bottom: 1px;
}
</style>
