<template>
  <div class="page">
    <!-- 搜索条件栏 -->
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">姓名：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            v-model:value.trim="searchData.name"
            placeholder="请输入姓名"
            allow-clear
            v-emoji
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">机构名称：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            v-model:value.trim="searchData.orgName"
            placeholder="请输入机构名称"
            allow-clear
            v-emoji
          />
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch(true)">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="handleReset(true)" style="margin-left: 22px">
          重置
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <!-- 工具栏 -->
      <a-row>
        <a-col :span="24">
          <a-table
            style="padding-top: 10px; padding-left: 10px; padding-right: 10px"
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
            <!-- #00ff00 -->
            <template #num="{ index }">
              <span>{{
                (pagination.current - 1) * pagination.pageSize +
                Number(index) +
                1
              }}</span>
            </template>
            <template #action="{ record }">
              <a-button
                  type="link"
                  style="color: #387ff1"
                  @click="handleDetail(record)"
              >
                查看
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    <ExpertView ref="detail" />
    <!-- 导入框 -->
  </div>
</template>

<script>
import {
  InboxOutlined,
  UploadOutlined,
  SearchOutlined,
} from "@ant-design/icons-vue";
// import { delTClassifiedProtectionInfo, getTClassifiedProtectionInfoList } from '@/request/iscspm/TClassifiedProtectionInfo'
import { getDictArray } from '@/utils/dict';
import { getTClassifiedProtectionExpertList } from '@/request/iscspm/TClassifiedProtectionExpert'
import dayjs from "dayjs";
import { Modal, message } from "ant-design-vue";
import ExpertView from "@/views/iscspm/expert/modules/expertView.vue";

export default {
  components: { InboxOutlined, UploadOutlined, SearchOutlined,ExpertView},
  data() {
    return {
      tableLoading: false,
      flexHeight: "0px",
      tableHeight: 0,
      searchData: {},
      lastsearchData: {},
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: '姓名',
          dataIndex: 'name'
        },
        {
          title: '联系电话',
          dataIndex: 'phone'
        },
        {
          title: '机构名称',
          dataIndex: 'orgName',
          slots: { customRender: 'orgName' }
        },
        {
          title: '备注',
          dataIndex: 'remark',
          ellipsis: true
        },
        {
          title: '操作',
          width: '100px',
          dataIndex: 'action',
          align: 'center',
          slots: { customRender: "action" },
        }
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
    };
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  mounted() {
    this.handleSearch();
    setTimeout(() => {
      this.flexHeight = `calc(100vh - ${this.searchHeight}px - 180px)`;
      this.tableHeight =
        (window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight) -
        378 -
        this.searchHeight;
    }, 100);
  },
  methods: {
    beforeSearch(flag) {
      this.lastsearchData = { ...this.searchData };
      this.handleSearch(flag);
    },
    handleDetail(record) {
      this.$refs.detail.open(record);
    },
    handleSearch(flag) {
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
      }
      const params = {
        ...this.lastsearchData,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
      };
      this.tableLoading = true;
      getTClassifiedProtectionExpertList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            this.tableData = res.data.rows;
            this.pagination.total = res.data.total;
          } else {
            message.error(res.data.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
    },
    handleTableChange(pagination) {
      this.searchData.pageNum = pagination.current;
      this.searchData.pageSize = pagination.pageSize;
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.searchData = {};
      this.lastsearchData = {};
      this.handleSearch(true);
    },
  },
};
</script>

<style scoped lang='less'>
.ant-card {
  margin-bottom: 1px;
}
</style>
