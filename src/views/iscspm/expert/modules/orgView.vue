<template>
  <a-modal
      title="机构详情"
      style="top: 20px"
      :width="600"
      v-model:visible="visible"
      :footer="null"
      :destroyOnClose="true"
  >
    <template #footer>
      <a-button key="submit" type="primary" @click="handleClose">关闭</a-button>
    </template>
    <a-descriptions :column="1" >
      <!-- 机构名称 -->
      <a-descriptions-item label="机构名称">
        {{ data.organizationName || '-' }}
      </a-descriptions-item>

      <!-- 地址 -->
      <a-descriptions-item label="地址">
        {{ data.address || '-' }}
      </a-descriptions-item>

      <!-- 联系人 -->
      <a-descriptions-item label="联系人">
        {{ data.contactPerson || '-' }}
      </a-descriptions-item>

      <!-- 联系电话 -->
      <a-descriptions-item label="联系电话">
        {{ data.contactPhone }}
      </a-descriptions-item>

      <!-- 联系邮箱 -->
      <a-descriptions-item label="联系邮箱">
        {{ data.contactEmail || '-' }}
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
export default {
  name: "orgView",
  data() {
    return {
      visible: false,
      data: {}
    };
  },
  methods: {
    // 打开弹窗
    open(record) {
      this.data = { ...record };
      this.visible = true;
    },

    // 电话脱敏处理
    formatPhone(phone) {
      return phone ? phone.replace(/(\d{3})\d{4}(\d{4})/, "$1****$2") : "-";
    },
    handleClose() {
      this.visible = false
    }
  }
};
</script>

<style scoped>
/* 统一描述列表样式 */
.ant-descriptions-item-label {
  width: 100px;
  text-align: right;
  background: #fafafa;
}
.ant-descriptions-item-content {
  padding: 12px 24px;
  min-width: 200px;
}
</style>
