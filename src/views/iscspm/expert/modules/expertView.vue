<template>
  <a-modal
      title="专家详情"
      style="top: 20px"
      :width="600"
      v-model:visible="visible"
      :footer="null"
      :destroyOnClose="true"
  >
    <template #footer>
      <a-button key="submit" type="primary" @click="handleClose">关闭</a-button>
    </template>
    <a-descriptions :column="1" >
      <!-- 姓名 -->
      <a-descriptions-item label="姓名">
        {{ data.name || '-' }}
      </a-descriptions-item>

      <!-- 联系电话 -->
      <a-descriptions-item label="联系电话">
        {{ data.phone }}
      </a-descriptions-item>

      <!-- 机构名称 -->
      <a-descriptions-item label="所属机构">
        {{ data.orgName || '-' }}
      </a-descriptions-item>

      <!-- 备注 -->
      <a-descriptions-item label="备注">
        {{ data.remark || '无' }}
      </a-descriptions-item>
    </a-descriptions>
  </a-modal>
</template>

<script>
export default {
  name: "ExpertView",
  data() {
    return {
      visible: false,
      data: {}
    };
  },
  methods: {
    // 打开弹窗
    open(record) {
      this.data = { ...record };
      this.visible = true;
    },

    // 电话脱敏处理
    formatPhone(phone) {
      if (!phone) return '-';
      // 处理带区号的情况 (010-8888****)
      return phone.replace(/(\d{3})-?(\d{4})\d{4}/, '$1-$2****');
    },
    handleClose() {
      this.visible = false
    }
  }
};
</script>

<style scoped>
/* 统一描述列表样式 */
.ant-descriptions-item-label {
  width: 80px;
  text-align: right;
  background: #fafafa;
}

.ant-descriptions-item-content {
  padding: 12px 24px;
  min-width: 200px;
}

/* 备注内容样式 */
.remark-content {
  max-height: 150px;
  overflow-y: auto;
  white-space: pre-wrap;
  line-height: 1.6;
}
</style>
