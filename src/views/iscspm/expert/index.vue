<template>
  <div class="page-tab-out">
    <a-tabs type="card" v-model:activeKey="activeKey">
      <a-tab-pane key="1" tab="机构查询">
        <orgSelect />
      </a-tab-pane>
      <a-tab-pane key="2" tab="专家查询" force-render>
        <expertSelect />
      </a-tab-pane>
    </a-tabs>
  </div>
</template>

<script>
import orgSelect from "./orgSelect.vue";
import expertSelect from "./expertSelect.vue";
export default {
  components: {
    orgSelect,
    expertSelect,
  },
  data() {
    return {
      activeKey: "1",
    };
  },
  watch: {
    // 监听 activeKey 变化，当 activeKey 发生变化时，存储当前键值到sessionStorage
    activeKey(newKey) {
      sessionStorage.setItem("activeTabKey", newKey);
    },
  },
  created() {
    // 页面加载或刷新时，从sessionStorage获取存储的键值并且设为当前 activeKey
    const savedKey = sessionStorage.getItem("activeTabKey");
    if (savedKey) {
      this.activeKey = savedKey;
    }
  },
  beforeRouteLeave(to, from, next) {
    // 当离开当前路由时，重置 activeKey 到 '1'
    this.activeKey = "1";
    sessionStorage.removeItem("activeTabKey");
    next();
  },
  methods: {},
};
</script>

<style scoped lang="less">
.page-tab-out {
  background: #f0f2f5;
  height: 100%;
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding: 30px 18px 30px 18px;
  width: 100%;
  display: grid;
  // text-align: center;
}
:deep(.ant-tabs) {
  overflow-y: hidden;
}
:deep(.page) {
  padding: 0px !important;
}
</style>
