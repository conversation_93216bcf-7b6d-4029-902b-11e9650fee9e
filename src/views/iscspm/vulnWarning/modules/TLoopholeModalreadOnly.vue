<template>
    <a-modal title="漏洞详情"
             style="top: 20px;"
             :width="1040"
             v-model:visible="visible"
             :bodyStyle="{ height: '70vh',padding: 0, overflowY: 'auto', overflowX: 'hidden' }"
             :ok-button-props="{ style: { disabled: confirmLoading?'disabled':'' } }"
             :confirmLoading="confirmLoading"
             v-if="visible"
             @ok="handleSubmit">
      <template #footer>
        <a-button key="back" type="primary" @click="visible = false" style="margin: 10px 0; margin-right: 48%">
          关 闭
        </a-button>
      </template>
        <div style="padding: 24px;position: relative;" id="TLoopholeModalreadOnlyContent">
          <img style="position: absolute;right: 20px;top: 5px; width: 120px;opacity: 0.9;" src="@/assets/portalClient/low.png" alt="" v-if="mdl.hazardLevel === 1">
          <img style="position: absolute;right: 20px;top: 5px; width: 120px;opacity: 0.9;" src="@/assets/portalClient/middle.png" alt="" v-else-if="mdl.hazardLevel === 2">
          <img style="position: absolute;right: 20px;top: 5px; width: 120px;opacity: 0.9;" src="@/assets/portalClient/high.png" alt="" v-else-if="mdl.hazardLevel === 3">
          <img style="position: absolute;right: 20px;top: 5px; width: 120px;opacity: 0.9;" src="@/assets/portalClient/super.png" alt="" v-else-if="mdl.hazardLevel === 4">
          <!-- <img src="" alt="" v-else=""> -->
          <a-form :model="form">
            <a-row :gutter="64">
              <a-col :span="12">
                <div class="modal-divider-text">
                  基本信息
                </div>
                <a-descriptions bordered
                                class="pts-primary-description"
                                :column="1">
                  <a-descriptions-item label="漏洞名称">
                    <template v-if="mdl.vulnerabilityName">
                      {{ mdl.vulnerabilityName }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="漏洞编号">
                    <template v-if="mdl.vulnerabilityNum">
                      {{ mdl.vulnerabilityNum }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="漏洞类型">
                    <template v-if="mdl.vulnerabilityType">
                      {{ mdl.vulnerabilityType }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="危害等级">
                    <template v-if="mdl.hazardLevel">
                      <span :class="`harmLevelColor_${mdl.hazardLevel}`">
                        {{ riskLevelValue }}
                      </span>
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="收录时间">
                    <template v-if="mdl.collectionTime">
                      {{ mdl.collectionTime }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="更新时间">
                    <template v-if="mdl.updateTime">
                      {{ mdl.updateTime }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                </a-descriptions>
              </a-col>
              <a-col :span="12">
                <div class="modal-divider-text">
                  详细信息
                </div>
                <a-descriptions bordered
                                class="pts-primary-description"
                                :column="1">
                  <a-descriptions-item label="厂商">
                    <template v-if="mdl.manufacturer">
                      {{ mdl.manufacturer }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="热度值">
                    <template v-if="mdl.hotFlag">
                      {{ mdl.hotFlag }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                  <!-- <a-descriptions-item label="CVE编号">
                    <template v-if="mdl.cveCode">
                      {{ mdl.cveCode }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item> -->
                  <a-descriptions-item label="CNNVD编号">
                    <template v-if="mdl.cnnvdNum">
                      {{ mdl.cnnvdNum }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="CNVD编号">
                    <template v-if="mdl.cnvdNum">
                      {{ mdl.cnvdNum }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="影响组件">
                    <template v-if="mdl.impactComponents">
                      {{ mdl.impactComponents }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                  <a-descriptions-item label="点击量">
                    <template v-if="mdl.hotFlag">
                      {{ mdl.hotFlag }}
                    </template>
                    <template v-else>
                      暂无信息
                    </template>
                  </a-descriptions-item>
                </a-descriptions>
              </a-col>
            </a-row>
            <div class="modal-text-title">
              其它信息
            </div>
            <div>
              <a-descriptions bordered
                              class="pts-primary-description"
                              :column="1">
                <a-descriptions-item label="漏洞描述">
                  <template v-if="mdl.vulnerabilityDescription">
                    <div v-html="mdl.vulnerabilityDescription" class="Desc"></div>
                  </template>
                  <template v-else>
                    暂无信息
                  </template>
                </a-descriptions-item>
                <a-descriptions-item label="危害描述">
                  <template v-if="mdl.hazardDescription">
                    <div v-html="mdl.hazardDescription" class="Desc"></div>
                  </template>
                  <template v-else>
                    暂无信息
                  </template>
                </a-descriptions-item>
                <a-descriptions-item label="解决方案（加固建议）">
                  <template v-if="mdl.solution">
                    <div v-html="mdl.solution" class="Desc"></div>
                  </template>
                  <template v-else>
                    暂无信息
                  </template>
                </a-descriptions-item>
              </a-descriptions>
            </div>
          </a-form>
        </div>
    </a-modal>
</template>
<script>
import { getDictArray } from '@/utils/dict'
import dayjs from 'dayjs'
// import Template from '@/components/OA/Template.vue'

export default {
  name: 'TLoopholeModal',
  props: {},
  components: {
    // Template
  },
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 6 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 17 }
      },
      leftLabelCol: {
        xs: { span: 24 },
        sm: { span: 3 }
      },
      leftWrapperCol: {
        xs: { span: 24 },
        sm: { span: 17 }
      },
      rightLabelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      confirmLoading: false,
      mdl: {},
      form: {},
      mockArray: [],
      riskLevelArray: [],
      scriptVersionArray: [],
      vulnerabilityLevelArray: [],
      statusArry: [],
      scriptTypeArry: [],
      evidenceTypeArry: [],
      accessProtocolArry: [],
      objTypeTreeData: [],
      typeTreeData: [],
      vulnerabilityTypeTreeData: [],
      TScriptClasses: [],
      typeAll: []
    }
  },
  computed: {
    riskLevelValue() {
      let str = ''
      switch (this.mdl.hazardLevel) {
          case 1:
          str = '低危'
            break
          case 2:
          str = '中危'
            break
          case 3:
            str = '高危'
            break
          case 4:
          str = '超危'
            break
        }
      return str
    }
  },
  beforeCreate() {
  },
  created() {
  },
  methods: {
    open(record) {
      console.log(record, 'recordrecordrecordrecordrecordrecord')
    //   this.loadtree()
      this.mdl = Object.assign({}, record)
      const date1 = this.mdl.recordTime ? dayjs(this.mdl.recordTime).format('YYYY-MM-DD') : null
      const date2 = this.mdl.replaceTime ? dayjs(this.mdl.replaceTime).format('YYYY-MM-DD') : null
      // delete date._f
      // delete date._i
      this.mdl.recordTime = date1
      this.mdl.replaceTime = date2
      this.form = this.mdl
      this.visible = true
    },
    filterType(input, option) {
      return (
        option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
      )
    },
    handleSubmit(e) {
      this.confirmLoading = true
      e.preventDefault()
      this.form.validateFields((err, values) => {
        if (!err) {
          if (values.id !== 0) {
            delete values.code
          }
        //   saveTLoophole(values)
        //     .then(res => {
        //       if (res.code === 0) {
        //         this.$message.success('保存成功')
        //         this.$emit('ok')
        //         this.visible = false
        //       } else {
        //         this.$message.error(res.msg)
        //       }
        //     })
        //     .catch(() => {
        //       this.$message.error('系统错误，请稍后再试')
        //     })
        //     .finally(() => {
        //       this.confirmLoading = false
        //     })
        } else {
          this.confirmLoading = false
        }
      })
    },
  }
}

</script>
<style lang="less" scoped>
.modal-text-title, .modal-divider-text {
  height: 40px;
  line-height: 40px;
  color: #2468f3;
  font-size: 16px;
  margin-bottom: 16px;
  font-size: 16px;
  letter-spacing: 1px;
  font-family: Helvetica, 'Hiragino Sans GB', 'Microsoft Yahei', '微软雅黑', Arial, sans-serif;
  border-right: 0px;
  margin: 28px 0 15px;
}
.modal-divider-text {
  margin: 0 0 15px;
}

:deep(.pts-primary-description.ant-descriptions.ant-descriptions-bordered) {
  &.description_1,&.description_2 {
    .ant-descriptions-view {
      .ant-descriptions-row {
        .ant-descriptions-item-label {
          width: 160px;
        }
      }
    }
  }
  .ant-descriptions-view {
    border: unset;
    .ant-descriptions-row {
        //#123aac
    //   border: 1px solid;
      border: 1px solid #e8e8e8;
      .ant-descriptions-item-label {
        width: 200px;
        // color: #b1c7f4;
        // background-color: #1b308b;
        // border: 1px solid #123aac;
        text-align: center;
        &:not(:last-child) {
          border-bottom: unset;
        }
      }
      .ant-descriptions-item-content {
        // color: #fff;
        // border: 1px solid #123aac;
        border-bottom: unset;
      }
    }
  }
  .Desc {
    p {
      margin: 0;
    }
  }
//   .harmLevelColor_1 {
//     color: #28A9FF;
//   }
  .harmLevelColor_2 {
    color: #EDA712;
  }
  .harmLevelColor_3 {
    color: #FF600E;
  }
  .harmLevelColor_4 {
    color: #E20707;
  }
}
</style>
