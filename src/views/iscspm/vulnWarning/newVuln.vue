<template>
  <div class="page">
    <!-- 搜索条件栏 -->
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">漏洞名称：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            v-model:value.trim="searchData.vulnerabilityName"
            placeholder="请输入漏洞名称"
            allow-clear
            v-emoji
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">漏洞编号：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            v-model:value.trim="searchData.vulnerabilityNum"
            placeholder="请输入漏洞编号"
            allow-clear
            v-emoji
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">危害等级：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
            placeholder="等保等级"
            allowClear
            v-model:value="searchData.hazardLevel"
            style="width: 100%"
          >
            <a-select-option
              v-for="(b, index) in hazardArr"
              :key="index"
              :value="b.value"
              >{{ b.label }}</a-select-option
            >
          </a-select>
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch(true)">
          <template #icon>
            <SearchOutlined />
          </template>
          查询
        </a-button>
        <a-button @click="handleReset(true)" style="margin-left: 22px">
          重置
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <!-- 工具栏 -->
      <a-row>
        <a-col :span="24">
          <a-table
            style="padding-top: 10px; padding-left: 10px; padding-right: 10px"
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
            <!-- #00ff00 -->
            <template #num="{ index }">
              <span>{{
                (pagination.current - 1) * pagination.pageSize +
                Number(index) +
                1
              }}</span>
            </template>
            <template #hazardLevel="{ text }">
              {{ text && hazardFilter(text) }}
            </template>
            <template #action="{ record }">
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleDetail(record)"
              >
                查看
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    <!-- 导入框 -->
    <TLoopholeModalreadOnly ref="modal" />
  </div>
</template>

<script>
import { getDictArray } from "@/utils/dict";
import {
  InboxOutlined,
  UploadOutlined,
  SearchOutlined,
  FireOutlined,
} from "@ant-design/icons-vue";
import dayjs from "dayjs";
import {
  delTMajorVulnerabilityWarning,
  getTMajorVulnerabilityWarningList,
} from "@/request/iscspm/TMajorVulnerabilityWarning";
import TLoopholeModalreadOnly from "./modules/TLoopholeModalreadOnly.vue";
import { Modal, message } from "ant-design-vue";

export default {
  components: {
    TLoopholeModalreadOnly,
    FireOutlined,
    InboxOutlined,
    UploadOutlined,
    SearchOutlined,
  },
  data() {
    return {
      tableLoading: false,
      flexHeight: "0px",
      tableHeight: 0,
      searchData: {},
      lastsearchData: {},
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: "漏洞名称",
          dataIndex: "vulnerabilityName",
          ellipsis: true,
          // width: '240px'
        },
        {
          title: "漏洞编号",
          dataIndex: "vulnerabilityNum",
          ellipsis: true,
          // width: '180px'
        },
        {
          title: "漏洞类型",
          dataIndex: "vulnerabilityType",
          // ellipsis: true
        },
        {
          title: "危害等级",
          dataIndex: "hazardLevel",
          slots: { customRender: "hazardLevel" },
          width: "200px",
        },
        {
          title: "收录时间",
          width: "120px",
          dataIndex: "collectionTime",
          align: "center",
        },
        {
          title: "操作",
          width: "120px",
          dataIndex: "action",
          align: "center",
          slots: { customRender: "action" },
        },
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      hazardArr: [],
      hazardArrMap: {},
    };
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  async created() {
    const arr = await getDictArray("hazard_level");
    this.hazardArr = arr.map((d) => {
      this.hazardArrMap[d.dictValue] = { text: d.dictLabel };
      return {
        value: d.dictValue,
        label: d.dictLabel,
      };
    });
  },
  mounted() {
    this.handleSearch();
    setTimeout(() => {
      this.flexHeight = `calc(100vh - ${this.searchHeight}px - 180px)`;
      this.tableHeight =
        (window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight) -
        378 -
        this.searchHeight;
    }, 100);
  },
  methods: {
    hazardFilter(status) {
      return this.hazardArrMap[status]?.text;
    },
    beforeSearch(flag) {
      this.lastsearchData = { ...this.searchData };
      this.handleSearch(flag);
    },
    handleSearch(flag) {
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
      }
      const params = {
        ...this.lastsearchData,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
        orderBy: "collection_time",
      };
      this.tableLoading = true;
      getTMajorVulnerabilityWarningList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            this.tableData = res.data.rows;
            this.pagination.total = res.data.total;
          } else {
            message.error(res.data.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
    },
    handleTableChange(pagination) {
      this.searchData.pageNum = pagination.current;
      this.searchData.pageSize = pagination.pageSize;
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.searchData = {};
      this.lastsearchData = {};
      this.handleSearch(true);
    },
    handleDetail(value) {
      this.$refs.modal.open(value);
    }
  },
};
</script>

<style scoped lang="less">
.ant-card {
  margin-bottom: 1px;
}
</style>
