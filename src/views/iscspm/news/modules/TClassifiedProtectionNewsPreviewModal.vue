<template>
  <a-modal
    title="资讯预览"
    style="top: 20px"
    :width="1200"
    :bodyStyle="{ height: '75vh',padding: 0 }"
    v-model:visible="visible"
    :destroyOnClose="true"
    :confirmLoading="confirmLoading"
  >
    <div class="informationDetail">
      <div class="top">
        <div class="title">{{ mdl.title }}</div>
        <div class="brief">
          <span class="span-1">发布时间: {{ mdl.releaseDate }}</span>
          <span class="span-2">点击量: {{ mdl.clicks }}</span>
          <span class="span-3">新闻来源: {{ mdl.source }}</span>
        </div>
      </div>
      <div class="bottom">
        <div class="informationDetail-content" v-html="mdl.content"></div>
      </div>
    </div>
    <template #footer>
      <a-button key="back" type="primary" @click="visible = false" style="margin: 10px 0; margin-right: 48%">
        关 闭
      </a-button>
    </template>
  </a-modal>
</template>
<script>
import { pick } from 'lodash'
import dayjs from 'dayjs'
import { getNewsViewNews } from '@/request/iscspm/TClassifiedProtectionNews'

export default {
  name: 'TClassifiedProtectionNewsModal',
  props: {},
  components: {
  },
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      mdl: {},
      form: {}
    }
  },
  beforeCreate() {},
  created() {
    this.init()
  },
  methods: {
    init() {},
    add() {
      this.edit({ id: 0 })
    },
    edit(record) {
      this.mdl = Object.assign({}, record)
      const date1 = this.mdl.releaseDate ? dayjs(this.mdl.releaseDate).format('YYYY-MM-DD') : null
      this.mdl.releaseDate = date1
      this.visible = true
      this.form = pick(
            this.mdl,
            'id',
            'title',
            'source',
            'releaseDate',
            'category',
            'content',
            'clicks',
            'status',
            'delFlag',
            'createBy',
            'createTime',
            'updateBy',
            'updateTime',
            'remark'
          )
      this.initView()
    },
    async initView() {
      await getNewsViewNews({ id: this.mdl.id })
    },
  }
}
</script>
<style lang="less" scoped>
.informationDetail {
  width: 100%;
  height: 100%;
  padding: 30px 0 50px;
  margin: auto;
  overflow: auto;
  .top {
    padding-bottom: 10px;
    border-bottom: 1px solid #dddde3;
    .title {
      font-size: 30px;
      font-family: Source Han Sans CN, Source Han Sans CN-Medium;
      font-weight: 500;
      color: #292e3d;
      line-height: 54px;
      margin-bottom: 20px;
      text-align: center;
    }
    .brief {
      display: flex;
      justify-content: space-around;
      align-items: center;
      margin-bottom: 14px;
      padding: 0 246px;
      span {
        position: relative;
        font-size: 14px;
        font-family: Source Han Sans CN, Source Han Sans CN-Regular;
        font-weight: 400;
        text-align: left;
        color: #a3a7b2;
        line-height: 21px;
        // margin-right: 90px;
        padding-left: 18px;
        &::before {
          position: absolute;
          inset: 0;
          right: unset;
          margin: auto;
          display: inline-block;
          width: 14px;
          height: 14px;
          content: '';
        }
      }
      .span-1::before {
        // background: url('../../../assets/portalClient/information/span1.png') no-repeat 50% 50%;
        // background-size: contain;
      }
      .span-2::before {
        width: 16px;
        height: 16px;
        // background: url('../../../assets/portalClient/information/span2.png') no-repeat 50% 50%;
        // background-size: contain;
      }
      .span-3::before {
        width: 13px;
        height: 13px;
        // background: url('../../../assets/portalClient/information/span3.png') no-repeat 50% 50%;
        // background-size: contain;
      }
    }
  }
  .bottom {
    padding: 40px;
    .informationDetail-content {
      width: 900px;
      margin: auto;
      min-height: 100px;
    }
  }
}
.ql-align-justify-span {
  display: inline-block;
  width: 100%;
  img {
    width: 100%;
  }
}

:deep(.ql-align-center) {
  img {
    width: 100%;
    object-fit: contain;
  }
}
</style>
