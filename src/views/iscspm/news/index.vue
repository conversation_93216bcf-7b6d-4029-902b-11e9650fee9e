<template>
  <div class="page">
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">新闻分类：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select allowClear placeholder="请选择新闻分类"
                    style="width: 100%"
                    v-model:value="searchData.category">
            <a-select-option v-for="(b, index) in categoryArr" :key="index" :value="b.value">{{
              b.label
            }}</a-select-option>
          </a-select>
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">发布时间：</div>
        <div class="page-search-item-value time" style="display: flex">
          <a-range-picker
            v-model:value="searchData.time"
            style="width: 400px"
          />
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 15px">
          重置
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <a-row>
        <a-col :span="24">
          <a-table
            style="padding-top: 10px; padding-left: 10px; padding-right: 10px"
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
            <template #num="{ index }">
              <span>{{
                (pagination.current - 1) * pagination.pageSize +
                Number(index) +
                1
              }}</span>
            </template>
            <template #category="{ text }">
              {{ categoryFilter(text) }}
            </template>
            <template #action="{ record }">
              <!-- <a-button
                type="link"
                style="color: #387ff1"
                @click="handleEdit(record)"
              >
                编辑
              </a-button> -->
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handlePreview(record)"
              >
                查看
              </a-button>
              <!-- <a-button
                type="link"
                style="color: #fc5532"
                @click="handleDelete(record)"
              >
                删除
              </a-button> -->
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    
    <!-- <tClassifiedProtectionNews-modal ref="modal" @ok="handleOk"/> -->
    <tClassifiedProtectionNewsPreview-modal ref="previewModal" @ok="onOk" />
</div>
</template>

<script>
import {
  SearchOutlined,
  PlusOutlined
} from "@ant-design/icons-vue";
import { delTClassifiedProtectionNews, getTClassifiedProtectionNewsList } from '@/request/iscspm/TClassifiedProtectionNews'
// import TClassifiedProtectionNewsModal from './modules/TClassifiedProtectionNewsModal.vue'
import TClassifiedProtectionNewsPreviewModal from './modules/TClassifiedProtectionNewsPreviewModal.vue'
import { getDictArray } from '@/utils/dict'
import dayjs from 'dayjs'

import { Modal, message } from 'ant-design-vue';

export default {
  name: "TableList",
  components: {
    // TClassifiedProtectionNewsModal,
    TClassifiedProtectionNewsPreviewModal,
    SearchOutlined,
    PlusOutlined
  },
  data() {
    return {
      categoryArray: [],
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      tableLoading: false,
      searchData: {
      },
      lastsearchData: {
      },
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: '标题',
          dataIndex: 'title'
        },
        {
          title: '来源',
          dataIndex: 'source'
        },
        {
          title: '发布日期',
          dataIndex: 'releaseDate',
          slots: { customRender: 'releaseDate' }
        },
        {
          title: '分类',
          dataIndex: 'category',
          slots: { customRender: 'category' }
        },
        // {
        //   title: '内容',
        //   dataIndex: 'content'
        // },
        {
          title: '点击数',
          dataIndex: 'clicks'
        },
        {
          title: '操作',
          width: '230px',
          dataIndex: 'action',
          align: 'left',
          slots: { customRender: "action" },
        }
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
      categoryMap: {},
      categoryArr: []
    };
  },
  async created() {
    const categoryArr = await getDictArray('t_news_category')
    this.categoryArr = categoryArr.map((d) => {
      this.categoryMap[d.dictValue] = d.dictLabel
      return {
        value: d.dictValue,
        label: d.dictLabel
      }
    })
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  mounted() {
    setTimeout(() => {
      this.flexHeight = `calc(100vh - ${this.searchHeight}px - 140px)`;
      this.tableHeight =
        (window.innerHeight ||
          document.documentElement.clientHeight ||
          document.body.clientHeight) -
        336 -
        this.searchHeight;
    }, 100);
    this.handleSearch(true)
  },
  methods: {
    categoryFilter(data) {
      return this.categoryMap[data] || data
    },
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    handleSearch(flag) {
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
      const params = {
        category: this.lastsearchData.category,
        beginTime: (this.lastsearchData.time && dayjs(this.lastsearchData.time[0]).format('YYYY-MM-DD')) || null,
        endTime: (this.lastsearchData.time && dayjs(this.lastsearchData.time[1]).format('YYYY-MM-DD')) || null,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
      };
      this.tableLoading = true;
      getTClassifiedProtectionNewsList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            this.tableData = res.data.rows.map(item => {
              item.releaseDate = item.releaseDate && dayjs(item.releaseDate).format('YYYY-MM-DD')
              return item
            });
            this.pagination.total = res.data.total;
          } else {
            message.error(res.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {
      };
      this.searchData = {
      };
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    handleAdd() {
      this.$refs.modal.add();
    },
    handleEdit(record) {
      this.$refs.modal.edit(record);
    },
    handlePreview(record) {
      this.$refs.previewModal.edit(record)
    },
    handleDelete(value) {
      Modal.confirm({
        title: "确定要删除选中内容吗?",
        okText: "确定",
        okType: "primary",
        cancelText: "取消",
        onOk: async () => {
          delTClassifiedProtectionNews({ ids: value.id }).then((res) => {
            if (res.data.code === 0) {
              message.success('删除成功')
              this.handleSearch();
            } else {
              message.error(res.data.msg)
            }
          })
          },
          onCancel() {},
        });
    },    
    onOk() {
      this.handleSearch(true);
    }
  },
  watch: {},
};
</script>
<style lang="less" scoped></style>
