<template>
  <a-card :bordered="false" style="height: 100%" class="system-card">
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">姓名：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            allowClear
            placeholder="请输入姓名"
            v-model:value="searchData.name"
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">机构名称：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            allowClear
            placeholder="请输入机构名称"
            v-model:value="searchData.orgName"
          />
        </div>
      </div>

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 15px">
          重置
        </a-button>
        <a-button type="primary" @click="handleAdd()" style="margin-left: 15px">
          <template #icon> <PlusOutlined /> </template>新增
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <a-row>
        <a-col :span="24">
          <a-table
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
          <template #num="{ index }">
            <span>{{
              (pagination.current - 1) * pagination.pageSize +
              Number(index) +
              1
            }}</span>
          </template>
          <template #systemType="{ text }">
            {{ switchValue(text, 'systemType') }}
          </template>
          <template #protectionLevel="{ text }">
            <span :class="`protectionLevel_${text}`">
              {{ switchValue(text, 'protectionLevel') }}
            </span>
          </template>
          <template #expireState="{ text }">
            <span :style="{ color: text === '1' ? '#20C59F' : text === '2' ? '#FF6D04' : '#DA0E0E' }">
              {{ switchValue(text, 'expireState') }}
            </span>
          </template>
            <template #action="{ record }">
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleDetail(record)"
              >
                查看
              </a-button>
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleEdit(record)"
              >
                编辑
              </a-button>
              <a-button
                type="link"
                style="color: #fc5532"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    <tClassifiedProtectionExpert-modal ref="modal" @onOk="onOk" />
    <TClassifiedProtectionExpertView ref="detail" />
  </a-card>
</template>

<script>
import {
  SearchOutlined,
  PlusOutlined,
  DownloadOutlined,
    UploadOutlined
} from "@ant-design/icons-vue";
import {
  delTClassifiedProtectionExpert,
  getTClassifiedProtectionExpertList
} from '@/request/iscspm/TClassifiedProtectionExpert'
import TClassifiedProtectionExpertModal from './modules/TClassifiedProtectionExpertModal.vue'
import TClassifiedProtectionExpertView from './modules/TClassifiedProtectionExpertView.vue'
import { getDictArray } from '@/utils/dict'
import { Modal, message } from 'ant-design-vue';

export default {
  name: "TableList",
  components: {
    TClassifiedProtectionExpertModal,
    TClassifiedProtectionExpertView,
    SearchOutlined,
    PlusOutlined
  },
  data() {
    return {
      categoryArray: [],
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      tableLoading: false,
      searchData: {
      },
      lastsearchData: {
      },
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: '姓名',
          dataIndex: 'name',
          ellipsis: true,
          width: '180px'
        },
        {
          title: '联系电话',
          dataIndex: 'phone'
        },
        {
          title: '机构名称',
          dataIndex: 'orgName',
          slots: { customRender: 'orgName' }
        },
        {
          title: '排序',
          dataIndex: 'orderNum'
        },
        {
          title: '备注',
          dataIndex: 'remark',
          ellipsis: true
        },
        {
          title: '操作',
          width: '240px',
          dataIndex: 'action',
          align: 'center',
          slots: { customRender: "action" },
        }
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
    };
  },
  async created() {
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  mounted() {
    setTimeout(() => {
      this.flexHeight = `calc(${
        document.getElementsByClassName("system-card")[0].clientHeight
        }px - ${this.searchHeight}px - 48px)`;
        this.tableHeight =
        document.getElementsByClassName("system-card")[0].clientHeight -
        this.searchHeight -
        180;
      }, 100);
    this.handleSearch(true)
  },
  methods: {
    switchValue(value, key) {
      if (key === 'protectionLevel') {
        return this.protectionMap[value]?.text
      } else if (key === 'expireState') {
        return value ? this.expireMap[value]?.text : '--'
      } else if (key === 'systemType') {
        return value ? this.systemTypeMap[value]?.text : '--'
      }
    },
    categoryFilter(data) {
      return this.categoryMap[data] || data
    },
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    handleSearch(flag) {
      console.log(new Date())
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
      const params = {
        ...this.lastsearchData,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
      };
      this.tableLoading = true;
      getTClassifiedProtectionExpertList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            this.tableData = res.data.rows;
            this.pagination.total = res.data.total;
          } else {
            message.error(res.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {
      };
      this.searchData = {
      };
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    handleAdd() {
      this.$refs.modal.add();
    },
    handleDetail(record) {
      this.$refs.detail.open(record);
    },
    handleEdit(record) {
      this.$refs.modal.edit(record);
    },
    handlePreview(record) {
      this.$refs.previewModal.edit(record)
    },
    handleDelete(value) {
      Modal.confirm({
        title: "确定要删除选中内容吗?",
        okText: "确定",
        okType: "primary",
        cancelText: "取消",
        onOk: async () => {
          delTClassifiedProtectionExpert({ ids: value.id }).then((res) => {
            if (res.data.code === 0) {
              message.success('删除成功')
              this.handleSearch();
            } else {
              message.error(res.data.msg)
            }
          })
          },
          onCancel() {},
        });
    },
    onOk() {
      this.handleSearch(true);
    },
  },
  watch: {},
};
</script>
<style lang="less" scoped>
.protectionLevel_0 {
  color: #a2a7b5;
}
.protectionLevel_1 {
  color: #3270ee;
}
.protectionLevel_2 {
  color: #ddc12d;
}
.protectionLevel_3 {
  color: #ff6d04;
}
.protectionLevel_4 {
  color: #e81919;
}

.expireState_1 {
  color: #20C59F;
}
.expireState_2 {
  color: #FF6D04;
}
.expireState_3 {
  color: #DA0E0E;
}
.expireState_4 {
  color: #a2a7b5;
}

</style>
