<template>
  <a-modal
    title="操作"
    style="top: 20px"
    :width="500"
    v-model:visible="visible"
    :destroyOnClose="true"
    :confirmLoading="confirmLoading"
    @cancel="handleClose"
    @ok="handleSubmit"
  >
    <a-form :model="form" ref="form">
      <a-form-item style="display: none">
        <a-input v-model:value="form.id" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="中文名"
        name="cName"
        :rules="[{ required: true, message: '请输入中文名' }]"
      >
        <a-input placeholder="中文名" v-model:value="form.cName" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="英文名"
        name="eName"
        :rules="[{ required: true, message: '请输入英文名' }]"
      >
        <a-input placeholder="英文名" v-model:value="form.eName" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="缩写"
        name="abbreviation"
        :rules="[{ required: true, message: '请输入缩写' }]"
      >
        <a-input placeholder="缩写" v-model:value="form.abbreviation" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { saveTMajorVulnerabilityType } from "@/request/iscspm/TMajorVulnerabilityType";
import { pick } from "lodash";
import { message } from "ant-design-vue";
import { getDictArray } from "@/utils/dict";

export default {
  name: "TMajorVulnerabilityTypeModal",
  props: {},
  components: {},
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      mdl: {},
      form: {},
    };
  },
  beforeCreate() {},
  created() {
    this.init();
  },
  methods: {
    init() {},
    add() {
      this.edit({
        id: 0,
      });
    },
    edit(record) {
      this.mdl = Object.assign({}, record);
      this.form = pick(this.mdl, "id", "cName", "eName", "abbreviation")
      this.visible = true;
    },
    handleSubmit(e) {
      this.$refs["form"]
        .validateFields()
        .then((value) => {
          this.confirmLoading = true;
          saveTMajorVulnerabilityType(this.form)
            .then((res) => {
              if (res.data.code === 0) {
                message.success("保存成功");
                this.handleClose()
                this.$emit("onOk");
              } else {
                message.error(res.data.msg);
              }
            })
            .catch(() => {
              message.error("系统错误，请稍后再试");
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        })
        .catch((err) => {
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    handleClose() {
      this.$refs["form"].resetFields();
      this.visible = false;
    },
  },
  watch: {
    /*
      'selectedRows': function (selectedRows) {
        this.needTotalList = this.needTotalList.map(item => {
          return {
            ...item,
            total: selectedRows.reduce( (sum, val) => {
              return sum + val[item.dataIndex]
            }, 0)
          }
        })
      }
      */
  },
};
</script>
