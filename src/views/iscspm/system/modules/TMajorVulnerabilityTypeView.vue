<template>
  <a-modal
      title="漏洞类型详情"
      style="top: 20px"
      :width="500"
      v-model:visible="visible"
      :destroyOnClose="true"
  >
    <template #footer>
      <a-button key="submit" type="primary" @click="handleClose">关闭</a-button>
    </template>
  <a-descriptions :column="1" >
    <!-- 中文名 -->
    <a-descriptions-item label="中文名">
      {{ form.cName || '-' }}
    </a-descriptions-item>

    <!-- 英文名 -->
    <a-descriptions-item label="英文名">
      {{ form.eName || '-' }}
    </a-descriptions-item>

    <!-- 缩写 -->
    <a-descriptions-item label="缩写">
      {{ form.abbreviation || '-' }}
    </a-descriptions-item>
  </a-descriptions>
  </a-modal>
</template>

<script>
export default {
  name: "TMajorVulnerabilityTypeView",
  data() {
    return {
      visible: false,
      form: {}
    };
  },
  methods: {
    // 打开查看弹窗
    open(record) {
      this.form = {
        cName: record.cName,
        eName: record.eName,
        abbreviation: record.abbreviation
      }
      this.visible = true;
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false;
    }
  }
};
</script>

<style scoped>
/* 调整描述列表样式 */
.ant-descriptions-item-label {
  width: 80px;
  text-align: right;
  background: #fafafa;
}
.ant-descriptions-item-content {
  padding-left: 24px;
}
</style>
