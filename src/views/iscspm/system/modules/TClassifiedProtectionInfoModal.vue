<template>
  <a-modal
    title="操作"
    style="top: 20px"
    :width="500"
    v-model:visible="visible"
    :confirmLoading="confirmLoading"
    @cancel="handleClose"
    @ok="handleSubmit"
  >
    <a-form :model="form" ref="form">
      <a-form-item style="display: none">
        <a-input v-model:value="form.id" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        :rules="[{ required: true, message: '请输入系统名称' }]"
        label="系统名称"
        name="systemName"
      >
        <a-input placeholder="系统名称" v-model:value="form.systemName" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="公司名称"
        :rules="[{ required: true, message: '请选择公司名称' }]"
        name="deptId"
      >
        <a-select placeholder="公司名称" v-model:value="form.deptId">
          <a-select-option
            v-for="(b, index) in companyArr"
            :key="index"
            :value="b.value"
            >{{ b.label }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="系统类型"
        :rules="[{ required: true, message: '请选择系统类型' }]"
        name="systemType"
      >
        <a-select placeholder="请选择系统类型" v-model:value="form.systemType">
          <a-select-option
            v-for="(b, index) in systemTypeArr"
            :key="index"
            :value="b.value"
            >{{ b.label }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="等保等级"
        :rules="[{ required: true, message: '请选择等保等级' }]"
        name="protectionLevel"
      >
        <a-select placeholder="等保等级" v-model:value="form.protectionLevel">
          <a-select-option
            v-for="(b, index) in protectionArr"
            :key="index"
            :value="b.value"
            >{{ b.label }}</a-select-option
          >
        </a-select>
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="备案时间"
        :rules="[{ required: false, message: '请选择备案时间' }]"
        name="filingTime"
      >
        <a-date-picker
          style="width: 100%"
          format="YYYY/MM/DD"
          placeholder="请选择备案时间"
          v-model:value="form.filingTime"
        />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="备案地点"
        :rules="[{ required: true, message: '请输入备案地点' }]"
        name="filingAddr"
      >
        <a-input placeholder="备案地点" v-model:value="form.filingAddr" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="联系人"
        :rules="[{ required: true, message: '请输入联系人' }]"
        name="contactPerson"
      >
        <a-input placeholder="联系人" v-model:value="form.contactPerson" />
      </a-form-item>
      <a-form-item
        :labelCol="labelCol"
        :wrapperCol="wrapperCol"
        label="联系电话"
        :rules="[
          { required: true, message: '请输入联系电话' },
          {
            required: false,
            message: '请输入正确的手机号',
            pattern: /^1[3456789]\d{9}$/,
          },
          { validateTrigger: ['blur', 'change'] },
        ]"
        name="contactPhone"
      >
        <a-input placeholder="联系电话" v-model:value="form.contactPhone" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { saveTClassifiedProtectionInfo } from "@/request/iscspm/TClassifiedProtectionInfo";
import { pick } from "lodash";
import { getDictArray } from "@/utils/dict";
import { getDeptLists, delDept } from "@/request/iscspm/system";
import dayjs from "dayjs";
import { message } from "ant-design-vue";

export default {
  name: "TClassifiedProtectionInfoModal",
  props: {},
  components: {},
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      mdl: {},
      form: {},
      deptIdList: [],
      systemTypeArr: [],
      protectionArr: [],
      companyArr: []
    };
  },
  beforeCreate() {},
  async created() {
    this.init();
    const companyArr = await getDictArray("iscspm_company");
    this.companyArr = companyArr.map((d) => {
      return {
        value: d.dictValue,
        label: d.dictLabel,
      };
    });
    const arr = await getDictArray("t_system_type");
    this.systemTypeArr = arr.map((d) => {
      return {
        value: d.dictValue,
        label: d.dictLabel,
      };
    });
    const protectionArr = await getDictArray("t_protection_level");
    this.protectionArr = protectionArr.map((d) => {
      return {
        value: d.dictValue,
        label: d.dictLabel,
      };
    });
    const res = await getDeptLists();
    if (res.data.code === 0) {
      this.deptIdList = res.data.rows;
    }
  },
  methods: {
    init() {},
    add() {
      this.edit({ id: 0 });
    },
    edit(record) {
      this.mdl = Object.assign({}, record);
      if (this.mdl.filingTime) {
        const date = dayjs(this.mdl.filingTime);
        this.mdl.filingTime = date;
      }
      this.form = pick(
        this.mdl,
        "id",
        "systemName",
        "deptId",
        "systemType",
        "protectionLevel",
        "filingTime",
        "filingAddr",
        "contactPerson",
        "contactPhone",
        "status",
        "delFlag",
        "createBy",
        "createTime",
        "updateBy",
        "updateTime",
        "remark"
      );
      this.visible = true;
    },
    handleSubmit() {
      this.$refs["form"]
        .validateFields()
        .then((value) => {
          this.confirmLoading = true;
          this.form.systemName = this.form.systemName.replace(/\s*/g, "");
          this.form.filingAddr = this.form.filingAddr.replace(/\s*/g, "");
          this.form.contactPerson = this.form.contactPerson.replace(/\s*/g, "");
          const params = {
            filingTime: dayjs(this.form.filingTime).format("YYYY-MM-DD 00:00:00"),
          }
          saveTClassifiedProtectionInfo(Object.assign({}, this.form, params))
            .then((res) => {
              if (res.data.code === 0) {
                message.success("保存成功");
                this.$emit("onOk");
                this.handleClose();
              } else {
                message.error(res.data.msg);
              }
            })
            .catch(() => {
              message.error("系统错误，请稍后再试");
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        })
        .catch((err) => {
          console.log(7777, err);
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    handleClose() {
      this.$refs["form"].resetFields();
      this.visible = false;
    },
  },
  watch: {
    /*
              'selectedRows': function (selectedRows) {
                this.needTotalList = this.needTotalList.map(item => {
                  return {
                    ...item,
                    total: selectedRows.reduce( (sum, val) => {
                      return sum + val[item.dataIndex]
                    }, 0)
                  }
                })
              }
              */
  },
};
</script>
