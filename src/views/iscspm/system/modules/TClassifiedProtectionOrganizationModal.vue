<template>
  <a-modal title="操作"
           style="top: 20px;"
           :width="500"
           v-model:visible="visible"
           :confirmLoading="confirmLoading"
           @cancel="handleClose"
           @ok="handleSubmit">
    <a-form :model="form" ref="form">
      <a-form-item style="display:none">
        <a-input v-model:value="form.id" />
      </a-form-item>
      <a-form-item :labelCol="labelCol"
                   :wrapperCol="wrapperCol"
                   name="organizationName"
                   :rules="[{ required: true, message: '请输入机构名称' }]"
                   label="机构名称">
        <a-input
          placeholder="机构名称"
          v-model:value="form.organizationName"
        />
      </a-form-item>
      <a-form-item :labelCol="labelCol"
                   :wrapperCol="wrapperCol"
                   name="address"
                   :rules="[{ required: true, message: '请输入地址' }]"
                   label="地址">
        <a-input
            placeholder="地址"
            v-model:value="form.address" />
      </a-form-item>
      <a-form-item :labelCol="labelCol"
                   :wrapperCol="wrapperCol"
                   name="contactPerson"
                   :rules="[{ required: true, message: '请输入联系人' }]"
                   label="联系人">
        <a-input
            placeholder="联系人"
            v-model:value="form.contactPerson" />
      </a-form-item>
      <a-form-item :labelCol="labelCol"
                   :wrapperCol="wrapperCol"
                   name="contactPhone"
                   :rules="[
                { required: true, message: '请输入联系电话' },
                { required: false, message: '请输入正确的联系电话', pattern: /^1[3456789]\d{9}$|^(?:(?:\d{3}-)?\d{8}|^(?:\d{4}-)?\d{7,8})(?:-\d+)?$/},
                { validateTrigger: ['blur', 'change'] }
              ]"
                   label="联系电话">
        <a-input
            placeholder="联系电话"
            v-model:value="form.contactPhone" />
      </a-form-item>
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="联系邮箱" :rules="[
                { required: false, message: '请输入联系邮箱' },
                {
                  pattern: /^[A-Za-z0-9\u4e00-\u9fa5]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/,
                  message: '请输入正确的邮箱'
                },
                { validateTrigger: ['blur', 'change'] }
              ]">
        <a-input
          placeholder="联系邮箱"
          v-model:value="form.contactEmail"
        />
      </a-form-item>
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="排序">
        <a-input-number
        style="width: 100%;"
          placeholder="排序"
          :min="1"
          v-model:value="form.orderNum"
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { saveTClassifiedProtectionOrganization } from '@/request/iscspm/TClassifiedProtectionOrganization'
import { pick } from 'lodash'
import { getDictArray } from '@/utils/dict'
import { message } from 'ant-design-vue'

const statusMap = {}
export default {
  name: 'TProdCategoryModal',
  props: {},
  components: {},
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      mdl: {},
      form: {}
    }
  },
  beforeCreate() { },
  created() {
  },
  methods: {
    add() {
      this.edit({ id: 0 })
    },
    async edit(record) {
      this.mdl = Object.assign(record)
      this.form = 
          pick(
            this.mdl,
            'id',
            'organizationName',
            'address',
            'contactPerson',
            'contactPhone',
            'contactEmail',
            'orderNum',
            'status',
            'delFlag',
            'createBy',
            'createTime',
            'updateBy',
            'updateTime',
            'remark'
          )
      this.visible = true
    },
    handleSubmit() {
      this.$refs["form"]
      .validateFields()
      .then((value) => {
          this.confirmLoading = true
          saveTClassifiedProtectionOrganization(this.form)
            .then(res => {
              if (res.data.code === 0) {
                message.success("保存成功");
                this.handleClose();
                this.$emit("onOk");
              } else {
                message.error(res.data.msg)
              }
            })
            .catch(() => {
              message.error('系统错误，请稍后再试')
            })
            .finally(() => {
              this.confirmLoading = false
            })
        })
        .catch((err) => {
          console.log(7777, err);
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    handleClose() {
      this.$refs["form"].resetFields();
      this.visible = false;
    },
  },
  watch: {

  }
}
</script>
