<template>
  <a-modal title="导入" style="top: 20px" :width="460" v-if="visible" v-model:visible="visible" :closable="false"
    :keyboard="false" :maskClosable="false" :destroyOnClose="true">
    <template #footer>
      <a-button type="primary" @click="closeUploadModal()"> 关 闭 </a-button>
      <!-- <a-button class="btn" @click="submit"> 确 定 </a-button> -->
    </template>
    <div>
      <a-form ref="resources" :model="resources" layout="horizontal">
        <a-row> </a-row>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-upload name="file" :accept="fileType" :multiple="true" :beforeUpload="beforeUpload"
            :customRequest="customRequest" @change="uploadChange" :file-list="fileList">
            <a-button :disabled="disabled" style="margin-bottom: 10px; width: 406px">
              <upload-outlined />
              点击导入
            </a-button>
          </a-upload>
        </a-form-item>
        <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-button :disabled="disabled" type="primary" @click="downTemplate(true)" style="width: 406px">
            <DownloadOutlined />
            模板导出
          </a-button>
        </a-form-item>
      </a-form>
      <a-alert message="提示：仅允许导入“xls”或“xlsx”格式文件！" banner />
      <div v-if="showDetail" style="margin-top: 5px" class="info">
        <span class="context-text-sum">应该导入：{{ planSum }}，</span>
        <span class="context-text-sum">实际导入：{{ realSum }}，</span>
        <span class="context-text-sum">重复导入：{{ rapece }}，</span>
        <span class="context-text-sum">失败导入：{{ failSum }}</span>
      </div>
      <div v-if="errMsgList.length > 0" class="errorList">
        失败原因:
        <p v-for="(item, i) in errMsgList" :key="i">{{ item }}</p>
      </div>
      <div style="margin-top: 24px" v-if="footerVisible">
        <a-button type="primary" @click="visible = false"> 关闭 </a-button>
        <!-- <a-button type="primary"
                  style="margin-left: 8px"
                  :loading="confirmLoading"
                  @click="handleSubmit">
          扫描服务
        </a-button> -->
      </div>
    </div>
  </a-modal>
</template>
<script>
// import pick from 'lodash.pick'
// import { getArea } from '@/request/system.js'
import { uploadTResources } from '@/request/publicServiceManage/TResources'
import { message } from 'ant-design-vue';
import {
  UploadOutlined,
  DownloadOutlined
} from "@ant-design/icons-vue";
import { createUuid } from '@/utils/util'
import { exportExcel, uploadIscspmFile } from '@/request/iscspm/TClassifiedProtectionInfo'


export default {
  name: 'trainEditUploadFileModel',
  props: {
    fTypeList: {
      type: Array,
      default: () => []
    }
  },
  components: {
    DownloadOutlined,
    UploadOutlined
  },
  data() {
    return {
      showDetail: false,
      errMsgList: [],
      disabled: false,
      visible: false,
      fileType: '.docx,.xlsx,.pdf,.mp4',
      acceptType: ['xlsx', 'docx', 'pdf', 'mp4'],
      uploadUrl: '',
      loading: false,
      uploadMap: new Map(),
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 23 }
      },
      fileList: [
        {
          uid: '1',
          name: 'xxx.png',
          status: 'done',
          url: 'http://www.baidu.com/xxx.png'
        }
      ],
      resources: {
        url: ''
      },
      uuid: null,
      footerVisible: false
    }
  },
  beforeCreate() { },
  created() { },
  methods: {
    show() {
      this.resources = {}
      this.fileList = []
      this.errMsgList = []
      this.footerVisible = false
      this.visible = true
      this.uuid = createUuid()
    },
    closeUploadModal() {
      if (this.uploadMap.size === 0) {
        this.visible = false
        this.disabled = false
        this.$emit("onOk");
      } else {
        message.warning('导入未完成,无法关闭当前窗口')
      }
    },
    closeModal() {
      if (this.uploadMap.size === 0) {
        this.visible = false
        this.disabled = false
        this.$emit("onOk");
      } else {
        message.warning('导入未完成,无法关闭当前窗口')
      }
    },
    handleChange(info) {
      let fileList = [...info.fileList]
      fileList = fileList.slice(-2)
      fileList = fileList.map((file) => {
        if (file.response) {
          file.url = file.response.url
        }
        return file
      })

      this.fileList = fileList
    },
    uploadChange(info) {
      let fileList = [...info.fileList]
      fileList = fileList.slice(-1)
      fileList = fileList.map((file) => {
        if (file.response) {
          file.url = file.response.url
        }
        return file
      })
      if (info.file.status === 'uploading') {
        this.loading = true
      }
      if (info.file.status === 'done') {
        const file = info.file
        if (file.response.code === 0) {
          message.success(file.response.message)
          this.$emit('ok')
        } else {
          message.error(file.response.message)
        }
      } else if (info.file.status === 'error') {
        console.log(info.file.status)
        if (info.file.error.code === 500) {
          message.error(info.file.error.message)
        }
      }

      if (info.file.status === 'removed') {
        this.resources = {}
        this.disabled = false
      }
      this.fileList = fileList
    },
    beforeUpload(file, fileList) {
      this.resetForm()
    },
    /**
     * 自定义上传请求
     * @param {*} param
     */
    async customRequest({ onSuccess, onError, file, onProgress }) {
      new Promise((resolve, reject) => {
        if (!file.name && file.name === '') {
          reject(`文件名不能为空!`)
          return
        }
        const fix = file.name.split('.')[file.name.split('.').length - 1]
        const isAcceptType = this.typeMatch(fix)
        if (!isAcceptType) {
          reject(`文件类型限制为xlsx`)
          return
        }
        if (file.size > 524288000) {
          reject(`导入文件请勿超过50MB`)
          return
        }
        resolve()
      }).then(async () => {
        const formData = new FormData()
        formData.append('file', file)
        // formData.append('categoriesId', this.categoriesId)
        // formData.append('deptId', '1')
        try {
          this.disabled = true
          const res = await uploadIscspmFile({
            id: this.uuid,
            parameter: formData,
            onUploadProgress: (ev) => {
              // ev.loaded 当前已上传内容的大小，ev.total - 本次上传请求内容总大小
              console.log(ev)
              this.uploadMap.set(file.name, file.size)
              const percent = (ev.loaded / ev.total) * 100
              console.log(percent)
              // 计算出上传进度，调用组件进度条方法
              onProgress({ percent })
            }
          })
          if (res.data.code === 0) {
            onSuccess({ code: 0, message: `${file.name}导入成功` }, file)
          } else {
            throw res.data.msg
          }
          this.disabled = false
          this.uploadMap.delete(file.name)
        } catch (error) {
          this.disabled = true
          this.uploadMap.delete(file.name)
          onError({ code: 500, message: error }, file);
        }
      }).catch((err) => {
        onError({ code: 500, message: err }, file)
      })
    },
    typeMatch(fileType) {
      if (this.acceptType.includes(fileType)) {
        return true
      }
      return false
    },
    resetForm() {
      this.$refs.resources.resetFields()
    },
    downTemplate(value) {
      message.success('正在导出模板,请稍后.........')
      exportExcel({ isTemplate: value })
    },
  }
}
</script>
<style lang="less" scoped>
.ant-select.ant-select-enabled {
  width: 100%;
  margin-bottom: 20px;
}

.btn {
  border-radius: 0;

  &:last-child {
    border: none;
    border-radius: 0;
    background: #246df3;
    color: #fff;

    &:hover {
      filter: brightness(1.2);
    }
  }
}
</style>
