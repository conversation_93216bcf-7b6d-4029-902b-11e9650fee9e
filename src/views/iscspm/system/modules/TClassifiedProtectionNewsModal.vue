<template>
  <a-modal
    title="操作"
    style="top: 20px"
    :width="1000"
    v-model:visible="visible"
    :confirmLoading="confirmLoading"
    :destroyOnClose="true"
    @cancel="handleClose"
    @ok="handleSubmit"
  >
    <div style="padding: 20px" class="news">
      <a-form :model="form" ref="form">
        <a-row :gutter="64" style="padding: 0 18px">
          <a-col :span="12" style="border-right: 1px solid #eaeaea">
            <div class="modal-divider-text">基本信息</div>
            <a-form-item style="display: none">
              <a-input v-model:value="form.id" />
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="标题"
              name="title"
              :rules="[{ required: true, message: '请输入标题' }]"
            >
              <a-input placeholder="标题" v-model:value="form.title" />
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="分类"
              name="category"
              :rules="[{ required: true, message: '请输入分类' }]"
            >
              <a-select placeholder="分类" v-model:value="form.category">
                <a-select-option
                  v-for="(b, index) in categoryArr"
                  :key="index"
                  :value="b.value"
                  >{{ b.label }}</a-select-option
                >
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <div class="modal-divider-text">详细信息</div>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="来源"
              name="source"
              :rules="[{ required: true, message: '请输入来源' }]"
            >
              <a-input placeholder="来源" v-model:value="form.source" />
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="发布时间"
              name="releaseDate"
              :rules="[{ required: true, message: '请选择发布时间' }]"
            >
              <a-date-picker
                style="width: 100%;"
                v-model:value="form.releaseDate"
                format="YYYY-MM-DD"
                placeholder="请选择时间"
                :getCalendarContainer="(triggerNode) => triggerNode.parentNode"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <div style="padding: 0 16px 0 16px">
          <div class="modal-divider-text" style="margin-bottom: 15px;">主要内容</div>
          <a-form-item
            label="内容"
            style="padding: 0 16px 0 34px"
            name="content"
            :rules="[{ required: true, message: '请输入内容' }]"
          >
            <Editor
              placeholder="请输入内容..."
              v-model:value="form.content"
            ></Editor>
          </a-form-item>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>
<script>
import { saveTClassifiedProtectionNews } from "@/request/iscspm/TClassifiedProtectionNews";
import { pick } from "lodash";
import { getDictArray } from "@/utils/dict";
import Editor from "@/components/Editor";
import dayjs from "dayjs";
import { message } from "ant-design-vue";

export default {
  name: "TClassifiedProtectionNewsModal",
  props: {},
  components: {
    Editor,
  },
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 },
      },
      confirmLoading: false,
      mdl: {},
      form: {},
      categoryArr: [],
    };
  },
  beforeCreate() {},
  async created() {
    const categoryArr = await getDictArray("t_news_category");
    this.categoryArr = categoryArr.map((d) => {
      // this.categoryMap[d.dictValue] = { text: d.dictLabel }
      return {
        value: d.dictValue,
        label: d.dictLabel,
      };
    });
    await this.init();
  },
  methods: {
    init() {},
    add() {
      this.edit({ id: 0 });
    },
    edit(record) {
      this.mdl = Object.assign({}, record);
      const date1 = this.mdl.releaseDate ? dayjs(this.mdl.releaseDate) : null;
      this.mdl.releaseDate = date1;
      this.form = pick(
        this.mdl,
        "id",
        "title",
        "source",
        "releaseDate",
        "category",
        "content",
        "clicks",
        "status",
        "delFlag",
        "createBy",
        "createTime",
        "updateBy",
        "updateTime",
        "remark"
      );
      this.visible = true;
    },
    handleSubmit() {
      this.$refs["form"]
        .validateFields()
        .then((value) => {
          this.confirmLoading = true;
          const params = {
            ...this.form,
          };
          params.releaseDate = dayjs(params.releaseDate).format(
            "YYYY-MM-DD 00:00:00"
          );
          saveTClassifiedProtectionNews(params)
            .then((res) => {
              if (res.data.code === 0) {
                message.success("保存成功");
                this.handleClose();
                this.$emit("onOk");
              } else {
                message.error(res.data.msg);
              }
            })
            .catch(() => {
              message.error("系统错误，请稍后再试");
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        })
        .catch((err) => {
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    handleClose() {
      this.$refs["form"].resetFields();
      this.visible = false;
    },
  },
  watch: {
    /*
              'selectedRows': function (selectedRows) {
                this.needTotalList = this.needTotalList.map(item => {
                  return {
                    ...item,
                    total: selectedRows.reduce( (sum, val) => {
                      return sum + val[item.dataIndex]
                    }, 0)
                  }
                })
              }
              */
  },
};
</script>
<style lang="less" scoped>
.ant-calendar-picker {
  width: 100%;
}
.news {
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
}
</style>
