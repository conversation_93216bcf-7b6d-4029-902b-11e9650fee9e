<template>
  <a-modal
    title="操作"
    style="top: 20px"
    :width="450"
    v-model:visible="visible"
    :confirmLoading="confirmLoading"
    @ok="handleSubmit"
  >
    <a-form :model="form" ref="form">
      <a-form-item style="display: none">
        <a-input v-model:value="form.id" />
      </a-form-item>
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="姓名" name="name" :rules="[{ required: true, message: '请输入姓名' },{ max: 10, message: '已超出最大长度10,请重新输入' }]">
        <a-input placeholder="姓名" v-model:value="form.name" />
      </a-form-item>
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="联系电话" name="phone" :rules="[{ required: true, message: '请输入联系电话' }, { required: false, message: '请输入正确的手机号', pattern: /^1[3456789]\d{9}$/ }, { validateTrigger: ['blur', 'change' ] }]">
        <a-input
          placeholder="联系电话"
          v-model:value="form.phone"
        />
      </a-form-item>
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="机构名称" name="orgId" :rules="[{ required: true, message: '请输入机构名称' }]">
        <a-select
          v-model:value="form.orgId"
          :not-found-content="null"
          :filter-option="true"
          allowClear
          style="width: 100%"
          placeholder="请选择或输入机构名称"
        >
          <a-select-option v-for="(item, index) in list" :key="index" :value="item.id">
            {{ item.organizationName }}
          </a-select-option>
        </a-select>
      </a-form-item>
      <!-- <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="机构Id">
        <a-input
          placeholder="机构Id"
          v-decorator="['orgId', { rules: [{ required: true, message: '请输入机构Id' }] }]"
        />
      </a-form-item> -->
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="排序">
        <a-input-number placeholder="排序" :min="1" v-model:value="form.orderNum" />
      </a-form-item>
      <a-form-item :labelCol="labelCol" :wrapperCol="wrapperCol" label="备注">
        <a-textarea placeholder="备注" v-model:value="form.remark" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script>
import { saveTClassifiedProtectionExpert } from '@/request/iscspm/TClassifiedProtectionExpert'
import { getTClassifiedProtectionOrganizationList } from '@/request/iscspm/TClassifiedProtectionOrganization'
import { pick } from 'lodash'
import { message } from 'ant-design-vue'

export default {
  name: 'TClassifiedProtectionExpertModal',
  props: {},
  components: {},
  data() {
    return {
      list: [],
      // 选择器值(最终数据)
      result: undefined, // 选择下拉列表中选项后的值
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 5 }
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 16 }
      },
      confirmLoading: false,
      mdl: {},
      form: {}
    }
  },
  beforeCreate() {},
  created() {
    this.init()
  },
  methods: {
    async init() {
      const res = await getTClassifiedProtectionOrganizationList()
      if (res.data.code === 0) {
        this.list = res.data.rows
      }
    },
    add() {
      this.edit({ id: 0 })
    },
    edit(record) {
      this.mdl = Object.assign({}, record)
      this.visible = true
      this.form = 
        pick(
          this.mdl,
          'id',
          'name',
          'phone',
          'orgName',
          'orgId',
          'orderNum',
          'status',
          'delFlag',
          'createBy',
          'createTime',
          'updateBy',
          'updateTime',
          'remark'
        )
    },
    handleSubmit(e) {
      this.$refs["form"]
      .validateFields()
      .then((value) => {
          this.confirmLoading = true
          // this.form.orgId = ''
          // const idList = this.list.map((item) => {
          //   return item.id
          // })
          // const orgNameList = this.list.map((item) => {
          //   return item.organizationName
          // })
          // if (idList.includes(this.form.orgName)) {
          //   this.form.orgId = this.form.orgName
          //   this.form.orgName = ''
          // }

          // if (orgNameList.includes(this.form.orgName)) {
          //   const item = this.list.filter((item) => {
          //     return item.organizationName === this.form.orgName
          //   })[0]
          //   this.form.orgId = item.id
          //   this.form.orgName = ''
          // }
          saveTClassifiedProtectionExpert(this.form)
            .then((res) => {
              if (res.data.code === 0) {
                message.success('保存成功')
                this.handleClose();
                this.$emit("onOk");
              } else {
                message.error(res.data.msg)
              }
            })
            .catch(() => {
              message.error('系统错误，请稍后再试')
            })
            .finally(() => {
              this.confirmLoading = false
            })
        })
        .catch((err) => {
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    handleClose() {
      this.$refs["form"].resetFields();
      this.visible = false;
    },
  },
  watch: {
    /*
              'selectedRows': function (selectedRows) {
                this.needTotalList = this.needTotalList.map(item => {
                  return {
                    ...item,
                    total: selectedRows.reduce( (sum, val) => {
                      return sum + val[item.dataIndex]
                    }, 0)
                  }
                })
              }
              */
  }
}
</script>
<style lang="less" scoped>
.ant-form-item-children {
  width: 100%;
}
.ant-select.ant-select-enabled {
  width: 100% !important;
}
.ant-input-number {
  width: 100%;
}
</style>
