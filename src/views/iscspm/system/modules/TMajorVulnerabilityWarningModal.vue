<template>
  <a-modal
    title="漏洞"
    style="top: 20px"
    :width="1000"
    v-model:visible="visible"
    :bodyStyle="{ height: '70vh', padding: 0 }"
    :confirmLoading="confirmLoading"
    :destroyOnClose="true"
    @cancel="handleClose"
    @ok="handleSubmit"
  >
    <div style="padding: 24px" class="waring">
      <a-form :model="form" ref="form">
        <a-row :gutter="64">
          <a-col :span="12" style="border-right: 1px solid #eaeaea">
            <div class="modal-divider-text">基本信息</div>
            <a-form-item style="display: none">
              <a-input v-model:value="form.id" />
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="漏洞名称"
              name="vulnerabilityName"
              :rules="[{ required: true, message: '请输入漏洞名称' }]"
            >
              <a-input
                v-model:value="form.vulnerabilityName"
                placeholder="漏洞名称"
              />
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="漏洞编号"
              name="vulnerabilityNum"
              :rules="[{ required: true, message: '请输入漏洞编号' }]"
            >
              <a-input
                v-model:value="form.vulnerabilityNum"
                placeholder="漏洞编号"
              />
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="漏洞类型"
              name="vulnerabilityType"
              :rules="[{ required: true, message: '请选择漏洞类型' }]"
            >
              <a-select
                placeholder="请选择漏洞类型"
                v-model:value="form.vulnerabilityType"
              >
                <a-select-option
                  v-for="(b, index) in vulnerabilityTypeArray"
                  :key="index"
                  :value="b.cName"
                  >{{ b.cName + "(" + b.eName + ")" }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="危害等级"
              name="hazardLevel"
              :rules="[{ required: true, message: '请选择危害等级' }]"
            >
              <a-select
                placeholder="请选择危害等级"
                v-model:value="form.hazardLevel"
              >
                <a-select-option
                  v-for="(b, index) in hazardArr"
                  :key="index"
                  :value="b.value"
                >
                  {{ b.label }}
                </a-select-option>
              </a-select>
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="收录时间"
              name="collectionTime"
              :rules="[{ required: false, message: '请选择时间' }]"
            >
              <a-date-picker
                style="width: 100%;"
                v-model:value="form.collectionTime"
                format="YYYY-MM-DD"
                placeholder="请选择时间"
                :getCalendarContainer="(triggerNode) => triggerNode.parentNode"
              />
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <div class="modal-divider-text">详细信息</div>

            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="厂商"
              name="manufacturer"
              :rules="[{ required: false, message: '请输入厂商' }]"
            >
              <a-input v-model:value="form.manufacturer" placeholder="厂商" />
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="热度值"
              name="hotFlag"
              :rules="[{ required: false, message: '请输入热度值' }]"
            >
              <a-input-number v-model:value="form.hotFlag" placeholder="热度值" style="width: 100%;" />
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="CNNVD编号"
              name="cnnvdNum"
              :rules="[{ required: false, message: '请输入CNNVD编号' }]"
            >
              <a-input v-model:value="form.cnnvdNum" placeholder="CNNVD编号" />
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="CNVD编号"
              name="cnvdNum"
              :rules="[{ required: false, message: '请输入CNVD编号' }]"
            >
              <a-input v-model:value="form.cnvdNum" placeholder="CNVD编号" />
            </a-form-item>
            <a-form-item
              :labelCol="labelCol"
              :wrapperCol="wrapperCol"
              label="影响组件"
              name="impactComponents"
              :rules="[{ required: false, message: '请输入影响组件' }]"
            >
              <a-input
                v-model:value="form.impactComponents"
                placeholder="影响组件"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <div class="modal-divider-text">其它信息</div>
        <div style="padding: 0 16px 0 36px">
          <a-form-item
            label="漏洞描述"
            name="vulnerabilityDescription"
            :rules="[{ required: true, message: '请输入执行风险描述' }]"
          >
            <Editor
              placeholder="输入执行漏洞描述..."
              v-model:value="form.vulnerabilityDescription"
            ></Editor>
          </a-form-item>
          <a-form-item
            label="危害描述"
            name="hazardDescription"
            :rules="[{ required: true, message: '请输入危害描述' }]"
          >
            <Editor
              placeholder="输入危害描述..."
              v-model:value="form.hazardDescription"
            ></Editor>
          </a-form-item>
          <a-form-item
            label="加固建议"
            name="solution"
            :rules="[{ required: true, message: '请输入加固建议' }]"
          >
            <Editor
              placeholder="输入加固建议..."
              v-model:value="form.solution"
            ></Editor>
          </a-form-item>
        </div>
      </a-form>
    </div>
  </a-modal>
</template>
<script>
import { saveTMajorVulnerabilityWarning } from "@/request/iscspm/TMajorVulnerabilityWarning";
import Editor from "@/components/Editor";
import { pick } from "lodash";
import { getDictArray } from "@/utils/dict";
import { getTMajorVulnerabilityTypeListDict } from "@/request/iscspm/TMajorVulnerabilityType";
import dayjs from "dayjs";
import { message } from "ant-design-vue";

export default {
  name: "TMajorVulnerabilityWarningModal",
  props: {},
  components: {
    Editor,
    // ,
  },
  data() {
    return {
      visible: false,
      labelCol: {
        xs: { span: 24 },
        sm: { span: 8 },
      },
      wrapperCol: {
        xs: { span: 24 },
        sm: { span: 15 },
      },
      hotArr: [
        { value: 1, label: "是" },
        { value: 0, label: "否" },
      ],
      vulnerabilityTypeArray: [],
      hazardArr: [],
      confirmLoading: false,
      mdl: {},
      form: {},
    };
  },
  beforeCreate() {},
  async created() {
    const arr = await getDictArray("hazard_level");
    this.hazardArr = arr.map((d) => {
      return {
        value: d.dictValue,
        label: d.dictLabel,
      };
    });
    getTMajorVulnerabilityTypeListDict(null).then((res) => {
      this.vulnerabilityTypeArray = res.data.rows;
    });
  },
  methods: {
    add() {
      this.edit({ id: 0 });
    },
    async edit(record) {
      this.mdl = Object.assign({}, record);
      if (this.mdl.hazardLevel) {
        this.mdl.hazardLevel = String(this.mdl.hazardLevel);
      }
      if (this.mdl.collectionTime) {
        const date = dayjs(this.mdl.collectionTime);
        this.mdl.collectionTime = date;
      }
      if (this.mdl.updateTime) {
        const date = dayjs(this.mdl.updateTime);
        this.mdl.updateTime = date;
      }
      this.form = pick(
        this.mdl,
        "id",
        "vulnerabilityName",
        "vulnerabilityNum",
        "vulnerabilityType",
        "hazardLevel",
        "collectionTime",
        "manufacturer",
        "hotFlag",
        "cnnvdNum",
        "cnvdNum",
        "impactComponents",
        "vulnerabilityDescription",
        "hazardDescription",
        "solution",
        "delTag",
        "createTime",
        "createBy",
        "updateTime",
        "updateBy",
        "remark",
        "hotFlag",
        "cveNum"
      );
      this.visible = true;
    },
    handleSubmit() {
      this.$refs["form"]
        .validateFields()
        .then((value) => {
          this.confirmLoading = true;
          const params = {
            ...this.form
          }
          params.collectionTime = dayjs(params.collectionTime).format(
            "YYYY-MM-DD 00:00:00"
          );
          params.updateTime = dayjs(params.updateTime).format(
            "YYYY-MM-DD 00:00:00"
          );
          console.log(params, 'this.formthis.formthis.formthis.formthis.form')
          saveTMajorVulnerabilityWarning(params)
            .then((res) => {
              if (res.data.code === 0) {
                message.success("保存成功");
                this.handleClose()
                this.$emit("onOk");
              } else {
                message.error(res.data.msg);
              }
            })
            .catch(() => {
              message.error("系统错误，请稍后再试");
            })
            .finally(() => {
              this.confirmLoading = false;
            });
        })
        .catch((err) => {
          console.log("error submit!!");
          message.error("有必填项校验未通过,请填写后再保存");
          return false;
        });
    },
    handleClose() {
      this.$refs["form"].resetFields();
      this.visible = false;
    },
  },
  watch: {
    /*
      'selectedRows': function (selectedRows) {
        this.needTotalList = this.needTotalList.map(item => {
          return {
            ...item,
            total: selectedRows.reduce( (sum, val) => {
              return sum + val[item.dataIndex]
            }, 0)
          }
        })
      }
      */
  },
};
</script>
<style lang="less" scoped>
.waring {
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
}
</style>
