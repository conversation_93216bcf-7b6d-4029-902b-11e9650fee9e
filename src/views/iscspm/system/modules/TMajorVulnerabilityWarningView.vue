<template>
  <a-modal
      title="漏洞详情"
      style="top: 20px"
      :width="1000"
      v-model:visible="visible"
      :bodyStyle="{ height: '70vh', padding: 0 }"
      :destroyOnClose="true"
  >
    <template #footer>
      <a-button key="submit" type="primary" @click="handleClose">关闭</a-button>
    </template>
    <div style="padding: 24px" class="waring">
      <a-row :gutter="64">
        <!-- 基本信息 -->
        <a-col :span="12" style="border-right: 1px solid #eaeaea">
          <div class="modal-divider-text">基本信息</div>
          <a-descriptions :column="1">
            <a-descriptions-item label="漏洞名称">{{ form.vulnerabilityName || '-' }}</a-descriptions-item>
            <a-descriptions-item label="漏洞编号">{{ form.vulnerabilityNum || '-' }}</a-descriptions-item>
            <a-descriptions-item label="漏洞类型">{{ formatVulnerabilityType(form.vulnerabilityType) }}</a-descriptions-item>
            <a-descriptions-item label="危害等级">{{ getDictLabel(hazardArr, form.hazardLevel) }}</a-descriptions-item>
            <a-descriptions-item label="收录时间">{{ formatDate(form.collectionTime) }}</a-descriptions-item>
          </a-descriptions>
        </a-col>

        <!-- 详细信息 -->
        <a-col :span="12">
          <div class="modal-divider-text">详细信息</div>
          <a-descriptions :column="1">
            <a-descriptions-item label="厂商">{{ form.manufacturer || '-' }}</a-descriptions-item>
            <a-descriptions-item label="热度值">{{ form.hotFlag || 0 }}</a-descriptions-item>
            <a-descriptions-item label="CNNVD编号">{{ form.cnnvdNum || '-' }}</a-descriptions-item>
            <a-descriptions-item label="CNVD编号">{{ form.cnvdNum || '-' }}</a-descriptions-item>
            <a-descriptions-item label="影响组件">{{ form.impactComponents || '-' }}</a-descriptions-item>
          </a-descriptions>
        </a-col>
      </a-row>

      <!-- 其它信息 -->
      <div class="modal-divider-text">其它信息</div>
      <div style="padding: 0 16px 0 36px">
        <a-descriptions layout="vertical" :column="1">
          <a-descriptions-item label="漏洞描述">
            <div class="html-content" v-html="form.vulnerabilityDescription || '-'"></div>
          </a-descriptions-item>
          <a-descriptions-item label="危害描述">
            <div class="html-content" v-html="form.hazardDescription || '-'"></div>
          </a-descriptions-item>
          <a-descriptions-item label="加固建议">
            <div class="html-content" v-html="form.solution || '-'"></div>
          </a-descriptions-item>
        </a-descriptions>
      </div>
    </div>
  </a-modal>
</template>

<script>
import { getDictArray } from "@/utils/dict";
import { getTMajorVulnerabilityTypeListDict } from "@/request/iscspm/TMajorVulnerabilityType";
import dayjs from "dayjs";

export default {
  name: "TMajorVulnerabilityWarningView",
  data() {
    return {
      visible: false,
      hazardArr: [],
      vulnerabilityTypeArray: [],
      form: {}
    };
  },
  async created() {
    // 初始化字典数据
    const arr = await getDictArray("hazard_level");
    this.hazardArr = arr.map(d => ({ value: d.dictValue, label: d.dictLabel }));

    // 获取漏洞类型列表
    const res = await getTMajorVulnerabilityTypeListDict(null);
    this.vulnerabilityTypeArray = res.data.rows;
  },
  methods: {
    // 打开查看弹窗
    open(record) {
      this.form = { ...record };
      this.visible = true;
    },

    // 格式化漏洞类型显示
    formatVulnerabilityType(type) {
      const found = this.vulnerabilityTypeArray.find(item =>
          item.cName === type || item.eName === type
      );
      return found ? `${found.cName}(${found.eName})` : '-';
    },

    // 字典值转标签
    getDictLabel(options, value) {
      console.log(options)
      console.log(value)
      value = value + ''
      const item = options.find(opt => opt.value === value);
      return item ? item.label : '-';
    },

    // 日期格式化
    formatDate(date) {
      return date ? dayjs(date).format("YYYY-MM-DD") : '-';
    }
  }
};
</script>

<style scoped>
/* 富文本内容样式 */
.html-content {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #f0f0f0;
  padding: 8px;
  border-radius: 4px;
}

/* 描述列表样式 */
.ant-descriptions-item-label {
  width: 100px;
  text-align: right;
  color: rgba(0, 0, 0, 0.85);
}
.ant-descriptions-item-content {
  padding-left: 24px;
}

/* 分割线样式 */
.modal-divider-text {
  margin: 16px 0;
  color: #1890ff;
  font-weight: 500;
}
.waring {
  height: 100%;
  overflow: auto;
  overflow-x: hidden;
}
</style>
