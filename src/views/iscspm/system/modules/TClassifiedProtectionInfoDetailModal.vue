<template>
  <a-modal
      title="等保维护详情"
      style="top: 20px"
      :width="500"
      v-model:visible="visible"
  >
    <template #footer>
      <a-button key="submit" type="primary" @click="handleClose">关闭</a-button>
    </template>
  <a-descriptions :column="0">
    <!-- 系统名称 -->
    <a-descriptions-item label="系统名称">
      {{ form.systemName }}
    </a-descriptions-item>

    <!-- 公司名称 -->
    <a-descriptions-item label="公司名称">
      {{ getDictLabel(companyArr, form.deptId) }}
    </a-descriptions-item>

    <!-- 系统类型 -->
    <a-descriptions-item label="系统类型">
      {{ getDictLabel(systemTypeArr, form.systemType) }}
    </a-descriptions-item>

    <!-- 等保等级 -->
    <a-descriptions-item label="等保等级">
      {{ getDictLabel(protectionArr, form.protectionLevel) }}
    </a-descriptions-item>

    <!-- 备案时间 -->
    <a-descriptions-item label="备案时间">
      {{ formatDate(form.filingTime) }}
    </a-descriptions-item>

    <!-- 备案地点 -->
    <a-descriptions-item label="备案地点">
      {{ form.filingAddr }}
    </a-descriptions-item>

    <!-- 联系人 -->
    <a-descriptions-item label="联系人">
      {{ form.contactPerson }}
    </a-descriptions-item>

    <!-- 联系电话 -->
    <a-descriptions-item label="联系电话">
      {{ form.contactPhone }}
    </a-descriptions-item>

    <a-descriptions-item label="等保状态">
      {{ getDictLabel(expireStateArr, form.expireState) }}
    </a-descriptions-item>

    <a-descriptions-item label="到期时间">
      {{ form.expireDate }}
    </a-descriptions-item>

    <a-descriptions-item label="距到期时间">
      {{ form.expireDay }}
    </a-descriptions-item>
  </a-descriptions>
  </a-modal>
</template>

<script>
import { getDictArray } from "@/utils/dict";
import { getDeptLists } from "@/request/iscspm/system";
import dayjs from "dayjs";

export default {
  name: "TClassifiedProtectionInfoView",
  data() {
    return {
      visible: false,
      form: {},
      systemTypeArr: [],
      protectionArr: [],
      companyArr: [],
      expireStateArr: []
    };
  },
  async created() {
    // 初始化字典数据
    const loadDicts = async () => {
      const [companyArr, systemTypeArr, protectionArr,expireStateArr] = await Promise.all([
        getDictArray("iscspm_company"),
        getDictArray("t_system_type"),
        getDictArray("t_protection_level"),
        getDictArray('t_expire_state')
      ]);

      this.companyArr = companyArr.map(d => ({
        value: d.dictValue,
        label: d.dictLabel
      }));

      this.systemTypeArr = systemTypeArr.map(d => ({
        value: d.dictValue,
        label: d.dictLabel
      }));

      this.protectionArr = protectionArr.map(d => ({
        value: d.dictValue,
        label: d.dictLabel
      }));

      this.expireStateArr = expireStateArr.map(d => ({
        value: d.dictValue,
        label: d.dictLabel
      }));
    };

    await loadDicts();
  },
  methods: {
    // 打开查看弹窗
    open(record) {
      this.form = { ...record };
      if (this.form.filingTime) {
        this.form.filingTime = dayjs(this.form.filingTime);
      }
      this.visible = true;
    },

    // 字典值转标签
    getDictLabel(options, value) {
      const item = options.find(opt => opt.value === value);
      return item ? item.label : "-";
    },

    // 日期格式化
    formatDate(date) {
      return date ? dayjs(date).format("YYYY/MM/DD") : "-";
    },

    // 电话格式化
    formatPhone(phone) {
      return phone ? phone.replace(/(\d{3})(\d{4})(\d{4})/, "$1****$2") : "-";
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false;
    }
  }
};
</script>

<style scoped>
/* 调整描述列表样式 */
.ant-descriptions-item-content {
  padding: 12px 24px !important;
}
.ant-descriptions-item-label {
  background: #fafafa;
  width: 120px;
  text-align: right;
}
</style>
