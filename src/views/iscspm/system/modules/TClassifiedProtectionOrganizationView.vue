<template>
  <a-modal
      title="详情"
      style="top: 20px"
      :width="500"
      v-model:visible="visible"
  >
    <template #footer>
      <a-button key="submit" type="primary" @click="handleClose">关闭</a-button>
    </template>
  <a-descriptions :column="1" >
    <!-- 机构名称 -->
    <a-descriptions-item label="机构名称">
      {{ form.organizationName || '-' }}
    </a-descriptions-item>

    <!-- 地址 -->
    <a-descriptions-item label="地址">
      {{ form.address || '-' }}
    </a-descriptions-item>

    <!-- 联系人 -->
    <a-descriptions-item label="联系人">
      {{ form.contactPerson || '-' }}
    </a-descriptions-item>

    <!-- 联系电话 -->
    <a-descriptions-item label="联系电话">
      {{ form.contactPhone }}
    </a-descriptions-item>

    <!-- 联系邮箱 -->
    <a-descriptions-item label="联系邮箱">
      {{ form.contactEmail || '-' }}
    </a-descriptions-item>

    <!-- 排序 -->
    <a-descriptions-item label="排序">
      {{ form.orderNum || 0 }}
    </a-descriptions-item>
  </a-descriptions>
  </a-modal>
</template>

<script>
import { pick } from 'lodash'

export default {
  name: 'TClassifiedProtectionOrganizationView',
  data() {
    return {
      visible: false,
      form: {}
    }
  },
  methods: {
    // 打开查看弹窗
    open(record) {
      this.form = pick(record,
          'id',
          'organizationName',
          'address',
          'contactPerson',
          'contactPhone',
          'contactEmail',
          'orderNum'
      )
      this.visible = true
    },

    // 电话脱敏显示
    formatPhone(phone) {
      if (!phone) return '-'
      // 保留前3后4，中间用****代替
      return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false
    }
  }
}
</script>

<style scoped>
/* 调整描述列表样式 */
.ant-descriptions-item-content {
  padding: 12px 24px !important;
  min-width: 200px;
}
.ant-descriptions-item-label {
  background: #fafafa;
  width: 100px;
  text-align: right;
}
</style>
