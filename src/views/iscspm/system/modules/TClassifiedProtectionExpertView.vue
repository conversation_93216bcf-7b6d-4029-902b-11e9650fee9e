<template>
  <a-modal
      title="专家详情"
      style="top: 20px"
      :width="450"
      v-model:visible="visible"
      :footer="null"
  >
    <template #footer>
      <a-button key="submit" type="primary" @click="handleClose">关闭</a-button>
    </template>
  <a-descriptions :column="1">
    <!-- 姓名 -->
    <a-descriptions-item label="姓名">
      {{ form.name || '-' }}
    </a-descriptions-item>

    <!-- 联系电话 -->
    <a-descriptions-item label="联系电话">
      {{ form.phone }}
    </a-descriptions-item>

    <!-- 机构名称 -->
    <a-descriptions-item label="所属机构">
      {{ getOrgName(form.orgId) }}
    </a-descriptions-item>

    <!-- 排序 -->
    <a-descriptions-item label="排序">
      {{ form.orderNum || 0 }}
    </a-descriptions-item>

    <!-- 备注 -->
    <a-descriptions-item label="备注">
      {{ form.remark || '-' }}
    </a-descriptions-item>
  </a-descriptions>
  </a-modal>
</template>

<script>
import { pick } from 'lodash'
import { getTClassifiedProtectionOrganizationList } from '@/request/iscspm/TClassifiedProtectionOrganization'

export default {
  name: 'TClassifiedProtectionExpertView',
  data() {
    return {
      visible: false,
      list: [],  // 机构列表
      form: {}
    }
  },
  async created() {
    await this.loadOrganizations()
  },
  methods: {
    // 加载机构数据
    async loadOrganizations() {
      const res = await getTClassifiedProtectionOrganizationList()
      if (res.data.code === 0) {
        this.list = res.data.rows
      }
    },

    // 打开查看弹窗
    open(record) {
      this.form = pick(record,
          'id',
          'name',
          'phone',
          'orgId',
          'orderNum',
          'remark'
      )
      this.visible = true
    },

    // 机构ID转名称
    getOrgName(orgId) {
      const org = this.list.find(item => item.id === orgId)
      return org ? org.organizationName : '-'
    },

    // 电话脱敏显示
    formatPhone(phone) {
      return phone ? phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2') : '-'
    },

    // 关闭弹窗
    handleClose() {
      this.visible = false
    }
  }
}
</script>

<style scoped>
/* 调整描述列表样式 */
.ant-descriptions-item-content {
  padding: 12px 24px !important;
  min-width: 200px;
}
.ant-descriptions-item-label {
  background: #fafafa;
  width: 100px;
  text-align: right;
}
</style>
