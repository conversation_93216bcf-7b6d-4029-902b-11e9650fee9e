<template>
  <a-card :bordered="false" style="height: 100%" class="system-card">
    <div class="page-search" id="datalist_search">
      <div class="page-search-item">
        <div class="page-search-item-label">中文名：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            allowClear
            placeholder="请输入中文名"
            v-model:value="searchData.cName"
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">英文名：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            allowClear
            placeholder="请输入英文名"
            v-model:value="searchData.eName"
          />
        </div>
      </div>
      <div class="page-search-item">
        <div class="page-search-item-label">缩写：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            allowClear
            placeholder="请输入缩写"
            v-model:value="searchData.abbreviation"
          />
        </div>
      </div>
      <!-- <div class="page-search-item">
        <div class="page-search-item-label">危害等级：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-select
                style="width: 100%;"
                allowClear
                placeholder="请选择危害等级"
                v-model:value="searchData.hazardLevel"
              >
                <a-select-option v-for="(b, index) in hazardArr" :key="index" :value="b.value">{{
                    b.label
                  }}</a-select-option>
              </a-select>
        </div>
      </div> -->
      <!-- <div class="page-search-item">
        <div class="page-search-item-label">CVE编号：</div>
        <div class="page-search-item-value" style="display: flex">
          <a-input
            allowClear
            placeholder="请输入CVE编号"
            v-model:value="searchData.cveNum"
          />
        </div>
      </div> -->

      <div class="page-search-buttons">
        <a-button type="primary" @click="beforeSearch()">
          <template #icon> <SearchOutlined /> </template>查询
        </a-button>
        <a-button @click="handleReset()" style="margin-left: 15px">
          重置
        </a-button>
        <a-button type="primary" @click="handleAdd()" style="margin-left: 15px">
          <template #icon> <PlusOutlined /> </template>新增
        </a-button>
      </div>
    </div>
    <!-- 表格展示栏 -->
    <div class="page-content" :style="{ minHeight: flexHeight }">
      <a-row>
        <a-col :span="24">
          <a-table
            :columns="tableColumns"
            :data-source="tableData"
            :loading="tableLoading"
            :pagination="pagination"
            :scroll="{ y: tableHeight }"
            @change="handleTableChange"
          >
          <template #num="{ index }">
            <span>{{
              (pagination.current - 1) * pagination.pageSize +
              Number(index) +
              1
            }}</span>
          </template>
          <template #hazardLevel="{ text }">
            {{ hazardFilter(text) }}
          </template>
            <template #action="{ record }">
              <a-button
                  type="link"
                  style="color: #387ff1"
                  @click="handleDetail(record)"
              >
                查看
              </a-button>
              <a-button
                type="link"
                style="color: #387ff1"
                @click="handleEdit(record)"
              >
                编辑
              </a-button>
              <a-button
                type="link"
                style="color: #fc5532"
                @click="handleDelete(record)"
              >
                删除
              </a-button>
            </template>
          </a-table>
        </a-col>
      </a-row>
    </div>
    <tMajorVulnerabilityType-modal ref="modal" @onOk="onOk"/>
    <TMajorVulnerabilityTypeView ref="detail"/>
  </a-card>
</template>

<script>
import {
  SearchOutlined,
  PlusOutlined,
  DownloadOutlined,
    UploadOutlined
} from "@ant-design/icons-vue";
import { delTMajorVulnerabilityType, getTMajorVulnerabilityTypeList } from '@/request/iscspm/TMajorVulnerabilityType'
import TMajorVulnerabilityTypeModal from './modules/TMajorVulnerabilityTypeModal.vue'
import TMajorVulnerabilityTypeView from './modules/TMajorVulnerabilityTypeView.vue'
import { getDictArray } from '@/utils/dict'
import { Modal, message } from 'ant-design-vue';

export default {
  name: "TableList",
  components: {
    TMajorVulnerabilityTypeModal,
    TMajorVulnerabilityTypeView,
    SearchOutlined,
    PlusOutlined
  },
  data() {
    return {
      categoryArray: [],
      flexHeight: "0px",
      tableHeight: 0,
      operType: "add", //控制弹窗标题是新增还是修改，以及后续确认按钮调用新增接口还是修改接口
      tableLoading: false,
      searchData: {
      },
      lastsearchData: {
      },
      tableColumns: [
        {
          title: "序号",
          dataIndex: "num",
          width: "5%",
          align: "center",
          slots: {
            customRender: "num",
          },
        },
        {
          title: '中文名',
          dataIndex: 'cName',
        },
        {
          title: '英文名',
          dataIndex: 'eName'
        },
        {
          title: '缩写',
          dataIndex: 'abbreviation'
        },
        {
          title: '操作',
          width: '240px',
          dataIndex: 'action',
          align: 'center',
          slots: { customRender: "action" },
        }
      ],
      tableData: [],
      pagination: {
        current: 1,
        pageSize: 10,
        total: 0,
        showTotal: (total) => `共${total}项`,
        showQuickJumper: true,
        showSizeChanger: true,
        pageSizeOptions: ["10", "20", "50"],
      },
      selectedRowKeys: [],
    };
  },
  async created() {
  },
  computed: {
    searchHeight() {
      return document.getElementById("datalist_search").offsetHeight;
    },
  },
  mounted() {
    setTimeout(() => {
      this.flexHeight = `calc(${
        document.getElementsByClassName("system-card")[0].clientHeight
        }px - ${this.searchHeight}px - 48px)`;
        this.tableHeight =
        document.getElementsByClassName("system-card")[0].clientHeight -
        this.searchHeight -
        180;
      }, 100);
    this.handleSearch(true)
  },
  methods: {
    beforeSearch() {
      this.lastsearchData = {
        ...this.searchData,
      };
      this.handleSearch(true);
    },
    handleSearch(flag) {
      console.log(new Date())
      //查询表格数据，进入界面默认无条件查询一次，用于展示表格
      if (flag) {
        // 如果是点击查询按钮，需要从第一页开始查询
        this.pagination.current = 1;
        // this.pagination.pageSize = 10;
      }
      const params = {
        ...this.lastsearchData,
        pageSize: this.pagination.pageSize,
        pageNum: this.pagination.current,
      };
      this.tableLoading = true;
      getTMajorVulnerabilityTypeList(params)
        .then((res) => {
          if (res.data.code === 0) {
            this.tableData = [];
            this.tableData = res.data.rows;
            this.pagination.total = res.data.total;
          } else {
            message.error(res.msg);
          }
          this.tableLoading = false;
        })
        .catch((error) => {
          message.error(error.msg);
          this.tableLoading = false;
        });
    },
    handleTableChange(pagination) {
      this.pagination = { ...pagination, showTotal: (total) => `共${total}项` };
      this.handleSearch();
    },
    handleReset() {
      this.lastsearchData = {
      };
      this.searchData = {
      };
      this.handleSearch(true); //查询表格数据，进入界面默认无条件查询一次，用于展示表格
    },
    handleAdd() {
      this.$refs.modal.add();
    },
    handleEdit(record) {
      this.$refs.modal.edit(record);
    },
    handleDetail(record) {
      this.$refs.detail.open(record);
    },
    handlePreview(record) {
      this.$refs.previewModal.edit(record)
    },
    handleDelete(value) {
      Modal.confirm({
        title: "确定要删除选中内容吗?",
        okText: "确定",
        okType: "primary",
        cancelText: "取消",
        onOk: async () => {
          delTMajorVulnerabilityType({ ids: value.id }).then((res) => {
            if (res.data.code === 0) {
              message.success('删除成功')
              this.handleSearch();
            } else {
              message.error(res.data.msg)
            }
          })
          },
          onCancel() {},
        });
    },
    onOk() {
      this.handleSearch(true);
    },
  },
  watch: {},
};
</script>
