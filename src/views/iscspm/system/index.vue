<template>
    <div class="page">
      <!-- 搜索条件栏 -->
      <div class="page-left">
        <div v-if="typeListData.length > 0" class="menuList">
          <div
            class="listItem"
            v-for="(item, i) in typeListData"
            :key="i"
            @click="clickCategory(item.id)"
            :class="currentTypeIndex === item.id ? 'active' : ''"
            :title="item.name"
          >
            {{ item.name }}
          </div>
        </div>
        <div class="menuList" v-else>
          <div
            style="
              font-size: 16px;
              height: 100%;
              display: flex;
              justify-content: center;
              align-items: center;
            "
          >
            暂无内容
          </div>
        </div>
      </div>
      <div class="page-right" style="width: calc(100% - 180px);">
        <component :is="componentName" style="height: 100%"></component>
      </div>
    </div>
  </template>
  
  <script>
  import {
    InboxOutlined,
    UploadOutlined,
    SearchOutlined,
  } from "@ant-design/icons-vue";

  import TClassifiedProtectionNewsList from './TClassifiedProtectionNewsList.vue'
  import TClassifiedProtectionInfoList from './TClassifiedProtectionInfoList.vue'
  import TClassifiedProtectionOrganizationList from './TClassifiedProtectionOrganizationList.vue'
  import TClassifiedProtectionExpertList from './TClassifiedProtectionExpertList.vue'
  import TMajorVulnerabilityWarningList from './TMajorVulnerabilityWarningList.vue'
  import TMajorVulnerabilityTypeList from './TMajorVulnerabilityTypeList.vue'
  
  export default {
    components: {
      InboxOutlined,
      UploadOutlined,
      SearchOutlined,
      TClassifiedProtectionNewsList,
      TClassifiedProtectionInfoList,
      TClassifiedProtectionOrganizationList,
      TClassifiedProtectionExpertList,
      TMajorVulnerabilityWarningList,
      TMajorVulnerabilityTypeList
    },
    data() {
      return {
        componentName: null,
        currentTypeIndex: null,
        typeListData: [
          {
            id: "TClassifiedProtectionNewsList",
            name: "公文资讯管理",
          },
          {
            id: "TClassifiedProtectionInfoList",
            name: "等保维护",
          },
          {
            id: "TClassifiedProtectionOrganizationList",
            name: "测评机构",
          },
          {
            id: "TClassifiedProtectionExpertList",
            name: "专家管理",
          },
          {
            id: "TMajorVulnerabilityWarningList",
            name: "重大漏洞",
          },
          {
            id: "TMajorVulnerabilityTypeList",
            name: "漏洞类型",
          },
        ],
      };
    },
    computed: {
      searchHeight() {
        return document.getElementById("datalist_search").offsetHeight;
      },
    },
    created() {
      this.currentTypeIndex = this.typeListData[0].id;
      this.componentName = this.currentTypeIndex;
    },
    mounted() {},
    methods: {
      clickCategory(index) {
        this.currentTypeIndex = index;
        this.componentName = index;
      },
    },
  };
  </script>
  
  <style scoped lang="less">
  .page {
    display: flex;
    align-items: flex-start;
    margin-top: 15px;
    gap: 15px;
    .page-left {
      overflow: hidden;
      width: 180px;
      background: #fff;
      border-radius: 5px;
    }
    .page-left,
    .page-right {
      height: calc(100vh - 136px);
    }
  }
  
  .menuList {
    height: 605px;
    width: 100%;
    padding: 0;
    overflow-y: overlay;
  
    .listItem {
      font-size: 16px;
      font-family: PingFangSC-Regular, PingFang SC;
      font-weight: 600;
      color: #3c404c;
      padding: 16px 20px;
      cursor: pointer;
      // transition: all 0.3s;
      text-align: center;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
  
      &:hover {
        background: #0f79e9;
        color: #fff;
      }
    }
  
    //   .listItem:not(:first-child) {
    //     margin: 10px 0;
    //   }
    .active {
      background: #0f79e9;
      color: #fff;
    }
  }
  
  .ant-card {
    margin-bottom: 1px;
  }
  </style>
  