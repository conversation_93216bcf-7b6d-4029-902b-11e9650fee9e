<!-- 安全设备弹窗 -->

<template>
  <component
    v-model:visible="visible"
    :is="componentName"
    v-if="visible"
    :row-data="rowData"
    @confirm="refresh"
  />
</template>

<script >
import {ref,computed,defineComponent, provide } from "vue";
import IpsConfigDrawer from "./components/ips/index.vue";
import LeakScanConfigDrawer from "./components/leak-scan/index.vue";
import WafConfigDrawer from "./components/waf/index.vue";
import DatabaseConfigDrawer from './components/database-audit/index.vue';
import LogConfigDrawer from './components/log-audit/index.vue';
import FireWallConfigDrawer from './components/firewall/index.vue';
import WagConfigDrawer from './components/wag/index.vue';
import IdsConfigDrawer from './components/ids/index.vue';
import DdosConfigDrawer from './components/ddos/index.vue';
import EdrConfigDrawer from './components/edr/index.vue';
import { updateDeviceSafetyId } from '@/utils/device-type-id'


export default defineComponent({
  props: {
    typeId: {
      type: [String, Number],
      default: "",
    },
    typeData: {
      type: Array,
      default: () => [],
    }
  },
  components: {
    IpsConfigDrawer,
    LeakScanConfigDrawer,
    WafConfigDrawer,
    DatabaseConfigDrawer,
    FireWallConfigDrawer,
    WagConfigDrawer,
    IdsConfigDrawer,
    LogConfigDrawer,
    DdosConfigDrawer,
    EdrConfigDrawer
  },
  emits: ["refresh"],
  setup(props, { emit }) {
    const visible = ref(false);
    const rowData = ref({});

    // 打开弹窗
    function open(data) {
      visible.value = true;
      rowData.value = data;
      updateDeviceSafetyId(data.id);
    }

    // 动态组件
    const componentName = computed(() => {
      const deviceItem = props.typeData.find((item) => item.deviceTypeId === rowData.value.deviceTypeId);
      if(!deviceItem) return 'IpsConfigDrawer';
      const deviceTypeKey = deviceItem.deviceTypeKey;
      const componentMap = {
        'ips_qiming': "IpsConfigDrawer",
        'waf_qiming': "WafConfigDrawer",
        'vul_qiming': "LeakScanConfigDrawer",
        'db_audit_qiming': 'DatabaseConfigDrawer',
        'firewall_qiming': 'FireWallConfigDrawer',
        'wag_qiming': 'WagConfigDrawer',
        'ids_qiming': 'IdsConfigDrawer',
        'log_audit_qiming':'LogConfigDrawer',
        'ddos_qiming': 'DdosConfigDrawer',
        'edr_qiming': 'EdrConfigDrawer'
      };
      return componentMap[deviceTypeKey] || 'IpsConfigDrawer';
    });

    const deviceSafetyId = computed(() => rowData.value.id);

    provide('deviceSafetyId', deviceSafetyId)

    function refresh() {
      emit("refresh");
    }

    return {
      visible,
      rowData,
      open,
      componentName,
      refresh,
    };
  },
});
</script>
