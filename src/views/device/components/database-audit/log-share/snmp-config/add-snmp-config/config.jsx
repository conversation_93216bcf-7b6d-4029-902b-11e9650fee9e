// 版本选项
const VERSION_OPTIONS = [
  { label: 'V1', value: 'V1' },
  { label: 'V2C', value: 'V2C' },
  { label: 'V3', value: 'V3' }
];

// 对应级别选项
const LEVEL_OPTIONS = [
  { label: '紧急(emerg)', value: '紧急(emerg)' },
  { label: '告警(alert)', value: '告警(alert)' },
  { label: '严重(crit)', value: '严重(crit)' },
  { label: '错误(err)', value: '错误(err)' },
  { label: '警示(warning)', value: '警示(warning)' },
  { label: '通知(notice)', value: '通知(notice)' },
  { label: '信息(info)', value: '信息(info)' },
  { label: '调试(debug)', value: '调试(debug)' }
];

// 编码方式选项
const ENCODED_OPTIONS = [
  { label: 'UTF-8', value: 'UTF-8' },
  { label: 'GB2312', value: 'GB2312' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: true,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          },
          name: {
            type: 'string',
            title: '名称',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          trap: {
            type: 'string',
            title: 'Trap地址',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入Trap地址'
              }
            ]
          },
          groupWord: {
            type: 'string',
            title: 'SNMP团体',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入SNMP团体'
              }
            ]
          },
          version: {
            type: 'string',
            title: '版本',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择版本',
            },
            enum: VERSION_OPTIONS
          },
          timeLimit: {
            type: 'string',
            title: '时间阈值',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入，如20，单位为秒',
            }
          },
          level: {
            type: 'string',
            title: '对应级别',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择对应级别',
            },
            enum: LEVEL_OPTIONS
          },
          encoded: {
            type: 'string',
            title: '编码方式',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择编码方式',
            },
            enum: ENCODED_OPTIONS
          }
        }
      }
    }
  };
}
