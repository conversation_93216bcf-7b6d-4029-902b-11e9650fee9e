// SNMP配置

// 版本选项
export const VERSION_OPTIONS = [
  { label: 'V1', value: 'V1' },
  { label: 'V2C', value: 'V2C' },
  { label: 'V3', value: 'V3' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入配置名称',
    },
  },
  {
    title: 'Trap地址',
    name: 'trap',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入Trap地址',
    },
  },
  {
    title: 'SNMP团体',
    name: 'groupWord',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入SNMP团体',
    },
  },
  {
    title: '版本',
    name: 'version',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择版本',
    },
    enum: [
      { label: '全部', value: null },
      ...VERSION_OPTIONS
    ],
  },
  {
    title: '启用状态',
    name: 'enable',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择启用状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: "center",
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: 'Trap地址',
      dataIndex: 'trap',
      key: 'trap',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: 'SNMP团体',
      dataIndex: 'groupWord',
      key: 'groupWord',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 100,
      align: "center",
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
