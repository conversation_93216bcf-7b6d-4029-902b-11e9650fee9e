// 协议选项 - SFTP固定协议
const PROTOCOL_OPTIONS = [
  { label: 'SFTP', value: 'SFTP' }
];

// 对应级别选项
const LEVEL_OPTIONS = [
  { label: '紧急(emerg)', value: '紧急(emerg)' },
  { label: '警报(alert)', value: '警报(alert)' },
  { label: '严重(crit)', value: '严重(crit)' },
  { label: '错误(err)', value: '错误(err)' },
  { label: '警告(warning)', value: '警告(warning)' },
  { label: '通知(notice)', value: '通知(notice)' },
  { label: '信息(info)', value: '信息(info)' },
  { label: '调试(debug)', value: '调试(debug)' }
];

// 编码方式选项
const ENCODED_OPTIONS = [
  { label: 'UTF8', value: 'UTF8' },
  { label: 'UTF-8', value: 'UTF-8' },
  { label: 'GBK', value: 'GBK' },
  { label: 'GB2312', value: 'GB2312' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: true,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          },
          name: {
            type: 'string',
            title: '名称',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          ip: {
            type: 'string',
            title: 'IP地址',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入IP地址'
              }
            ]
          },
          port: {
            type: 'string',
            title: '端口',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入端口'
              }
            ]
          },
          protocol: {
            type: 'string',
            title: '协议',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入协议'
              }
            ]
          },
          username: {
            type: 'string',
            title: '用户名',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入用户名'
              }
            ]
          },
          password: {
            type: 'string',
            title: '密码',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
              type: 'password'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入密码'
              }
            ]
          },
          directory: {
            type: 'string',
            title: '目录',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '/',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入目录'
              }
            ]
          },
          timeLimit: {
            type: 'string',
            title: '时间阈值',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入，如20，单位为秒',
            }
          },
          level: {
            type: 'string',
            title: '对应级别',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择对应级别',
            },
            enum: LEVEL_OPTIONS
          },
          encoded: {
            type: 'string',
            title: '编码方式',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择编码方式',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择编码方式'
              }
            ],
            enum: ENCODED_OPTIONS
          }
        }
      }
    }
  };
}
