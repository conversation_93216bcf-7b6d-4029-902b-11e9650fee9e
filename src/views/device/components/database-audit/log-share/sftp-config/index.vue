<template>
  <div>
    <!-- <DynamicSearch
      ref="dynamicSearchRef"
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    /> -->

    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" @click="handleView(record)">详情</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>

    <AddSftpConfig ref="addSftpConfigRef" @refresh="handleRefresh" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import DynamicSearch from '@/components/dynamic-search/index.vue';
import CommonTable from '@/components/common-table/index.vue';
import { SEARCH_CONFIG, getTableColumns } from './config';
import AddSftpConfig from './add-sftp-config/index.vue';
import {
  getSftpConfigPageData,
  deleteSftpConfig,
  modifySftpConfig
} from '@/request/api-device-db/log-share';

const searchParams = ref({});
const tableRef = ref(null);
const dynamicSearchRef = ref(null);
const addSftpConfigRef = ref(null);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

function handleSearch (params) {
  searchParams.value = params;
  handleRefresh();
}

function handleRefresh () {
  tableRef.value?.refresh();
}

async function fetchData (params) {
  try {
    const { data } = await getSftpConfigPageData({
      ...params,
      ...searchParams.value,
    });
    return { items: data.data, total: data.total };
  } catch (error) {
    console.error('获取数据失败:', error);
    return { items: [], total: 0 };
  }
}

const tableColumns = computed(() => getTableColumns(tableRef.value?.pagination ?? {}, handleStatusChange));

// 状态切换处理
async function handleStatusChange(record) {
  try {
    // 调用修改接口
    await modifySftpConfig(record);
    message.success(`${record.enable ? '启用' : '禁用'}成功`);
  } catch (error) {
    console.error('状态切换失败:', error);
    message.error('状态切换失败');
    throw error; // 重新抛出错误，让配置文件中的 finally 块处理加载状态
  }
}

// 新增
function handleAdd () {
  addSftpConfigRef.value.open({
    title: '新增SFTP',
    data: {},
    mode: 'add',
  });
}

// 编辑
function handleEdit (record) {
  addSftpConfigRef.value.open({
    title: '编辑SFTP',
    data: { ...record },
    mode: 'edit',
  });
}

// 查看
function handleView (record) {
  addSftpConfigRef.value.open({
    title: '查看SFTP',
    data: { ...record },
    mode: 'view',
  });
}

// 删除
async function handleDelete (record) {
  Modal.confirm({
    title: '删除SFTP配置',
    content: '确定删除该SFTP配置吗？',
    onOk: async () => {
      try {
        await deleteSftpConfig({ id: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (err) {
        message.error('删除失败');
      }
    },
  });
}
</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  ::v-deep .ant-btn {
    padding: 4px 6px;
  }
}
</style>
