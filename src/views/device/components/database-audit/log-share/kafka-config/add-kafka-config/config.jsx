// 认证方式选项
const REGISTER_WAY_OPTIONS = [
  { label: "无", value: "无" },
  { label: "SASL/PLAIN", value: "SASL/PLAIN" },
  { label: "SASL/SCRAM-SHA-256", value: "SASL/SCRAM-SHA-256" },
  { label: "SASL/SCRAM-SHA-512", value: "SASL/SCRAM-SHA-512" },
];

// 对应级别选项
const LEVEL_OPTIONS = [
  { label: "紧急(emerg)", value: "紧急(emerg)" },
  { label: "告警(alert)", value: "告警(alert)" },
  { label: "严重(crit)", value: "严重(crit)" },
  { label: "错误(err)", value: "错误(err)" },
  { label: "警示(warning)", value: "警示(warning)" },
  { label: "通知(notice)", value: "通知(notice)" },
  { label: "信息(info)", value: "信息(info)" },
  { label: "调试(debug)", value: "调试(debug)" },
];

// 编码方式选项
const ENCODED_OPTIONS = [
  { label: "UTF-8", value: "UTF-8" },
  { label: "GB2312", value: "GB2312" },
];

export function getSchema() {
  return {
    type: "object",
    properties: {
      layout: {
        type: "void",
        "x-component": "FormLayout",
        "x-component-props": {
          labelWidth: 120,
        },
        properties: {
          enable: {
            type: "boolean",
            default: true,
            title: "启用",
            "x-component": "Switch",
            "x-decorator": "FormItem",
            "x-component-props": {
              checkedChildren: "启用",
              unCheckedChildren: "禁用",
            },
          },
          name: {
            type: "string",
            title: "名称",
            "x-component": "Input",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
            },
            "x-validator": [
              {
                required: true,
                message: "请输入名称",
              },
            ],
          },
          serversAddr: {
            type: "array",
            title: "服务器地址",
            "x-component": "ArrayItems",
            "x-decorator": "FormItem",
            "x-validator": [
              {
                required: true,
                message: "请添加服务器地址",
              },
            ],
            items: {
              type: "object",
              properties: {
                space: {
                  type: "void",
                  "x-component": "Space",
                  properties: {
                    ip: {
                      type: "string",
                      "x-decorator": "FormItem",
                      "x-component": "Input",
                      "x-component-props": {
                        placeholder: "请输入",
                        addonBefore: "IP地址",
                      },
                      "x-validator": [
                        {
                          required: true,
                          message: "请输入IP地址",
                        },
                      ],
                    },
                    port: {
                      type: "string",
                      "x-decorator": "FormItem",
                      "x-component": "Input",
                      "x-component-props": {
                        placeholder: "请输入",
                        addonBefore: "端口",
                      },
                      "x-validator": [
                        {
                          required: true,
                          message: "请输入端口",
                        },
                      ],
                    },
                    hostname: {
                      type: "string",
                      "x-decorator": "FormItem",
                      "x-component": "Input",
                      "x-component-props": {
                        placeholder: "请输入",
                        addonBefore: "主机名",
                      },
                    },
                    remove: {
                      type: "void",
                      "x-decorator": "FormItem",
                      "x-component": "ArrayItems.Remove",
                      "x-component-props": {
                        style: {
                          marginTop: "5px",
                        },
                      },
                    },
                  },
                },
              },
            },
            properties: {
              add: {
                type: "void",
                title: "新增",
                "x-component": "ArrayItems.Addition",
              },
            },
          },
          groupWord: {
            type: "string",
            title: "团体字",
            "x-component": "Input",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
            },
            "x-validator": [
              {
                required: true,
                message: "请输入团体字",
              },
            ],
          },
          registerWay: {
            type: "string",
            title: "认证方式",
            "x-component": "Select",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请选择认证方式",
            },
            "x-validator": [
              {
                required: true,
                message: "请选择认证方式",
              },
            ],
            enum: REGISTER_WAY_OPTIONS,
          },
          username: {
            type: "string",
            title: "用户名",
            "x-component": "Input",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
            },
            "x-reactions": {
              dependencies: ["registerWay"],
              fulfill: {
                state: {
                  visible: '{{$deps[0] !== "无"}}',
                },
              },
            },
          },
          password: {
            type: "string",
            title: "密码",
            "x-component": "Password",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
            },
            "x-reactions": {
              dependencies: ["registerWay"],
              fulfill: {
                state: {
                  visible: '{{$deps[0] !== "无"}}',
                },
              },
            },
          },
          timeLimit: {
            type: "string",
            title: "时间阈值",
            "x-component": "Input",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入，如20，单位为秒",
            },
          },
          level: {
            type: "string",
            title: "对应级别",
            "x-component": "Select",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请选择对应级别",
            },
            enum: LEVEL_OPTIONS,
          },
          encoded: {
            type: "string",
            title: "编码方式",
            "x-component": "Select",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请选择编码方式",
            },
            enum: ENCODED_OPTIONS,
          }
        },
      },
    },
  };
}
