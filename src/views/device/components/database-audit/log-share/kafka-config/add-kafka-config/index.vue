<template>
  <a-modal
    v-model:visible="visible"
    :title="modalTitle"
    width="800px"
    :maskClosable="false"
    :keyboard="false"
  >
    <ProForm :form="form" v-if="!loading" :schema="schema" />
    
    <!-- 提示信息 -->
    <!-- <div class="tips-box" v-if="!isView">
      <div class="tips-content">
        <div>1. 服务器地址的ip和端口号要求必填</div>
        <div>2. 当认证方式为无方式时不需要填写用户名和密码，其他方式需要填写</div>
      </div>
    </div> -->

    <template #footer>
      <div class="operator-group" :style="{ justifyContent: 'flex-end' }">
        <a-button key="back" @click="handleCancel">{{ isView ? '关闭' : '取消' }}</a-button>
        <a-button
          :loading="btnLoading"
          v-show="!isView"
          key="submit"
          type="primary"
          @click="handleOk"
        >
          保存
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { createForm } from "@formily/vue";
import { ref, defineExpose, defineEmits, computed, shallowRef } from 'vue';
import { message } from 'ant-design-vue';
import ProForm from '@/components/pro-form/index.vue';
import { getSchema } from './config.jsx';
import { insertKafkaConfig, modifyKafkaConfig } from '@/request/api-device-db/log-share';

const emits = defineEmits(['refresh']);
const visible = ref(false);
const btnLoading = ref(false);
const loading = ref(false);
const modalTitle = ref('新增KAFKA');
const mode = ref('add');

const form = shallowRef(null);
const schema = shallowRef(null);

const isView = computed(() => mode.value === 'view');

async function handleOk() {
  try {
    await form.value.validate();
    btnLoading.value = true;

    const payload = { ...form.value.values };

    switch (mode.value) {
      case 'add':
        await insertKafkaConfig(payload);
        message.success('新增成功');
        break;
      case 'edit':
        await modifyKafkaConfig(payload);
        message.success('编辑成功');
        break;
    }

    visible.value = false;
    emits('refresh');
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败');
  } finally {
    btnLoading.value = false;
  }
}

function handleCancel() {
  visible.value = false;
}

function initForm(values) {
  schema.value = getSchema();
  
  // 处理服务器地址数据格式
  const formValues = { ...values };
  if (formValues.serversAddr && !Array.isArray(formValues.serversAddr)) {
    // 如果是字符串格式，需要转换为数组格式
    if (typeof formValues.serversAddr === 'string') {
      formValues.serversAddr = [{ ip: '', port: '', hostname: '' }];
    }
  }
  
  // 如果没有服务器地址，初始化一个空的
  if (!formValues.serversAddr || formValues.serversAddr.length === 0) {
    formValues.serversAddr = [{ ip: '', port: '', hostname: '' }];
  }
  
  form.value = createForm({
    values: formValues,
  });
}

function open({ title, data = {}, mode: modeType }) {
  initForm({ ...data });
  visible.value = true;
  mode.value = modeType;
  modalTitle.value = title;

  if (mode.value === "view") {
    setTimeout(() => {
      form.value.readPretty = true;
    }, 50);
  }
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.operator-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.tips-box {
  margin-top: 16px;
  padding: 12px;
  background-color: #fffbe6;
  border: 1px solid #ffe58f;
  border-radius: 4px;
  
  .tips-content {
    color: #d48806;
    font-size: 12px;
    line-height: 1.5;
    
    div {
      margin-bottom: 4px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
