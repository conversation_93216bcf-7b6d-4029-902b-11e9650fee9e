// KAFKA配置

// 认证方式选项
export const REGISTER_WAY_OPTIONS = [
  { label: '无', value: '无' },
  { label: 'SASL/PLAIN', value: 'SASL/PLAIN' },
  { label: 'SASL/SCRAM-SHA-256', value: 'SASL/SCRAM-SHA-256' },
  { label: 'SASL/SCRAM-SHA-512', value: 'SASL/SCRAM-SHA-512' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入配置名称',
    },
  },
  {
    title: '服务器地址',
    name: 'serversAddr',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入服务器地址',
    },
  },
  {
    title: '认证方式',
    name: 'registerWay',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择认证方式',
    },
    enum: [
      { label: '全部', value: null },
      ...REGISTER_WAY_OPTIONS
    ],
  },
  {
    title: '启用状态',
    name: 'enable',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择启用状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: "center",
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '服务器地址',
      dataIndex: 'serversAddr',
      key: 'serversAddr',
      width: 250,
      align: "center",
      ellipsis: true,
      customRender: ({ record }) => {
        // 如果是数组格式，转换为字符串显示
        if (Array.isArray(record.serversAddr)) {
          return record.serversAddr.map(server => `${server.ip}:${server.port}`).join(', ');
        }
        return record.serversAddr;
      }
    },
    {
      title: '认证方式',
      dataIndex: 'registerWay',
      key: 'registerWay',
      width: 150,
      align: "center",
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
