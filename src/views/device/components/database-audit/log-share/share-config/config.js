// 共享配置

// 发送方式选项
export const SEND_WAY_OPTIONS = [
  { label: 'SYSLOG', value: 'SYSLOG' },
  { label: 'KAFKA', value: 'KAFKA' },
  { label: 'SFTP', value: 'SFTP' },
  { label: 'SNMP', value: 'SNMP' }
];

// 失败策略选项
export const FAIL_STRATEGY_OPTIONS = [
  { label: '丢弃', value: '丢弃' },
  { label: '暂停发送', value: '暂停发送' },
  { label: '重试', value: '重试' }
];

// 日志类型选项
export const LOG_TYPE_OPTIONS = [
  { label: '系统日志', value: '系统日志' },
  { label: '审计事件', value: '审计事件' },
  { label: '返回结果集', value: '返回结果集' },
  { label: '操作日志', value: '操作日志' }
];

// 认证方式选项
export const AUTH_TYPE_OPTIONS = [
  { label: '无认证', value: '无认证' },
  { label: 'SASL/PLAIN', value: 'SASL/PLAIN' },
  { label: 'SSL', value: 'SSL' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入配置名称',
    },
  },
  {
    title: '发送方式',
    name: 'sendWay',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择发送方式',
    },
    enum: [
      { label: '全部', value: null },
      ...SEND_WAY_OPTIONS
    ],
  },
  {
    title: '日志类型',
    name: 'logType',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择日志类型',
    },
    enum: [
      { label: '全部', value: null },
      ...LOG_TYPE_OPTIONS
    ],
  },
  {
    title: '启用状态',
    name: 'enable',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择启用状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: "center",
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 180,
      align: "center",
      ellipsis: true,
      customRender: ({ text }) => text || '-'
    },
    {
      title: '日志类型',
      dataIndex: 'logType',
      key: 'logType',
      width: 120,
      align: "center",
    },
    {
      title: '发送方式',
      dataIndex: 'sendWay',
      key: 'sendWay',
      width: 100,
      align: "center",
    },
    {
      title: '条数阈值',
      dataIndex: 'elementLimit',
      key: 'elementLimit',
      width: 100,
      align: "center",
    },
    {
      title: '发送间隔时间（分钟）',
      dataIndex: 'sendInterval',
      key: 'sendInterval',
      width: 150,
      align: "center",
      customRender: ({ text }) => {
        // 将秒转换为分钟显示
        if (text && text.includes('s')) {
          const seconds = parseInt(text.replace('s', ''));
          return Math.round(seconds / 60);
        }
        return text;
      }
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
