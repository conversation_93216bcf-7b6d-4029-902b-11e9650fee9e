<template>
  <a-modal
    v-model:visible="visible"
    :title="modalTitle"
    width="650px"
    :maskClosable="false"
    :keyboard="false"
  >
    <ProForm :form="form" v-if="!loading" :schema="schema" />
    <template #footer>
      <div class="operator-group" :style="{ justifyContent: 'flex-end' }">
        <a-button key="back" @click="handleCancel">{{ isView ? '关闭' : '取消' }}</a-button>
        <a-button
          :loading="btnLoading"
          v-show="!isView"
          key="submit"
          type="primary"
          @click="handleOk"
        >
          确定
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { createForm } from "@formily/vue";
import { ref, defineExpose, defineEmits, computed, shallowRef } from 'vue';
import { message } from 'ant-design-vue';
import ProForm from '@/components/pro-form/index.vue';
import { getSchema } from './config.jsx';
import { insertShareConfig, modifyShareConfig } from '@/request/api-device-db/log-share';

const emits = defineEmits(['refresh']);
const visible = ref(false);
const btnLoading = ref(false);
const loading = ref(false);
const modalTitle = ref('新增共享配置');
const mode = ref('add');

const form = shallowRef(null);
const schema = shallowRef(null);

const isView = computed(() => mode.value === 'view');

async function handleOk() {
  try {
    await form.value.validate();
    btnLoading.value = true;

    const payload = { ...form.value.values };

    // 处理日期格式
    if (payload.logStartTime) {
      payload.logStartTime = payload.logStartTime.format ?
        payload.logStartTime.format('YYYY-MM-DDTHH:mm:ss') :
        payload.logStartTime;
    }

    // 处理发送间隔：将分钟转换为秒
    if (payload.sendInterval) {
      const minutes = parseInt(payload.sendInterval);
      if (!isNaN(minutes)) {
        payload.sendInterval = `${minutes * 60}s`;
      }
    }

    switch (mode.value) {
      case 'add':
        await insertShareConfig(payload);
        message.success('新增成功');
        break;
      case 'edit':
        await modifyShareConfig(payload);
        message.success('编辑成功');
        break;
    }

    visible.value = false;
    emits('refresh');
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败');
  } finally {
    btnLoading.value = false;
  }
}

function handleCancel() {
  visible.value = false;
}

function initForm(values) {
  schema.value = getSchema();

  // 处理日期格式
  if (values.logStartTime && typeof values.logStartTime === 'string') {
    values.logStartTime = new Date(values.logStartTime);
  }

  // 处理发送间隔：将秒转换为分钟显示
  if (values.sendInterval && values.sendInterval.includes('s')) {
    const seconds = parseInt(values.sendInterval.replace('s', ''));
    values.sendInterval = Math.round(seconds / 60).toString();
  }

  form.value = createForm({
    values: values,
  });
}

function open({ title, data = {}, mode: modeType }) {
  initForm({ ...data });
  visible.value = true;
  mode.value = modeType;
  modalTitle.value = title;

  if (mode.value === "view") {
    setTimeout(() => {
      form.value.readPretty = true;
    }, 50);
  }
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.operator-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
