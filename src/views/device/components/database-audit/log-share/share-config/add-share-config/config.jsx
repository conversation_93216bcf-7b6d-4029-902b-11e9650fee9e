// 发送方式选项
const SEND_WAY_OPTIONS = [
  { label: 'SYSLOG', value: 'SYSLOG' },
  { label: 'KAFKA', value: 'KAFKA' },
  { label: 'SFTP', value: 'SFTP' },
  { label: 'SNMP', value: 'SNMP' }
];

// 失败策略选项
const FAIL_STRATEGY_OPTIONS = [
  { label: '丢弃', value: '丢弃' },
  { label: '暂停发送', value: '暂停发送' },
  { label: '重试', value: '重试' }
];

// 日志类型选项
const LOG_TYPE_OPTIONS = [
  { label: '系统日志', value: '系统日志' },
  { label: '审计事件', value: '审计事件' },
  { label: '返回结果集', value: '返回结果集' },
  { label: '操作日志', value: '操作日志' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 200
        },
        properties: {
          enable: {
            type: 'boolean',
            default: true,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          },

          name: {
            type: 'string',
            title: '名称',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          description: {
            type: 'string',
            title: '描述',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          sendWay: {
            type: 'string',
            title: '发送方式',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择发送方式',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择发送方式'
              }
            ],
            enum: SEND_WAY_OPTIONS
          },
          elementLimit: {
            type: 'string',
            title: '条数阈值',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入条数阈值'
              }
            ]
          },
          sendInterval: {
            type: 'string',
            title: '发送间隔（分钟）',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入发送间隔'
              }
            ]
          },
          failStrategy: {
            type: 'string',
            title: '失败处理',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择失败处理方式',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择失败处理方式'
              }
            ],
            enum: FAIL_STRATEGY_OPTIONS
          },
          logType: {
            type: 'string',
            title: '日志类型',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择日志类型',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择日志类型'
              }
            ],
            enum: LOG_TYPE_OPTIONS
          },
          logStartTime: {
            type: 'string',
            title: '日志起始时间',
            'x-component': 'DatePicker',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
              showTime: true,
              format: 'YYYY-MM-DD HH:mm:ss'
            },
            'x-validator': [
              {
                required: true,
                message: '请选择日志起始时间'
              }
            ]
          },
          backlogEnable: {
            type: 'boolean',
            default: true,
            title: '高级设置-防积压策略',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            },
            'x-validator': [
              {
                required: true,
              }
            ]
          },
        }
      }
    }
  };
}
