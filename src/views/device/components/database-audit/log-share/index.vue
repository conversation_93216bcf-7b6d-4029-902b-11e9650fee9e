<!-- 审计对象管理Tab -->

<template>
  <a-tabs type="card" v-model:activeKey="activeKey" size="small">
    <a-tab-pane v-for="tab in TABS" :key="tab.componentName" :tab="tab.label">
    </a-tab-pane>
  </a-tabs>
  <component :is="activeKey" />
</template>
<script lang="js">
import { defineComponent, provide, toRefs, computed, reactive, watch } from 'vue'
import { TABS } from './config';
import ShareConfig from './share-config/index.vue';
import SyslogConfig from './syslog-config/index.vue';
import SftpConfig from './sftp-config/index.vue';
import SnmpConfig from './snmp-config/index.vue';
import KafkaConfig from './kafka-config/index.vue';


export default defineComponent({
  name: 'LogShare',
  components: {
    ShareConfig,
    SyslogConfig,
    SftpConfig,
    SnmpConfig,
    KafkaConfig,
  },
  setup (props, ctx) {
    const state = reactive({
      activeKey: TABS[0].componentName
    })

    return {
      TABS,
      ...toRefs(state),
    }
  }
})
</script>
