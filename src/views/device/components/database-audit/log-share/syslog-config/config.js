// SYSLOG配置

// 协议选项
export const PROTOCOL_OPTIONS = [
  { label: 'UDP', value: 'UDP' },
  { label: 'TCP', value: 'TCP' }
];

// 编码方式选项
export const ENCODED_OPTIONS = [
  { label: 'UTF8', value: 'UTF8' },
  { label: 'UTF-8', value: 'UTF-8' },
  { label: 'GBK', value: 'GBK' },
  { label: 'GB2312', value: 'GB2312' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入配置名称',
    },
  },
  {
    title: 'IP地址',
    name: 'ip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入IP地址',
    },
  },
  {
    title: '协议',
    name: 'protocol',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择协议',
    },
    enum: [
      { label: '全部', value: null },
      ...PROTOCOL_OPTIONS
    ],
  },
  {
    title: '启用状态',
    name: 'enable',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择启用状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: "center",
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
      width: 100,
      align: "center",
    },
    {
      title: '协议',
      dataIndex: 'protocol',
      key: 'protocol',
      width: 100,
      align: "center",
    },
    {
      title: '编码方式',
      dataIndex: 'encoded',
      key: 'encoded',
      width: 120,
      align: "center",
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
