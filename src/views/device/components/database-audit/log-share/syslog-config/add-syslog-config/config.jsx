// 协议选项
const PROTOCOL_OPTIONS = [
  { label: 'UDP', value: 'UDP' },
  { label: 'TCP', value: 'TCP' }
];

// 编码方式选项
const ENCODED_OPTIONS = [
  { label: 'UTF8', value: 'UTF8' },
  { label: 'UTF-8', value: 'UTF-8' },
  { label: 'GBK', value: 'GBK' },
  { label: 'GB2312', value: 'GB2312' }
];

// 映射级别选项
const MAPPING_LEVEL_OPTIONS = [
  { label: '高', value: '高' },
  { label: '中', value: '中' },
  { label: '低', value: '低' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: true,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          },
          name: {
            type: 'string',
            title: '名称',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          ip: {
            type: 'string',
            title: 'IP地址',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入IP地址'
              }
            ]
          },
          port: {
            type: 'string',
            title: '端口',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入端口'
              }
            ]
          },
          protocol: {
            type: 'string',
            title: '协议',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择协议',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择协议'
              }
            ],
            enum: PROTOCOL_OPTIONS
          },
          encoded: {
            type: 'string',
            title: '编码方式',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择编码方式',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择编码方式'
              }
            ],
            enum: ENCODED_OPTIONS
          },
          mappingLevel: {
            type: 'string',
            title: '映射级别',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择映射级别',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择映射级别'
              }
            ],
            enum: MAPPING_LEVEL_OPTIONS
          },
          referenceCount: {
            type: 'string',
            title: '引用次数',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          }
        }
      }
    }
  };
}
