// 源IP汇聚配置

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '源IP',
    name: 'sip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入源IP',
    },
  },
  {
    title: '目的IP',
    name: 'dip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入目的IP',
    },
  },
  {
    title: '时间范围',
    name: 'timeRange',
    type: 'array',
    'x-component': 'DatePicker.RangePicker',
    'x-component-props': {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  }
];

// 表格列配置
export function getTableColumns(pagination = {}) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '服务器名',
      dataIndex: 'serverName',
      key: 'serverName',
      width: 150,
      align: "center",
    },
    {
      title: '源IP',
      dataIndex: 'sip',
      key: 'sip',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '目的IP',
      dataIndex: 'dip',
      key: 'dip',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '协议',
      dataIndex: 'protocol',
      key: 'protocol',
      width: 100,
      align: "center",
    },
    {
      title: '规则名称',
      dataIndex: 'ruleName',
      key: 'ruleName',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '资源账户',
      dataIndex: 'sourceAccount',
      key: 'sourceAccount',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '错误码',
      dataIndex: 'errorCode',
      key: 'errorCode',
      width: 100,
      align: "center",
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 150,
      fixed: 'right',
      align: "center",
    }
  ];
}
