// 异常行为配置

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '源IP',
    name: 'sip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入源IP',
    },
  },
  {
    title: '目的IP',
    name: 'dip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入目的IP',
    },
  },
  {
    title: '时间范围',
    name: 'timeRange',
    type: 'array',
    'x-component': 'DatePicker.RangePicker',
    'x-component-props': {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  }
];

// 表格列配置
export function getTableColumns(pagination = {}) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '开始时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '异常行为学习模型',
      dataIndex: 'abnormalModel',
      key: 'abnormalModel',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '告警类型',
      dataIndex: 'alarmType',
      key: 'alarmType',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '异常资源账号',
      dataIndex: 'sourceAccount',
      key: 'sourceAccount',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '目的IP',
      dataIndex: 'dip',
      key: 'dip',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '源IP',
      dataIndex: 'sip',
      key: 'sip',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '目的端口',
      dataIndex: 'dport',
      key: 'dport',
      width: 100,
      align: "center",
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 100,
      fixed: 'right',
      align: "center",
    }
  ];
}
