<!-- 审计对象管理Tab -->

<template>
  <a-tabs type="card" v-model:activeKey="activeKey" size="small">
    <a-tab-pane v-for="tab in TABS" :key="tab.componentName" :tab="tab.label">
    </a-tab-pane>
  </a-tabs>
  <component :is="activeKey" />
</template>
<script lang="js">
import { defineComponent, provide, toRefs, computed, reactive, watch } from 'vue'
import { TABS } from './config';
import EventQuery from './event-query/index.vue';
import SourceIpAggregation from './source-ip-aggregation/index.vue';
import SessionManagement from './session-management/index.vue';
import ThreeLayerManagement from './three-layer-management/index.vue';
import AbnormalBehavior from './abnormal-behavior/index.vue';
import FileQuery from './file-query/index.vue';
import AptLinkage from './apt-linkage/index.vue';
import InternetBehavior from './internet-behavior/index.vue';
import SyslogQuery from './syslog-query/index.vue';



export default defineComponent({
  name: 'DbAudit',
  components: {
    EventQuery,
    SourceIpAggregation,
    SessionManagement,
    ThreeLayerManagement,
    AbnormalBehavior,
    FileQuery,
    AptLinkage,
    InternetBehavior,
    SyslogQuery
  },
  setup (props, ctx) {
    const state = reactive({
      activeKey: TABS[0].componentName
    })

    return {
      TABS,
      ...toRefs(state),
    }
  }
})
</script>
