// SYSLOG查询配置

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '级别',
    name: 'level',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入级别',
    },
  },
  {
    title: '时间范围',
    name: 'timeRange',
    type: 'array',
    'x-component': 'DatePicker.RangePicker',
    'x-component-props': {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  }
];

// 表格列配置
export function getTableColumns(pagination = {}) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: "开始时间",
      dataIndex: "startTime",
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '学习模型',
      dataIndex: 'studyModel',
      key: 'studyModel',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '告警类型',
      dataIndex: 'alarmType',
      key: 'alarmType',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '账号',
      dataIndex: 'account',
      key: 'account',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '主机信息',
      dataIndex: 'hostInfo',
      key: 'hostInfo',
      width: 200,
      align: "center",
      ellipsis: true,
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      align: "center",
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 100,
      fixed: 'right',
      align: "center",
    }
  ];
}
