// 三层管理配置

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '源IP',
    name: 'sip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入源IP',
    },
  },
  {
    title: '目的IP',
    name: 'dip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入目的IP',
    },
  },
  {
    title: '时间范围',
    name: 'timeRange',
    type: 'array',
    'x-component': 'DatePicker.RangePicker',
    'x-component-props': {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  }
];

// 表格列配置
export function getTableColumns(pagination = {}) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: "时间",
      dataIndex: "createTime",
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '源ip',
      dataIndex: 'sip',
      key: 'sip',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: 'web server ip',
      dataIndex: 'webIp',
      key: 'webIp',
      width: 130,
      align: "center",
      ellipsis: true,
    },
    {
      title: '目的ip',
      dataIndex: 'dip',
      key: 'dip',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '目的端口',
      dataIndex: 'dport',
      key: 'dport',
      width: 100,
      align: "center",
    },
    {
      title: 'web账号',
      dataIndex: 'webAccount',
      key: 'webAccount',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: 'db账号',
      dataIndex: 'dbAccount',
      key: 'dbAccount',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '协议',
      dataIndex: 'protocol',
      key: 'protocol',
      width: 120,
      align: "center",
    },
    {
      title: '影响范围',
      dataIndex: 'incidence',
      key: 'incidence',
      width: 120,
      align: "center",
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 100,
      fixed: 'right',
      align: "center",
    }
  ];
}
