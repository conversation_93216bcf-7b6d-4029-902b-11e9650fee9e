<template>
  <div>
    <DynamicSearch
      ref="dynamicSearchRef"
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    />

    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleExport">导出</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>
  </div>
</template>

<script setup>
import { ref, computed, getCurrentInstance } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { createVNode } from 'vue';
import dayjs from 'dayjs';
import DynamicSearch from '@/components/dynamic-search/index.vue';
import CommonTable from '@/components/common-table/index.vue';
import { SEARCH_CONFIG, getTableColumns } from './config';
import {
  getLogNetworkPageData,
  deleteLogNetwork,
  exportLogNetwork
} from '@/request/api-device-db/log-search';
import { downloadFile } from '@/utils/util';

const { proxy } = getCurrentInstance();

// 表格引用
const tableRef = ref();
const dynamicSearchRef = ref();

// 表格高度
const tableHeight = computed(() => 'calc(100vh - 420px)');

// 表格列配置
const tableColumns = computed(() => getTableColumns(tableRef.value?.pagination || {}));

// 搜索参数
let searchParams = {};

// 处理搜索
const handleSearch = (params = {}) => {
  // 处理时间范围
  if (params.timeRange && params.timeRange.length === 2) {
    // 使用dayjs包装时间对象，确保format方法可用
    params.startTime = dayjs(params.timeRange[0]).format('YYYY-MM-DD HH:mm:ss');
    params.endTime = dayjs(params.timeRange[1]).format('YYYY-MM-DD HH:mm:ss');
    delete params.timeRange;
  }

  searchParams = { ...params };
  tableRef.value?.refresh();
};

// 获取数据
const fetchData = async (params) => {
  try {
    const requestParams = {
      ...searchParams,
      page: params.current,
      size: params.pageSize,
    };
    
    const { data } = await getLogNetworkPageData(requestParams);
    return {
      items: data.data || [],
      total: data.total || 0,
    };
  } catch (error) {
    console.error('获取互联网行为数据失败:', error);
    message.error('获取数据失败');
    return {
      items: [],
      total: 0,
    };
  }
};

// 删除数据
const handleDelete = (record) => {
  Modal.confirm({
    title: '确认删除',
    icon: createVNode(ExclamationCircleOutlined),
    content: '确定要删除这条记录吗？',
    okText: '确定',
    cancelText: '取消',
    async onOk() {
      try {
        await deleteLogNetwork({ id: [record.id] });
        message.success('删除成功');
        tableRef.value?.refresh();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    },
  });
};

// 导出数据
const handleExport = async () => {
  try {
    const params = new URLSearchParams(searchParams).toString();
    downloadFile(`/device-atomic/db/logNetwork/export?${params}`)
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
};

// 刷新数据
const handleRefresh = () => {
  tableRef.value?.refresh();
};
</script>

<style lang="less" scoped>
.list-operator {
  margin-bottom: 16px;
  display: flex;
  justify-content: flex-end;
}

.list-operator-column {
  display: flex;
  gap: 8px;
}
</style>
