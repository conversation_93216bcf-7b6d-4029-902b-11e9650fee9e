// WAF联动配置

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入',
    },
  },
  {
    title: 'IP地址',
    name: 'ip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入',
    },
  },
  {
    title: '时间范围',
    name: 'timeRange',
    type: 'array',
    'x-component': 'DatePicker.RangePicker',
    'x-component-props': {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: "center",
      ellipsis: true,
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
      width: 100,
      align: "center",
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
