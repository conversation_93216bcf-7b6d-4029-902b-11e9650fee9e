<template>
  <div>
    <DynamicSearch
      ref="dynamicSearchRef"
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    />

    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" @click="handleView(record)">详情</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>

    <AddWafConfig ref="addWafConfigRef" @refresh="handleRefresh" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import DynamicSearch from '@/components/dynamic-search/index.vue';
import CommonTable from '@/components/common-table/index.vue';
import { SEARCH_CONFIG, getTableColumns } from './config';
import AddWafConfig from './add-waf-config/index.vue';
import {
  getSystemDevlinkWafPageData,
  deleteSystemDevlinkWaf,
  modifySystemDevlinkWaf,
  exportSystemDevlinkWaf
} from '@/request/api-device-db/system-log';
import { downloadFile } from '@/utils/util';

const searchParams = ref({});
const tableRef = ref(null);
const dynamicSearchRef = ref(null);
const addWafConfigRef = ref(null);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

// 计算表格列配置
const tableColumns = computed(() => getTableColumns(
  { current: 1, pageSize: 10 }, // 分页信息
  handleStatusChange
));

// 格式化日期时间
function formatDateTime(dateTime) {
  if (!dateTime) return '';

  // 如果是moment对象，使用format方法
  if (typeof dateTime.format === 'function') {
    return dateTime.format('YYYY-MM-DD HH:mm:ss');
  }

  // 如果是Date对象，转换为字符串
  if (dateTime instanceof Date) {
    const year = dateTime.getFullYear();
    const month = String(dateTime.getMonth() + 1).padStart(2, '0');
    const day = String(dateTime.getDate()).padStart(2, '0');
    const hours = String(dateTime.getHours()).padStart(2, '0');
    const minutes = String(dateTime.getMinutes()).padStart(2, '0');
    const seconds = String(dateTime.getSeconds()).padStart(2, '0');
    return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
  }

  // 如果是字符串，直接返回
  if (typeof dateTime === 'string') {
    return dateTime;
  }

  return '';
}

function handleSearch (params) {
  // 处理时间范围
  if (params.timeRange && params.timeRange.length === 2) {
    const startTime = params.timeRange[0];
    const endTime = params.timeRange[1];

    if (startTime && endTime) {
      params.startTime = formatDateTime(startTime);
      params.endTime = formatDateTime(endTime);
    }
    delete params.timeRange;
  }

  searchParams.value = params;
  handleRefresh();
}

function handleRefresh () {
  tableRef.value?.refresh();
}

async function fetchData (params) {
  try {
    const { data } = await getSystemDevlinkWafPageData({
      ...params,
      ...searchParams.value,
    });
    return { items: data.data, total: data.total };
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
    return {
      items: [],
      total: 0
    };
  }
}

function handleAdd() {
  addWafConfigRef.value?.open({
    title: '新增联动WAF配置',
    mode: 'add'
  });
}

function handleEdit(record) {
  addWafConfigRef.value?.open({
    title: '编辑联动WAF配置',
    data: record,
    mode: 'edit'
  });
}

function handleView(record) {
  addWafConfigRef.value?.open({
    title: '查看联动WAF配置',
    data: record,
    mode: 'view'
  });
}

async function handleDelete(record) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除配置 "${record.name}" 吗？`,
    async onOk() {
      try {
        await deleteSystemDevlinkWaf({ id: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    }
  });
}

async function handleStatusChange(record) {
  try {
    await modifySystemDevlinkWaf(record);
    message.success(`${record.enable ? '启用' : '禁用'}成功`);
    handleRefresh();
  } catch (error) {
    console.error('状态修改失败:', error);
    message.error('状态修改失败');
  }
}

// 导出
async function handleExport () {
  try {
    await exportSystemDevlinkWaf();
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
}
</script>
<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  :deep(.ant-btn) {
    padding: 4px 6px;
  }
}
</style>