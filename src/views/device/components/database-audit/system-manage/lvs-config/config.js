// LVS配置配置

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '虚拟IP地址/掩码',
      dataIndex: 'virtualIp',
      key: 'virtualIp',
      width: 160,
      align: "center",
      ellipsis: true,
    },
    {
      title: '设备A（优先级高）',
      dataIndex: 'deviceA',
      key: 'deviceA',
      width: 140,
      align: "center",
    },
    {
      title: '设备B（优先级低）',
      dataIndex: 'deviceB',
      key: 'deviceB',
      width: 140,
      align: "center",
    },
    {
      title: '管理设备',
      dataIndex: 'ip',
      key: 'ip',
      width: 120,
      align: "center",
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
      width: 100,
      align: "center",
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
