export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            title: '启用',
            'x-decorator': 'FormItem',
            'x-component': 'Switch',
            'x-component-props': {
              size: 'small'
            },
            default: false
          },
          name: {
            type: 'string',
            title: '名称',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入名称'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          description: {
            type: 'string',
            title: '描述',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入描述'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入描述'
              }
            ]
          },
          virtualIp: {
            type: 'string',
            title: '虚拟IP地址/掩码',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入虚拟IP地址/掩码'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入虚拟IP地址/掩码'
              }
            ]
          },
          deviceA: {
            type: 'string',
            title: '设备A（优先级高）',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入设备A IP地址'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入设备A IP地址'
              },
              {
                pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
                message: '请输入正确的IP地址格式'
              }
            ]
          },
          deviceB: {
            type: 'string',
            title: '设备B（优先级低）',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入设备B IP地址'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入设备B IP地址'
              },
              {
                pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
                message: '请输入正确的IP地址格式'
              }
            ]
          },
          ip: {
            type: 'string',
            title: 'IP',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入管理设备IP地址'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入管理设备IP地址'
              },
              {
                pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
                message: '请输入正确的IP地址格式'
              }
            ]
          },
          port: {
            type: 'string',
            title: '端口',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入端口号，多个端口用逗号分隔'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入端口号'
              }
            ]
          }
        }
      }
    }
  };
}
