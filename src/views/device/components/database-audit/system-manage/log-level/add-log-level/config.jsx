// 日志级别选项
const LOG_LEVEL_OPTIONS = [
  { label: 'DEBUG', value: 'DEBUG' },
  { label: 'INFO', value: 'INFO' },
  { label: 'WARN', value: 'WARN' },
  { label: 'ERROR', value: 'ERROR' },
  { label: 'FATAL', value: 'FATAL' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: false,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              size: 'small'
            }
          },
          serverIp: {
            type: 'string',
            title: '服务器IP',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入服务器IP'
              }
            ]
          },
          communicationProcess: {
            type: 'string',
            title: '进程通讯',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择进程通讯级别'
              }
            ],
            enum: LOG_LEVEL_OPTIONS
          },
          protocolProcess: {
            type: 'string',
            title: '协议解析过程',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择协议解析过程级别'
              }
            ],
            enum: LOG_LEVEL_OPTIONS
          },
          mergeProcess: {
            type: 'string',
            title: '入库进程',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择入库进程级别'
              }
            ],
            enum: LOG_LEVEL_OPTIONS
          }
        }
      }
    }
  };
}
