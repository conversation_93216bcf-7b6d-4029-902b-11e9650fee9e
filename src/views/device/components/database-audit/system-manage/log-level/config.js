// 日志级别管理配置

// 日志级别选项
export const LOG_LEVEL_OPTIONS = [
  { label: 'DEBUG', value: 'DEBUG' },
  { label: 'INFO', value: 'INFO' },
  { label: 'WARN', value: 'WARN' },
  { label: 'ERROR', value: 'ERROR' },
  { label: 'FATAL', value: 'FATAL' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '服务器IP',
    name: 'serverIp',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入',
    },
  },
  {
    title: '时间范围',
    name: 'timeRange',
    type: 'array',
    'x-component': 'DatePicker.RangePicker',
    'x-component-props': {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '服务器IP',
      dataIndex: 'serverIp',
      key: 'serverIp',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '通信过程',
      dataIndex: 'communicationProcess',
      key: 'communicationProcess',
      width: 120,
      align: "center",
    },
    {
      title: '协议解析过程',
      dataIndex: 'protocolProcess',
      key: 'protocolProcess',
      width: 140,
      align: "center",
    },
    {
      title: '入库过程',
      dataIndex: 'mergeProcess',
      key: 'mergeProcess',
      width: 120,
      align: "center",
    },
    {
      title: '生成时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
