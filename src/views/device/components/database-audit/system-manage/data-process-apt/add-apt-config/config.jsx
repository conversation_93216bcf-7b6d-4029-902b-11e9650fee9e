// 检测类型选项
const DETECTION_TYPE_OPTIONS = [
  { label: '实时检测', value: '实时检测' },
  { label: '闲时检测', value: '闲时检测' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: false,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              size: 'small'
            }
          },
          name: {
            type: 'string',
            title: '名称',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          detectionType: {
            type: 'string',
            title: '检测类型',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择检测类型',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择检测类型'
              }
            ],
            enum: DETECTION_TYPE_OPTIONS
          },
          ip: {
            type: 'string',
            title: 'IP地址',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入IP地址'
              }
            ]
          },
          port: {
            type: 'string',
            title: '联动端口',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入联动端口'
              }
            ]
          },
          username: {
            type: 'string',
            title: '用户名',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入用户名'
              }
            ]
          },
          password: {
            type: 'string',
            title: '密码',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入密码'
              }
            ]
          }
        }
      }
    }
  };
}
