// 系统日志配置

// 日志级别选项
export const LEVEL_OPTIONS = [
  { label: '高', value: '高' },
  { label: '中', value: '中' },
  { label: '低', value: '低' },
  { label: '告警', value: '告警' }
];

// 结果选项
export const RESULT_OPTIONS = [
  { label: '成功', value: '成功' },
  { label: '失败', value: '失败' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '事件名称',
    name: 'eventName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入事件名称',
    },
  },
  {
    title: '级别',
    name: 'level',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择级别',
    },
    enum: [
      { label: '全部', value: null },
      ...LEVEL_OPTIONS
    ],
  }
];

// 表格列配置
export function getTableColumns(pagination = {}) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '事件名称',
      dataIndex: 'eventName',
      key: 'eventName',
      width: 300,
      align: "left",
      ellipsis: true,
    },
    {
      title: '结果',
      dataIndex: 'result',
      key: 'result',
      width: 100,
      align: "center",
      customRender: ({ text, record }) => {
        const result = text || record.result;
        const color = result === '成功' ? 'green' : 'red';
        return <a-tag color={color}>{result}</a-tag>;
      }
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 100,
      align: "center",
      customRender: ({ text, record }) => {
        const level = text || record.level;
        const colorMap = {
          '高': 'red',
          '中': 'orange',
          '低': 'blue',
          '告警': 'purple'
        };
        const color = colorMap[level] || 'default';
        return <a-tag color={color}>{level}</a-tag>;
      }
    },
    {
      title: '创建人',
      dataIndex: 'createdBy',
      key: 'createdBy',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 100,
      fixed: 'right',
      align: "center",
    }
  ];
}
