<template>
  <div>
    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleAdd">新增</a-button>
        <a-button type="primary" @click="handleImport">导入</a-button>
        <a-button type="primary" @click="handleExport">导出</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" @click="handleView(record)">详情</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>

    <AddSystemCascade ref="addSystemCascadeRef" @refresh="handleRefresh" />

    <!-- 导入文件上传 -->
    <a-modal :visible="importModalVisible" title="导入多级配置" @ok="handleImportOk" @cancel="importModalVisible = false"
      :confirmLoading="importLoading">
      <a-upload
        v-model:file-list="fileList"
        :before-upload="beforeUpload"
        :remove="handleRemoveFile"
        :custom-request="customRequest"
        accept=".xlsx,.xls"
        :multiple="false"
        :show-upload-list="true"
        :max-count="1"
        @change="uploadChange"
      >
        <a-button>
          <upload-outlined />
          选择文件
        </a-button>
      </a-upload>
      <div style="margin-top: 8px; color: #666;">
        支持 .xlsx、.xls 格式文件
      </div>
    </a-modal>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import { UploadOutlined } from '@ant-design/icons-vue';
import CommonTable from '@/components/common-table/index.vue';
import { getTableColumns } from './config';
import AddSystemCascade from './add-system-cascade/index.vue';
import {
  getSystemCascadePageData,
  deleteSystemCascade,
  modifySystemCascade,
  exportSystemCascade,
  importSystemCascade
} from '@/request/api-device-db/system-log';
import { downloadFile } from '@/utils/util';

const tableRef = ref(null);
const addSystemCascadeRef = ref(null);
const importModalVisible = ref(false);
const importLoading = ref(false);
const fileList = ref([]);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

function handleRefresh () {
  tableRef.value?.refresh();
}

async function fetchData (params) {
  try {
    const { data } = await getSystemCascadePageData(params);
    return { items: data.data, total: data.total };
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
    return {
      items: [],
      total: 0
    };
  }
}

const tableColumns = computed(() => getTableColumns(tableRef.value?.pagination ?? {}, handleStatusChange));

// 状态切换处理
async function handleStatusChange(record) {
  try {
    // 调用修改接口
    await modifySystemCascade(record);
    message.success(`${record.enable ? '启用' : '禁用'}成功`);
  } catch (error) {
    console.error('状态切换失败:', error);
    message.error('状态切换失败');
    throw error; // 重新抛出错误，让配置文件中的 finally 块处理加载状态
  }
}

// 新增
function handleAdd () {
  addSystemCascadeRef.value.open({
    title: '新增多级配置',
    data: {},
    mode: 'add',
  });
}

// 编辑
function handleEdit (record) {
  addSystemCascadeRef.value.open({
    title: '编辑多级配置',
    data: { ...record },
    mode: 'edit',
  });
}

// 查看
function handleView (record) {
  addSystemCascadeRef.value.open({
    title: '查看多级配置',
    data: { ...record },
    mode: 'view',
  });
}

// 删除
async function handleDelete (record) {
  Modal.confirm({
    title: '删除多级配置',
    content: '确定删除该多级配置吗？',
    onOk: async () => {
      try {
        await deleteSystemCascade({ id: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (err) {
        message.error('删除失败');
      }
    },
  });
}

// 导出
async function handleExport () {
  try {
    downloadFile(`/device-atomic/db/systemCascade/export`)
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
}

// 导入
function handleImport () {
  importModalVisible.value = true;
  fileList.value = [];
}

// 文件上传前处理
function beforeUpload(file) {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel';
  if (!isExcel) {
    message.error('只能上传 Excel 文件!');
    return false;
  }
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    message.error('文件大小不能超过 10MB!');
    return false;
  }
  return false; // 阻止自动上传
}

// 自定义上传
function customRequest() {
  // 不执行任何操作，阻止自动上传
}

// 文件变化处理
function uploadChange(info) {
  fileList.value = info.fileList.slice(-1); // 只保留最后一个文件
}

// 移除文件
function handleRemoveFile() {
  fileList.value = [];
}

// 确认导入
async function handleImportOk() {
  if (fileList.value.length === 0) {
    message.error('请选择要导入的文件');
    return;
  }

  try {
    importLoading.value = true;
    const formData = new FormData();
    formData.append('file', fileList.value[0].originFileObj);

    await importSystemCascade(formData);
    message.success('导入成功');
    importModalVisible.value = false;
    fileList.value = [];
    handleRefresh();
  } catch (error) {
    console.error('导入失败:', error);
    message.error('导入失败');
  } finally {
    importLoading.value = false;
  }
}
</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  ::v-deep .ant-btn {
    padding: 4px 6px;
  }
}
</style>
