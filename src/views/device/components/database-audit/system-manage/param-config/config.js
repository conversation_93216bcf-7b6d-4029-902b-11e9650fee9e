// 多级配置配置

// 授权状态选项
export const AUTH_STATUS_OPTIONS = [
  { label: '未认证', value: '未认证' },
  { label: '试用授权', value: '试用授权' },
  { label: '正式授权', value: '正式授权' },
  { label: '已认证', value: '已认证' },
  { label: '授权过期', value: '授权过期' }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '序列号',
      dataIndex: 'serialNo',
      key: 'serialNo',
      width: 200,
      align: "center",
      ellipsis: true,
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip',
      width: 140,
      align: "center",
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
      width: 100,
      align: "center",
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 200,
      align: "center",
      ellipsis: true,
    },
    {
      title: '下发时间',
      dataIndex: 'issueTime',
      key: 'issueTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '状态',
      dataIndex: 'authStatus',
      key: 'authStatus',
      width: 120,
      align: "center",
      customRender: ({ text, record }) => {
        const authStatus = text || record.authStatus;
        const colorMap = {
          '未认证': 'red',
          '试用授权': 'orange',
          '正式授权': 'green',
          '已认证': 'green',
          '授权过期': 'red'
        };
        const color = colorMap[authStatus] || 'default';
        return <a-tag color={color}>{authStatus}</a-tag>;
      }
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
