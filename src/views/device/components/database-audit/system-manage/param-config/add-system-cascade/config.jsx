export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            title: '启用',
            'x-decorator': 'FormItem',
            'x-component': 'Switch',
            'x-component-props': {
              size: 'small'
            },
            default: false
          },
          name: {
            type: 'string',
            title: '名称',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入名称'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          serialNo: {
            type: 'string',
            title: '序列号',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入序列号'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入序列号'
              }
            ]
          },
          ip: {
            type: 'string',
            title: 'IP',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入IP地址'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入IP地址'
              },
              {
                pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
                message: '请输入正确的IP地址格式'
              }
            ]
          },
          port: {
            type: 'string',
            title: '端口',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入端口号'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入端口号'
              },
              {
                pattern: /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
                message: '请输入正确的端口号(1-65535)'
              }
            ]
          },
          description: {
            type: 'string',
            title: '描述',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              placeholder: '请输入描述',
              rows: 3
            },
            'x-validator': [
              {
                required: true,
                message: '请输入描述'
              }
            ]
          },
          // issueTime: {
          //   type: 'string',
          //   title: '下发时间',
          //   'x-decorator': 'FormItem',
          //   'x-component': 'DatePicker',
          //   'x-component-props': {
          //     placeholder: '请选择下发时间',
          //     showTime: true,
          //     format: 'YYYY-MM-DD HH:mm:ss'
          //   }
          // },
          // authStatus: {
          //   type: 'string',
          //   title: '授权状态',
          //   'x-decorator': 'FormItem',
          //   'x-component': 'Select',
          //   'x-component-props': {
          //     placeholder: '请选择授权状态'
          //   },
          //   enum: [
          //     { label: '未认证', value: '未认证' },
          //     { label: '试用授权', value: '试用授权' },
          //     { label: '正式授权', value: '正式授权' },
          //     { label: '已认证', value: '已认证' },
          //     { label: '授权过期', value: '授权过期' }
          //   ]
          // }
        }
      }
    }
  };
}
