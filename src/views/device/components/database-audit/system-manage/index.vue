<!-- 系统管理Tab -->

<template>
  <a-tabs type="card" v-model:activeKey="activeKey" size="small">
    <a-tab-pane v-for="tab in TABS" :key="tab.componentName" :tab="tab.label">
    </a-tab-pane>
  </a-tabs>
  <component :is="activeKey" />
</template>
<script lang="js">
import { defineComponent, provide, toRefs, computed, reactive, watch } from 'vue'
import { TABS } from './config';
import SystemLog from './system-log/index.vue';
import DataProcessWaf from './data-process-waf/index.vue';
import DataProcessApt from './data-process-apt/index.vue';
import DataProcessDsmp from './data-process-dsmp/index.vue';
import SystemPlugin from './SystemPlugin/index.vue';
import ParamConfig from './param-config/index.vue';
import LvsConfig from './lvs-config/index.vue';
import SystemPacketConfig from './system-packet-config/index.vue';
import LogLevel from './log-level/index.vue';
import UpgradeConfig from './upgrade-config/index.vue';

export default defineComponent({
  name: 'SystemManage',
  components: {
    SystemLog,
    DataProcessWaf,
    DataProcessApt,
    DataProcessDsmp,
    SystemPlugin,
    ParamConfig,
    LvsConfig,
    SystemPacketConfig,
    LogLevel,
    UpgradeConfig
  },
  setup (props, ctx) {
    const state = reactive({
      activeKey: TABS[0].componentName
    })

    return {
      TABS,
      ...toRefs(state),
    }
  }
})
</script>
