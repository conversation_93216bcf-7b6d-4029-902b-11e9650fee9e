// 抓包配置配置

// 协议选项
export const PROTOCOL_OPTIONS = [
  { label: 'ALL', value: 'ALL' },
  { label: 'TCP', value: 'TCP' },
  { label: 'UDP', value: 'UDP' },
  { label: 'ICMP', value: 'ICMP' },
  { label: 'HTTP', value: 'HTTP' },
  { label: 'HTTPS', value: 'HTTPS' }
];

// 引擎接口选项
export const ENGINE_INTERFACE_OPTIONS = [
  { label: 'eth0', value: 'eth0' },
  { label: 'eth1', value: 'eth1' },
  { label: 'eth2', value: 'eth2' },
  { label: 'eth3', value: 'eth3' },
  { label: 'eth4', value: 'eth4' },
  { label: 'eth5', value: 'eth5' }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
      width: 300,
      align: "center",
      ellipsis: true,
    },
    {
      title: '文件大小(KB)',
      dataIndex: 'packageSize',
      key: 'packageSize',
      width: 150,
      align: "center",
    },
    {
      title: '生成时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
