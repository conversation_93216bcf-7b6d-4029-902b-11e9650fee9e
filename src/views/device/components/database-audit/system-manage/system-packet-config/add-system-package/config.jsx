export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            title: '启用',
            'x-decorator': 'FormItem',
            'x-component': 'Switch',
            'x-component-props': {
              size: 'small'
            },
            default: false
          },
          name: {
            type: 'string',
            title: '名称',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入名称'
            }
          },
          engineInter: {
            type: 'string',
            title: '引擎接口',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              placeholder: '请选择引擎接口'
            },
            enum: [
              { label: 'eth0', value: 'eth0' },
              { label: 'eth1', value: 'eth1' },
              { label: 'eth2', value: 'eth2' },
              { label: 'eth3', value: 'eth3' },
              { label: 'eth4', value: 'eth4' },
              { label: 'eth5', value: 'eth5' }
            ],
            'x-validator': [
              {
                required: true,
                message: '请选择引擎接口'
              }
            ]
          },
          packageSize: {
            type: 'string',
            title: '抓包大小（M）',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入抓包大小'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入抓包大小'
              },
              {
                pattern: /^\d+(\.\d+)?$/,
                message: '请输入正确的数字格式'
              }
            ]
          },
          protocol: {
            type: 'string',
            title: '协议',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              placeholder: '请选择协议'
            },
            enum: [
              { label: 'ANY', value: 'ANY' },
              { label: 'ALL', value: 'ALL' },
              { label: 'TCP', value: 'TCP' },
              { label: 'UDP', value: 'UDP' },
              { label: 'ICMP', value: 'ICMP' },
              { label: 'HTTP', value: 'HTTP' },
              { label: 'HTTPS', value: 'HTTPS' }
            ],
            'x-validator': [
              {
                required: true,
                message: '请选择协议'
              }
            ]
          },
          ipFirst: {
            type: 'string',
            title: 'IP1',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入IP地址'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入IP地址'
              },
              {
                pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
                message: '请输入正确的IP地址格式'
              }
            ]
          },
          ipSecond: {
            type: 'string',
            title: 'IP2',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入IP地址（可选）'
            },
            'x-validator': [
              {
                pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
                message: '请输入正确的IP地址格式'
              }
            ]
          }
        }
      }
    }
  };
}
