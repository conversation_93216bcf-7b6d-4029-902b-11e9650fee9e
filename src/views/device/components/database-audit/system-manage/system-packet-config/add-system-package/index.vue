<template>
  <a-modal
    :visible="visible"
    :title="modalTitle"
    :width="600"
    @ok="handleOk"
    @cancel="handleCancel"
    :confirmLoading="btnLoading"
    :maskClosable="false"
  >
    <a-spin :spinning="loading">
      <ProForm
        ref="formRef"
        :form="form"
        :schema="schema"
        :disabled="isView"
      />
    </a-spin>
  </a-modal>
</template>

<script setup>
import { createForm } from "@formily/vue";
import { ref, defineExpose, defineEmits, computed, shallowRef } from 'vue';
import { message } from 'ant-design-vue';
import ProForm from '@/components/pro-form/index.vue';
import { getSchema } from './config.jsx';
import { insertSystemPackage, modifySystemPackage } from '@/request/api-device-db/system-log';

const emits = defineEmits(['refresh']);
const visible = ref(false);
const btnLoading = ref(false);
const loading = ref(false);
const modalTitle = ref('新增抓包');
const mode = ref('add');

const form = shallowRef(null);
const schema = shallowRef(null);

const isView = computed(() => mode.value === 'view');

async function handleOk() {
  try {
    await form.value.validate();
    btnLoading.value = true;

    const payload = { ...form.value.values };

    switch (mode.value) {
      case 'add':
        await insertSystemPackage(payload);
        message.success('新增成功');
        break;
      case 'edit':
        await modifySystemPackage(payload);
        message.success('编辑成功');
        break;
    }

    visible.value = false;
    emits('refresh');
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败');
  } finally {
    btnLoading.value = false;
  }
}

function handleCancel() {
  visible.value = false;
}

function open(options) {
  const { title, data, mode: openMode } = options;
  
  modalTitle.value = title;
  mode.value = openMode;
  
  // 创建表单实例
  form.value = createForm({
    initialValues: data || {},
  });
  
  // 创建schema
  schema.value = getSchema();
  
  visible.value = true;
}

defineExpose({
  open
});
</script>
