// 引擎配置配置

// 接口类型选项
export const INTERFACE_TYPE_OPTIONS = [
  { label: '管理口', value: '管理口' },
  { label: '数据接口', value: '数据接口' },
  { label: '冗余接口', value: '冗余接口' },
  { label: '监控接口', value: '监控接口' }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '接口名称',
      dataIndex: 'name',
      key: 'name',
      width: 120,
      align: "center",
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      align: "center",
    },
    {
      title: 'MAC',
      dataIndex: 'mac',
      key: 'mac',
      width: 150,
      align: "center",
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: 'IP地址',
      dataIndex: 'ipv4',
      key: 'ipv4',
      width: 160,
      align: "center",
    },
    {
      title: '协商速率',
      dataIndex: 'speed',
      key: 'speed',
      width: 120,
      align: "center",
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 150,
      fixed: 'right',
      align: "center",
    }
  ];
}
