export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            title: '启用',
            'x-decorator': 'FormItem',
            'x-component': 'Switch',
            'x-component-props': {
              size: 'small'
            },
            default: false
          },
          name: {
            type: 'string',
            title: '接口名称',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入接口名称',
              disabled: true
            },
            'x-validator': [
              {
                required: true,
                message: '请输入接口名称'
              }
            ]
          },
          type: {
            type: 'string',
            title: '接口类型',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              placeholder: '请选择接口类型',
              disabled: true
            },
            enum: [
              { label: '管理口', value: '管理口' },
              { label: '数据接口', value: '数据接口' },
              { label: '冗余接口', value: '冗余接口' },
              { label: '监控接口', value: '监控接口' }
            ],
            'x-validator': [
              {
                required: true,
                message: '请选择接口类型'
              }
            ]
          },
          ipv4: {
            type: 'string',
            title: 'IPv4地址/掩码',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '例如：************/24'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入IPv4地址/掩码'
              },
              {
                pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)\/([1-2]?\d|3[0-2])$/,
                message: '请输入正确的IPv4地址/掩码格式，例如：************/24'
              }
            ]
          },
          ipv6: {
            type: 'string',
            title: 'IPv6地址/掩码',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '例如：2001:db8:2::40/64（可选）'
            },
            'x-validator': [
              {
                validator: (_, value) => {
                  if (!value) return Promise.resolve(); // 可选字段，空值通过验证

                  // 简化的IPv6验证，支持常见格式
                  const ipv6Pattern = /^([0-9a-fA-F]{1,4}:){1,7}[0-9a-fA-F]{0,4}(\/([1-9]?\d|1[0-1]\d|12[0-8]))?$|^::1(\/\d+)?$|^::(\/\d+)?$/;

                  if (!ipv6Pattern.test(value)) {
                    return Promise.reject(new Error('请输入正确的IPv6地址/掩码格式，例如：2001:db8:2::40/64'));
                  }

                  return Promise.resolve();
                }
              }
            ]
          }
        }
      }
    }
  };
}
