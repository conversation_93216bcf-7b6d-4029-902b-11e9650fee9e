<template>
  <div>
    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" @click="handleView(record)">详情</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>

    <AddSystemEngine ref="addSystemEngineRef" @refresh="handleRefresh" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import CommonTable from '@/components/common-table/index.vue';
import { getTableColumns } from './config';
import AddSystemEngine from './add-system-engine/index.vue';
import {
  getSystemEnginePageData,
  deleteSystemEngine,
  modifySystemEngine
} from '@/request/api-device-db/system-log';

const tableRef = ref(null);
const addSystemEngineRef = ref(null);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

function handleRefresh () {
  tableRef.value?.refresh();
}

async function fetchData (params) {
  try {
    const { data } = await getSystemEnginePageData(params);
    return { items: data.data, total: data.total };
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
    return {
      items: [],
      total: 0
    };
  }
}

const tableColumns = computed(() => getTableColumns(tableRef.value?.pagination ?? {}, handleStatusChange));

// 状态切换处理
async function handleStatusChange(record) {
  try {
    // 调用修改接口
    await modifySystemEngine(record);
    message.success(`${record.enable ? '启用' : '禁用'}成功`);
  } catch (error) {
    console.error('状态切换失败:', error);
    message.error('状态切换失败');
    throw error; // 重新抛出错误，让配置文件中的 finally 块处理加载状态
  }
}

// 新增
function handleAdd () {
  addSystemEngineRef.value.open({
    title: '编辑接口',
    data: {
      name: 'eth0',
      type: '管理口',
      enable: false
    },
    mode: 'add',
  });
}

// 编辑
function handleEdit (record) {
  addSystemEngineRef.value.open({
    title: '编辑接口',
    data: { ...record },
    mode: 'edit',
  });
}

// 查看
function handleView (record) {
  addSystemEngineRef.value.open({
    title: '查看接口',
    data: { ...record },
    mode: 'view',
  });
}

// 删除
async function handleDelete (record) {
  Modal.confirm({
    title: '删除引擎配置',
    content: '确定删除该引擎配置吗？',
    onOk: async () => {
      try {
        await deleteSystemEngine({ id: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (err) {
        message.error('删除失败');
      }
    },
  });
}
</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  ::v-deep .ant-btn {
    padding: 4px 6px;
  }
}
</style>
