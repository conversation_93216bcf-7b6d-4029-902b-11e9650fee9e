<template>
  <div class="module-container">
    <div class="page-header">
      <h3>异常检查</h3>
      <p>系统异常检查与监控管理</p>
    </div>
    
    <div class="content-area">
      <a-card>
        <a-empty description="功能开发中，敬请期待..." />
      </a-card>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

onMounted(() => {
  console.log('异常检查模块已加载');
});
</script>

<style lang="less" scoped>
.module-container {
  padding: 16px;
  
  .page-header {
    margin-bottom: 16px;
    
    h3 {
      margin: 0 0 8px 0;
      color: #1890ff;
    }
    
    p {
      margin: 0;
      color: #666;
    }
  }
}
</style>
