export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            title: '启用',
            'x-decorator': 'FormItem',
            'x-component': 'Switch',
            'x-component-props': {
              size: 'small'
            },
            default: false
          },
          engineIp: {
            type: 'string',
            title: '引擎IP',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入引擎IP'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入引擎IP'
              },
              {
                pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
                message: '请输入正确的IP地址格式'
              }
            ]
          },
          hostIp: {
            type: 'string',
            title: '主机IP',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入主机IP'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入主机IP'
              },
              {
                pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
                message: '请输入正确的IP地址格式'
              }
            ]
          },
          port: {
            type: 'string',
            title: '端口',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入端口号'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入端口号'
              },
              {
                pattern: /^([1-9]\d{0,3}|[1-5]\d{4}|6[0-4]\d{3}|65[0-4]\d{2}|655[0-2]\d|6553[0-5])$/,
                message: '请输入正确的端口号(1-65535)'
              }
            ]
          },
          username: {
            type: 'string',
            title: '用户名',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入用户名'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入用户名'
              }
            ]
          },
          password: {
            type: 'string',
            title: '密码',
            required: true,
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入密码'
            },
            'x-validator': [
              {
                required: true,
                message: '请输入密码'
              }
            ]
          }
        }
      }
    }
  };
}
