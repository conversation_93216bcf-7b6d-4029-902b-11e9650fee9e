// 导流插件配置

// 状态选项
export const STATUS_OPTIONS = [
  { label: '安装失败', value: '安装失败' },
  { label: '未运行', value: '未运行' },
  { label: '运行中', value: '运行中' },
  { label: '已停止', value: '已停止' }
];

// 授权状态选项
export const AUTH_STATUS_OPTIONS = [
  { label: '未授权', value: '未授权' },
  { label: '试用授权', value: '试用授权' },
  { label: '正式授权', value: '正式授权' },
  { label: '授权过期', value: '授权过期' }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '插件id',
      dataIndex: 'pluginId',
      key: 'pluginId',
      width: 100,
      align: "center",
      ellipsis: true,
    },
    {
      title: '主机IP',
      dataIndex: 'hostIp',
      key: 'hostIp',
      width: 140,
      align: "center",
      ellipsis: true,
    },
    {
      title: '插件版本',
      dataIndex: 'pluginVersion',
      key: 'pluginVersion',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      align: "center",
      customRender: ({ text, record }) => {
        const status = text || record.status;
        const colorMap = {
          '安装失败': 'red',
          '未运行': 'orange',
          '运行中': 'green',
          '已停止': 'gray'
        };
        const color = colorMap[status] || 'default';
        return <a-tag color={color}>{status}</a-tag>;
      }
    },
    {
      title: 'CPU使用率',
      dataIndex: 'cpuUsage',
      key: 'cpuUsage',
      width: 120,
      align: "center",
    },
    {
      title: '内存使用率',
      dataIndex: 'memoryUsage',
      key: 'memoryUsage',
      width: 120,
      align: "center",
    },
    {
      title: '授权状态',
      dataIndex: 'authStatus',
      key: 'authStatus',
      width: 120,
      align: "center",
      customRender: ({ text, record }) => {
        const authStatus = text || record.authStatus;
        const colorMap = {
          '未授权': 'red',
          '试用授权': 'orange',
          '正式授权': 'green',
          '授权过期': 'red'
        };
        const color = colorMap[authStatus] || 'default';
        return <a-tag color={color}>{authStatus}</a-tag>;
      }
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
