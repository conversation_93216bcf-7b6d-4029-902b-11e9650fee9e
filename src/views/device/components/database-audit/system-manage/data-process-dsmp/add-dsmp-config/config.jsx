// 版本信息选项
const VERSION_OPTIONS = [
  { label: 'dsmp3.2', value: 'dsmp3.2' },
  { label: 'dsmp3.0', value: 'dsmp3.0' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: false,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              size: 'small'
            }
          },
          name: {
            type: 'string',
            title: '名称',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          version: {
            type: 'string',
            title: '版本信息',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择版本信息',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择版本信息'
              }
            ],
            enum: VERSION_OPTIONS
          },
          ip: {
            type: 'string',
            title: 'IP地址',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入IP地址'
              }
            ]
          },
          port: {
            type: 'string',
            title: '端口',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入端口'
              }
            ]
          },
          username: {
            type: 'string',
            title: '用户名',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入用户名'
              }
            ]
          },
          password: {
            type: 'string',
            title: '密码',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入密码'
              }
            ]
          }
        }
      }
    }
  };
}
