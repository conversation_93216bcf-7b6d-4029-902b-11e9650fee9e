<template>
  <div>
    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" @click="handleView(record)">查看</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>

    <AddSms ref="addSmsRef" @refresh="handleRefresh" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import CommonTable from '@/components/common-table/index.vue';
import { SEARCH_CONFIG, getTableColumns } from './config';
import AddSms from './add-sms/index.vue';
import {
  getBaseSmsPageData,
  deleteBaseSms,
  modifyBaseSms,
  exportBaseSms
} from '@/request/api-device-db/basic-setting';

const searchParams = ref({});
const tableRef = ref(null);
const dynamicSearchRef = ref(null);
const addSmsRef = ref(null);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

// 计算表格列配置
const tableColumns = computed(() => getTableColumns(
  { current: 1, pageSize: 10 }, // 分页信息
  handleStatusChange
));

function handleSearch (params) {
  searchParams.value = params;
  handleRefresh();
}

function handleRefresh () {
  tableRef.value?.refresh();
}

async function fetchData (params) {
  try {
    const { data } = await getBaseSmsPageData({
      ...params,
      ...searchParams.value,
    });
    return { items: data.data, total: data.total };
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
    return {
      items: [],
      total: 0
    };
  }
}

function handleAdd() {
  addSmsRef.value.open({
    title: '新增短信配置',
    data: {},
    mode: 'add'
  });
}

function handleEdit(record) {
  addSmsRef.value.open({
    title: '编辑短信配置',
    data: { ...record },
    mode: 'edit'
  });
}

function handleView(record) {
  addSmsRef.value.open({
    title: '查看短信配置',
    data: { ...record },
    mode: 'view'
  });
}

async function handleDelete(record) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除短信配置 "${record.name}" 吗？`,
    async onOk() {
      try {
        await deleteBaseSms({ id: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    }
  });
}

async function handleStatusChange(record) {
  try {
    await modifyBaseSms(record);
    message.success(`${record.enable ? '启用' : '禁用'}成功`);
    handleRefresh();
  } catch (error) {
    console.error('状态修改失败:', error);
    message.error('状态修改失败');
  }
}

// 导出
async function handleExport () {
  try {
    await exportBaseSms();
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
}
</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  :deep(.ant-btn) {
    padding: 4px 6px;
  }
}
</style>
