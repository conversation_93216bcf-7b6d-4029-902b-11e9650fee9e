// 表单配置
export function getFormSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用',
            },
          },
          name: {
            type: 'string',
            title: '名称',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          smsServer: {
            type: 'string',
            title: '短信服务器地址',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入短信服务器地址'
              }
            ]
          },
          smsPort: {
            type: 'string',
            title: '服务端口号',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入服务端口号'
              }
            ]
          },
          username: {
            type: 'string',
            title: '用户名',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入用户名'
              }
            ]
          },
          password: {
            type: 'string',
            title: '密码',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入密码'
              }
            ]
          },
          testPhone: {
            type: 'string',
            title: '测试手机号',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入测试手机号'
              },
              {
                validator: (value) => {
                  if (!value) return true;
                  const phoneRegex = /^1[3-9]\d{9}$/;
                  if (!phoneRegex.test(value)) {
                    return '请输入正确的手机号码';
                  }
                  return true;
                }
              }
            ]
          },
          serviceContent: {
            type: 'string',
            title: '服务内容',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
          }
        }
      }
    }
  };
}
