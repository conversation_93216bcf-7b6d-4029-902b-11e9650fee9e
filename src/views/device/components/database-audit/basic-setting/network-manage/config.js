// 网络管理配置

// 接口类型选项
export const INTERFACE_TYPE_OPTIONS = [
  { label: '管理口', value: '管理口' },
  { label: '业务口', value: '业务口' },
  { label: '镜像口', value: '镜像口' }
];

// 协商速率选项
export const RATE_OPTIONS = [
  { label: '10Mbps', value: '10Mbps' },
  { label: '100Mbps', value: '100Mbps' },
  { label: '1000Mbps', value: '1000Mbps' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '接口类型',
    name: 'interType',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择接口类型',
    },
    enum: [
      { label: '全部', value: null },
      ...INTERFACE_TYPE_OPTIONS
    ],
  },
  {
    title: 'IP地址',
    name: 'ip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入',
    },
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: "接口名",
      dataIndex: "interName",
      width: 120,
      align: "center",
    },
    {
      title: '接口类型',
      dataIndex: 'interType',
      key: 'interType',
      width: 120,
      align: "center",
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '协商速率',
      dataIndex: 'rate',
      key: 'rate',
      width: 120,
      align: "center",
    },
    {
      title: '路由配置',
      dataIndex: 'routeConfig',
      key: 'routeConfig',
      width: 200,
      align: "center",
      ellipsis: true,
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
