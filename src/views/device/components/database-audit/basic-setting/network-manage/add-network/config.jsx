import { INTERFACE_TYPE_OPTIONS, RATE_OPTIONS } from '../config';

// 表单配置
export function getFormSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用',
            },
          },
          interName: {
            type: 'string',
            title: '接口名称',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入接口名称'
              }
            ]
          },
          interType: {
            type: 'string',
            title: '接口类型',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择接口类型',
            },
            enum: INTERFACE_TYPE_OPTIONS,
            'x-validator': [
              {
                required: true,
                message: '请选择接口类型'
              }
            ]
          },
          ip: {
            type: 'string',
            title: 'IP地址',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入IP地址'
              },
              {
                validator: (value) => {
                  if (!value) return true;
                  // 简单的IP地址格式验证（支持CIDR格式）
                  const ipRegex = /^(\d{1,3}\.){3}\d{1,3}(\/\d{1,2})?$/;
                  if (!ipRegex.test(value)) {
                    return '请输入正确的IP地址格式（如：***********/24）';
                  }
                  return true;
                }
              }
            ]
          },
          rate: {
            type: 'string',
            title: '协商速率',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择协商速率',
            },
            enum: RATE_OPTIONS,
            'x-validator': [
              {
                required: true,
                message: '请选择协商速率'
              }
            ]
          },
          routeConfig: {
            type: 'string',
            title: '路由配置',
            'x-component': 'Input.TextArea',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
              rows: 3,
              maxLength: 19,
              showCount: true,
            },
            'x-validator': [
              {
                validator: (value) => {
                  if (!value) return true;
                  if (value.length < 8 || value.length > 19) {
                    return '路由配置长度必须在8-19个字符之间';
                  }
                  return true;
                }
              }
            ]
          }
        }
      }
    }
  };
}
