import { ENCODING_OPTIONS } from '../config';

// 表单配置
export function getFormSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用',
            },
          },
          name: {
            type: 'string',
            title: '名称',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          smtpServer: {
            type: 'string',
            title: 'SMTP服务器',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入SMTP服务器'
              }
            ]
          },
          smtpPort: {
            type: 'string',
            title: 'SMTP服务器端口',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
          },
          encoded: {
            type: 'string',
            title: '编码方式',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择编码方式',
            },
            enum: ENCODING_OPTIONS,
            'x-validator': [
              {
                required: true,
                message: '请选择编码方式'
              }
            ]
          },
          sender: {
            type: 'string',
            title: '发件人',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入发件人'
              }
            ]
          },
          smtpUser: {
            type: 'string',
            title: 'SMTP用户',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入SMTP用户'
              }
            ]
          },
          password: {
            type: 'string',
            title: '密码',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入密码'
              }
            ]
          },
          testAddress: {
            type: 'string',
            title: '测试收件地址',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入测试收件地址'
              }
            ]
          }
        }
      }
    }
  };
}
