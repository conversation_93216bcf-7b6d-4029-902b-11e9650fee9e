// EMAIL配置

// 编码方式选项
export const ENCODING_OPTIONS = [
  { label: 'UTF-8', value: 'UTF-8' },
  { label: 'UTF-16', value: 'UTF-16' },
  { label: 'GB2312', value: 'GB2312' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: "名称",
      dataIndex: "name",
      width: 150,
      align: "center",
    },
    {
      title: 'SMTP服务器',
      dataIndex: 'smtpServer',
      key: 'smtpServer',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '端口',
      dataIndex: 'smtpPort',
      key: 'smtpPort',
      width: 100,
      align: "center",
    },
    {
      title: '编码方式',
      dataIndex: 'encoded',
      key: 'encoded',
      width: 120,
      align: "center",
    },
    {
      title: '启用',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
