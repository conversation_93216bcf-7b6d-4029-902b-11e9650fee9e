<!-- 审计对象管理Tab -->

<template>
  <a-tabs type="card" v-model:activeKey="activeKey" size="small">
    <a-tab-pane v-for="tab in TABS" :key="tab.componentName" :tab="tab.label">
    </a-tab-pane>
  </a-tabs>
  <component :is="activeKey" />
</template>
<script lang="js">
import { defineComponent, toRefs, reactive, watch } from 'vue'
import { TABS } from './config';
import NetworkManage from './network-manage/index.vue';
import SnmpService from './snmp-service/index.vue';
import EmailConfig from './email-config/index.vue';
import SmsConfig from './sms-config/index.vue';

export default defineComponent({
  name: 'DbAudit',
  components: {
    NetworkManage,
    SnmpService,
    EmailConfig,
    SmsConfig,
  },
  setup (props, ctx) {
    const state = reactive({
      activeKey: TABS[0].componentName
    })

    return {
      TABS,
      ...toRefs(state),
    }
  }
})
</script>
