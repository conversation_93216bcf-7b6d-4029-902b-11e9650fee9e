<template>
  <div>
    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" @click="handleView(record)">查看</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>

    <AddSnmp ref="addSnmpRef" @refresh="handleRefresh" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import CommonTable from '@/components/common-table/index.vue';
import { SEARCH_CONFIG, getTableColumns } from './config';
import AddSnmp from './add-snmp/index.vue';
import {
  getBaseSnmpPageData,
  deleteBaseSnmp,
  modifyBaseSnmp,
  exportBaseSnmp
} from '@/request/api-device-db/basic-setting';

const searchParams = ref({});
const tableRef = ref(null);
const dynamicSearchRef = ref(null);
const addSnmpRef = ref(null);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

// 计算表格列配置
const tableColumns = computed(() => getTableColumns(
  { current: 1, pageSize: 10 }, // 分页信息
  handleStatusChange
));

function handleSearch (params) {
  searchParams.value = params;
  handleRefresh();
}

function handleRefresh () {
  tableRef.value?.refresh();
}

async function fetchData (params) {
  try {
    const { data } = await getBaseSnmpPageData({
      ...params,
      ...searchParams.value,
    });
    return { items: data.data, total: data.total };
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
    return {
      items: [],
      total: 0
    };
  }
}

function handleAdd() {
  addSnmpRef.value.open({
    title: '新增SNMP',
    data: {},
    mode: 'add'
  });
}

function handleEdit(record) {
  addSnmpRef.value.open({
    title: '编辑SNMP',
    data: { ...record },
    mode: 'edit'
  });
}

function handleView(record) {
  addSnmpRef.value.open({
    title: '查看SNMP',
    data: { ...record },
    mode: 'view'
  });
}

async function handleDelete(record) {
  Modal.confirm({
    title: '确认删除',
    content: `确定要删除SNMP配置 "${record.name}" 吗？`,
    async onOk() {
      try {
        await deleteBaseSnmp({ id: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (error) {
        console.error('删除失败:', error);
        message.error('删除失败');
      }
    }
  });
}

async function handleStatusChange(record) {
  try {
    await modifyBaseSnmp(record);
    message.success(`${record.enable ? '启用' : '禁用'}成功`);
    handleRefresh();
  } catch (error) {
    console.error('状态修改失败:', error);
    message.error('状态修改失败');
  }
}

// 导出
async function handleExport () {
  try {
    await exportBaseSnmp();
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
}
</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  :deep(.ant-btn) {
    padding: 4px 6px;
  }
}
</style>
