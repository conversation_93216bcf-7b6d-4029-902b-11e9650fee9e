// SNMP服务配置

// SNMP版本选项
export const SNMP_VERSION_OPTIONS = [
  { label: 'V1', value: 'V1' },
  { label: 'V2C', value: 'V2C' },
  { label: 'V3', value: 'V3' }
];

// 密码HASH算法选项
export const HASH_ALGORITHM_OPTIONS = [
  { label: 'MD5', value: 'MD5' },
  { label: 'SHA', value: 'SHA' },
  { label: 'SHA-256', value: 'SHA-256' },
  { label: 'SHA-512', value: 'SHA-512' }
];

// 加密算法选项
export const ENCRYPTION_ALGORITHM_OPTIONS = [
  { label: 'AES', value: 'AES' },
  { label: 'DES', value: 'DES' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "名称",
      dataIndex: "name",
      width: 150,
      align: "center",
    },
    {
      title: '版本',
      dataIndex: 'version',
      key: 'version',
      width: 100,
      align: "center",
    },
    {
      title: '服务名称',
      dataIndex: 'groupWord',
      key: 'groupWord',
      width: 150,
      align: "center",
      ellipsis: true,
      customRender: ({ record }) => {
        // V1和V2C显示groupWord，V3不显示
        return record.version === 'V3' ? '-' : (record.groupWord || '-');
      }
    },
    {
      title: '密码HASH算法',
      dataIndex: 'hashAlgorithm',
      key: 'hashAlgorithm',
      width: 150,
      align: "center",
      customRender: ({ record }) => {
        // 只有V3版本才显示
        return record.version === 'V3' ? (record.hashAlgorithm || '-') : '-';
      }
    },
    {
      title: '加密算法',
      dataIndex: 'encryptionAlgorithm',
      key: 'encryptionAlgorithm',
      width: 120,
      align: "center",
      customRender: ({ record }) => {
        // 只有V3版本才显示
        return record.version === 'V3' ? (record.encryptionAlgorithm || '-') : '-';
      }
    },
    {
      title: '启用',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
