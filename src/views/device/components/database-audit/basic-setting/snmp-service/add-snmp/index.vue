<template>
  <a-modal
    v-model:visible="visible"
    :title="title"
    :width="600"
    :maskClosable="false"
    :destroyOnClose="true"
    @cancel="handleCancel"
  >
    <template #footer>
      <div class="operator-group" :style="{ justifyContent: 'flex-end' }">
        <a-button key="back" @click="handleCancel">{{ isView ? '关闭' : '取消' }}</a-button>
        <a-button
          :loading="btnLoading"
          v-show="!isView"
          key="submit"
          type="primary"
          @click="handleSubmit"
        >
          确定
        </a-button>
      </div>
    </template>

    <div class="form-container">
      <ProForm :form="form" v-if="!loading" :schema="schema" />
    </div>
  </a-modal>
</template>

<script setup>
import { createForm } from "@formily/vue";
import { ref, defineExpose, defineEmits, computed, shallowRef } from 'vue';
import { message } from 'ant-design-vue';
import ProForm from '@/components/pro-form/index.vue';
import { getFormSchema } from './config';
import { insertBaseSnmp, modifyBaseSnmp } from '@/request/api-device-db/basic-setting';

const emit = defineEmits(['refresh']);

const visible = ref(false);
const loading = ref(false);
const title = ref('');
const mode = ref('add'); // add, edit, view
const btnLoading = ref(false);

const form = shallowRef(null);
const schema = shallowRef(null);

const isView = computed(() => mode.value === 'view');

function initForm(values) {
  schema.value = getFormSchema();
  form.value = createForm({
    values: values,
  });
}

function open({ title: modalTitle, data = {}, mode: modeType }) {
  // 处理默认值
  const defaultValues = {
    enable: true,
    version: 'V1',
    hashAlgorithm: 'MD5',
    encryptionAlgorithm: 'AES',
    ...data
  };

  initForm(defaultValues);
  visible.value = true;
  mode.value = modeType;
  title.value = modalTitle;

  if (mode.value === "view") {
    setTimeout(() => {
      form.value.readPretty = true;
    }, 50);
  }
}

function handleCancel() {
  visible.value = false;
}

async function handleSubmit() {
  try {
    await form.value.validate();
    btnLoading.value = true;

    const payload = { ...form.value.values };

    switch (mode.value) {
      case 'add':
        await insertBaseSnmp(payload);
        message.success('新增成功');
        break;
      case 'edit':
        await modifyBaseSnmp(payload);
        message.success('修改成功');
        break;
    }

    visible.value = false;
    emit('refresh');
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败');
  } finally {
    btnLoading.value = false;
  }
}

defineExpose({
  open
});
</script>

<style lang="less" scoped>
.form-container {
  padding: 16px 0;
}

.operator-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.version-notice {
  margin-top: 16px;
  padding: 12px;
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;

  p {
    margin: 0 0 8px 0;
    color: #d46b08;
    font-size: 12px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
