import { SNMP_VERSION_OPTIONS, HASH_ALGORITHM_OPTIONS, ENCRYPTION_ALGORITHM_OPTIONS } from '../config';

// 表单配置
export function getFormSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 200
        },
        properties: {
          enable: {
            type: 'boolean',
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用',
            },
          },
          name: {
            type: 'string',
            title: '名称',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          version: {
            type: 'string',
            title: '版本',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择版本',
            },
            enum: SNMP_VERSION_OPTIONS,
        'x-reactions': [
          {
            target: '*(groupWord)',
            fulfill: {
              state: {
                visible: '{{$self.value === "V1" || $self.value === "V2C"}}',
                required: '{{$self.value === "V1" || $self.value === "V2C"}}'
              }
            }
          },
          {
            target: '*(username)',
            fulfill: {
              state: {
                visible: '{{$self.value === "V3"}}',
                required: '{{$self.value === "V3"}}'
              }
            }
          },
          {
            target: '*(password)',
            fulfill: {
              state: {
                visible: '{{$self.value === "V3"}}',
                required: '{{$self.value === "V3"}}'
              }
            }
          },
          {
            target: '*(hashAlgorithm)',
            fulfill: {
              state: {
                visible: '{{$self.value === "V3"}}',
                required: '{{$self.value === "V3"}}'
              }
            }
          },
          {
            target: '*(encryptionAlgorithm)',
            fulfill: {
              state: {
                visible: '{{$self.value === "V3"}}',
                required: '{{$self.value === "V3"}}'
              }
            }
          },
          {
            target: '*(secretCode)',
            fulfill: {
              state: {
                visible: '{{$self.value === "V3"}}',
                required: '{{$self.value === "V3"}}'
              }
            }
          }
        ]
      },
          groupWord: {
            type: 'string',
            title: '服务名称',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入服务名称'
              }
            ]
          },
          username: {
            type: 'string',
            title: 'SNMP(V3)用户名',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入SNMP(V3)用户名'
              }
            ]
          },
          password: {
            type: 'string',
            title: 'SNMP(V3)密码',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
              maxLength: 19,
              showCount: true,
            },
            'x-validator': [
              {
                required: true,
                message: '请输入SNMP(V3)密码'
              },
              {
                validator: (value) => {
                  if (!value) return true;
                  if (value.length < 8 || value.length > 19) {
                    return 'SNMP(V3)密码长度必须在8-19个字符之间';
                  }
                  return true;
                }
              }
            ]
          },
          hashAlgorithm: {
            type: 'string',
            title: '密码HASH算法',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择密码HASH算法',
            },
            enum: HASH_ALGORITHM_OPTIONS,
            'x-validator': [
              {
                required: true,
                message: '请选择密码HASH算法'
              }
            ]
          },
          encryptionAlgorithm: {
            type: 'string',
            title: '加密算法',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择加密算法',
            },
            enum: ENCRYPTION_ALGORITHM_OPTIONS,
            'x-validator': [
              {
                required: true,
                message: '请选择加密算法'
              }
            ]
          },
          secretCode: {
            type: 'string',
            title: '加密口令',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
              maxLength: 19,
              showCount: true,
            },
            'x-validator': [
              {
                required: true,
                message: '请输入加密口令'
              },
              {
                validator: (value) => {
                  if (!value) return true;
                  if (value.length < 8 || value.length > 19) {
                    return '加密口令长度必须在8-19个字符之间';
                  }
                  return true;
                }
              }
            ]
          }
        }
      }
    }
  };
}
