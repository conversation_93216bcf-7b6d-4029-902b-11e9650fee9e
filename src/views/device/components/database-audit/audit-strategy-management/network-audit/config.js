// 网络审计配置

// 审计协议选项
export const AUDIT_PROTOCOL_OPTIONS = [
  { label: 'HTTP', value: 'HTTP' },
  { label: 'HTTPS', value: 'HTTPS' },
  { label: 'FTP', value: 'FTP' },
  { label: 'TELNET', value: 'TELNET' },
  { label: 'SSH', value: 'SSH' },
  { label: 'SMTP', value: 'SMTP' },
  { label: 'POP3', value: 'POP3' },
  { label: 'IMAP', value: 'IMAP' }
];

// 审计来源选项
export const AUDIT_SOURCE_OPTIONS = [
  { label: 'WebProxy', value: 'WebProxy' },
  { label: 'FirewallLog', value: 'FirewallLog' },
  { label: 'NetworkMonitor', value: 'NetworkMonitor' },
  { label: 'SecurityGateway', value: 'SecurityGateway' }
];

// 规则选项
export const RULE_OPTIONS = [
  { label: '登录招聘网站', value: '登录招聘网站' },
  { label: '浏览非法站点', value: '浏览非法站点' },
  { label: '访问社交媒体', value: '访问社交媒体' },
  { label: '下载大文件', value: '下载大文件' },
];

// 响应方式选项
export const RESPONSE_WAY_OPTIONS = [
  { label: '告警', value: '告警' },
  { label: '阻断', value: '阻断' },
  { label: '记录', value: '记录' },
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '策略名称',
    name: 'strategyName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入策略名称',
    },
  },
  {
    title: '规则',
    name: 'rule',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择规则',
    },
    enum: [
      { label: '全部', value: null },
      ...RULE_OPTIONS
    ],
  },
  {
    title: '审计源',
    name: 'auditSource',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择审计源',
    },
    enum: [
      { label: '全部', value: null },
      ...AUDIT_SOURCE_OPTIONS
    ],
  },
  {
    title: '审计协议',
    name: 'auditProtocol',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择审计协议',
    },
    enum: [
      { label: '全部', value: null },
      ...AUDIT_PROTOCOL_OPTIONS
    ],
  },
  {
    title: 'IP地址',
    name: 'ip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入IP地址',
    },
  },
  {
    title: '启用状态',
    name: 'enable',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择启用状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  }
];

// 表格列配置
export function getTableColumns(pagination = {}) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '策略名称',
      dataIndex: 'strategyName',
      key: 'strategyName',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '策略描述',
      dataIndex: 'strategyDesc',
      key: 'strategyDesc',
      width: 150,
      align: "center",
      ellipsis: true,
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '引擎/引擎组',
      dataIndex: 'engine',
      key: 'engine',
      width: 120,
      align: "center",
      ellipsis: true,
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '规则',
      dataIndex: 'rule',
      key: 'rule',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '响应方式',
      dataIndex: 'responseWay',
      key: 'responseWay',
      width: 100,
      align: "center",
    },
    {
      title: '资源账号',
      dataIndex: 'sourceAccount',
      key: 'sourceAccount',
      width: 120,
      align: "center",
      ellipsis: true,
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '审计源',
      dataIndex: 'auditSource',
      key: 'auditSource',
      width: 120,
      align: "center",
      ellipsis: true,
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '审计协议',
      dataIndex: 'auditProtocol',
      key: 'auditProtocol',
      width: 100,
      align: "center",
    },
    {
      title: 'IP地址',
      dataIndex: 'ip',
      key: 'ip',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender({ text }) {
        return text ? '启用' : '禁用';
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
