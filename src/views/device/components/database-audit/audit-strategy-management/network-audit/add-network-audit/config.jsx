// 审计协议选项
const AUDIT_PROTOCOL_OPTIONS = [
  { label: 'HTTP', value: 'HTTP' },
  { label: 'FTP', value: 'FTP' },
  { label: 'TELNET', value: 'TELNET' },
  { label: 'DNS', value: 'DNS' },
];

// 规则选项
const RULE_OPTIONS = [
  { label: '登录招聘网站', value: '登录招聘网站' },
  { label: '浏览非法站点', value: '浏览非法站点' },
  { label: '访问社交媒体', value: '访问社交媒体' },
  { label: '下载大文件', value: '下载大文件' },
];

// 响应方式选项
const RESPONSE_WAY_OPTIONS = [
  { label: '告警', value: '告警' },
  { label: '阻断', value: '阻断' },
  { label: '记录', value: '记录' },
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: true,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          },
          strategyName: {
            type: 'string',
            title: '策略名称',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入策略名称'
              }
            ]
          },
          strategyDesc: {
            type: 'string',
            title: '策略描述',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          engine: {
            type: 'string',
            title: '引擎/引擎组',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          rule: {
            type: 'string',
            default: '登录招聘网站',
            title: '规则',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择规则',
            },
            enum: RULE_OPTIONS
          },
          responseWay: {
            type: 'string',
            default: '告警',
            title: '响应方式',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择响应方式',
            },
            enum: RESPONSE_WAY_OPTIONS
          },
          sourceAccount: {
            type: 'string',
            title: '资源账号',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          auditSource: {
            type: 'string',
            title: '审计源',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入审计源IP，不输入为ANY',
            }
          },
          auditProtocol: {
            type: 'string',
            default: 'FTP',
            title: '审计协议',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择审计协议',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择审计协议'
              }
            ],
            enum: AUDIT_PROTOCOL_OPTIONS
          },
          ip: {
            type: 'string',
            title: 'IP地址',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入IP地址'
              }
            ]
          }
        }
      }
    }
  };
}
