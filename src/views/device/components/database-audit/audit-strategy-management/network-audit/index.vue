<template>
  <div>

    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" @click="handleView(record)">查看</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>

    <AddNetworkAudit ref="addNetworkAuditRef" @refresh="handleRefresh" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import DynamicSearch from '@/components/dynamic-search/index.vue';
import CommonTable from '@/components/common-table/index.vue';
import { SEARCH_CONFIG, getTableColumns } from './config';
import AddNetworkAudit from './add-network-audit/index.vue';
import {
  getAuditNetworkPageData,
  deleteAuditNetwork
} from '@/request/api-device-db/assets';

const searchParams = ref({});
const tableRef = ref(null);
const dynamicSearchRef = ref(null);
const addNetworkAuditRef = ref(null);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

function handleSearch (params) {
  searchParams.value = params;
  handleRefresh();
}

function handleRefresh () {
  tableRef.value?.refresh();
}

async function fetchData (params) {
  try {
    const { data } = await getAuditNetworkPageData({
      ...params,
      ...searchParams.value,
    });
    return { items: data.data, total: data.total };
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
    return {
      items: [],
      total: 0
    };
  }
}

const tableColumns = computed(() => getTableColumns(tableRef.value?.pagination ?? {}));

// 新增
function handleAdd () {
  addNetworkAuditRef.value.open({
    title: '新建网络审计',
    data: {},
    mode: 'add',
  });
}

// 编辑
function handleEdit (record) {
  addNetworkAuditRef.value.open({
    title: '编辑网络审计',
    data: { ...record },
    mode: 'edit',
  });
}

// 查看
function handleView (record) {
  addNetworkAuditRef.value.open({
    title: '查看网络审计',
    data: { ...record },
    mode: 'view',
  });
}

// 删除
async function handleDelete (record) {
  Modal.confirm({
    title: '删除网络审计',
    content: '确定删除该网络审计策略吗？',
    onOk: async () => {
      try {
        await deleteAuditNetwork({ ids: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (err) {
        message.error('删除失败');
      }
    },
  });
}
</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  ::v-deep .ant-btn {
    padding: 4px 6px;
  }
}
</style>
