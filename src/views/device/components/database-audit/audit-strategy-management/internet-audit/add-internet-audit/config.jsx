// 新增/编辑互联网审计配置

import { ENGINE_OPTIONS } from '../config';

export function getSchema() {
  return {
    type: 'object',
    properties: {
      enable: {
        type: 'boolean',
        title: '启用',
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        'x-component-props': {
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
        default: true,
      },
      ip: {
        type: 'string',
        title: '服务器IP',
        required: true,
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-component-props': {
          placeholder: '请输入服务器IP',
        },
        'x-validator': [
          {
            required: true,
            message: '请输入服务器IP',
          },
          {
            pattern: /^((25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(25[0-5]|2[0-4]\d|[01]?\d\d?)$/,
            message: '请输入正确的IP地址格式',
          },
        ],
      },
      engine: {
        type: 'string',
        title: '引擎/引擎组',
        'x-decorator': 'FormItem',
        'x-component': 'Input',
        'x-component-props': {
          placeholder: '请输入引擎名称',
        },
        default: 'InternetAuditEngine',
      },
      webmailEnable: {
        type: 'boolean',
        title: 'WebMail',
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        'x-component-props': {
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
        default: false,
      },
      networkDiskEnable: {
        type: 'boolean',
        title: '网盘',
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        'x-component-props': {
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
        default: false,
      },
      maliciousUrlEnable: {
        type: 'boolean',
        title: '恶意URL检测',
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        'x-component-props': {
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
        default: false,
      },
      internetEnable: {
        type: 'boolean',
        title: '上网行为',
        'x-decorator': 'FormItem',
        'x-component': 'Switch',
        'x-component-props': {
          checkedChildren: '启用',
          unCheckedChildren: '禁用',
        },
        default: false,
      },
    },
  };
}
