// 互联网审计配置

// 引擎选项
export const ENGINE_OPTIONS = [
  { label: 'InternetAuditEngine', value: 'InternetAuditEngine' },
  { label: 'DefaultEngine', value: 'DefaultEngine' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '服务器IP',
    name: 'ip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入服务器IP',
    },
  },
  {
    title: '创建时间',
    name: 'createTime',
    type: 'string',
    'x-component': 'DatePicker.RangePicker',
    'x-component-props': {
      placeholder: ['开始时间', '结束时间'],
      showTime: true,
      format: 'YYYY-MM-DD HH:mm:ss',
    },
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '服务器IP',
      dataIndex: 'ip',
      key: 'ip',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '引擎/引擎组',
      dataIndex: 'engine',
      key: 'engine',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: 'WebMail',
      dataIndex: 'webmailEnable',
      key: 'webmailEnable',
      width: 100,
      align: "center",
      customRender: ({ text }) => text ? '已启用' : '未启用'
    },
    {
      title: '网盘',
      dataIndex: 'networkDiskEnable',
      key: 'networkDiskEnable',
      width: 100,
      align: "center",
      customRender: ({ text }) => text ? '已启用' : '未启用'
    },
    {
      title: '恶意URL检测',
      dataIndex: 'maliciousUrlEnable',
      key: 'maliciousUrlEnable',
      width: 120,
      align: "center",
      customRender: ({ text }) => text ? '已启用' : '未启用'
    },
    {
      title: '上网行为',
      dataIndex: 'internetEnable',
      key: 'internetEnable',
      width: 100,
      align: "center",
      customRender: ({ text }) => text ? '已启用' : '未启用'
    },
    {
      title: '时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
