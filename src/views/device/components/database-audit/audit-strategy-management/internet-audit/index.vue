<template>
  <div>
    <DynamicSearch
      ref="dynamicSearchRef"
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    />

    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" @click="handleView(record)">详情</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>

    <AddInternetAudit ref="addInternetAuditRef" @refresh="handleRefresh" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import DynamicSearch from '@/components/dynamic-search/index.vue';
import CommonTable from '@/components/common-table/index.vue';
import { SEARCH_CONFIG, getTableColumns } from './config';
import AddInternetAudit from './add-internet-audit/index.vue';
import {
  getAuditInternetPageData,
  deleteAuditInternet
} from '@/request/api-device-db/audit-strategy';

const searchParams = ref({});
const tableRef = ref(null);
const dynamicSearchRef = ref(null);
const addInternetAuditRef = ref(null);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

function handleSearch(params) {
  // 处理时间范围参数
  if (params.createTime && params.createTime.length === 2) {
    params.startTime = params.createTime[0].format('YYYY-MM-DD HH:mm:ss');
    params.endTime = params.createTime[1].format('YYYY-MM-DD HH:mm:ss');
    delete params.createTime;
  }
  searchParams.value = params;
  handleRefresh();
}

function handleRefresh() {
  tableRef.value?.refresh();
}

async function fetchData(params) {
  try {
    const { data } = await getAuditInternetPageData({
      ...params,
      ...searchParams.value,
    });
    return { items: data.data, total: data.total };
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
    return {
      items: [],
      total: 0
    };
  }
}

const tableColumns = computed(() => getTableColumns(tableRef.value?.pagination ?? {}));

// 新增
function handleAdd() {
  addInternetAuditRef.value.open({
    title: '新增互联网审计',
    data: {},
    mode: 'add',
  });
}

// 编辑
function handleEdit(record) {
  addInternetAuditRef.value.open({
    title: '编辑互联网审计',
    data: { ...record },
    mode: 'edit',
  });
}

// 查看
function handleView(record) {
  addInternetAuditRef.value.open({
    title: '查看互联网审计',
    data: { ...record },
    mode: 'view',
  });
}

// 删除
async function handleDelete(record) {
  Modal.confirm({
    title: '删除互联网审计',
    content: '确定删除该互联网审计配置吗？',
    onOk: async () => {
      try {
        await deleteAuditInternet({ ids: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (err) {
        message.error('删除失败');
      }
    },
  });
}
</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  :deep(.ant-btn) {
    padding: 4px 6px;
  }
}
</style>
