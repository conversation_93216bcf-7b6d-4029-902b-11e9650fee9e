<template>
  <a-modal
    v-model:visible="visible"
    :title="modalTitle"
    width="650px"
    :maskClosable="false"
    :keyboard="false"
  >
    <ProForm :form="form" v-if="!loading" :schema="schema" />
    <template #footer>
      <div class="operator-group" :style="{ justifyContent: 'flex-end' }">
        <a-button key="back" @click="handleCancel">{{ isView ? '关闭' : '取消' }}</a-button>
        <a-button
          :loading="btnLoading"
          v-show="!isView"
          key="submit"
          type="primary"
          @click="handleOk"
        >
          确定
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { createForm } from "@formily/vue";
import { ref, defineExpose, defineEmits, computed, shallowRef } from 'vue';
import { message } from 'ant-design-vue';
import ProForm from '@/components/pro-form/index.vue';
import { getSchema } from './config.jsx';
import { insertAuditWhite, modifyAuditWhite } from '@/request/api-device-db/audit-strategy';

const emits = defineEmits(['refresh']);
const visible = ref(false);
const btnLoading = ref(false);
const loading = ref(false);
const modalTitle = ref('新增审计白名单');
const mode = ref('add');

const form = shallowRef(null);
const schema = shallowRef(null);

const isView = computed(() => mode.value === 'view');

async function handleOk() {
  try {
    await form.value.validate();
    btnLoading.value = true;

    const payload = { ...form.value.values };

    // 处理时间字段
    if (payload.timeType === '限制时长' && payload.limitTime) {
      // 计算结束时间
      const now = new Date();
      const duration = payload.limitTime.validity_period_time || 5;
      const unit = payload.limitTime.validity_period_unit || 'minute';

      let endTime = new Date(now);
      switch (unit) {
        case 'minute':
          endTime.setMinutes(endTime.getMinutes() + duration);
          break;
        case 'hour':
          endTime.setHours(endTime.getHours() + duration);
          break;
        case 'day':
          endTime.setDate(endTime.getDate() + duration);
          break;
      }

      payload.time1 = now.toISOString().slice(0, 19).replace('T', ' ');
      payload.time2 = endTime.toISOString().slice(0, 19).replace('T', ' ');
    } else if (payload.timeType === '时间范围' && payload.rangeTime && payload.rangeTime.timeRange) {
      // 使用时间范围
      const [startTime, endTime] = payload.rangeTime.timeRange;
      payload.time1 = startTime;
      payload.time2 = endTime;
    } else if (payload.timeType === '永久有效') {
      payload.time1 = '';
      payload.time2 = '';
    }

    // 清理不需要的字段
    delete payload.limitTime;
    delete payload.rangeTime;

    switch (mode.value) {
      case 'add':
        await insertAuditWhite(payload);
        message.success('新增成功');
        break;
      case 'edit':
        await modifyAuditWhite(payload);
        message.success('编辑成功');
        break;
    }

    visible.value = false;
    emits('refresh');
  } catch (error) {
    console.error('操作失败:', error);
  } finally {
    btnLoading.value = false;
  }
}

function handleCancel() {
  visible.value = false;
}

function initForm(values) {
  schema.value = getSchema();
  form.value = createForm({
    values: values,
  });
}

function open({ title, data = {}, mode: modeType }) {
  initForm({ ...data });
  visible.value = true;
  mode.value = modeType;
  modalTitle.value = title;

  if (mode.value === "view") {
    setTimeout(() => {
      form.value.readPretty = true;
    }, 50);
  }
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.operator-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
