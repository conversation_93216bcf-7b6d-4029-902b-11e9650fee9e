// 时间类型选项
const TIME_TYPE_OPTIONS = [
  { label: '限制时长', value: '限制时长' },
  { label: '时间范围', value: '时间范围' },
  { label: '永久有效', value: '永久有效' }
];

// 规则选项
const RULE_OPTIONS = [
  { label: '登录招聘网站', value: '登录招聘网站' },
  { label: '登录网盘行为', value: '登录网盘行为' },
  { label: '浏览非法站点', value: '浏览非法站点' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: true,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          },
          name: {
            type: 'string',
            title: '名称',
            'x-validator': [
              { required: true, message: '请输入名称' },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          strategyDesc: {
            type: 'string',
            title: '策略描述',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          engine: {
            type: 'string',
            title: '引擎/引擎组',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          sourceAudit: {
            type: 'string',
            title: '审计源',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入审计源IP，不输入为ANY',
            }
          },
          timeType: {
            type: 'string',
            title: '时间',
            'x-validator': [
              { required: true, message: '请选择时间类型' },
            ],
            default: '限制时长',
            'x-decorator': 'FormItem',
            'x-component': 'Radio.Group',
            'x-component-props': {
              optionType: 'button',
            },
            enum: TIME_TYPE_OPTIONS
          },
          limitTime: {
            type: 'void',
            title: ' ',
            'x-display': 'hidden',
            'x-decorator': 'FormItem',
            'x-component': 'Space',
            'x-decorator-props': {
              colon: false,
              feedbackLayout: 'terse',
            },
            properties: {
              validity_period_time: {
                type: 'number',
                'x-decorator': 'FormItem',
                'x-component': 'InputNumber',
                'x-component-props': {
                  placeholder: '请输入'
                },
                'x-decorator-props': {
                  colon: false,
                  addonBefore: '从当前时间起'
                },
                'x-validator': [
                  { required: true, message: '请输入' },
                  {
                    format: 'integer'
                  },
                  {
                    minimum: 1
                  }
                ]
              },
              validity_period_unit: {
                type: 'string',
                title: '',
                default: 'minute',
                'x-decorator': 'FormItem',
                'x-component': 'Select',
                'x-component-props': {
                  placeholder: '请选择',
                  showSearch: true,
                  filterOption: (input, option = {}) => {
                    if (!input) return true;
                    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                  },
                },
                enum: [
                  { label: '分钟', value: 'minute' },
                  { label: '小时', value: 'hour' },
                  { label: '天', value: 'day' }
                ],
                'x-decorator-props': {
                  colon: false,
                  addonAfter: '内有效'
                },
              }
            },
            'x-reactions': {
              dependencies: ['.timeType'],
              fulfill: {
                state: {
                  visible: `{{$deps[0] === '限制时长'}}`
                }
              }
            }
          },
          rangeTime: {
            type: 'void',
            title: ' ',
            'x-display': 'hidden',
            'x-decorator': 'FormItem',
            'x-component': 'VoidField',
            'x-decorator-props': {
              colon: false
            },
            properties: {
              timeRange: {
                type: 'array',
                title: '',
                'x-validator': [
                  { required: true, message: '请选择' },
                ],
                'x-decorator': 'FormItem',
                'x-decorator-props': {
                  colon: false
                },
                'x-component': 'DatePicker.RangePicker',
                'x-component-props': {
                  showTime: {format: 'HH:mm:ss'},
                  placeholder: ['开始时间', '结束时间'],
                  valueFormat: 'YYYY-MM-DD HH:mm:ss',
                  format: "YYYY-MM-DD HH:mm:ss"
                },
              },
            },
            'x-reactions': {
              dependencies: ['.timeType'],
              fulfill: {
                state: {
                  visible: `{{$deps[0] === '时间范围'}}`
                }
              }
            }
          },
          rule: {
            type: 'string',
            title: '规则',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              placeholder: '登录招聘网站',
            },
            enum: RULE_OPTIONS
          },
          ip: {
            type: 'string',
            title: 'IP地址',
            'x-validator': [
              { required: true, message: '请输入IP地址' },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          sourceAccount: {
            type: 'string',
            title: '资源账号',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          remark: {
            type: 'string',
            title: '备注',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              placeholder: '请输入备注',
              rows: 3
            },
            'x-decorator': 'FormItem'
          }
        }
      }
    }
  };
}
