/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
  {
    title: '启用状态',
    name: 'enable',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择启用状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  }
]

/** @type {*} 引擎/引擎组选项 */
export const ENGINE_OPTIONS = [
  { label: 'WebSecurityEngine', value: 'WebSecurityEngine' },
  { label: 'DatabaseEngine', value: 'DatabaseEngine' },
  { label: 'NetworkEngine', value: 'NetworkEngine' }
]

/** @type {*} 审计源选项 */
export const SOURCE_AUDIT_OPTIONS = [
  { label: 'Firewall', value: 'Firewall' },
  { label: 'IDS', value: 'IDS' },
  { label: 'Database', value: 'Database' }
]

/** @type {*} 时间类型选项 */
export const TIME_TYPE_OPTIONS = [
  { label: '限制时长', value: '限制时长' },
  { label: '时间范围', value: '时间范围' },
  { label: '永久有效', value: '永久有效' }
]

/** @type {*} 规则选项 */
export const RULE_OPTIONS = [
  { label: '登录招聘网站', value: '登录招聘网站' },
  { label: '登录网盘行为', value: '登录网盘行为' },
  { label: '浏览非法站点', value: '浏览非法站点' }
]



/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns () {
  return [
    {
      title: "名称",
      dataIndex: "name",
      width: 150,
      align: "center",
      fixed: 'left',
      ellipsis: true,
    },
    {
      title: '审计源',
      dataIndex: 'sourceAudit',
      key: 'sourceAudit',
      width: 120,
      align: "center",
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip',
      width: 180,
      align: "center",
      ellipsis: true,
      customRender: ({ text }) => text || '-'
    },
    {
      title: '协议',
      dataIndex: 'protocol',
      key: 'protocol',
      width: 100,
      align: "center",
      customRender: ({ text }) => text || 'ANY'
    },
    {
      title: '起始时间',
      dataIndex: 'time1',
      key: 'time1',
      width: 160,
      align: "center",
      customRender: ({ text }) => {
        if (!text) return '-'
        return text
      }
    },
    {
      title: '结束时间',
      dataIndex: 'time2',
      key: 'time2',
      width: 160,
      align: "center",
      customRender: ({ text }) => {
        if (!text) return '-'
        return text
      }
    },
    {
      title: '启用',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ text }) => {
        return text ? '是' : '否';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ]
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 120
      },
      properties: {
        enable: {
          type: 'boolean',
          default: true,
          title: '启用',
          'x-component': 'Switch',
          'x-decorator': 'FormItem',
          'x-component-props': {
            checkedChildren: '启用',
            unCheckedChildren: '禁用'
          }
        },
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          }
        },
        strategyDesc: {
          type: 'string',
          title: '策略描述',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          }
        },
        engine: {
          type: 'string',
          title: '引擎/引擎组',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          }
        },
        sourceAudit: {
          type: 'string',
          title: '审计源',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入审计源IP，不输入为ANY',
          }
        },
        timeType: {
          type: 'string',
          title: '时间',
          'x-validator': [
            { required: true, message: '请选择时间类型' },
          ],
          default: '限制时长',
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          'x-component-props': {
            optionType: 'button',
          },
          enum: TIME_TYPE_OPTIONS
        },
        timeRange: {
          type: 'object',
          title: '时间设置',
          'x-decorator': 'FormItem',
          'x-component': 'Space',
          'x-component-props': {
            align: 'center',
          },
          'x-reactions': {
            dependencies: ['.timeType'],
            fulfill: {
              state: {
                visible: `{{$deps[0] === '限制时长'}}`
              }
            }
          },
          properties: {
            text: {
              type: 'void',
              'x-component': 'Text',
              'x-component-props': {
                content: '从当前时间起'
              }
            },
            duration: {
              type: 'number',
              'x-component': 'InputNumber',
              'x-component-props': {
                placeholder: '5',
                min: 1,
                style: { width: '80px' }
              }
            },
            unit: {
              type: 'string',
              'x-component': 'Select',
              'x-component-props': {
                style: { width: '80px' }
              },
              enum: [
                { label: '分钟', value: '分钟' },
                { label: '小时', value: '小时' },
                { label: '天', value: '天' }
              ]
            },
            text2: {
              type: 'void',
              'x-component': 'Text',
              'x-component-props': {
                content: '内有效'
              }
            }
          }
        },
        rule: {
          type: 'string',
          title: '规则',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '登录招聘网站',
          },
          enum: RULE_OPTIONS
        },
        ip: {
          type: 'string',
          title: 'IP地址',
          'x-validator': [
            { required: true, message: '请输入IP地址' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          }
        },
        sourceAccount: {
          type: 'string',
          title: '资源账号',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入',
          }
        },
        remark: {
          type: 'string',
          title: '备注',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入备注',
            rows: 3
          },
          'x-decorator': 'FormItem'
        }
      }
    }
  }
}
