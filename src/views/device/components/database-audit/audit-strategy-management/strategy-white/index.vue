<template>
  <div>
    <DynamicSearch
      ref="dynamicSearchRef"
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    />

    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" @click="handleView(record)">查看</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>

    <AddWhiteAudit ref="addWhiteAuditRef" @refresh="handleRefresh" />
  </div>
</template>
<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import DynamicSearch from '@/components/dynamic-search/index.vue';
import CommonTable from '@/components/common-table/index.vue';
import { SEARCH_CONFIG, getColumns } from './config';
import AddWhiteAudit from './add-white-audit/index.vue';
import {
  getAuditWhitePageData,
  deleteAuditWhite
} from '@/request/api-device-db/audit-strategy';

const searchParams = ref({});
const tableRef = ref(null);
const dynamicSearchRef = ref(null);
const addWhiteAuditRef = ref(null);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

function handleSearch (params) {
  searchParams.value = params;
  handleRefresh();
}

function handleRefresh () {
  tableRef.value?.refresh();
}

async function fetchData (params) {
  const { data } = await getAuditWhitePageData({
    ...params,
    ...searchParams.value,
  });
  return { items: data.data, total: data.total };
}

const tableColumns = computed(() => getColumns(tableRef.value?.pagination ?? {}));

// 新增
function handleAdd () {
  addWhiteAuditRef.value.open({
    title: '新建白名单',
    data: {},
    mode: 'add',
  });
}

// 编辑
function handleEdit (record) {
  addWhiteAuditRef.value.open({
    title: '编辑白名单',
    data: { ...record },
    mode: 'edit',
  });
}

// 查看
function handleView (record) {
  addWhiteAuditRef.value.open({
    title: '查看白名单',
    data: { ...record },
    mode: 'view',
  });
}

// 删除
async function handleDelete (record) {
  Modal.confirm({
    title: '删除白名单',
    content: '确定删除该白名单策略吗？',
    onOk: async () => {
      try {
        await deleteAuditWhite({ ids: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (err) {
        message.error('删除失败');
      }
    },
  });
}
</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  ::v-deep .ant-btn {
    padding: 4px 6px;
  }
}
</style>
