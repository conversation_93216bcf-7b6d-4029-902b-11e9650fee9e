<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="700px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form" >
        <SchemaField :schema="schema" :scope="formScope" ></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave"
        >保存</a-button
      >
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{
        isView ? "关闭" : "取消"
      }}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, inject, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { keyBy, cloneDeep, values } from 'lodash'
import { createForm, onFieldInputValueChange } from '@formily/core'
import { SCHEMA, lineToTree } from './config';
import { formatTreeDataSource } from '../../audit-object-manage/policy-object/config'
import {
  getDbIpObjectList,
  getDbTimeObjectList,
  getDbPolicyRuleAndChildrenList,
  getDbPolicyConfigServerList
} from '@/request/api-device-db.js'


export default defineComponent({
  name: 'EditList',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { deviceSafetyId } = inject('rowData')
    const { visible, mode, defaultData } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}策略`
    })

    const dataSourceMap = shallowRef({
      serverMap: {},
      ruleMap: {}
    })

    const state = reactive({
      loading: false
    })

    const form = createForm({
      effects: () =>{
        onFieldInputValueChange('.server_object_id', (field) => {
          if (!field) return
          const rulesField = field.query('.rules').take();
          if (rulesField) rulesField.value = []
        })
      }
    })

    // 获取服务器对象数据源
    let serverReqPromise = null
    const getServerDataSource = () => {
      return async (field) => {
        let data = []
        try {
          if (!serverReqPromise) serverReqPromise = getDbPolicyConfigServerList({deviceSafetyId, current: 1, size: 1000})
          const res = await serverReqPromise;
          data = res?.data?.data ?? []
          dataSourceMap.value.serverMap = keyBy(data, 'id')
        } finally {
          field.setDataSource(data?.map(({name, id}) => ({label: name, value: id})) ?? [])
        }
      }
    }

    // 获取源IP地址对象数据源
    let sipReqPromise = null
    const getSipDataSource = () => {
      return async (field) => {
        let data = []
        try {
          if (!sipReqPromise) sipReqPromise = getDbIpObjectList({deviceSafetyId, current: 1, size: 1000})
          const res = await sipReqPromise;
          data = res?.data?.data ?? []
        } finally {
          field.setDataSource(data?.map(({name, id}) => ({label: name, value: id})) ?? [])
        }
      }
    }

    // 获取时间对象数据源
    let timeReqPromise = null
    const getTimeDataSource = () => {
      return async (field) => {
        let data = []
        try {
          if (!timeReqPromise) timeReqPromise = getDbTimeObjectList({deviceSafetyId, current: 1, size: 1000})
          const res = await timeReqPromise;
          data = res?.data?.data ?? []
        } finally {
          const DEFAULT_NAME_MAP = {
            '<time_name.worktime>': '工作时间',
            '<time_name.notworktime>': '非工作时间'
          }
          field.setDataSource(data?.map(({name, id}) => ({label: DEFAULT_NAME_MAP[name] ?? name, value: id})) ?? [])
        }
      }
    }

    // 获取策略树对象数据源
    const getRuleTreeDataSource = () => {
      return async (field) => {
        const serverField = field?.query('server_object_id')?.take()
        if (!serverField || !serverField.value) return
        const protocol_id = dataSourceMap.value?.serverMap?.[serverField.value]?.protocol_id
        let data = []
        try {
          state.loading = true
          const { data: result = {} } = await getDbPolicyRuleAndChildrenList({deviceSafetyId, protocol_id})
          const { treeList = [], treeMap = {} } = formatTreeDataSource(lineToTree(result))
          data = treeList
          dataSourceMap.value.ruleMap = keyBy(values(cloneDeep(treeMap)), 'id')
        } finally {
          state.loading = false
          field?.setDataSource(data ?? [])
        }
      }
    }

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    // 表单初始化
    const initForm = async () => {
      if (mode.value === 'add') return;
      const formData = cloneDeep(defaultData.value)
      formData.rules = formData.rule_set?.rules?.map(({id}) => id) ?? []
      form.setValues({...formData})
    }

    // 关闭弹窗
    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    // 保存
    const handleSave = async () => {
      await form.validate()
      state.loading = true
      const params = cloneDeep(form.values)
      params.policy_type ??= 1
      params.enable_session_data ??= 0
      if (!params.action) params.action = {}
      params.action.enable_record ??= 0
      params.action.block ??= []
      params.action.alarm ??= []
      params.action.outer_send ??= []
      const rules = params.rules?.reduce((prev, cur) => {
        const ruleObj = dataSourceMap.value.ruleMap?.[cur]
        if (ruleObj) prev.push({...ruleObj, columns: [], content: []})
        return prev
      }, [])
      if (!params.rule_set) params.rule_set = {}
      params.rule_set.rules = rules
      if (!params.rule_set.name) {
        const ruleField = form.query('.rules').take()
        const { dataSource = []} = ruleField?.getState() ?? {}
        params.rule_set.name = dataSource?.[0]?.name?.split('/')?.[0] ?? 'test'
        // params.rule_set.description = params.rule_set.name ?? ''
      }
      ctx.emit('on-success', {
        mode: mode.value,
        data: params,
        callback: handleClose
      })
    }

    return {
      title,
      isView,
      form,
      formScope: {
        getSipDataSource,
        getTimeDataSource,
        getRuleTreeDataSource,
        getServerDataSource
      },
      schema: SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script>
