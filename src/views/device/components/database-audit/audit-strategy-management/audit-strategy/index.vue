<!-- 审计策略管理Tab -->

<template>
  <div class="audit-strategy-manage">
    <a-spin :spinning="globalLoading">
      <a-space align="end" class="table-operate">
        <a-button type="primary" @click="handleOpenDialog('add')">
          新增
        </a-button>
      </a-space>
      <a-table
        :scroll="{y: 'calc(100vh - 370px)'}"
        v-if="!globalLoading"
        :data-source="dataSource"
        :columns="columns"
        :loading="loading"
        :pagination="pagination"
        @change="handleTableChange"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.key === 'operation'">
            <a-button type="link" @click="handleOpenDialog('edit', record)"
              >编辑</a-button
            >
            <a-button type="link" danger @click="handleDelete(record)"
              >删除</a-button
            >
          </template>
        </template>
      </a-table>
      <EditDialog
        v-model:visible="dialogConfig.visible"
        v-bind="dialogConfig"
        @on-success="handleSave"
      />
    </a-spin>
  </div>
</template>

<script>
import { keyBy } from 'lodash'
import { usePagination } from "vue-request";
import {
  onMounted,
  inject,
  computed,
  getCurrentInstance,
  reactive,
  createVNode,
  toRefs,
  shallowRef,
  ref
} from "vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import DynamicSearch from "@/components/dynamic-search/index.vue";
import { SEARCH_CONFIG, getColumns } from "./config";
import EditDialog from "./edit-dialog.vue";
import {
  addDbPolicyConfig,
  getDbPolicyConfigList,
  updateDbPolicyConfig,
  deleteDbPolicyConfig,
  getDbPolicyConfigServerList,
  getDbTimeObjectList
} from '@/request/api-device-db';

export default {
  components: {
    DynamicSearch,
    EditDialog
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const { deviceSafetyId } = inject("rowData");
    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getDbPolicyConfigList, {
      manual: true,
      defaultParams: {
        deviceSafetyId,
      },
      formatResult: ({data = {}}) => {
        return { items: data?.data ?? [], total: data?.total ?? 0 }
      },
      pagination: {
        currentKey: 'current',
        pageSizeKey: 'size',
      }
    });

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`,
    }));

    // 数据源映射
    const dataMap = shallowRef({
      serverList: [],
      serverMap: {},
      timeList: [],
      timeMap: {}
    })

    // 列表数据
    const dataSource = computed(() => {
      return data.value?.items || [];
    });

    // 分页器变化
    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        deviceSafetyId,
        size: pag.pageSize,
        current: pag?.current,
        // ...state.searchParams
      })
    }

    // 刷新列表
    const refreshTableData = (isReload = true) => {
      run({
        deviceSafetyId,
        current: isReload ? 1 : pagination.value.current,
        size: pagination.value?.pageSize,
        // ...state.searchParams
      })
    }

    const state = reactive({
      globalLoading: true,
      columns: [],
      dialogConfig: {
        visible: false,
        mode: "add",
        defaultData: {},
      },
    });

    // 删除
    const handleDelete = async ({ id }) => {
      proxy.$confirm({
        title: "删除",
        content: "删除后信息将无法找回，是否进行删除?",
        icon: createVNode(ExclamationCircleOutlined),
        okText: "确定",
        okType: "warning",
        cancelText: "取消",
        async onOk() {
          try {
            await deleteDbPolicyConfig({ id, deviceSafetyId });
            proxy.$message.success('删除成功！')
            refreshTableData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    };

    // 打开弹窗
    const handleOpenDialog = (mode = "add", row = {}) => {
      state.dialogConfig.visible = true;
      state.dialogConfig.mode = mode;
      state.dialogConfig.defaultData = row;
    };

    // 新增、编辑数据
    const handleSave = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addDbPolicyConfig,
        edit: updateDbPolicyConfig
      }
      try {
        const res = await requestMethodEnum[mode]?.({...data, deviceSafetyId: deviceSafetyId})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        refreshTableData()
      } catch (err) {
        // const errMsg = isAdd ? '新增失败！' : '更新失败！';
        // proxy.$message.error(err.message || errMsg)
        callback(true, err)
      }
    }

    // 数据源初始化
    const initDataMap = async () => {
      try {
        state.globalLoading = true
        const [{value: serverRes = []}, {value: timeRes = []}] = await Promise.allSettled([
          getDbPolicyConfigServerList({deviceSafetyId, current: 1, size: 1000}),
          getDbTimeObjectList({deviceSafetyId, current: 1, size: 1000})
        ])
        const serverData = serverRes?.data?.data ?? []
        const timeData = timeRes?.data?.data ?? []
        dataMap.value.serverList = serverData
        dataMap.value.serverMap = serverData?.reduce((prev, cur) => {
          prev[cur.id] = cur.name
          return prev
        }, {}) ?? {}
        dataMap.value.timeList = timeData
        dataMap.value.timeMap = timeData?.reduce((prev, cur) => {
          prev[cur.id] = cur.name
          return prev
        }, {}) ?? {}
      } finally {
        state.globalLoading = false
      }
    }

    onMounted(async () => {
      await initDataMap()
      state.columns = getColumns({timeMap: dataMap.value.timeMap ?? {}, serverMap: dataMap.value.serverMap ?? {}, pagination})
      refreshTableData()
    })

    return {
      SEARCH_CONFIG,
      columns: [],
      dataSource,
      pagination,
      ...toRefs(state),
      loading,
      handleTableChange,
      handleOpenDialog,
      handleDelete,
      handleSave
    };
  },
};
</script>

<style lang="less" scoped>
.audit-strategy-manage {
  .table-operate{
    margin-bottom: 8px 0;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
