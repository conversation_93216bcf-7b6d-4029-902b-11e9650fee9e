import CellExpand from '@/components/cell-expand'

/**
 * 数组转map
 *
 * @param {*} [dataSource=[]]
 * @param {*} [map={}]
 * @return {*}
 */
function transformDataSourceMap(dataSource = [], map = {}) {
  dataSource.forEach((item) => {
    map[item.id] = item.name;
    if (item.children) {
      transformDataSourceMap(item.children, map)
    }
  })
  return map
}

/**
 * 平级转树状
 *
 * @param {*} [list=[]]
 * @return {*}
 */
export const lineToTree = (list = []) => {
  if (!list.length) return []
  const map = {}
  const treeList = []
  list.forEach((item) => {
    if (item?.rules.length) item.children = item.rules
    map[item.id] = item
  })
  list.forEach((item) => {
    const parent = map[item.parent_id]
    if (!parent) {
      treeList.push(item)
    } else {
      parent.children ??= []
      parent.selectable = false
      parent.children.push(item)
    }
  })
  return treeList
}

/** @type {*} 事件级别枚举 */
const EVENT_LEVEL_ENUM = {
  HIGH: 5,
  MIDDLE: 10,
  LESS: 20,
  LOW: 30
}

/** @type {*} 事件级别Label映射 */
const EVENT_LEVEL_MAP = {
  [EVENT_LEVEL_ENUM.HIGH]: '极高',
  [EVENT_LEVEL_ENUM.MIDDLE]: '高',
  [EVENT_LEVEL_ENUM.LESS]: '中',
  [EVENT_LEVEL_ENUM.LOW]: '低'
}

/** @type {*} 筛选条件配置 */
export const SEARCH_CONFIG = [
  {
    title: '资产名称',
    name: 'label',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入资产名称',
    },
  },
  {
    title: 'IP地址',
    name: 'ip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入IP地址',
    },
  },
  {
    title: '负责人',
    name: 'person',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入负责人',
    },
  },
]


/**
 * 表格列配置
 *
 * @param {*} {timeMap = {}, serverMap = {}}
 * @return {*}
 */
export const getColumns = ({timeMap = {}, serverMap = {}, pagination = {}}) => {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '策略名称',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '服务器对象',
      dataIndex: 'server_object_id',
      key: 'server_object_id',
      customRender: ({ record }) => {
        return serverMap[record.server_object_id] ?? record.server_object_id
      }
    },
    {
      title: '地址对象',
      dataIndex: 'engine_ip',
      key: 'engine_ip'
    },
    {
      title: '时间对象',
      dataIndex: 'time_object_id',
      key: 'time_object_id',
      customRender: ({ record }) => {
        const DEFAULT_NAME_MAP = {
          '<time_name.worktime>': '工作时间',
          '<time_name.notworktime>': '非工作时间'
        }
        if (DEFAULT_NAME_MAP[timeMap[record.time_object_id]]) return DEFAULT_NAME_MAP[timeMap[record.time_object_id]]
        return timeMap[record.time_object_id] ?? record.time_object_id
      }
    },
    {
      title: '事件级别',
      dataIndex: 'event_level',
      key: 'event_level',
      customRender: ({ record }) => {
        return EVENT_LEVEL_MAP[record.event_level] ?? '-'
      }
    },
    {
      title: '规则对象',
      dataIndex: 'rule_set',
      key: 'rule_set',
      customRender: ({ record }) => {
        if (record.rule_set?.name) return record.rule_set?.name
        const rules = record.rule_set?.rules?.map(({name}) => name) ?? []
        if (rules.length) return <CellExpand title="规则对象" data={rules} />
        return '-'
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 180
    }
  ]
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 120
      },
      properties: {
        description: {
          type: 'string',
          title: '策略名称',
          'x-validator': [
            { required: true, message: '请输入策略名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入策略名称'
          }
        },
        server_object_id: {
          type: 'string',
          title: '服务器对象',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择服务器对象'
          },
          'x-decorator': 'FormItem',
          'x-validator': [
            { required: true, message: '请选择服务器对象' },
          ],
          'x-reactions': '{{getServerDataSource()}}'
        },
        client_ip: {
          type: 'string',
          title: '源IP地址对象',
          'x-validator': [
            { required: true, message: '请选择源IP地址对象' },
          ],
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择源IP地址对象'
          },
          'x-decorator': 'FormItem',
          'x-reactions': '{{getSipDataSource()}}'
        },
        time_object_id: {
          type: 'string',
          title: '时间对象',
          'x-component': 'Select',
          'x-validator': [
            { required: true, message: '请选择时间对象' },
          ],
          'x-component-props': {
            placeholder: '请选择时间对象'
          },
          'x-decorator': 'FormItem',
          'x-reactions': '{{getTimeDataSource()}}'
        },
        event_level: {
          type: 'number',
          title: '事件级别',
          'x-validator': [
            { required: true, message: '请选择事件级别' },
          ],
          default: EVENT_LEVEL_ENUM.HIGH,
          'x-component': 'Radio.Group',
          'x-component-props': {
            placeholder: '请选择事件级别'
          },
          'x-decorator': 'FormItem',
          enum: [
            { label: EVENT_LEVEL_MAP[EVENT_LEVEL_ENUM.LOW], value: EVENT_LEVEL_ENUM.LOW },
            { label: EVENT_LEVEL_MAP[EVENT_LEVEL_ENUM.LESS], value: EVENT_LEVEL_ENUM.LESS },
            { label: EVENT_LEVEL_MAP[EVENT_LEVEL_ENUM.MIDDLE], value: EVENT_LEVEL_ENUM.MIDDLE },
            { label: EVENT_LEVEL_MAP[EVENT_LEVEL_ENUM.HIGH], value: EVENT_LEVEL_ENUM.HIGH },
          ]
        },
        rules: {
          type: 'array',
          title: '规则对象',
          'x-component': 'TreeSelect',
          'x-component-props': {
            multiple: true,
            treeCheckable: true,
            placeholder: '请选择规则对象',
            fieldNames: {label: 'name', key: 'id', value: 'id', children: 'children'},
            'tree-default-expand-all': true
          },
          'x-validator': [
            { required: true, message: '请选择规则对象' },
            (value, rule, ctx) => {
              if (!value.length) return '';
              const dataSource = ctx.field.dataSource ?? [];
              const dataSourceMap = transformDataSourceMap(dataSource);
              const dataSourceNames = value.map((id) => dataSourceMap[id]);
              if([...new Set([...dataSourceNames])].length !== dataSourceNames.length) return '规则对象不能重复选择';
              return ''
            }
          ],
          'x-decorator': 'FormItem',
          'x-reactions': '{{getRuleTreeDataSource()}}'
        },
        // rule_set: {
        //   type: 'array',
        //   required: true,
        //   title: '规则对象',
        //   default: [],
        //   'x-decorator': 'FormItem',
        //   'x-component': 'ArrayTable',
        //   'x-component-props': {
        //     rowKey: 'index',
        //     pagination: { pageSize: 10 },
        //     scroll: { x: '100%' },
        //   },
        //   items: {
        //     type: 'object',
        //     properties: {
        //       column1: {
        //         type: 'void',
        //         'x-component': 'ArrayTable.Column',
        //         'x-component-props': { title: '规则名称', key: 'name', dataIndex: 'name' },
        //         ellipsis: true,
        //         properties: {
        //           name: {
        //             type: 'string',
        //             required: true,
        //             'x-decorator': 'FormItem',
        //             'x-component': 'Select',
        //             'x-component-props': {
        //               placeholder: '请选择规则名称',
        //             },
        //             'x-reactions': useAsyncDataSource(async () => {
        //               return []
        //             })
        //           }
        //         }
        //       },
        //       column2: {
        //         type: 'void',
        //         'x-component': 'ArrayTable.Column',
        //         'x-component-props': { title: '规则分类名称', width: 240, key: 'classifyName', dataIndex: 'classifyName' },
        //         ellipsis: true,
        //         properties: {
        //           classifyName: {
        //             type: 'string',
        //             required: true,
        //             'x-decorator': 'FormItem',
        //             'x-component': 'Select',
        //             'x-component-props': {
        //               placeholder: '请选择规则分类',
        //             },
        //             'x-reactions': useAsyncDataSource(async () => {
        //               return []
        //             })
        //           }
        //         }
        //       },
        //       column3: {
        //         type: 'void',
        //         'x-component': 'ArrayTable.Column',
        //         'x-component-props': {
        //           title: '操作',
        //           dataIndex: 'operations',
        //           ellipsis: true,
        //           width: 70,
        //           fixed: 'right'
        //         },
        //         properties: {
        //           item: {
        //             type: 'void',
        //             'x-component': 'FormItem',
        //             properties: {
        //               remove: {
        //                 type: 'void',
        //                 'x-component': 'ArrayTable.Remove'
        //               }
        //             }
        //           }
        //         }
        //       }
        //     }
        //   },
        //   properties: {
        //     add: {
        //       type: 'void',
        //       'x-component': 'ArrayTable.Addition',
        //       title: '添加'
        //     }
        //   }
        // },
      }
    },
  }
}
