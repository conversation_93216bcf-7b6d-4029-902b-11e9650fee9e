// 敏感信息类型选项
const SENSITIVE_MSG_OPTIONS = [
  { label: '姓名', value: '姓名' },
  { label: '银行卡号', value: '银行卡号' },
  { label: '身份证', value: '身份证' },
  { label: '住址', value: '住址' },
  { label: '户口本', value: '户口本' },
  { label: '军官证', value: '军官证' },
  { label: '护照', value: '护照' },
  { label: '港澳通行证', value: '港澳通行证' }
];

// 匹配方式选项
const MATCH_WAY_OPTIONS = [
  { label: '表匹配', value: '表匹配' },
  { label: '前缀匹配', value: '前缀匹配' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: false,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              size: 'small'
            }
          },
          serverName: {
            type: 'string',
            title: '服务器名称',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入服务器名称',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入服务器名称'
              }
            ]
          },
          databaseName: {
            type: 'string',
            title: '数据库名',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          serviceName: {
            type: 'string',
            title: '服务名',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          schemaName: {
            type: 'string',
            title: '模式名',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          description: {
            type: 'string',
            title: '描述信息',
            'x-component': 'Input.TextArea',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
              rows: 3
            }
          },
          tableName: {
            type: 'string',
            title: '表名/Type',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          sensitiveMsg: {
            type: 'string',
            title: '敏感信息',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '姓名',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择敏感信息'
              }
            ],
            enum: SENSITIVE_MSG_OPTIONS
          },
          matchWay: {
            type: 'string',
            title: '匹配方式',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '表匹配',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择匹配方式'
              }
            ],
            enum: MATCH_WAY_OPTIONS
          }
        }
      }
    }
  };
}
