// 敏感数据配置

// 敏感信息类型选项
export const SENSITIVE_MSG_OPTIONS = [
  { label: '姓名', value: '姓名' },
  { label: '银行卡号', value: '银行卡号' },
  { label: '身份证', value: '身份证' },
  { label: '住址', value: '住址' },
  { label: '户口本', value: '户口本' },
  { label: '军官证', value: '军官证' },
  { label: '护照', value: '护照' },
  { label: '港澳通行证', value: '港澳通行证' }
];

// 匹配方式选项
export const MATCH_WAY_OPTIONS = [
  { label: '表匹配', value: '表匹配' },
  { label: '前缀匹配', value: '前缀匹配' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '服务器名称',
    name: 'serverName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入服务器名称',
    },
  },
  {
    title: '数据库名称',
    name: 'databaseName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入数据库名称',
    },
  },
  {
    title: '服务名称',
    name: 'serviceName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入服务名称',
    },
  },
  {
    title: '表名',
    name: 'tableName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入表名',
    },
  },
  {
    title: '敏感信息',
    name: 'sensitiveMsg',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择敏感信息',
    },
    enum: [
      { label: '全部', value: null },
      ...SENSITIVE_MSG_OPTIONS
    ],
  },
  {
    title: '匹配方式',
    name: 'matchWay',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择匹配方式',
    },
    enum: [
      { label: '全部', value: null },
      ...MATCH_WAY_OPTIONS
    ],
  },
  {
    title: '启用状态',
    name: 'enable',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择启用状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
        {
      title: '敏感信息',
      dataIndex: 'sensitiveMsg',
      key: 'sensitiveMsg',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '服务器名称',
      dataIndex: 'serverName',
      key: 'serverName',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '数据库名称',
      dataIndex: 'databaseName',
      key: 'databaseName',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '服务名称',
      dataIndex: 'serviceName',
      key: 'serviceName',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '模式名称',
      dataIndex: 'schemaName',
      key: 'schemaName',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '表名',
      dataIndex: 'tableName',
      key: 'tableName',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '匹配方式',
      dataIndex: 'matchWay',
      key: 'matchWay',
      width: 100,
      align: "center",
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
