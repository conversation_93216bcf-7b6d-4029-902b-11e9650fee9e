// 数据库协议选项
const DB_PROTOCOL_OPTIONS = [
  { label: 'MS-SQL', value: 'MS-SQL' },
  { label: 'Oracle', value: 'Oracle' },
  { label: 'DB2', value: 'DB2' },
  { label: 'Informix', value: 'Informix' },
  { label: 'DB2-DAS', value: 'DB2-DAS' },
  { label: 'MySQL', value: 'MySQL' },
  { label: 'PostgreSQL', value: 'PostgreSQL' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: true,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          },
          server: {
            type: 'string',
            title: '服务器',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入服务器',
            }
          },
          dbProtocol: {
            type: 'string',
            default: 'MS-SQL',
            title: '数据库协议',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择数据库协议',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择数据库协议'
              }
            ],
            enum: DB_PROTOCOL_OPTIONS
          },
          name: {
            type: 'string',
            title: '名称',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入存储过程名称',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入存储过程名称'
              }
            ]
          },
          description: {
            type: 'string',
            title: '描述',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入描述信息',
            }
          },
          content: {
            type: 'string',
            title: '内容',
            'x-component': 'Input.TextArea',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入存储过程内容',
              rows: 8
            },
            'x-validator': [
              {
                required: true,
                message: '请输入存储过程内容'
              }
            ]
          }
        }
      }
    }
  };
}
