// 存储过程配置

// 数据库协议选项
export const DB_PROTOCOL_OPTIONS = [
  { label: 'MS-SQL', value: 'MS-SQL' },
  { label: 'Oracle', value: 'Oracle' },
  { label: 'DB2', value: 'DB2' },
  { label: 'Informix', value: 'Informix' },
  { label: 'DB2-DAS', value: 'DB2-DAS' },
  { label: 'MySQL', value: 'MySQL' },
  { label: 'PostgreSQL', value: 'PostgreSQL' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '存储过程名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入存储过程名称',
    },
  },
  {
    title: '数据库协议',
    name: 'dbProtocol',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择数据库协议',
    },
    enum: [
      { label: '全部', value: null },
      ...DB_PROTOCOL_OPTIONS
    ],
  },
  {
    title: '启用状态',
    name: 'enable',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择启用状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '服务器',
      dataIndex: 'server',
      key: 'server',
      width: 150,
      align: "center",
      ellipsis: true,
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '数据库协议',
      dataIndex: 'dbProtocol',
      key: 'dbProtocol',
      width: 120,
      align: "center",
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '内容',
      dataIndex: 'content',
      key: 'content',
      width: 200,
      align: "center",
      ellipsis: true,
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      key: 'description',
      width: 150,
      align: "center",
      ellipsis: true,
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
