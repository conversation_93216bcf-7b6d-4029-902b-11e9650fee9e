// 数据发现配置

// 数据库类型选项
export const DB_TYPE_OPTIONS = [
  { label: 'MySQL', value: 'MySQL' },
  { label: 'Oracle', value: 'Oracle' },
  { label: 'PostgreSQL', value: 'PostgreSQL' },
  { label: 'SQL Server', value: 'SQL Server' },
  { label: 'MS-SQL', value: 'MS-SQL' }
];

// 编码选项
export const ENCODED_OPTIONS = [
  { label: '自动', value: '自动' },
  { label: 'UTF-8', value: 'UTF-8' },
  { label: 'GB2312', value: 'GB2312' },
  { label: 'GBK', value: 'GBK' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
  {
    title: '类型',
    name: 'dbType',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择类型',
    },
    enum: [
      { label: '全部', value: null },
      ...DB_TYPE_OPTIONS
    ],
  },
  {
    title: 'IP',
    name: 'ip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入IP',
    },
  },
  {
    title: '数据库名/服务名',
    name: 'databaseName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入数据库名/服务名',
    },
  },
  {
    title: '模式匹配',
    name: 'modelMatch',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择模式匹配',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  },
  {
    title: '启用状态',
    name: 'enable',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择启用状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: "center",
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '类型',
      dataIndex: 'dbType',
      key: 'dbType',
      width: 100,
      align: "center",
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip',
      width: 140,
      align: "center",
      ellipsis: true,
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
      width: 80,
      align: "center",
    },
    {
      title: '数据库名/服务名',
      dataIndex: 'databaseName',
      key: 'databaseName',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '模式匹配',
      dataIndex: 'modelMatch',
      key: 'modelMatch',
      width: 100,
      align: "center",
      customRender({ text }) {
        return text ? '启用' : '禁用';
      }
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
