// 数据库类型选项
const DB_TYPE_OPTIONS = [
  { label: 'MySQL', value: 'MySQL' },
  { label: 'Oracle', value: 'Oracle' },
  { label: 'PostgreSQL', value: 'PostgreSQL' },
  { label: 'SQL Server', value: 'SQL Server' },
  { label: 'MS-SQL', value: 'MS-SQL' }
];

// 编码选项
const ENCODED_OPTIONS = [
  { label: '自动', value: '自动' },
  { label: 'UTF-8', value: 'UTF-8' },
  { label: 'GB2312', value: 'GB2312' },
  { label: 'GBK', value: 'GBK' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: true,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          },
          name: {
            type: 'string',
            title: '名称',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          dbType: {
            type: 'string',
            title: '数据库类型',
            default: 'MS-SQL',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: 'MS-SQL',
            },
            enum: DB_TYPE_OPTIONS
          },
          username: {
            type: 'string',
            title: '用户名',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入用户名'
              }
            ]
          },
          password: {
            type: 'string',
            title: '密码',
            'x-component': 'Password',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          encoded: {
            type: 'string',
            default: '自动',
            title: '编码',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '自动',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择编码'
              }
            ],
            enum: ENCODED_OPTIONS
          },
          ip: {
            type: 'string',
            title: 'IP',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入IP地址'
              },
              (value) => {
                if (!value) return '';
                const regex = /^((?:(?:25[0-5]|2[0-4]\d|[01]?\d\d?)\.){3}(?:25[0-5]|2[0-4]\d|[01]?\d\d?))$/;
                if (!regex.test(value)) return "请输入正确的IP地址";
              }
            ]
          },
          port: {
            type: 'string',
            title: '端口',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入端口'
              },
              (value) => {
                if (!value) return '';
                const port = parseInt(value);
                if (isNaN(port) || port < 1 || port > 65535) return "请输入正确的端口号(1-65535)";
              }
            ]
          },
          databaseName: {
            type: 'string',
            title: '数据库名/服务名',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入数据库名/服务名'
              }
            ]
          },
          modelMatch: {
            type: 'boolean',
            default: false,
            title: '模式匹配',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          }
        }
      }
    }
  };
}
