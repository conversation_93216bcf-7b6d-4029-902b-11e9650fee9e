// 敏感信息类型选项
const SENSITIVE_TYPE_OPTIONS = [
  { label: '个人身份信息', value: '个人身份信息' },
  { label: '个人财产信息', value: '个人财产信息' },
  { label: '个人通信信息', value: '个人通信信息' },
  { label: '个人位置信息', value: '个人位置信息' },
  { label: '个人生物识别信息', value: '个人生物识别信息' },
  { label: '其他', value: '其他' }
];

// 识别方式选项
const RECOGNITION_TYPE_OPTIONS = [
  { label: '探测器', value: '探测器' },
  { label: '正则表达式', value: '正则表达式' }
];

// 探测器选项
const DETECTOR_OPTIONS = [
  { label: '姓名探测器', value: '姓名探测器' },
  { label: '身份证探测器', value: '身份证探测器' },
  { label: '银行卡探测器', value: '银行卡探测器' },
  { label: '手机号探测器', value: '手机号探测器' },
  { label: '邮箱探测器', value: '邮箱探测器' },
  { label: 'IP地址探测器', value: 'IP地址探测器' }
];

// 级别选项
const LEVEL_OPTIONS = [
  { label: '1级', value: '1级' },
  { label: '2级', value: '2级' },
  { label: '3级', value: '3级' },
  { label: '4级', value: '4级' }
];

// 类别选项
const CATEGORY_OPTIONS = [
  { label: '财产账号', value: '财产账号' },
  { label: '身份', value: '身份' },
  { label: '地址', value: '地址' },
  { label: '密码', value: '密码' },
  { label: '通讯', value: '通讯' },
  { label: '行为轨迹', value: '行为轨迹' }
];

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: true,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          },
          name: {
            type: 'string',
            title: '敏感信息名称',
            required: true,
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入敏感信息名称'
              }
            ]
          },
          type: {
            type: 'string',
            title: '敏感信息类型',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            }
          },
          level: {
            type: 'string',
            default: '4级',
            title: '级别',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '4级',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择级别'
              }
            ],
            enum: LEVEL_OPTIONS
          },
          category: {
            type: 'string',
            default: '财产账号',
            title: '类别',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '财产账号',
            },
            'x-validator': [
              {
                required: true,
                message: '请选择类别'
              }
            ],
            enum: CATEGORY_OPTIONS
          },
          identifyWay: {
            type: 'string',
            default: '探测器',
            title: '识别方式',
            required: true,
            'x-component': 'Radio.Group',
            'x-decorator': 'FormItem',
            'x-validator': [
              {
                required: true,
                message: '请选择识别方式'
              }
            ],
            enum: RECOGNITION_TYPE_OPTIONS
          },
          identifyMsg: {
            type: 'string',
            title: '探测器',
            required: true,
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '姓名探测器',
            },
            'x-reactions': [
              {
                dependencies: ['identifyWay'],
                fulfill: {
                  state: {
                    visible: '{{$deps[0] === "探测器"}}',
                    required: '{{$deps[0] === "探测器"}}'
                  }
                }
              }
            ],
            'x-validator': [
              {
                required: true,
                message: '请选择探测器'
              }
            ],
            enum: DETECTOR_OPTIONS
          },
          regexPattern: {
            type: 'string',
            title: '正则表达式',
            'x-component': 'Input.TextArea',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入正则表达式',
              rows: 3
            },
            'x-reactions': [
              {
                dependencies: ['identifyWay'],
                fulfill: {
                  state: {
                    visible: '{{$deps[0] === "正则表达式"}}',
                    required: '{{$deps[0] === "正则表达式"}}'
                  }
                }
              }
            ],
            'x-validator': [
              {
                required: true,
                message: '请输入正则表达式'
              },
              (value) => {
                if (!value) return '';
                try {
                  new RegExp(value);
                } catch (e) {
                  return '请输入有效的正则表达式';
                }
              }
            ]
          }
        }
      }
    }
  };
}
