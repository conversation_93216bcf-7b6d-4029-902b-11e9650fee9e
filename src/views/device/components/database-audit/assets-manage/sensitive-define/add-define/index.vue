<template>
  <a-modal
    v-model:visible="visible"
    :title="modalTitle"
    width="650px"
    :maskClosable="false"
    :keyboard="false"
  >
    <ProForm :form="form" v-if="!loading" :schema="schema" />
    <template #footer>
      <div class="operator-group" :style="{ justifyContent: 'flex-end' }">
        <a-button key="back" @click="handleCancel">{{ isView ? '关闭' : '取消' }}</a-button>
        <a-button
          :loading="btnLoading"
          v-show="!isView"
          key="submit"
          type="primary"
          @click="handleOk"
        >
          确定
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { createForm } from "@formily/vue";
import { ref, defineExpose, defineEmits, computed, shallowRef } from 'vue';
import { message } from 'ant-design-vue';
import ProForm from '@/components/pro-form/index.vue';
import { getSchema } from './config.jsx';
import { insertAssetSensitiveDefine, modifyAssetSensitiveDefine } from '@/request/api-device-db/assets';

const emits = defineEmits(['refresh']);
const visible = ref(false);
const btnLoading = ref(false);
const loading = ref(false);
const modalTitle = ref('新增敏感数据定义');
const mode = ref('add');

const form = shallowRef(null);
const schema = shallowRef(null);

const isView = computed(() => mode.value === 'view');

async function handleOk() {
  try {
    await form.value.validate();
    btnLoading.value = true;

    const formValues = { ...form.value.values };

    // 处理识别信息字段
    const payload = {
      name: formValues.name,
      type: formValues.type,
      level: formValues.level,
      category: formValues.category,
      identifyWay: formValues.identifyWay,
      identifyMsg: formValues.identifyWay === '探测器' ? formValues.identifyMsg : formValues.regexPattern,
      enable: formValues.enable,
    };

    switch (mode.value) {
      case 'add':
        await insertAssetSensitiveDefine(payload);
        message.success('新增成功');
        break;
      case 'edit':
        payload.id = formValues.id;
        await modifyAssetSensitiveDefine(payload);
        message.success('编辑成功');
        break;
    }

    visible.value = false;
    emits('refresh');
  } catch (error) {
    console.error('操作失败:', error);
  } finally {
    btnLoading.value = false;
  }
}

function handleCancel() {
  visible.value = false;
}

function initForm(values) {
  schema.value = getSchema();
  form.value = createForm({
    values: values,
  });
}

function open({ title, data = {}, mode: modeType }) {
  // 处理数据回显
  const formData = { ...data };
  if (data.identifyWay && data.identifyMsg) {
    if (data.identifyWay === '探测器') {
      formData.detectorType = data.identifyMsg;
    } else if (data.identifyWay === '正则表达式') {
      formData.identifyMsg = data.identifyMsg;
    }
  }

  initForm(formData);
  visible.value = true;
  mode.value = modeType;
  modalTitle.value = title;

  if (mode.value === "view") {
    setTimeout(() => {
      form.value.readPretty = true;
    }, 50);
  }
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.operator-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
