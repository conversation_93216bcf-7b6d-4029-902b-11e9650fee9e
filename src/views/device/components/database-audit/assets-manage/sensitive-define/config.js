// 敏感数据定义配置

// 敏感信息类型选项
export const SENSITIVE_TYPE_OPTIONS = [
  { label: '个人身份信息', value: '个人身份信息' },
  { label: '个人财产信息', value: '个人财产信息' },
  { label: '个人通信信息', value: '个人通信信息' },
  { label: '个人位置信息', value: '个人位置信息' },
  { label: '个人生物识别信息', value: '个人生物识别信息' },
  { label: '其他', value: '其他' }
];

// 识别方式选项
export const RECOGNITION_TYPE_OPTIONS = [
  { label: '探测器', value: '探测器' },
  { label: '正则表达式', value: '正则表达式' }
];

// 探测器选项
export const DETECTOR_OPTIONS = [
  { label: '姓名探测器', value: '姓名探测器' },
  { label: '身份证探测器', value: '身份证探测器' },
  { label: '银行卡探测器', value: '银行卡探测器' },
  { label: '手机号探测器', value: '手机号探测器' },
  { label: '邮箱探测器', value: '邮箱探测器' },
  { label: 'IP地址探测器', value: 'IP地址探测器' }
];

// 级别选项
export const LEVEL_OPTIONS = [
  { label: '1级', value: '1级' },
  { label: '2级', value: '2级' },
  { label: '3级', value: '3级' },
  { label: '4级', value: '4级' }
];

// 类别选项
export const CATEGORY_OPTIONS = [
  { label: '财产账号', value: '财产账号' },
  { label: '身份', value: '身份' },
  { label: '地址', value: '地址' },
  { label: '密码', value: '密码' },
  { label: '通讯', value: '通讯' },
  { label: '行为轨迹', value: '行为轨迹' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '敏感信息名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入敏感信息名称',
    },
  },
  {
    title: '敏感信息类型',
    name: 'type',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择敏感信息类型',
    },
    enum: [
      { label: '全部', value: null },
      ...SENSITIVE_TYPE_OPTIONS
    ],
  },
  {
    title: '级别',
    name: 'level',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择级别',
    },
    enum: [
      { label: '全部', value: null },
      ...LEVEL_OPTIONS
    ],
  },
  {
    title: '类别',
    name: 'category',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择类别',
    },
    enum: [
      { label: '全部', value: null },
      ...CATEGORY_OPTIONS
    ],
  },
  {
    title: '识别方式',
    name: 'identifyWay',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择识别方式',
    },
    enum: [
      { label: '全部', value: null },
      ...RECOGNITION_TYPE_OPTIONS
    ],
  },
  {
    title: '启用状态',
    name: 'enable',
    type: 'string',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择启用状态',
    },
    enum: [
      { label: '全部', value: null },
      { label: '启用', value: true },
      { label: '禁用', value: false }
    ],
  }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: "center",
    },
    {
      title: '敏感信息名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '敏感信息类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      align: "center",
    },
    {
      title: '类别',
      dataIndex: 'category',
      key: 'category',
      width: 100,
      align: "center",
    },
    {
      title: '识别方式',
      dataIndex: 'identifyWay',
      key: 'identifyWay',
      width: 120,
      align: "center",
    },
    {
      title: '识别信息',
      dataIndex: 'identifyMsg',
      key: 'identifyMsg',
      width: 200,
      align: "center",
      ellipsis: true,
      customRender({ text }) {
        return text || '-';
      }
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
