<!-- 审计对象管理Tab -->

<template>
  <a-tabs type="card" v-model:activeKey="activeKey" size="small">
    <a-tab-pane v-for="tab in TABS" :key="tab.componentName" :tab="tab.label">
    </a-tab-pane>
  </a-tabs>
  <component :is="activeKey" />
</template>
<script lang="js">
import { defineComponent, provide, toRefs, computed, reactive, watch } from 'vue'
import { TABS } from './config';
import SensitiveData from './sensitive-data/index.vue';
import SensitiveDefine from './sensitive-define/index.vue';
import DataDiscovery from './data-discovery/index.vue';
import Procedure from './procedure/index.vue';


export default defineComponent({
  name: 'DbAudit',
  components: {
    SensitiveData,
    SensitiveDefine,
    DataDiscovery,
    Procedure
  },
  setup (props, ctx) {
    const state = reactive({
      activeKey: TABS[0].componentName
    })

    return {
      TABS,
      ...toRefs(state),
    }
  }
})
</script>
