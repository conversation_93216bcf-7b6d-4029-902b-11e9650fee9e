<template>
  <div>
    <DynamicSearch
      ref="dynamicSearchRef"
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    />
    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
    </CommonTable>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import DynamicSearch from '@/components/dynamic-search/index.vue';
import CommonTable from '@/components/common-table/index.vue';
import { SEARCH_CONFIG, getTableColumns } from './config';
import {
  getAuditLogPageData,
  deleteAuditLog,
  exportAuditLog
} from '@/request/api-device-db/audit-log';

const searchParams = ref({});
const tableRef = ref(null);
const dynamicSearchRef = ref(null);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

// 计算表格列配置
const tableColumns = computed(() => getTableColumns());

function handleSearch (params) {
  searchParams.value = params;
  handleRefresh();
}

function handleRefresh () {
  tableRef.value?.refresh();
}

async function fetchData (params) {
  try {
    const { data } = await getAuditLogPageData({
      ...params,
      ...searchParams.value,
      serviceName: 'DB_AUDIT', // 默认服务名称
    });
    return { items: data.data, total: data.total };
  } catch (error) {
    console.error('获取数据失败:', error);
    return {
      items: [],
      total: 0
    };
  }
}

// 删除
async function handleDelete (record) {
  Modal.confirm({
    title: '删除审计日志',
    content: '确定删除该审计日志吗？',
    onOk: async () => {
      try {
        await deleteAuditLog({ id: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (err) {
        message.error('删除失败');
      }
    },
  });
}

// 导出
async function handleExport () {
  try {
    await exportAuditLog({});
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
}
</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  :deep(.ant-btn) {
    padding: 4px 6px;
  }
}
</style>