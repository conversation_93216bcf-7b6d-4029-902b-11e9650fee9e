// 统计任务配置

// 任务类型选项
export const TASK_TYPE_OPTIONS = [
  { label: '运行一次', value: '运行一次' },
  { label: '日报表', value: '日报表' },
  { label: '周报表', value: '周报表' },
  { label: '月报表', value: '月报表' }
];

// 级别选项
export const LEVEL_OPTIONS = [
  { label: '高', value: '高' },
  { label: '中', value: '中' },
  { label: '低', value: '低' }
];

// 编码选项
export const ENCODING_OPTIONS = [
  { label: 'UTF-8', value: 'UTF-8' },
  { label: 'UTF-16', value: 'UTF-16' },
  { label: 'GB2312', value: 'GB2312' }
];

// 文件类型选项
export const FILE_TYPE_OPTIONS = [
  { label: 'HTML', value: 'HTML' },
  { label: 'PDF', value: 'PDF' },
  { label: 'WORD', value: 'WORD' },
  { label: 'EXCEL', value: 'EXCEL' },
  { label: 'WPS', value: 'WPS' }
];

// 表格列配置
export function getTableColumns(handleStatusChange) {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: "center",
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '服务器',
      dataIndex: 'server',
      key: 'server',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '级别',
      dataIndex: 'level',
      key: 'level',
      width: 80,
      align: "center",
    },
    {
      title: '编码',
      dataIndex: 'encoded',
      key: 'encoded',
      width: 100,
      align: "center",
    },
    {
      title: '调度周期',
      dataIndex: 'taskType',
      key: 'taskType',
      width: 120,
      align: "center",
    },
    {
      title: '格式',
      dataIndex: 'fileType',
      key: 'fileType',
      width: 200,
      align: "center",
      customRender: ({ text, record }) => {
        // 优先使用text参数，如果没有则使用record中的数据
        const fileType = text || record.fileType;

        // 如果是数组格式，转换为字符串显示
        if (Array.isArray(fileType)) {
          return fileType.join(', ');
        }

        // 如果是字符串，直接返回
        if (typeof fileType === 'string') {
          return fileType;
        }

        // 如果都没有，返回默认值
        return '-';
      }
    },
    {
      title: '发送邮件',
      dataIndex: 'sendEmail',
      key: 'sendEmail',
      width: 100,
      align: "center",
      customRender: ({ text, record }) => {
        const sendEmail = text !== undefined ? text : record.sendEmail;
        return sendEmail ? '是' : '否';
      }
    },
    // {
    //   title: '数据时间范围',
    //   dataIndex: 'timeRange',
    //   key: 'timeRange',
    //   width: 300,
    //   align: "center",
    //   customRender: ({ record }) => {
    //     if (record.time1 && record.time2) {
    //       try {
    //         const time1 = new Date(record.time1).toLocaleString('zh-CN');
    //         const time2 = new Date(record.time2).toLocaleString('zh-CN');
    //         return `${time1} ~ ${time2}`;
    //       } catch (error) {
    //         console.warn('时间格式转换失败:', error);
    //         return `${record.time1} ~ ${record.time2}`;
    //       }
    //     }
    //     return '-';
    //   }
    // },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
