<template>
  <a-modal
    v-model:visible="visible"
    :title="modalTitle"
    width="800px"
    :maskClosable="false"
    :keyboard="false"
  >
    <div v-if="loading" style="text-align: center; padding: 20px;">
      <a-spin size="large" />
    </div>
    <ProForm :form="form" v-else-if="form && schema" :schema="schema" :key="formKey" />
    <div v-else style="text-align: center; padding: 20px; color: #999;">
      表单加载中...
    </div>

    <template #footer>
      <div class="operator-group" :style="{ justifyContent: 'flex-end' }">
        <a-button key="back" @click="handleCancel">{{ isView ? '关闭' : '取消' }}</a-button>
        <a-button
          :loading="btnLoading"
          v-show="!isView"
          key="submit"
          type="primary"
          @click="handleOk"
        >
          保存
        </a-button>
      </div>
    </template>
  </a-modal>
</template>

<script setup>
import { createForm } from "@formily/vue";
import { ref, defineExpose, defineEmits, computed, shallowRef } from 'vue';
import { message } from 'ant-design-vue';
import dayjs from 'dayjs';
import ProForm from '@/components/pro-form/index.vue';
import { getSchema } from './config.jsx';
import { insertStatisticsTask, modifyStatisticsTask } from '@/request/api-device-db/report-design';

const emits = defineEmits(['refresh']);
const visible = ref(false);
const btnLoading = ref(false);
const loading = ref(false);
const modalTitle = ref('新建任务');
const mode = ref('add');

const form = shallowRef(null);
const schema = shallowRef(null);
const formKey = ref(0);

const isView = computed(() => mode.value === 'view');

async function handleOk() {
  try {
    await form.value.validate();
    btnLoading.value = true;

    const payload = { ...form.value.values };

    // 处理时间字段
    if (payload.time1 && typeof payload.time1 === 'object' && payload.time1.format) {
      payload.time1 = payload.time1.format('YYYY-MM-DD HH:mm:ss');
    }

    if (payload.time2 && typeof payload.time2 === 'object' && payload.time2.format) {
      payload.time2 = payload.time2.format('YYYY-MM-DD HH:mm:ss');
    }

    switch (mode.value) {
      case 'add':
        await insertStatisticsTask(payload);
        message.success('新增成功');
        break;
      case 'edit':
        await modifyStatisticsTask(payload);
        message.success('编辑成功');
        break;
    }

    visible.value = false;
    emits('refresh');
  } catch (error) {
    console.error('操作失败:', error);
    message.error('操作失败');
  } finally {
    btnLoading.value = false;
  }
}

function handleCancel() {
  visible.value = false;
  loading.value = false;
  // 重置表单状态
  if (form.value) {
    form.value.readPretty = false;
  }
}

function initForm(values) {
  try {
    // 确保schema正确加载
    if (!schema.value) {
      schema.value = getSchema();
    }

    // 处理时间字段数据回填
    const formValues = { ...values };

    // 处理time1字段
    if (formValues.time1 && typeof formValues.time1 === 'string') {
      try {
        formValues.time1 = dayjs(formValues.time1);
      } catch (error) {
        console.warn('time1格式转换失败:', error);
        formValues.time1 = null;
      }
    }

    // 处理time2字段
    if (formValues.time2 && typeof formValues.time2 === 'string') {
      try {
        formValues.time2 = dayjs(formValues.time2);
      } catch (error) {
        console.warn('time2格式转换失败:', error);
        formValues.time2 = null;
      }
    }

    // 确保表单创建成功
    if (form.value) {
      form.value.setValues(formValues);
    } else {
      form.value = createForm({
        values: formValues,
      });
    }
  } catch (error) {
    console.error('表单初始化失败:', error);
    // 创建一个空表单作为降级处理
    try {
      form.value = createForm({
        values: {},
      });
      schema.value = getSchema();
    } catch (fallbackError) {
      console.error('表单降级处理也失败:', fallbackError);
    }
  }
}

function open({ title, data = {}, mode: modeType }) {
  try {
    loading.value = true;
    mode.value = modeType;
    modalTitle.value = title;
    visible.value = true;

    // 重置表单key以强制重新渲染
    formKey.value += 1;

    // 异步初始化表单
    setTimeout(() => {
      try {
        // 先初始化表单
        initForm({ ...data });

        // 如果是查看模式，设置只读状态
        if (mode.value === "view") {
          setTimeout(() => {
            if (form.value) {
              form.value.readPretty = true;
            }
          }, 100);
        }

        loading.value = false;
      } catch (error) {
        console.error('表单初始化失败:', error);
        loading.value = false;
        message.error('表单初始化失败');
      }
    }, 100);

  } catch (error) {
    console.error('打开表单失败:', error);
    loading.value = false;
    message.error('打开表单失败');
  }
}

defineExpose({
  open,
});
</script>

<style lang="less" scoped>
.operator-group {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
</style>
