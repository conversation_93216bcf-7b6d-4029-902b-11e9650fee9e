// 导入选项配置
import { FILE_TYPE_OPTIONS } from "../config.js";

// 任务周期选项 - 根据原型图
const TASK_TYPE_OPTIONS = [
  { label: "运行一次", value: "运行一次" },
  { label: "日报表", value: "日报表" },
  { label: "周报表", value: "周报表" },
  { label: "月报表", value: "月报表" },
];

// 级别选项
const LEVEL_OPTIONS = [
  { label: "高", value: "高" },
  { label: "中", value: "中" },
  { label: "低", value: "低" },
];

// 编码选项
const ENCODING_OPTIONS = [
  { label: "UTF-8", value: "UTF-8" },
  { label: "UTF-16", value: "UTF-16" },
  { label: "GB2312", value: "GB2312" },
];

export function getSchema() {
  return {
    type: "object",
    properties: {
      layout: {
        type: "void",
        "x-component": "FormLayout",
        "x-component-props": {
          labelWidth: 120,
        },
        properties: {
          enable: {
            type: "boolean",
            default: true,
            title: "启用",
            "x-component": "Switch",
            "x-decorator": "FormItem",
            "x-component-props": {
              checkedChildren: "启用",
              unCheckedChildren: "禁用",
            },
          },
          name: {
            type: "string",
            title: "名称",
            "x-component": "Input",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
            },
            "x-validator": [
              {
                required: true,
                message: "请输入名称",
              },
            ],
          },
          server: {
            type: "string",
            title: "服务器",
            "x-component": "Input",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
            },
            "x-validator": [
              {
                required: true,
                message: "请输入服务器",
              },
            ],
          },
          level: {
            type: "string",
            title: "级别",
            "x-component": "Select",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请选择级别",
            },
            enum: LEVEL_OPTIONS,
          },
          encoded: {
            type: "string",
            title: "编码",
            "x-component": "Select",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请选择编码",
            },
            enum: ENCODING_OPTIONS,
          },
          publicExpression: {
            type: "string",
            title: "公有条件",
            "x-component": "Input",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
            },
          },
          privateExpression: {
            type: "string",
            title: "私有条件",
            "x-component": "Input",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
            },
          },

          taskType: {
            type: "string",
            title: "任务周期",
            "x-component": "Radio.Group",
            "x-decorator": "FormItem",
            "x-component-props": {
              optionType: "button",
              buttonStyle: "solid",
            },
            "x-validator": [
              {
                required: true,
                message: "请选择任务周期",
              },
            ],
            enum: TASK_TYPE_OPTIONS,
          },
          time1: {
            type: "string",
            title: "开始时间",
            "x-component": "DatePicker",
            "x-decorator": "FormItem",
            "x-component-props": {
              showTime: true,
              format: "YYYY-MM-DD HH:mm:ss",
              placeholder: "请选择开始时间",
            },
            "x-validator": [
              {
                required: true,
                message: "请选择开始时间",
              },
            ],
          },
          time2: {
            type: "string",
            title: "结束时间",
            "x-component": "DatePicker",
            "x-decorator": "FormItem",
            "x-component-props": {
              showTime: true,
              format: "YYYY-MM-DD HH:mm:ss",
              placeholder: "请选择结束时间",
            },
            "x-validator": [
              {
                required: true,
                message: "请选择结束时间",
              },
            ],
          },
          fileType: {
            type: "array",
            title: "文件格式",
            "x-component": "Checkbox.Group",
            "x-decorator": "FormItem",
            "x-component-props": {
              options: FILE_TYPE_OPTIONS.map((item) => ({
                label: item.label,
                value: item.value,
              })),
            },
            "x-validator": [
              {
                required: true,
                message: "请选择文件格式",
              },
            ],
          },
          sendEmail: {
            type: "boolean",
            title: "发送邮件",
            "x-component": "Switch",
            "x-decorator": "FormItem",
            "x-component-props": {
              checkedChildren: "是",
              unCheckedChildren: "否",
            },
          },
          emailReceiver: {
            type: "string",
            title: "收件人",
            "x-component": "Input",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
            },
            "x-reactions": {
              dependencies: ["sendEmail"],
              fulfill: {
                state: {
                  visible: "{{$deps[0] === true}}",
                },
              },
            },
          },
          emailTitle: {
            type: "string",
            title: "邮件标题",
            "x-component": "Input",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
            },
            "x-reactions": {
              dependencies: ["sendEmail"],
              fulfill: {
                state: {
                  visible: "{{$deps[0] === true}}",
                  required: "{{$deps[0] === true}}",
                },
              },
            },
          },
          emailContent: {
            type: "string",
            title: "邮件内容",
            "x-component": "Input.TextArea",
            "x-decorator": "FormItem",
            "x-component-props": {
              placeholder: "请输入",
              rows: 4,
            },
            "x-reactions": {
              dependencies: ["sendEmail"],
              fulfill: {
                state: {
                  visible: "{{$deps[0] === true}}",
                  required: "{{$deps[0] === true}}",
                },
              },
            },
          },
        },
      },
    },
  };
}
