// 导入选项配置
import { CATEGORY_OPTIONS, DATASET_OPTIONS, QUOTA_OPTIONS, CHART_TYPE_OPTIONS } from '../config.js';

export function getSchema() {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            default: true,
            title: '启用',
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedChildren: '启用',
              unCheckedChildren: '禁用'
            }
          },
          name: {
            type: 'string',
            title: '名称',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入',
            },
            'x-validator': [
              {
                required: true,
                message: '请输入名称'
              }
            ]
          },
          category: {
            type: 'string',
            title: '分类',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择分类',
              showSearch: true,
              filterOption: (input, option) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }
            },
            'x-validator': [
              {
                required: true,
                message: '请选择分类'
              }
            ],
            enum: CATEGORY_OPTIONS
          },
          dataSet: {
            type: 'string',
            title: '数据集',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择数据集',
              showSearch: true,
              filterOption: (input, option) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }
            },
            'x-validator': [
              {
                required: true,
                message: '请选择数据集'
              }
            ],
            enum: DATASET_OPTIONS
          },
          quota: {
            type: 'string',
            title: '指标',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择指标',
              showSearch: true,
              filterOption: (input, option) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }
            },
            'x-validator': [
              {
                required: true,
                message: '请选择指标'
              }
            ],
            enum: QUOTA_OPTIONS
          },
          chartType: {
            type: 'string',
            title: '图表设计',
            'x-component': 'Select',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择图表设计',
              showSearch: true,
              filterOption: (input, option) => {
                return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
              }
            },
            'x-validator': [
              {
                required: true,
                message: '请选择图表设计'
              }
            ],
            enum: CHART_TYPE_OPTIONS
          },
          filterExpression: {
            type: 'string',
            title: '过滤条件',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入条件，如sip=*************',
            }
          }
        }
      }
    }
  };
}
