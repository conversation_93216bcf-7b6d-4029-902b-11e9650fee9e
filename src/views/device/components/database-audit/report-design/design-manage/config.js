// 设计管理配置

// 分类选项 - 根据截图中的下拉选项
export const CATEGORY_OPTIONS = [
  { label: '服务器分析', value: '服务器分析' },
  { label: '用户访问行为分析', value: '用户访问行为分析' },
  { label: '用户操作行为分析', value: '用户操作行为分析' },
  { label: '数据库账号分析', value: '数据库账号分析' },
  { label: '服务器性能分析', value: '服务器性能分析' },
  { label: '服务器异常分析', value: '服务器异常分析' }
];

// 数据集选项 - 根据截图中的下拉选项
export const DATASET_OPTIONS = [
  { label: '基本信息表', value: '基本信息表' },
  { label: '数据库操作信息表', value: '数据库操作信息表' },
  { label: '事件策略统计表', value: '事件策略统计表' },
  { label: '错误码统计表', value: '错误码统计表' },
  { label: '影响行数统计表', value: '影响行数统计表' },
  { label: '响应时间统计表', value: '响应时间统计表' },
  { label: 'web统计表', value: 'web统计表' },
  { label: '基本信息字典表', value: '基本信息字典表' },
  { label: '超长响应时间统计表', value: '超长响应时间统计表' },
  { label: 'WebMail行为统计表', value: 'WebMail行为统计表' },
  { label: '网盘操作统计表', value: '网盘操作统计表' },
  { label: '互联网行为统计表', value: '互联网行为统计表' },
  { label: '恶意URL行为统计表', value: '恶意URL行为统计表' },
  { label: 'SQL模板统计表', value: 'SQL模板统计表' },
  { label: '敏感数据统计', value: '敏感数据统计' }
];

// 指标选项 - 根据截图中的下拉选项
export const QUOTA_OPTIONS = [
  { label: 'urgent', value: 'urgent' },
  { label: 'high', value: 'high' },
  { label: 'middle', value: 'middle' },
  { label: 'low', value: 'low' },
  { label: 'icount', value: 'icount' }
];

// 图表设计选项 - 根据截图中的下拉选项
export const CHART_TYPE_OPTIONS = [
  { label: '表格', value: '表格' },
  { label: '折线图', value: '折线图' },
  { label: '柱状图', value: '柱状图' }
];

// 表格列配置
export function getTableColumns(pagination = {}, handleStatusChange) {
  return [
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 80,
      align: "center",
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      align: "center",
      ellipsis: true,
    },
    {
      title: '分类',
      dataIndex: 'category',
      key: 'category',
      width: 150,
      align: "center",
      ellipsis: true,
    },
    {
      title: '图表设计',
      dataIndex: 'chartType',
      key: 'chartType',
      width: 120,
      align: "center",
    },
    {
      title: '过滤条件',
      dataIndex: 'filterExpression',
      key: 'filterExpression',
      width: 200,
      align: "center",
      ellipsis: true,
      customRender: ({ text }) => {
        return text || '-';
      }
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      align: "center",
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const enable = !record.enable;
            await handleStatusChange({...record, enable});
            record.enable = enable;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 200,
      fixed: 'right',
      align: "center",
    }
  ];
}
