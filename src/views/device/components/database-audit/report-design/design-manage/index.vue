<template>
  <div>
    <div class="list-operator">
      <a-space>
        <a-button type="primary" @click="handleExport">导出</a-button>
        <a-button type="primary" @click="handleAdd">新增</a-button>
      </a-space>
    </div>

    <CommonTable :scroll="{ y: tableHeight }" :columns="tableColumns" :fetchData="fetchData" ref="tableRef">
      <template #operation="{ record }">
        <div class="list-operator-column">
          <a-button type="link" @click="handleView(record)">详情</a-button>
          <a-button type="link" @click="handleEdit(record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </div>
      </template>
    </CommonTable>

    <AddDesign ref="addDesignRef" @refresh="handleRefresh" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { message, Modal } from 'ant-design-vue';
import CommonTable from '@/components/common-table/index.vue';
import { getTableColumns } from './config';
import AddDesign from './add-design/index.vue';
import {
  getDesignPageData,
  deleteDesign,
  modifyDesign,
  exportDesign
} from '@/request/api-device-db/report-design';
import { downloadFile } from '@/utils/util';

const tableRef = ref(null);
const addDesignRef = ref(null);

const tableHeight = ref(500);
onMounted(() => {
  tableHeight.value = (window.innerHeight || document.documentElement.clientHeight || document.body.clientHeight) - 615;
});

function handleRefresh () {
  tableRef.value?.refresh();
}

async function fetchData (params) {
  try {
    const { data } = await getDesignPageData({
      ...params,
    });
    return { items: data.data, total: data.total };
  } catch (error) {
    console.error('获取数据失败:', error);
    message.error('获取数据失败');
    return {
      items: [],
      total: 0
    };
  }
}

const tableColumns = computed(() => getTableColumns(tableRef.value?.pagination ?? {}, handleStatusChange));

// 状态切换处理
async function handleStatusChange(record) {
  try {
    // 调用修改接口
    await modifyDesign(record);
    message.success(`${record.enable ? '启用' : '禁用'}成功`);
  } catch (error) {
    console.error('状态切换失败:', error);
    message.error('状态切换失败');
    throw error; // 重新抛出错误，让配置文件中的 finally 块处理加载状态
  }
}

// 新增
function handleAdd () {
  addDesignRef.value.open({
    title: '新建任务',
    data: {},
    mode: 'add',
  });
}

// 编辑
function handleEdit (record) {
  addDesignRef.value.open({
    title: '编辑任务',
    data: { ...record },
    mode: 'edit',
  });
}

// 查看
function handleView (record) {
  addDesignRef.value.open({
    title: '查看任务',
    data: { ...record },
    mode: 'view',
  });
}

// 删除
async function handleDelete (record) {
  Modal.confirm({
    title: '删除任务',
    content: '确定删除该任务吗？',
    onOk: async () => {
      try {
        await deleteDesign({ id: [record.id] });
        message.success('删除成功');
        handleRefresh();
      } catch (err) {
        message.error('删除失败');
      }
    },
  });
}

// 导出
async function handleExport () {
  try {
    downloadFile(`/db/reportDesign/export`)
    message.success('导出成功');
  } catch (error) {
    console.error('导出失败:', error);
    message.error('导出失败');
  }
}
</script>

<style lang="less" scoped>
.list-operator {
  display: flex;
  justify-content: flex-end;
  margin: 12px 0;
}

.list-operator-column {
  display: flex;

  ::v-deep .ant-btn {
    padding: 4px 6px;
  }
}
</style>
