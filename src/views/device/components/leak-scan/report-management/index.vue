<template>
  <div>
    <DynamicSearch
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    />

    <a-table
      :scroll="{ y: 'calc(100vh - 330px)' }"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
    </a-table>
  </div>
</template>

<script>
import { getCurrentInstance, createVNode, ref, computed, reactive, inject, toRefs, nextTick } from "vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { usePagination } from "vue-request";
import { cloneDeep } from 'lodash'

import { SEARCH_CONFIG, getColumns, DETAILS_CONFIG, EXPORT_TYPE_ENUM } from "./config";
import DynamicSearch from "@/components/dynamic-search/index.vue";

import { deleteReport, getReportList } from "@/request/api-device-leak-scan.js";
import { downloadFile } from '@/utils/util'

export default {
  components: {
    DynamicSearch,
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const deviceSafetyId = inject("deviceSafetyId");

    const state = reactive({
      searchParams: {}
    });

    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getReportList, {
      manual: true,
      defaultParams: {
        deviceSafetyId: deviceSafetyId.value,
        pageDomain: {
          pageNum: 1,
          pageSize: 10
        },
        ...state.searchParams
      },
      formatResult: ({ data = {} }) => ({
        items: data?.data ?? [],
        total: data?.total ?? 0,
      }),
      pagination: {
        currentKey: "pageNum",
        pageSizeKey: "pageSize",
      },
    });

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`,
    }));

    const dataSource = computed(() => {
      return data.value?.items || [];
    });

    const refreshTableData = (isReload = true) => {
      const pageNum = isReload ? 1 : pagination.value.current
      const pageSize = pagination.value.pageSize
      run({
        deviceSafetyId: deviceSafetyId.value,
        pageNum,
        pageSize,
        pageDomain: {
          pageNum,
          pageSize,
        },
        ...state.searchParams,
      });
    };

    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        deviceSafetyId: deviceSafetyId.value,
        pageSize: pag.pageSize,
        pageNum: pag?.current,
        pageDomain: {
          pageSize: pag.pageSize,
          pageNum: pag?.current,
        },
        ...state.searchParams
      })
    }
    const handleSearch = async (params = {}) => {
      state.searchParams = cloneDeep(params);
      await nextTick();

      refreshTableData();
    };


    const handleDelete = async (record) => {
      console.log("record:", record); // 检查 record 对象是否有 reportId
      //alert(JSON.stringify(record))
      proxy.$confirm({
        title: "删除",
        content: "删除后信息将无法找回，是否进行删除?",
        icon: createVNode(ExclamationCircleOutlined),
        okText: "确定",
        okType: "warning",
        cancelText: "取消",
        async onOk() {
          await deleteReport({reportId: record.id, deviceSafetyId: deviceSafetyId.value});
          refreshTableData();
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    };

    function handleDownload(record, exportType) {
    console.log(exportType)
      const params = encodeURI(JSON.stringify({deviceSafetyId: deviceSafetyId.value, responseType: exportType, reportId: record.id}));
      downloadFile(`/device-atomic/VULQiming/downloadReport?params=${params}`)
    }

    return {
      SEARCH_CONFIG,
      EXPORT_TYPE_ENUM,
      columns: getColumns({handleDelete, handleDownload, pagination}),
      dataSource,
      pagination,
      ...toRefs(state),
      loading,
      handleDelete,
      handleSearch,
      handleTableChange,
      handleDownload,
    };
  },
};
</script>
