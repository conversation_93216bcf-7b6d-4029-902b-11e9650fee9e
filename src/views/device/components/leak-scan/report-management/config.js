import { useAsyncDataSource } from "@/utils/util";

export const EXPORT_TYPE_ENUM = {
  1: 'html',
  2: 'word',
  3: 'excel',
  4: 'pdf',
  5: 'xml',
  6: 'wps'
}

//  1生成中，2执行中，3成功 4失败
export const REPORT_STATUS_MAP = {
  1:'生成中',
  2:'执行中',
  3:'成功',
  4:'失败'
}
//'1119001'='主机漏扫任务报表'， '1119003'='web漏扫任务报表'， '1119005'='配置核查任务报表'， '1119006'='配置变更任务报表'， '1119007'='口令核查任务报表'， '1119008'='配置变更监测任务报表'， '1119035'='配置变更任务单主机报表'， '1119037'='配置变更监测任务单主机报表'
export const REPORT_TYPE_MAP = {
  '1119001':'主机漏扫任务报表',
  '1119003':'web漏扫任务报表',
  '1119005':'配置核查任务报表',
  '1119006':'配置变更任务报表',
  '1119007':'口令核查任务报表',
  '1119008':'配置变更监测任务报表',
  '1119035':'配置变更任务单主机报表',
  '1119037':'配置变更监测任务单主机报表'
}

const REPORT_TYPE_OPTIONS = Object.entries(REPORT_TYPE_MAP).map(([value, label]) => ({
  value,
  label,
}));

export const SEARCH_CONFIG = [
  {
    title: '报表名称',
    name: 'reportName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入报表名称',
    },
  },
  // {
  //   title: '报表类型',
  //   name: 'reportTemplate',
  //   type: 'string',
  //   'x-component': 'Select',
  //   'x-component-props': {
  //     placeholder: '请选择报表类型',
  //     options: REPORT_TYPE_OPTIONS
  //   },
  // },
]

export const getColumns = ({ handleDownload, handleDelete, pagination }) => {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: '报表名称',
      dataIndex: 'reportName',
      key: 'reportName',
      width: 250,
      fixed: 'left',
    },
    {
      title: '报表类型',
      dataIndex: 'reportTemplate',
      key: 'reportTemplate',
      width: 160,
      customRender({text}){
        return REPORT_TYPE_MAP[text]
      }
    },
    {
      title: '创建人',
      dataIndex: 'createUser',
      key: 'createUser',
      width: 160,
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      key: 'createTime',
      width: 160,
    },
    {
      title: '报表状态',
      dataIndex: 'reportStatus',
      key: 'reportStatus',
      width: 120,
      customRender({text}){
        return REPORT_STATUS_MAP[text]
      }
    },
    {
      title: '输出类型',
      dataIndex: 'reportOutType',
      key: 'reportOutType',
      width: 120,
      customRender({ text }) {
        const enumList = [
          { label: 'html', value: "1" },
          { label: 'word', value: "2" },
          { label: 'excel', value: "3" },
          { label: 'pdf', value: "4" },
          { label: 'xml', value: "5" },
          { label: 'wps', value: "6" },
        ];

        let values = [];

        try {
          if (typeof text === "string") {
            // 如果是逗号分隔的字符串，将其转换为数组
            values = text.split(",").map((item) => item.trim());
          } else {
            // 尝试将字符串形式的数组解析为真正的数组
            values = JSON.parse(text);
          }
        } catch (error) {
          console.error("Invalid text format:", text, error);
        }

        // 确保解析结果是一个数组
        if (!Array.isArray(values)) {
          values = [];
        }

        // 映射到对应的 label
        const labels = values
          .map((val) => {
            const item = enumList.find((e) => e.value === val);
            return item ? item.label : null;
          })
          .filter((label) => label !== null)
          .join(", ");

        return labels || "-";
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      width: 180,
      fixed: 'right',
      key: 'operation',
      customRender: ({record}) => {
        const exportList = record.reportOutType?.split(',')?.map((item) => item.trim()) ?? []
        return (<div>
          <a-dropdown>
            {{
              default: () => <a-button
                type="link"
              >
                下载
              </a-button>,
              overlay: () => <a-menu onClick={({key}) => handleDownload(record, key)}>
                {
                  exportList.map(item => {
                    return <a-menu-item key={item}>
                      {EXPORT_TYPE_ENUM[item]}
                    </a-menu-item>
                  })
                }
              </a-menu>
            }}
          </a-dropdown>
          <a-button
            type="link"
            danger={true}
            onClick={() => handleDelete(record)}
          >
            删除
          </a-button>
        </div>)
      }
    }
  ]
}

export const DETAILS_CONFIG = [
  {
    label: '报表名称',
    prop: 'reportName'
  },
  {
    label: '报表类型',
    prop:'reportTemplate'
  },
  {
    label: '报表单位',
    prop: 'reportDept'
  },
  {
    label: '创建人',
    prop: 'createUser'
  },
  {
    label: '创建时间',
    prop: 'createTime'
  },
  {
    label: '报表状态',
    prop: 'reportStatus'
  },
  {
    label: '失败原因',
    prop: 'taskStatus'
  },
  {
    label: '输出类型',
    prop: 'reportOutType',
  },
]
