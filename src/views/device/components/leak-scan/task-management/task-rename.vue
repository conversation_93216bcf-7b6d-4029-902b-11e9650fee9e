<template>
  <a-modal
    v-model:visible="dialogVisible"
    title="任务编辑"
    width="720px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema" :scope="{ formTab }"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        :loading="loading"
        @click="handleSave"
        >保存</a-button
      >
      <a-button  @click="handleClose">取消</a-button>
    </template>
  </a-modal>
</template>

<script setup>
import { updateLeakTaskName } from "@/request/api-device-leak-scan";
import { FormTab } from "@formily/antdv-x3";
import { createForm } from "@formily/vue";
import { ref, shallowRef, defineExpose, defineEmits, computed, inject } from "vue";

const formTab = FormTab.createFormTab();
const emits = defineEmits(["refresh"]);

const form = shallowRef(null);

const schema ={
  type: 'object',
  properties: {
    taskName: {
      type:'string',
      title: '任务名称',
      'x-component': 'Input',
      'x-decorator': 'FormItem',
      'x-component-props': {
        placeholder: '请输入任务名称',
      },
      required: true,
    },
  }
}

const dialogVisible = ref(false);

function open(data) {
  form.value = createForm({
    values: { ...data },
  });
  dialogVisible.value = true;
}


const loading = ref(false);
const deviceSafetyId = inject("deviceSafetyId");

async function handleSave() {
  try {
    loading.value = true;
    await form.value.validate();
    const values = { ...form.value.values, deviceSafetyId: deviceSafetyId.value };
    await updateLeakTaskName(values)
   dialogVisible.value = false;
    form.value.reset();
    emits("refresh");
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
}
function handleClose() {
  dialogVisible.value = false;
  form.value.reset();
}

defineExpose({
  open,
});
</script>

<style lang="less">
.common-space .ant-select{
  min-width: 250px;
 }
</style>
