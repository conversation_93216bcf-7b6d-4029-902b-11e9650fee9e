<template>
  <div class="content-wrapper">
    <a-textarea
      v-model:value="inputValue"
      placeholder="每一行输入一个资产IP"
      :auto-size="{ minRows: 4, maxRows: 4 }"
    />
    <a-button type="primary" @click="handleClick"> 选择资产 </a-button>
    <a-modal
      :width="900"
      v-model:visible="visible"
      :maskClosable="false"
      :keyboard="false"
      title="选择资产"
      @ok="handleAdd"
    >
      <DynamicSearch
        :config="SCAN_TARGET_SEARCH_CONFIG"
        @search="handleSearch"
      />
      <CommonTable
        :rowSelection="rowSelection"
        :rowKey="record => record.ipv4"
        style="margin-top: 12px"
        :columns="SCAN_TARGET_COLUMNS"
        :fetchData="fetchData"
        ref="tableRef"
      />
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch, inject } from "vue";
import CommonTable from "@/components/common-table/index.vue";
import DynamicSearch from "@/components/dynamic-search/index.vue";
import { SCAN_TARGET_SEARCH_CONFIG, SCAN_TARGET_COLUMNS } from "./config";
import { getAssetList } from "@/request/api-device-leak-scan";

export default defineComponent({
  components: { CommonTable, DynamicSearch },
  props: {
    value: {
      type: String,
      default: "",
    },
  },
  emits: ["change"],
  setup(props, { emit }) {
    const inputValue = ref("");
    const visible = ref(false);
    const dataSource = ref([]);


    const selectedKeys = ref([]);

    function handleAdd() {
      const assets = selectedKeys.value?.join(",");
      inputValue.value = assets??'';
      visible.value = false;
    }
    watch(() => inputValue.value, (val) => {
      emit("change", val);
    })

    function handleClick() {
      visible.value = true;
      selectedKeys.value = inputValue.value?.split(",")?.map((i) => i.trim());
    }

    const tableData = ref([]);

    const rowSelection = computed(() => {
      return {
        selectedRowKeys: selectedKeys.value,
        onChange(selectedRowKeys, selectedRows) {
          selectedKeys.value = selectedRowKeys??[];
        },
      };
    });
    const deviceSafetyId = inject("deviceSafetyId");
    async function fetchData(params) {
      const { data } = await getAssetList({ ...searchParams.value, deviceSafetyId: deviceSafetyId.value });
      tableData.value = data;
      return { items: data, total: data.length };
    }
    const searchParams = ref({});
    const tableRef = ref(null);

    function handleSearch(params) {
      searchParams.value = { ...params };
      tableRef.value?.refresh();
    }
    return {
      SCAN_TARGET_COLUMNS,
      SCAN_TARGET_SEARCH_CONFIG,
      inputValue,
      visible,
      handleClick,
      dataSource,
      handleAdd,
      fetchData,
      handleSearch,
      rowSelection,
      tableRef
    };
  },
});
</script>

<style lang="less" scoped>
.content-wrapper {
  display: flex;
}
</style>
