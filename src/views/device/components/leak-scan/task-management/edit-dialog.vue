
<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="700px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <a-tabs
        style="margin-bottom: 12px"
        v-model:activeKey="activeKey"
        @change="initForm"
      >
        <a-tab-pane
          v-for="item in TABLE_CONFIG"
          :key="item.key"
          :tab="item.label"
        />
      </a-tabs>

      <FormProvider v-if="dialogVisible" :form="form">
        <SchemaField
          :schema="schema"
          :scope="{ formTab }"
          :components="{ ScanTarget }"
        ></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave"
        >保存</a-button
      >
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{
        isView ? "关闭" : "取消"
      }}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs,inject, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm } from '@formily/core'
import { getTableSchemaMap, TABLE_CONFIG, SAVE_TASK_INTERFACE_MAP } from './config';
import { FormTab } from '@formily/antdv-x3'
import ScanTarget  from './scan-target.vue'
import { cloneDeep } from 'lodash'
import { PWD_TYPE_ENUM } from './config'
export default defineComponent({
name: 'EditList',
props: {
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add'
  }
},
emits: ['update:visible', 'on-success'],
setup (props, ctx) {
  const { proxy } = getCurrentInstance()
    const deviceSafetyId = inject("deviceSafetyId");

  const { visible, mode, type } = toRefs(props)
  // const isAdd = computed(() => mode.value === 'add')
  const isView = computed(() => mode.value === 'view')
  const dialogVisible = computed(() => visible.value)
  const title = computed(() => {
    const titleMap = {
      add: '新增',
      edit: '编辑',
      view: '查看'
    }
    return `${titleMap[mode.value]}任务`
  })

  const state = reactive({
    loading: false,
    activeKey: 'host',
    schema: {}
  })

  const form =  shallowRef(null);

  watch(
    () => visible.value,
    async (val) => {
      if (!val) return
      await nextTick()
      await initForm()
    }
  )

  const initForm = async () => {
    form.value = createForm({values: {}});
    state.schema = getTableSchemaMap(state.activeKey, deviceSafetyId.value);
  }

  const handleClose = async () => {
    await form.value?.reset()
    ctx.emit('update:visible', false)
  }

  const handleSave = async () => {
    await form.value.validate()
    try {
      state.loading = true
       const params = cloneDeep(form.value.values)
       Object.assign(params, {
          performancePara: {
            taskLevel: params.taskLevel
          },
          report: {
            reportType: params.reportType,
          }
        });
        if(params.passwordPara){
          params.passwordPara = PWD_TYPE_ENUM.reduce((acc, cur) => {
           acc[cur] = params.passwordPara.includes(cur)?'1': '0';
            return acc;
          }, {});
        }
        const saveInterface = SAVE_TASK_INTERFACE_MAP[state.activeKey]
      await saveInterface({...params, deviceSafetyId: deviceSafetyId.value});
      ctx.emit('on-success', {
        mode: mode.value,
        data: {
          ...params,
        },
        callback: handleClose
      })
      handleClose()
    } finally {
      state.loading = false
    }
  }



  const formTab = FormTab.createFormTab();
  return {
    title,
    isView,
    form,
    formTab,
    ...toRefs(state),
    dialogVisible,
    handleClose,
    handleSave,
    TABLE_CONFIG,
    initForm,
    ScanTarget
  }
}
})
</script>
