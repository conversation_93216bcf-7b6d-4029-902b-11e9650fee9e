import { createCheckTask, createHostScanTask, createWeakPwdTask, createWebScanTask, createCvsChangeTask, getAssetList, getCheckStrategyList, getHostScanStrategyList, getWebScanStrategyList } from "@/request/api-device-leak-scan"
import { useAsyncDataSource } from "@/utils/util"

export const SEARCH_CONFIG = [
  {
    title: '任务名称',
    name: 'taskName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入任务名称',
    },
  },
  {
    title: '扫描时间',
    name: 'scanTime',
    type: 'string',
    'x-component': 'DatePicker',
    'x-component-props': {
      type: 'datetimerange',
      placeholder: '请选择扫描时间',
    },
  },
]

const TASK_STATUS_MAP = {
  2: "执行",
  1: "等待",
  3: "暂停",
  5: "完成",
  6: "失败",
  4: "停止",
  10: "断点续扫",
}

export const COLUMNS = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '任务名称',
    dataIndex: 'taskName',
    key: 'taskName',
    fixed: 'left',
    width: 200,
  },
  {
    title: '扫描目标',
    dataIndex: 'scanTarget',
    key: 'scanTarget',
    width: 160,
  },
  {
    title: '扫描进度',
    dataIndex: 'progress',
    key: 'progress',
    width: 120,
    customRender({text}){
      return   <el-progress percentage={text} />
    }
  },
  {
    title: '开始时间',
    dataIndex: 'beginTime',
    key: 'beginTime',
    width: 165,
  },
  {
    title: '耗时',
    dataIndex: 'usedTime',
    key: 'usedTime',
    width: 100,
  },
  {
    title: '任务状态',
    dataIndex: 'taskStatus',
    key: 'taskStatus',
    width: 120,
    customRender({text}){
      return TASK_STATUS_MAP[text]
    }
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 320,
  }
]

export const TABLE_CONFIG = [
  {
    label: '主机漏洞扫描',
    key: 'host',
  },
  {
    label: 'WEB漏洞扫描',
    key: 'web',
  },
  {
    label: '配置核查',
    key: 'config',
  },
  {
    label: '弱口令',
    key: 'weakPwd',
  },
  {
    label: '配置变更任务',
    key: 'configChange',
  }
]

function getCommonSchema(schemaItems) {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 100
        },
        properties: {
          taskName: {
            default: schemaItems.defaultName,
            type: 'string',
            title: '任务名称',
            'x-validator': [
              { required: true, message: '请输入任务名称' },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入任务名称'
            }
          },
          scanTarget: {
            type: 'string',
            title: '扫描目标',
            'x-validator': [
              { required: true, message: '请输入扫描目标' },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'ScanTarget',
            'x-component-props': {
              placeholder: '请输入扫描目标'
            }
          },
          ...schemaItems,
          taskLevel: {
            title: '任务优先级',
            'x-validator': [
              { required: true, message: '请选择任务优先级' },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Radio.Group',
            'x-component-props': {
              placeholder: '请选择任务优先级'
            },
            enum: [
              { label: '极高', value: 5 },
              { label: '高', value: 4 },
              { label: '中', value: 3 },
              { label: '低', value: 2 },
              { label: '极低', value: 1 },
            ]
          },
          reportType: {
            title: '报告类型',
            'x-validator': [
              { required: true, message: '请选择报告类型' },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Checkbox.Group',
            'x-component-props': {
              placeholder: '请选择报告类型'
            },
            enum: [
              { label: 'html', value: "1" },
              { label: 'word', value: "2" },
              { label: 'excel', value: "3" },
              { label: 'pdf', value: "4" },
              { label: 'xml', value: "5" },
              // { label: 'wps', value: "6" },
            ]
          },
        }
      },
    }
  }
}


export const PWD_TYPE_ENUM = [
  "DB2",
  "FTP",
  "IMAP",
  "MSSQL",
  "MYSQL",
  "ORACLE",
  "POP3",
  "RLOGIN",
  "SMB",
  "SNMP",
  "SSH",
  "TELNET",
  "HIGHGO",
  "POSTGRESQL",
  "MONGODB",
  "RDP",
  "SYBASE",
  "WEBLOGIC",
  "TOMCAT",
  "WEBCAM",
  "HighGo",
  "PostgreSQL",
  "Kingbase",
  "UXDB",
  "STDB",
  "RTSP",
  "SMTP",
  "REDIS",
  "ActiveMQ"
]

export function generateDefaultName(defaultNamePre) {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, '0'); // 月份从0开始
  const date = String(now.getDate()).padStart(2, '0');
  const hours = String(now.getHours()).padStart(2, '0');
  const minutes = String(now.getMinutes()).padStart(2, '0');
  const seconds = String(now.getSeconds()).padStart(2, '0');
  const milliseconds = String(now.getMilliseconds()).padStart(3, '0');

  return defaultNamePre + `-${year}${month}${date}${hours}${minutes}${seconds}${milliseconds}`;
}

export function getTableSchemaMap(activeKey, deviceSafetyId){
const HOST_SCHEMA = {
  defaultName: generateDefaultName('主机漏扫'),
  policyID: {
    type: 'string',
    title: '策略模版',
    'x-validator': [
      { required: true, message: '请选择策略模版' },
    ],
    'x-decorator': 'FormItem',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择策略模版'
    },
    'x-reactions': useAsyncDataSource(async () => {
      const { data } = await getHostScanStrategyList({deviceSafetyId});
      return data.map(item => ({ label: item.policyName, value: item.policyID }));
    })
  },
}
 const WEB_SCHEMA = {
  defaultName: generateDefaultName('WEB扫描'),
  policyID: {
    type: 'string',
    title: '策略模版',
    'x-validator': [
      { required: true, message: '请选择策略模版' },
    ],
    'x-decorator': 'FormItem',
    'x-component': 'Select',
    'x-component-props': {
      placeholder: '请选择策略模版'
    },
    'x-reactions': useAsyncDataSource(async () => {
      const { data } = await getWebScanStrategyList({deviceSafetyId});
      return data.map(item => ({ label: item.policyName, value: item.id }));
    })
  },
}
  const TABLE_FORM_SCHEMA_MAP = {
    host: getCommonSchema(HOST_SCHEMA),
    web: getCommonSchema(WEB_SCHEMA),
    config: getCommonSchema({
      defaultName: generateDefaultName('配置核查'),
      loginConfig: {
        type: 'array',
        title: '登录配置',
        'x-decorator': 'FormItem',
        'x-component': "ArrayTable",
        'x-pattern': 'readPretty',
        'x-component-props': {
          pagination: { pageSize: 10 },
        },
        items: {
          type: 'object',
          properties: {
            column1: {
              type: 'void',
              'x-component': 'ArrayTable.Column',
              'x-component-props': { title: 'IP' },
              properties: {
                ip: {
                  type: 'string',
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                },
              },
            },
            column2: {
              type: 'void',
              'x-component': 'ArrayTable.Column',
              'x-component-props': { title: '资产类型' },
              properties: {
                assetType: {
                  type: 'string',
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                },
              },
            },
            column3: {
              type: 'void',
              'x-component': 'ArrayTable.Column',
              'x-component-props': { title: '登录协议' },
              properties: {
                osProtocol: {
                  type: 'string',
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                },
              },
            },
            column4: {
              type: 'void',
              'x-component': 'ArrayTable.Column',
              'x-component-props': { title: '端口' },
              properties: {
                osPort: {
                  type: 'string',
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                },
              },
            },
          }
        },
        'x-reactions': [
          async field => {
            const { data } = await getAssetList({deviceSafetyId});
            field.setData({dataSource: data})
          },
          field => {
            console.log(field)
            const scanTarget = field.query('scanTarget').value();
            if(scanTarget){
              console.log(field.data)
              const ips = scanTarget.split(',');
              const values = ips.map(ip => {
                const item = field.data?.dataSource?.find(i => i.ipv4 === ip);
                return item? {...item, ip}: {ip}
              })
              field.form.setValuesIn('loginConfig', values)
            }
          }
        ]
      },
      policyID: {
        type: 'string',
        title: '策略模版',
        'x-validator': [
          { required: true, message: '请选择策略模版' },
        ],
        'x-decorator': 'FormItem',
        'x-component': 'Select',
        'x-component-props': {
          placeholder: '请选择策略模版'
        },
        'x-reactions': useAsyncDataSource(async () => {
          const { data } = await getCheckStrategyList({deviceSafetyId});
          return data.map(item => ({ label: item.name, value: item.pId }));
        })
      },
    }),
    weakPwd: getCommonSchema({
      defaultName: generateDefaultName('弱口令'),
      pwdMethod: {
        type: 'string',
        title: '口令类型',
        'x-decorator': 'FormItem',
        'x-component': 'Select',
        'x-component-props': {
         mode:"multiple",
         placeholder: '请选择口令类型'
        },
        'x-validator': [
          { required: true, message: '请选择口令类型' },
        ],
        enum:PWD_TYPE_ENUM.map(item => ({ label: item, value: item }))
      },
    }),
    configChange: getCommonSchema({
      defaultName: generateDefaultName('配置变更'),
      loginConfig: {
        type: 'array',
        title: '登录配置',
        'x-decorator': 'FormItem',
        'x-component': "ArrayTable",
        'x-pattern': 'readPretty',
        'x-component-props': {
          pagination: { pageSize: 10 },
        },
        items: {
          type: 'object',
          properties: {
            column1: {
              type: 'void',
              'x-component': 'ArrayTable.Column',
              'x-component-props': { title: 'IP' },
              properties: {
                ip: {
                  type: 'string',
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                },
              },
            },
            column2: {
              type: 'void',
              'x-component': 'ArrayTable.Column',
              'x-component-props': { title: '资产类型' },
              properties: {
                assetType: {
                  type: 'string',
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                },
              },
            },
            column3: {
              type: 'void',
              'x-component': 'ArrayTable.Column',
              'x-component-props': { title: '登录协议' },
              properties: {
                osProtocol: {
                  type: 'string',
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                },
              },
            },
            column4: {
              type: 'void',
              'x-component': 'ArrayTable.Column',
              'x-component-props': { title: '端口' },
              properties: {
                osPort: {
                  type: 'string',
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                },
              },
            },
          }
        },
        'x-reactions': [
          async field => {
            const { data } = await getAssetList({deviceSafetyId});
            field.setData({dataSource: data})
          },
          field => {
            console.log(field)
            const scanTarget = field.query('scanTarget').value();
            if(scanTarget){
              console.log(field.data)
              const ips = scanTarget.split(',');
              const values = ips.map(ip => {
                const item = field.data?.dataSource?.find(i => i.ipv4 === ip);
                return item? {...item, ip}: {ip}
              })
              field.form.setValuesIn('loginConfig', values)
            }
          }
        ]
      },
      // policyID: {
      //   type: 'string',
      //   title: '策略模版',
      //   required: true,
      //   'x-decorator': 'FormItem',
      //   'x-component': 'Select',
      //   'x-component-props': {
      //     placeholder: '请选择策略模版'
      //   },
      //   'x-reactions': useAsyncDataSource(async () => {
      //     const { data } = await getCheckStrategyList({deviceSafetyId});
      //     return data.map(item => ({ label: item.name, value: item.pId }));
      //   })
      // },
    })
  }
  return TABLE_FORM_SCHEMA_MAP[activeKey]
}


export const SAVE_TASK_INTERFACE_MAP = {
  host: createHostScanTask,
  web: createWebScanTask,
  config: createCheckTask,
  weakPwd: createWeakPwdTask,
  configChange: createCvsChangeTask,
}






/**-------------------扫描----- */

export const SCAN_TARGET_SEARCH_CONFIG = [
  {
    title: '资产名称',
    name: 'assetName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入资产名称',
    },
  },
  {
    title: 'IP地址',
    name: 'ipv4',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入IP地址',
    },
  },
]


const PROTECT_LEVEL_MAP = {
  '1120000':'缺乏保护',
  '1120001':'一般保护',
  '1120002':'严格保护'
}

const ASSET_TYPE_MAP = {
  '1001000000':'UNIX',
  '1002000000':'LINUX',
  '1003000000':'WINDOWS',
  '1004000000':'交换机',
  '1005000000':'路由器',
  '1006000000':'防火墙',
  '1009100000':'WAF',
  '1009110000':'入侵检测',
  '1009120000':'安全防护网关',
  '1009130000':'网闸',
  '1009140000':'VPN设备',
  '1009150000':'流量检测设备',
  '1009160000':'入侵防御',
  '1009170000':'工业防火墙',
  '1010000000':'负载均衡',
  '3000000000':'WLAN'
}

export const SCAN_TARGET_COLUMNS = [
  {
    title: '资产名称',
    dataIndex: 'assetName',
    key: 'assetName'
  },
  {
    title: 'IP',
    dataIndex: 'ipv4',
    key: 'ipv4'
  },
  {
    title: '负责人',
    dataIndex: 'masterId',
    key: 'masterId'
  },
  {
    title: '保护等级',
    dataIndex: 'protectLevel',
    key: 'protectLevel',
    customRender({text}){
      return PROTECT_LEVEL_MAP[text]
    }
  },
  {
    title: '资产类型',
    dataIndex: 'assetType',
    key: 'assetType',
    customRender({text}){
      return ASSET_TYPE_MAP[text]
    }
  },
]



export const TASK_DETAIlS_CONFIG = [
  {
    label: '任务名称',
    prop: 'taskName'
  },
  {
    label: '扫描类型',
    prop: 'type'
  },
  {
    label: '扫描引擎',
    prop: 'number'
  },
  {
    label: '任务模板',
    prop: 'initiator'
  },
  {
    label: 'IPV4',
    prop: 'createTime'
  },
  {
    label: 'IPV6',
    prop:'status'
  },
  {
    label: '域名',
    prop: 'currentNode'
  },
  {
    label: '扫描目标',
    prop: 'scanTarget',
  },
  {
    label: '策略模板',
    prop: 'initiator'
  },
  {
    label: '优先级',
    prop: 'createTime'
  },
  {
    label: '执行时段',
    prop:'status'
  },
  {
    label: '任务来源',
    prop: 'currentNode'
  },
  {
    label: '执行方式',
    prop: 'initiator'
  },
  {
    label: '定时参数',
    prop: 'createTime'
  },
  {
    label: '创建人',
    prop:'status'
  },
  {
    label: '开始时间',
    prop: 'beginTime'
  },
  {
    label: '完成时间',
    prop:'status'
  },
  {
    label: '任务耗时',
    prop: 'usedTime'
  },
]
