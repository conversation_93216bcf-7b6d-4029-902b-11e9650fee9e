<template>
  <div>
    <DynamicSearch
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    />
    <a-space align="start" class="vul-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add')">
        新增
      </a-button>
      <!-- <div class="right-operate">
        <a-button type="primary" @click="handleExport"> 导出 </a-button>
      </div> -->
    </a-space>
    <a-table
      :scroll="{ y: 'calc(100vh - 370px)' }"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button type="link" @click="handleEdit(record)"
            >编辑</a-button
          >
          <a-dropdown>
            <template #overlay>
              <a-menu @click="handleMenuClick">
                <a-menu-item key="rescan">
                  <a-button
                    type="link"
                    @click="handleRescan(record)"
                    :disabled="!['5', '6'].includes(record.taskStatus)"
                    >重新扫描</a-button
                  >
                </a-menu-item>
                <a-menu-item key="continue">
                  <a-button
                    type="link"
                    @click="handleContinue(record)"
                    :disabled="!['3'].includes(record.taskStatus)"
                    >继续任务</a-button
                  >
                </a-menu-item>
                <a-menu-item key="pause">
                  <a-button
                    type="link"
                    @click="handlePause(record)"
                    :disabled="!['2'].includes(record.taskStatus)"
                    >暂停扫描</a-button
                  >
                </a-menu-item>
                <a-menu-item key="stop">
                  <a-button
                    type="link"
                    @click="handleStop(record)"
                    :disabled="!['2', '1'].includes(record.taskStatus)"
                    >停止任务</a-button
                  >
                </a-menu-item>
              </a-menu>
            </template>
            <a-button type="link"> 任务管理 </a-button>
          </a-dropdown>
          <a-button type="link" @click="handleExport(record)"
            >导出</a-button
          >
          <a-button type="link" danger @click="handleDelete(record)"
            >删除</a-button
          >
        </template>
      </template>
    </a-table>
    <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="refreshTableData"
    />
    <TaskRename ref="taskRenameRef" @refresh="refreshTableData" />
  </div>
</template>

<script>
import DynamicSearch from "@/components/dynamic-search/index.vue";
import { SEARCH_CONFIG, COLUMNS, TASK_DETAIlS_CONFIG } from "./config";
import { usePagination } from "vue-request";
import {
  computed,
  getCurrentInstance,
  reactive,
  createVNode,
  toRefs,
  nextTick,
  ref,
  inject
} from "vue";
import {
  getTaskListByPagination,
  pauseTask,
  stopTask,
  rescanTask,
  continueTask,
  deleteTask,
} from "@/request/api-device-leak-scan.js";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import EditDialog from "./edit-dialog.vue";
import { cloneDeep } from "lodash";
import { DownOutlined } from "@ant-design/icons-vue";
import TaskRename from './task-rename.vue'
import { downloadFile } from '@/utils/util';

export default {
  components: {
    DynamicSearch,
    EditDialog,
    DownOutlined,
    TaskRename
  },
  setup() {
    const deviceSafetyId = inject("deviceSafetyId");
    const { proxy } = getCurrentInstance();

    const state = reactive({
      searchParams: {},
      dialogConfig: {
        visible: false,
        mode: "add",
        defaultData: {},
      },
    });

    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getTaskListByPagination, {
      manual: true,
      defaultParams: {
        deviceSafetyId: deviceSafetyId.value,
        pageDomain: {
          pageNum: 1,
          pageSize: 10
        },
        ...state.searchParams
      },
      formatResult: ({ data = {} }) => ({
        items: data?.data ?? [],
        total: data?.total ?? 0,
      }),
      pagination: {
        currentKey: "pageNum",
        pageSizeKey: "pageSize",
      },
    });

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`,
    }));

    const dataSource = computed(() => {
      return data.value?.items || [];
    });

    const refreshTableData = (isReload = true) => {
      const pageNum = isReload ? 1 : pagination.value.current
      const pageSize = pagination.value.pageSize
      run({
        deviceSafetyId: deviceSafetyId.value,
        pageNum,
        pageSize,
        pageDomain: {
          pageNum,
          pageSize,
        },
        ...state.searchParams,
      });
    };

    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        deviceSafetyId: deviceSafetyId.value,
        pageSize: pag.pageSize,
        pageNum: pag?.current,
        pageDomain: {
          pageSize: pag.pageSize,
          pageNum: pag?.current,
        },
        ...state.searchParams
      })
    }

    const handleDelete = async (row) => {
      proxy.$confirm({
        title: "删除",
        content: "删除后信息将无法找回，是否进行删除?",
        icon: createVNode(ExclamationCircleOutlined),
        okText: "确定",
        okType: "warning",
        cancelText: "取消",
        async onOk() {
          // await deleteIpsPolicy({ id })
          // reload();
          handleDeleteTask(row);
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    };
    const taskRenameRef = ref(null);

    function handleEdit(row){
      taskRenameRef.value.open({...row});
    }

    const handleOpenDialog = (mode = "add", row = {}) => {
      state.dialogConfig.visible = true;
      state.dialogConfig.mode = mode;
      state.dialogConfig.defaultData = {...row, deviceSafetyId: deviceSafetyId.value};
    };

    const handleSearch = async (params = {}) => {
      state.searchParams = cloneDeep(params);
      await nextTick();

      refreshTableData();
    };

    async function handlePause(row) {
      await pauseTask({ ...row, resultId: row.id, deviceSafetyId: deviceSafetyId.value });
      refreshTableData();
    }
    async function handleContinue(row) {
      await continueTask({ ...row, resultId: row.id, deviceSafetyId: deviceSafetyId.value });
      refreshTableData();
    }
    async function handleStop(row) {
      await stopTask({ ...row, resultId: row.id, deviceSafetyId: deviceSafetyId.value });
      refreshTableData();
    }
    async function handleRescan(row) {
      await rescanTask({ ...row, resultId: row.id, deviceSafetyId: deviceSafetyId.value });
      refreshTableData();
    }

    async function handleDeleteTask(row) {
      await deleteTask({resultId: row.resultID, deviceSafetyId: deviceSafetyId.value });
      refreshTableData();
    }

    const handleExport = (record) => {
      console.log("record:", record.resultID); // 检查 record 对象是否有 resultID
      const params = encodeURI(JSON.stringify({deviceSafetyId: deviceSafetyId.value, resultId: record.resultID}));
      console.log("params:", params);

      downloadFile(`/device-atomic/VULQiming/exportTask?params=${params}`)
      //downloadFile(`/device-atomic/VULQiming/exportTask?deviceSafetyId=${deviceSafetyId.value}`)
    }

    const columns = computed(() => {
      return COLUMNS.map(column => {
        if (column.customRender) {
          return {
            ...column,
            customRender: (args) => column.customRender(args, pagination.value)
          };
        }
        return column;
      });
    });

    return {
      SEARCH_CONFIG,
      columns,
      dataSource,
      pagination,
      ...toRefs(state),
      loading,
      handleExport,
      handleOpenDialog,
      handleDelete,
      handleSearch,
      handleTableChange,
      refreshTableData,
      handleStop,
      handleContinue,
      handlePause,
      handleRescan,
      taskRenameRef,
      deleteTask,
      handleEdit
    };
  },
};
</script>

<style lang="less" scoped>
.table-operate {
  margin: 8px 0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
</style>
