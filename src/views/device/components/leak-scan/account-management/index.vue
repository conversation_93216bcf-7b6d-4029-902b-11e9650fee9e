<template>
  <div>
    <a-space align="start" class="vul-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add')">
        新增
      </a-button>
    </a-space>
    <CommonTable ref="tableRef" :scroll="{ y: 'calc(100vh - 290px)' }" :isImmediate="false" :columns="columns" :fetchData="fetchData"  >
      <template #operation="{ record }">
        <div>
        <a-button type="link" :disabled="record.editable === '0'" @click="handleOpenDialog('edit', record)"
          >编辑</a-button
        >
        <a-button type="link" :disabled="record.editable === '0'" danger @click="handleDelete(record)"
          >删除</a-button
        >
        </div>
      </template>
    </CommonTable>
    <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="handleSave" />
  </div>
</template>

<script>
import { getCurrentInstance, createVNode, ref, reactive, inject, computed, toRefs } from "vue";
import { v4 as uuidv4 } from 'uuid';
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";

import { COLUMNS } from "./config";
import EditDialog from "./edit-dialog/index.vue";
import CommonTable from "@/components/common-table/index.vue";
import {
  deleteVulAccountPasswordDict,
  getVulAccountPasswordDict,
  addVulAccountPasswordDict,
  editVulAccountPasswordDict
} from '@/request/api-device-leak-scan';


export default {
  components: {
    EditDialog,
    CommonTable,
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const deviceSafetyId = inject("deviceSafetyId");
    async function fetchData(params) {
      const {data } = await getVulAccountPasswordDict({...params, deviceSafetyId: deviceSafetyId.value});
      return {items: data, total: data.length}
    }
    const tableRef = ref(null);
    const state = reactive({
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })

    const handleDelete = async (row) => {
      proxy.$confirm({
        title: "删除",
        content: "删除后信息将无法找回，是否进行删除?",
        icon: createVNode(ExclamationCircleOutlined),
        okText: "确定",
        okType: "warning",
        cancelText: "取消",
        async onOk() {
          await deleteVulAccountPasswordDict({...row, deviceSafetyId: deviceSafetyId.value});
          tableRef.value.refresh();
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    };

    const handleOpenDialog = async (mode = "add", row = {}) => {
      state.dialogConfig.visible = true
      state.dialogConfig.mode = mode
      state.dialogConfig.defaultData = row
    };

    const handleRefresh = () => {
      tableRef.value.refresh();
    };

    const handleSave = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addVulAccountPasswordDict,
        edit: editVulAccountPasswordDict
      }
      try {
        const params = {...data};
        if (isAdd) params.dictID = uuidv4().replace(/-/g, '');
        const res = await requestMethodEnum[mode]?.({...params, deviceSafetyId: deviceSafetyId.value})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback?.()
        handleRefresh()
      } catch (err) {
        // const errMsg = isAdd ? '新增失败！' : '更新失败！';
        // proxy.$message.error(err.message || errMsg)
        callback?.(true, err)
      }
    }

    const columns = computed(() => {
      return COLUMNS.map(column => {
        if (column.customRender) {
          return {
            ...column,
            customRender: (args) => column.customRender(args, tableRef.value.pagination)
          };
        }
        return column;
      });
    });

    return {
      ...toRefs(state),
      handleSave,
      columns,
      tableRef,
      handleOpenDialog,
      handleDelete,
      fetchData
    };
  },
};
</script>
