import CellExpand from '@/components/cell-expand';

// 服务类型枚举
const SERVICE_TYPE_MAP = {
  '1':'SMB',
  '4':'POP3',
  '5':'MYSQL',
  '6':'ORACLE',
  '7':'SYBASE',
  '8':'MSSQL',
  '9':'SNMP',
  '13':'DB2',
  '2':'TELNET',
  '3':'FTP',
  '10':'IMAP',
  '12':'RLOGIN',
  '14':'OTHER',
  '16':'SSH',
  '17':'RDP',
  '18':'WEBLOGIC',
  '19':'TOMCAT',
  '20':'WEBCAM',
  '21':'HighGo',
  '22':'POSTGRESQL',
  '23':'MONGODB',
  '24':'Kingbase',
  '25':'UXDB',
  '26':'STDB',
  '27':'DMDB',
  '28':'RTSP',
  '29':'SMTP',
  '30':'REDIS',
  '31':'ActiveMQ',
  '90':'ONVIF',
  '91':'sip',
  '92':'SN（MQTT-SN）',
  '93':'Memcached',
  '94':'JBoss',
  '95':'WebSphere',
  '96':'GlassFish',
  '97':'HTTP',
  '98':'Hikvision',
  '99':'Dahua',
  '100':'HTTPS',
  '101':'SFTP',
  '102':'SCP',
  '103':'VNC',
  '104':'PHPADMIN',
  '105':'ODPS',
  '106':'WebSocket',
  '107':'HuaWei',
  '108':'TDWY'
}

export const COLUMNS = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination?.pageSize * (pagination?.current - 1) + index + 1;
    }
  },
  {
    title: '字典名称',
    dataIndex: 'dictName',
    key: 'dictName'
  },
  {
    title: '字典类型',
    dataIndex: 'dictType',
    key: 'dictType',
    customRender: ({text}) => {
      // 账号字典 0，密码字典 1，组合字典2
      const dictTypeMap = {
        0: '账号字典',
        1: '密码字典',
        2: '组合字典'
      }
      return dictTypeMap[text]
    }
  },
  {
    title: '服务类型',
    dataIndex: 'dictServiceType',
    key: 'dictServiceType',
    customRender({text}){
      return SERVICE_TYPE_MAP[text]
    }
  },
  {
    title: '字典内容',
    dataIndex: 'dictContent',
    key: 'dictContent',
    customRender: ({text}) => {
      const data = text?.split('\n') ?? []
      if (!data?.length) return '-'
      return <CellExpand title="字典内容" data={data} />
    }
  },
  {
    title: '字典描述',
    dataIndex: 'dictDesc',
    key: 'dictDesc',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 180
  }
]

