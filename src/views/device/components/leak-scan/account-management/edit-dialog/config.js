// 字典类型列表
const DICT_TYPE_OPTIONS = [
  // 账号字典 0，密码字典 1，组合字典 2
  { label: '账号字典', value: '0' },
  { label: '密码字典', value: '1' },
  { label: '组合字典', value: '2' },
]

// 字典服务类型
const DICT_SERVICE_TYPE_OPTIONS = [
  {value:'1',label:'SMB'},
  {value:'4',label:'POP3'},
  {value:'5',label:'MYSQL'},
  {value:'6',label:'ORACLE'},
  {value:'7',label:'SYBASE'},
  {value:'8',label:'MSSQL'},
  {value:'9',label:'SNMP'},
  {value:'13',label:'DB2'},
  {value:'2',label:'TELNET'},
  {value:'3',label:'FTP'},
  {value:'10',label:'IMAP'},
  {value:'12',label:'RLOGIN'},
  {value:'14',label:'OTHER'},
  {value:'16',label:'SSH'},
  {value:'17',label:'RDP'},
  {value:'18',label:'WEBLOGIC'},
  {value:'19',label:'TOMCAT'},
  {value:'20',label:'WEBCAM'},
  {value:'21',label:'HighGo'},
  {value:'22',label:'POSTGRESQL'},
  {value:'23',label:'MONGODB'},
  {value:'24',label:'Kingbase'},
  {value:'25',label:'UXDB'},
  {value:'26',label:'STDB'},
  {value:'27',label:'DMDB'},
  {value:'28',label:'RTSP'},
  {value:'29',label:'SMTP'},
  {value:'30',label:'REDIS'},
  {value:'31',label:'ActiveMQ'},
  {value:'90',label:'ONVIF'},
  {value:'91',label:'sip'},
  {value:'92',label:'SN（MQTT-SN）'},
  {value:'93',label:'Memcached'},
  {value:'94',label:'JBoss'},
  {value:'95',label:'WebSphere'},
  {value:'96',label:'GlassFish'},
  {value:'97',label:'HTTP'},
  {value:'98',label:'Hikvision'},
  {value:'99',label:'Dahua'},
  {value:'100',label:'HTTPS'},
  {value:'101',label:'SFTP'},
  {value:'102',label:'SCP'},
  {value:'103',label:'VNC'},
  {value:'104',label:'PHPADMIN'},
  {value:'105',label:'ODPS'},
  {value:'106',label:'WebSocket'},
  {value:'107',label:'HuaWei'},
  {value:'108',label:'TDWY'}
]


/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout1: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 120
      },
      properties: {
        dictName: {
          type: 'string',
          title: '字典名称',
          'x-component': 'Input',
          'x-decorator': 'FormItem',
          'x-component-props': {
            placeholder: '请输入字典名称'
          },
          'x-validator': [
            { required: true, message: '请输入字典名称' },
          ],
        },
        dictType: {
          type: 'string',
          title: '字典类型',
          'x-component': 'Select',
          'x-decorator': 'FormItem',
          enum: DICT_TYPE_OPTIONS,
          'x-validator': [
            { required: true, message: '请选择字典类型' },
          ],
          'x-component-props': {
            placeholder: '请选择字典类型'
          },
        },
        dictServiceType: {
          type: 'string',
          title: '字典服务类型',
          'x-component': 'Select',
          'x-decorator': 'FormItem',
          enum: DICT_SERVICE_TYPE_OPTIONS,
          'x-component-props': {
            placeholder: '请输入字典服务类型'
          },
          'x-validator': [
            { required: true, message: '请输入字典服务类型' },
          ],
        },
        dictContent: {
          type: 'string',
          title: '字典内容',
          'x-component': 'Input.TextArea',
          'x-decorator': 'FormItem',
          'x-component-props': {
            rows: 10,
            placeholder: '请输入字典内容'
          },
          'x-validator': [
            { required: true, message: '请输入字典内容' },
          ],
        },
        dictDesc: {
          type: 'string',
          title: '字典描述',
          'x-component': 'Input.TextArea',
          'x-decorator': 'FormItem',
          'x-component-props': {
            placeholder: '请输入字典描述'
          },
        },
      }
    }
  }
}

