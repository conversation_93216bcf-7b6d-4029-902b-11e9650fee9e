export const SEARCH_CONFIG = [
  {
    title: '资产名称',
    name: 'assetName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入资产名称',
    },
  },
  {
    title: 'IP地址',
    name: 'ipv4',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入IP地址',
    },
  },
]

const PROTECT_LEVEL_MAP = {
  '1120000':'缺乏保护',
  '1120001':'一般保护',
  '1120002':'严格保护'
}

const ASSET_TYPE_MAP = {
  '1001000000':'UNIX',
  '1002000000':'LINUX',
  '1003000000':'WINDOWS',
  '1004000000':'交换机',
  '1005000000':'路由器',
  '1006000000':'防火墙',
  '1009100000':'WAF',
  '1009110000':'入侵检测',
  '1009120000':'安全防护网关',
  '1009130000':'网闸',
  '1009140000':'VPN设备',
  '1009150000':'流量检测设备',
  '1009160000':'入侵防御',
  '1009170000':'工业防火墙',
  '1010000000':'负载均衡',
  '3000000000':'WLAN'
}

export const COLUMNS = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '资产名称',
    dataIndex: 'assetName',
    key: 'assetName'
  },
  {
    title: 'IP',
    dataIndex: 'ipv4',
    key: 'ipv4'
  },
  {
    title: '负责人',
    dataIndex: 'masterId',
    key: 'masterId'
  },
  {
    title: '保护等级',
    dataIndex: 'protectLevel',
    key: 'protectLevel',
    customRender({text}){
      return PROTECT_LEVEL_MAP[text]
    }
  },
  {
    title: '资产类型',
    dataIndex: 'assetType',
    key: 'assetType',
    customRender({text}){
      return ASSET_TYPE_MAP[text]
    }
  },
  {
    title: '资产来源',
    dataIndex: 'asset_source',
    key: 'asset_source'
  },
  // {
  //   title: '操作',
  //   dataIndex: 'operation',
  //   key: 'operation'
  // }
]

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        grid1: {
          type: 'void',
          'x-component': 'FormGrid',
          'x-component-props': {
            minColumns: 2,
            maxColumns: 2
          },
          properties: {
            assetName: {
              type: 'string',
              title: '资产名称',
              'x-validator': [
                { required: true, message: '请输入资产名称' },
              ],
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入资产名称'
              }
            },
            shortName: {
              type: 'string',
              title: '资产编号',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入资产编号'
              },
              'x-decorator': 'FormItem',
            },
            ipv4: {
              type: 'string',
              title: 'IPV4',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入IPV4'
              },
              'x-decorator': 'FormItem',
            },
            ipv6: {
              type: 'string',
              title: 'IPV6',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入IPV6'
              },
              'x-decorator': 'FormItem',
            },
            assetMac: {
              type: 'string',
              title: 'MAC地址',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入MAC地址'
              },
              'x-decorator': 'FormItem',
            },
            protectLevel: {
              type: 'string',
              title: '等保级别',
              'x-component': 'Select',
              'x-component-props': {
                placeholder: '请选择等保级别'
              },
              'x-decorator': 'FormItem',
            },
            assetValue: {
              type: 'string',
              title: '资产价值',
              'x-component': 'Select',
              'x-component-props': {
                placeholder: '请选择资产价值'
              },
              'x-decorator': 'FormItem',
            },
            assetType: {
              type: 'string',
              title: '资产类型',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入资产类型'
              },
              'x-decorator': 'FormItem',
              'x-validator': [
                { required: true, message: '请输入资产类型' },
              ],
            },
            osType: {
              type: 'string',
              title: '系统类型',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入系统类型'
              },
              'x-decorator': 'FormItem',
            },
            osVersion: {
              type: 'string',
              title: '系统版本',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入系统版本'
              },
              'x-decorator': 'FormItem',
            },
            osProtocol: {
              type: 'string',
              title: '登录协议',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入登录协议'
              },
              'x-decorator': 'FormItem',
            },
            osPort: {
              type: 'string',
              title: '登录端口',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入登录端口'
              },
              'x-decorator': 'FormItem',
            },
            loginUserName: {
              type: 'string',
              title: '登录账号',
              'x-component': 'Select',
              'x-component-props': {
                placeholder: '请选择登录账号'
              },
              'x-decorator': 'FormItem',
            },
            manageUserName: {
              type: 'string',
              title: '提权账号',
              'x-component': 'Select',
              'x-component-props': {
                placeholder: '请选择提权账号'
              },
              'x-decorator': 'FormItem',
            },
            assetCom: {
              type: 'string',
              title: '串口信息',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入串口信息'
              },
              'x-decorator': 'FormItem',
            },
            assetComBps: {
              type: 'string',
              title: '串口波特率',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入串口波特率'
              },
              'x-decorator': 'FormItem',
            },
            assetBasedOs: {
              type: 'string',
              title: '适用系统',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入适用系统'
              },
              'x-decorator': 'FormItem',
            },
            assetVersion: {
              type: 'string',
              title: '适用版本',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入适用版本'
              },
              'x-decorator': 'FormItem',
            },
            boardMachine: {
              type: 'string',
              title: '跳板机',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入跳板机'
              },
              'x-decorator': 'FormItem',
            },
            masterId: {
              type: 'string',
              title: '负责人',
              'x-component': 'Select',
              'x-component-props': {
                placeholder: '请选择负责人'
              },
              'x-decorator': 'FormItem',
            },
            description: {
              type: 'string',
              title: '描述',
              'x-component': 'Input.TextArea',
              'x-component-props': {
                placeholder: '请输入描述'
              },
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                gridSpan: 2
              }
            }
          }
        },
      }
    },
  }
}
