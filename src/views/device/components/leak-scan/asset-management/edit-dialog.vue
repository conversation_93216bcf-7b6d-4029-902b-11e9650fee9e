<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="700px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider v-if="initReady" :form="form">
        <SchemaField :schema="schema" :scope="{ formTab }"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave"
        >保存</a-button
      >
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{
        isView ? "关闭" : "取消"
      }}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent,ref, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm } from '@formily/core'
import { SCHEMA } from './config';
import { FormTab } from '@formily/antdv-x3'
import { cloneDeep } from 'lodash'
export default defineComponent({
name: 'EditList',
props: {
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add'
  },
  defaultData: {
      type: Object,
      default: () => ({})
    },
},
emits: ['update:visible', 'on-success'],
setup (props, ctx) {
  const { proxy } = getCurrentInstance()
  const { visible, mode, defaultData } = toRefs(props)
  // const isAdd = computed(() => mode.value === 'add')
  const isView = computed(() => mode.value === 'view')
  const dialogVisible = computed(() => visible.value)
  const title = computed(() => {
    const titleMap = {
      add: '新增',
      edit: '编辑',
      view: '查看'
    }
    return `${titleMap[mode.value]}资产`
  })

  const state = reactive({
    loading: false
  })

  const form = shallowRef({});

  watch(
    () => visible.value,
    async (val) => {
      if (!val) return
      await nextTick()
      await initForm()
    }
  )
const schema = ref(null)
    const initReady = ref(false)
    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      const formData = cloneDeep(defaultData.value)
      form.value = createForm({
        values: formData??{}
      });
      schema.value = SCHEMA;
      nextTick(() => {
      form.value.setPattern(patternMap[mode.value])
      })
      initReady.value = true;
    }

  const handleClose = async () => {
    await form.value.setValues(cloneDeep(form.value.initialValues), 'overwrite')
    await form.value.reset()
    ctx.emit('update:visible', false);
      nextTick(() =>  initReady.value = false)
  }

  const handleSave = async () => {
    await form.value.validate()
    try {
      state.loading = true
      const params = cloneDeep(form.value.values)
      ctx.emit('on-success', {
        mode: mode.value,
        data: {
          ...params,
        },
        callback: handleClose
      })
      nextTick(() =>  initReady.value = false)
      handleClose()
    } finally {
      state.loading = false
    }
  }

  onMounted(() => {
  })

  const formTab = FormTab.createFormTab();
  return {
    title,
    isView,
    form,
    schema,
    formTab,
    ...toRefs(state),
    dialogVisible,
    handleClose,
    handleSave,
    initReady
  }
}
})
</script>
