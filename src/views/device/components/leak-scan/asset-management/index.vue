<template>
  <div>
    <DynamicSearch
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    />
    <!-- <a-space align="start" class="vul-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add')">
        新增
      </a-button>
    </a-space> -->
    <a-table
      :scroll="{ y: 'calc(100vh - 330px)' }"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column }">
        <template v-if="column.key === 'operation'">
          <!-- <a-button type="link" @click="handleOpenDialog('view', record)"
            >查看</a-button
          > -->
          <!-- <a-button type="link" @click="handleOpenDialog('edit', record)"
            >编辑</a-button
          >
          <a-button type="link" danger @click="handleDelete(record)"
            >删除</a-button
          > -->
        </template>
      </template>
    </a-table>
    <!-- <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="handleSave"
    /> -->
  </div>
</template>

<script>
import DynamicSearch from "@/components/dynamic-search/index.vue";
import { SEARCH_CONFIG, COLUMNS } from "./config";
import { usePagination } from "vue-request";
import {
  computed,
  getCurrentInstance,
  reactive,
  createVNode,
  toRefs,
  nextTick,
  inject
} from "vue";
import { getAssetListByPagination } from "@/request/api-device-leak-scan.js";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
// import EditDialog from "./edit-dialog.vue";
import { cloneDeep } from "lodash";

export default {
  components: {
    DynamicSearch,
    // EditDialog,
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const deviceSafetyId = inject("deviceSafetyId");

    const state = reactive({
      searchParams: {},
      dialogConfig: {
        visible: false,
        mode: "add",
        defaultData: {},
      },
    });
    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getAssetListByPagination, {
        manual: true,
        defaultParams: {
          deviceSafetyId: deviceSafetyId.value,
          ...state.searchParams
        },
        formatResult: ({ data = {} }) => ({
          items: data?.data ?? [],
          total: data?.total ?? 0,
        }),
        pagination: {
          currentKey: "pageNum",
          pageSizeKey: "pageSize",
        },
      });

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`,
    }));

    const dataSource = computed(() => {
      return data.value?.items || [];
    });

    const handleDelete = async ({ id }) => {
      proxy.$confirm({
        title: "删除",
        content: "删除后信息将无法找回，是否进行删除?",
        icon: createVNode(ExclamationCircleOutlined),
        okText: "确定",
        okType: "warning",
        cancelText: "取消",
        async onOk() {
          // await deleteIpsPolicy({ id })
          reload();
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    };

    const refreshTableData = (isReload = true) => {
      const pageNum = isReload ? 1 : pagination.value.current
      const pageSize = pagination.value.pageSize
      run({
        deviceSafetyId: deviceSafetyId.value,
        pageNum,
        pageSize,
        pageDomain: {
          pageNum,
          pageSize,
        },
        ...state.searchParams,
      });
    };

    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        deviceSafetyId: deviceSafetyId.value,
        pageSize: pag.pageSize,
        pageNum: pag?.current,
        pageDomain: {
          pageSize: pag.pageSize,
          pageNum: pag?.current,
        },
        ...state.searchParams
      })
    }

    const handleSearch = async (params = {}) => {
      state.searchParams = cloneDeep(params);
      await nextTick();

      refreshTableData();
    };
    // const handleOpenDialog = (mode = "add", row = {}) => {
    //   console.log(row);
    //   state.dialogConfig.visible = true;
    //   state.dialogConfig.mode = mode;
    //   state.dialogConfig.defaultData = row;
    // };

    const columns = computed(() => {
      return COLUMNS.map(column => {
        if (column.customRender) {
          return {
            ...column,
            customRender: (args) => column.customRender(args, pagination.value)
          };
        }
        return column;
      });
    });

    return {
      SEARCH_CONFIG,
      columns,
      dataSource,
      pagination,
      ...toRefs(state),
      loading,
      // handleOpenDialog,
      handleDelete,
      handleSearch,
      handleTableChange
    };
  },
};
</script>
