<!-- 漏洞扫描 配置弹窗 -->

<template>
  <a-drawer
    v-model:visible="dialogVisible"
    placement="right"
    width="80%"
    :keyboard="false"
    :maskClosable="false"
    :onClose="handleClose"
  >
    <template #title>
      <a-tabs type="card" v-model:activeKey="activeKey">
        <a-tab-pane
          v-for="tab in LEAK_SCAN_TABS"
          :key="tab.componentName"
          :tab="tab.label"
        >
        </a-tab-pane>
      </a-tabs>
    </template>
    <a-spin :spinning="loading">
      <component :key="activeKey" :is="activeKey" />
    </a-spin>
  </a-drawer>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch } from 'vue'
import { LEAK_SCAN_TABS } from './config';
import AssetManagement from './asset-management/index.vue'
import TaskManagement from './task-management/index.vue'
import ReportManagement from './report-management/index.vue';
import AccountManagement from './account-management/index.vue'


export default defineComponent({
  name: 'LeakScan',
  components: {
    AssetManagement,
    TaskManagement,
    ReportManagement,
    AccountManagement
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible'],
  setup (props, ctx) {
    const { visible } = toRefs(props)
    const dialogVisible = computed(() => visible.value)

    const state = reactive({
      loading: false,
      activeKey: LEAK_SCAN_TABS[0].componentName
    })

  watch(() => state.activeKey, (val) => {
    console.log(val)
    })

    watch(
      () => visible.value,
      async (val) => {
        console.log('leak-scan-visible: ', val)
      },
      { immediate: true }
    )

    const handleClose = async () => {
      ctx.emit('update:visible', false)
    }

    return {
      ...toRefs(state),
      LEAK_SCAN_TABS,
      dialogVisible,
      handleClose
    }
  }
})
</script>

<style lang="less">
.vul-table-operate {
  margin: 8px 0;
  display: flex;
  justify-content: flex-end;
}
</style>
