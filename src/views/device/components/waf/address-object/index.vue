<template>
  <div class="address-object--wrapper">
    <!-- <DynamicSearch :config="SEARCH_CONFIG" @search="handleSearch" /> -->
    <a-space align="start" class="waf-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add')">
        新增
      </a-button>
    </a-space>
    <a-table
      :scroll="{y: 'calc(100vh - 330px)'}"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button type="link" @click="handleOpenDialog('view', record)">查看</a-button>
          <a-button type="link" :disabled="['any'].includes(record.name)" @click="handleOpenDialog('edit', record)">编辑</a-button>
          <a-button type="link" :disabled="['any'].includes(record.name)" danger @click="handleDelete(record)">删除</a-button>
        </template>
      </template>
    </a-table>
    <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="handleSave" />
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, inject, unref, computed, onMounted, getCurrentInstance, createVNode } from 'vue'
import { usePagination } from 'vue-request'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

// import DynamicSearch from "@/components/dynamic-search/index.vue";
import { getWafAddressObjectList, addWafAddressObject, updateWafAddressObject, deleteWafAddressObject } from '@/request/api-device-waf'
import { SEARCH_CONFIG, columns as COLUMNS } from './config';

import EditDialog from './edit-dialog.vue'

export default defineComponent({
  components: {
    EditDialog,
    // DynamicSearch
  },
  setup () {
    const { proxy } = getCurrentInstance()
    const { deviceSafetyId } = inject('rowData')

    const state = reactive({
      // searchParams: { name: '' },
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })
    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getWafAddressObjectList, {
      manual: true,
      defaultParams: {
        deviceSafetyId,
        // filter_data: state.searchParams
      },
      formatResult: ({data = {}}) => ({ items: data?.addrObject?.obj ?? [], total: data?.addrObject?.obj?.length ?? 0 }),
      pagination: {
        currentKey: 'page_num',
        pageSizeKey: 'page_size',
      }
    })

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条`
    }))

    const dataSource = computed(() => {
      return data.value?.items || []
    })

    /**
     * 处理表格分页变化
     * @param {Object} pag 分页参数
     */
    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        deviceSafetyId,
        page_size: pag.pageSize,
        page_num: pag?.current,
        // filter_data: state.searchParams
      })
    }

    /**
     * 处理搜索
     * @param {Object} params 搜索参数
     */
    const handleSearch = (params = {}) => {
      // state.searchParams = params;
      refreshTableData();
    }

    /**
     * 刷新表格数据
     * @param {boolean} isReload 是否重新加载第一页
     */
    const refreshTableData = (isReload = true) => {
      run({
        deviceSafetyId,
        page_num: isReload ? 1 : pagination.value.current,
        page_size: pagination.value?.pageSize,
        // filter_data: state.searchParams
      })
    }

    /**
     * 保存地址对象数据
     * @param {Object} options 保存选项
     */
    const handleSave = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addWafAddressObject,
        edit: updateWafAddressObject
      }
      try {
        const res = await requestMethodEnum[mode]?.({...data, deviceSafetyId})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        refreshTableData()
      } catch (err) {
        // const errMsg = isAdd ? '新增失败！' : '更新失败！';
        // proxy.$message.error(err.message || errMsg)
        callback(true, err)
      }
    }

    /**
     * 删除地址对象
     * @param {Object} row 行数据
     */
    const handleDelete = async (row = {}) => {
      proxy.$confirm({
        title: '删除',
        content: `确认删除该条数据吗?`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteWafAddressObject({ name: row.name, deviceSafetyId })
            proxy.$message.success('删除成功！')
            refreshTableData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
    }

    /**
     * 打开编辑弹窗
     * @param {string} mode 操作模式
     * @param {Object} row 行数据
     */
    const handleOpenDialog = (mode = 'add', row = {}) => {
      state.dialogConfig.visible = true
      state.dialogConfig.mode = mode
      state.dialogConfig.defaultData = row
    }

    onMounted(() => {
      refreshTableData()
    })

    const columns = computed(() => {
      return COLUMNS.map(column => {
        if (column.customRender) {
          return {
            ...column,
            customRender: (args) => column.customRender(args, pagination.value)
          };
        }
        return column;
      });
    });

    return {
      ...toRefs(state),
      SEARCH_CONFIG,
      pagination,
      loading,
      dataSource,
      handleSearch,
      refreshTableData,
      handleOpenDialog,
      handleTableChange,
      handleSave,
      handleDelete,
      columns
    }
  }
})
</script>
