import { join, compact } from 'lodash';
import { ipv4RangeReg, ipv4SubnetReg, ipv6RangeReg, ipv6SubnetReg } from "@/utils/reg";
import { ipv4Reg, ipv6Reg } from "@/utils/reg";

import CellExpand from '@/components/cell-expand'

/** @type {*} 地址字段映射 */
export const ADDRESS_FIELD_MAP = {
  0: 'host',
  1: 'net',
  10: 'host',
  11: 'net'
};

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '内容',
    dataIndex: 'item',
    key: 'item',
    customRender: ({ record }) => {
      const items = record.item?.map(item => {
        const value = item[ADDRESS_FIELD_MAP[item.type]]
        if (value) return value
        const range = compact([item.range1, item.range2]);
        return join(range, ' - ');
      }) ?? [];
      if (!items?.length) return '-'
      return <CellExpand title="内容" data={items} />
    },
  },
  {
    title: '引用',
    dataIndex: 'ref',
    key: 'ref'
  },
  {
    title: '描述',
    dataIndex: 'desc',
    key: 'desc'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 220
  }
]

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 85
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' }
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          }
        },
        desc: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          }
        },
        item: {
          type: 'array',
          'x-validator': [
            { required: true, message: '请添加地址节点' }
          ],
          title: '地址节点',
          default: [],
          'x-decorator': 'FormItem',
          'x-component': 'ArrayTable',
          'x-component-props': {
            pagination: { pageSize: 10 },
            rowKey: 'app',
          },
          items: {
            type: 'object',
            properties: {
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { width: 120, title: 'IP版本', key: 'ipType', dataIndex: 'ipType' },
                properties: {
                  ipType: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '请选择'
                    },
                    enum: [
                      { label: 'Ipv4', value: 'ipv4' },
                      { label: 'Ipv6', value: 'ipv6' },
                    ]
                  }
                }
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { width: 120, title: 'IP类型', key: 'type', dataIndex: 'type' },
                properties: {
                  type: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '请选择'
                    },
                    'x-reactions': {
                      dependencies: ['.ipType'],
                      fulfill: {
                        state: {
                          'dataSource': `{{
                            $deps[0] === 'ipv4' ? [
                              { label: '主机', value: 0 },
                              { label: '子网', value: 1 },
                              { label: '范围', value: 2 }
                            ] : [
                              { label: '主机', value: 10 },
                              { label: '子网', value: 11 },
                              { label: '范围', value: 12 }
                            ]}}`
                        }
                      }
                    },
                  }
                }
              },
              column3: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '对象信息', key: 'value', dataIndex: 'value' },
                properties: {
                  value: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    'x-component': 'Input',
                    'x-component-props': {
                      placeholder: '请输入'
                    },
                    'x-validator': (value, callback, ctx) => {
                      const ipType = ctx.field.query('..[].ipType').value();
                      const type = ctx.field.query('..[].type').value();
                      const typeMap = {
                        ipv4: {
                          1: {
                            reg:  ipv4SubnetReg,
                            message: `请输入正确的对象信息，如***********/24`
                          },
                          0: {
                            reg:  ipv4Reg,
                            message: `请输入正确的对象信息，如***********`
                          },
                          2: {
                            reg:  ipv4RangeReg,
                            message: `请输入正确的对象信息，如***********-************`
                          }
                        },
                        ipv6: {
                          11: {
                            reg:  ipv6SubnetReg,
                            message: `请输入正确的对象信息，如2001:db8::/64`
                          },
                          10: {
                            reg:  ipv6Reg,
                            message: `请输入正确的对象信息，如2001:db8::1`
                          },
                          12: {
                            reg:  ipv6RangeReg,
                            message: `请输入正确的对象信息，如2001:db8::1-2001:db8::10`
                          }
                        }
                      }
                      const { reg, message } = typeMap[ipType][type] ?? typeMap.ipv4[0];
                      if (reg.test(value)) return '';
                      return message;
                    },
                  },
                }
              },
              column4: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: '操作',
                  key: 'operations',
                  dataIndex: 'operations',
                  width: 120,
                  fixed: 'right'
                },
                properties: {
                  operations: {
                    type: 'void',
                    'x-component': 'FormItem',
                    properties: {
                      remove: {
                        type: 'void',
                        'x-component': 'ArrayTable.Remove',
                        'title': '删除'
                      }
                    }
                  }
                }
              }
            }
          },
          properties: {
            add: {
              type: 'void',
              'x-component': 'ArrayTable.Addition',
              title: '添加'
            }
          }
        },
      }
    }
  }
}

