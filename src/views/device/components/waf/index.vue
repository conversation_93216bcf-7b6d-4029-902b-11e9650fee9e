<!-- web应用防火墙 配置弹窗 -->

<template>
  <a-drawer
    v-model:visible="dialogVisible"
    placement="right"
    width="80%"
    :keyboard="false"
    :maskClosable="false"
    :onClose="handleClose"
  >
    <template #title>
      <a-tabs type="card" v-model:activeKey="activeKey">
        <a-tab-pane v-for="tab in TABS" :key="tab.componentName" :tab="tab.label">
        </a-tab-pane>
      </a-tabs>
    </template>
    <component :is="activeKey" />
  </a-drawer>
</template>

<script lang="js">
import { computed, defineComponent, provide, reactive, toRefs, watch } from 'vue';
import AddressObject from './address-object/index.vue';
import { TABS } from './config';
import FloodProtectionPolicy from './flood-protection-policy/index.vue';
import FloodProtection from './flood-protection/index.vue';
import IpBlackList from './ip-black-list/index.vue';
import IpWhiteList from './ip-white-list/index.vue';
import ServiceObject from './service-object/index.vue';
import TimeObject from './time-object/index.vue';
import UrlObject from './url-object/index.vue';
import VirtualService from './virtual-service/index.vue';

export default defineComponent({
  name: 'Waf',
  components: {
    AddressObject,
    UrlObject,
    ServiceObject,
    TimeObject,
    VirtualService,
    IpBlackList,
    IpWhiteList,
    FloodProtection,
    FloodProtectionPolicy
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible'],
  setup (props, ctx) {
    const { visible, rowData } = toRefs(props)
    const dialogVisible = computed(() => visible.value)

    const state = reactive({
      activeKey: TABS[0].componentName
    })

    provide('rowData', {
      rowData,
      deviceSafetyId: rowData.value.id,
    })

    watch(
      () => visible.value,
      async (val) => {
        console.log('waf-drawer-visible', val)
      },
      { immediate: true }
    )

    const handleClose = async () => {
      ctx.emit('update:visible', false)
    }

    return {
      ...toRefs(state),
      TABS,
      dialogVisible,
      handleClose
    }
  }
})
</script>

<style lang="less">
.waf-table-operate {
  display: flex;
  justify-content: flex-end;
  margin: 8px 0;
}
</style>
