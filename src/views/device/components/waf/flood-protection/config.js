import {
        getWafAddressObjectList,
        getWafFloodProtectionStrategyList
} from '@/request/api-device-waf';
import { useAsyncDataSource } from "@/utils/util";

export const getColumns = ({ handleChangeStatus }) => {
        return [{
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                fixed: 'left'
        },
        {
                title: '描述',
                dataIndex: 'description',
                key: 'description',
        },
        {
                title: '地址对象',
                dataIndex: 'addrObject',
                key: 'addrObject',
        },
        {
                title: '防护策略',
                dataIndex: 'strategyName',
                key: 'strategyName',
        },
        {
                title: '启用状态',
                dataIndex: 'enable',
                key: 'enable',
                customRender: ( { record }) => {
                        const handleClick = async () => {
                                try {
                                        record.statusLoading = true;
                                        const enable = !record.enable;
                                        console.log('enable', enable)
                                        await handleChangeStatus?.({...record, enable});
                                        record.enable = enable;
                                } finally {
                                        record.statusLoading = false;
                                }
                        }
                        return <a-switch
                                checked={record.enable}
                                loading={!!record.statusLoading}
                                checkedValue={true}
                                unCheckedValue={false}
                                onClick={handleClick}
                        />
                }
        },
        {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                width: 220,
                fixed: 'right'
        }
]}

export const getSchema = (deviceSafetyId) => {
        return {
                type: 'object',
                properties: {
                        layout: {
                                type: 'void',
                                'x-component': 'FormLayout',
                                'x-component-props': {
                                        labelWidth: 120
                                },
                                properties: {
                                        enable: {
                                                type: 'boolean',
                                                title: '启用',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'Switch',
                                        },
                                        name: {
                                                type: 'string',
                                                title: '名称',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'Input',
                                                'x-component-props': {
                                                        placeholder: '请输入名称'
                                                },
                                                'x-validator': [
                                                        { required: true, message: '请输入名称' }
                                                ]
                                        },
                                        description: {
                                                type: 'string',
                                                title: '描述',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'Input',
                                                'x-component-props': {
                                                        rows: 1,
                                                        placeholder: '请输入描述'
                                                }
                                        },
                                        addrObject: {
                                                type: 'string',
                                                title: '地址对象',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'Select',
                                                'x-validator': [
                                                        { required: true, message: '请选择地址对象' }
                                                ],
                                                'x-component-props': {
                                                        placeholder: '请选择地址对象'
                                                },
                                                'x-reactions': useAsyncDataSource(async () => {
                                                        const { data } = await getWafAddressObjectList({ deviceSafetyId });
                                                        return data.addrObject.obj?.map(item => ({ label: item.name, value: item.name }))
                                                })
                                        },
                                        strategyName: {
                                                type: 'string',
                                                title: '防护策略',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'Select',
                                                'x-validator': [
                                                        { required: true, message: '请选择防护策略' }
                                                ],
                                                'x-component-props': {
                                                        placeholder: '请选择防护策略'
                                                },
                                                'x-reactions': useAsyncDataSource(async () => {
                                                        const { data } = await getWafFloodProtectionStrategyList({ deviceSafetyId });
                                                        return data.data?.map(item => ({ label: item.name, value: `${item.name}#${item.id}` }));
                                                }),
                                                
                                        },
                                }
                        }
                }
        }
}