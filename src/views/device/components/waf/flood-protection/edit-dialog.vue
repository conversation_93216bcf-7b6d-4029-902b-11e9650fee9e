<template>
        <a-modal
                v-model:visible="dialogVisible"
                :title="title"
                width="1000px"
                :keyboard="false"
                :maskClosable="false"
                @cancel="handleClose"
        >
                <a-spin :spinning="loading">
                        <FormProvider v-if="initReady" :form="form">
                                <SchemaField :schema="schema"/>
                        </FormProvider>
                </a-spin>
                <template #footer>
                        <a-button
                                v-if="!isView"
                                type="primary"
                                :loading="loading"
                                @click="handleSave"
                        >
                                保存
                        </a-button>
                        <a-button
                                :type="isView ? 'primary' : 'default'"
                                @click="handleClose"
                        >
                                {{isView ? '关闭' : '取消'}}
                        </a-button>
                </template>
        </a-modal>
</template>

<script>
import { createForm } from '@formily/core';
import { cloneDeep } from 'lodash';
import { computed, defineComponent, inject, nextTick, reactive, ref, shallowRef, toRefs, watch } from 'vue';
import { getSchema } from './config';

export default defineComponent({
        name: 'EditDialog',
        props: {
                visible: {
                        type: Boolean,
                        default: false
                },
                mode: {
                        type: String,
                        default: 'add'
                },
                defaultData: {
                        type: Object,
                        default: () => ({})
                }
        },
        emits: ['update:visible', 'on-success'],
        setup(props, ctx) {
                const { deviceSafetyId } = inject('rowData')
                const { visible, mode, defaultData } = toRefs(props)
                const isView = computed(() => mode.value === 'view')
                const dialogVisible = computed(() => visible.value)
                const title = computed(() => {
                        const titleMap = {
                                add: '新增',
                                edit: '编辑',
                                view: '详情'
                        }
                        return `${titleMap[mode.value]}Flood防护`;
                })
                const state = reactive({
                        loading: false
                })
                const form = shallowRef({})
                const schema = shallowRef({})
                watch(
                        () => visible.value,
                        async (val) => {
                                if (!val) return
                                await nextTick()
                                await initForm()
                        }
                )

                const initReady = ref(false)
                const initForm = async () => {
                        const patternMap = {
                                add: 'editable',
                                view: 'readPretty',
                                edit: 'editable'
                        }
                        const formData = cloneDeep(defaultData.value)
                        form.value = createForm({
                                values: {
                                        ...(formData??{}),
                                        safetyId: deviceSafetyId
                                }
                        });
                        schema.value = getSchema(deviceSafetyId)
                        nextTick(() => {
                                form.value.setPattern(patternMap[mode.value])
                        })
                        initReady.value = true
                }

                const handleClose = async (hasError = false) => {
                        state.loading = false
                        if (hasError === true) return;
                        await form.value.setValues(cloneDeep(form.value.initialValues), 'overwrite')
                        await form.value.reset()
                        ctx.emit('update:visible', false)
                        nextTick(() => initReady.value = false)
                }

                const handleSave = async () => {
                        await form.value.validate()
                        state.loading = true
                        const params = cloneDeep(form.value.values)
                        // 处理参数
                        ctx.emit('on-success', {
                                mode: mode.value,
                                data: {...params},
                                callback: handleClose
                        })
                }

                return {
                        title,
                        isView,
                        form,
                        schema,
                        initReady,
                        ...toRefs(state),
                        dialogVisible,
                        handleClose,
                        handleSave
                }
        }
})

</script>