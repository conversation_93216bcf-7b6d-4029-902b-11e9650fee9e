<template>
        <div class = "flood-protection-wrapper">
                <a-space align="start" class="waf-table-operate">
                        <a-button type="primary" @click="handleOpenDialog('add')">
                                新增
                        </a-button>
                </a-space>
                <a-table
                        :scroll="{y: 'calc(100vh - 330px)'}"
                        :data-source="dataSource"
                        :columns="columns"
                        :loading="loading"
                        :pagination="pagination"
                        @change="handleTableChange"
                >
                        <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'operation'">
                                        <a-button type="link" @click="handleOpenDialog('view', record)">详情</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" @click="handleOpenDialog('edit', record)">编辑</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" danger @click="handleDelete(record)">删除</a-button>
                                </template>
                        </template>
                </a-table>
                <EditDialog
                        v-model:visible="dialogConfig.visible"
                        v-bind="dialogConfig"
                        @on-success="handleSave"
                />
        </div>
</template>

<script>
import {
        addWafFloodProtection,
        deleteWafFloodProtection,
        getWafFloodProtectionList,
        updateWafFloodProtection
} from '@/request/api-device-waf';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { computed, createVNode, defineComponent, getCurrentInstance, inject, onMounted, reactive, toRefs } from 'vue';
import { usePagination } from 'vue-request';
import { getColumns } from './config.js';
import EditDialog from './edit-dialog.vue';

export default defineComponent({
        name: 'FloodProtection',
        components: {
                EditDialog
        },
        setup() {
                const { proxy } = getCurrentInstance()
                const { deviceSafetyId } = inject('rowData')
                const state = reactive({
                        dialogConfig: {
                                visible: false,
                                mode: 'add',
                                defaultData: {}
                        }
                })
                const {
                        data,
                        run,
                        loading,
                        total,
                        current,
                        pageSize,
                        reload
                } = usePagination(getWafFloodProtectionList, {
                        manual: true,
                        defaultParams: {
                                deviceSafetyId,
                        },
                        formatResult: ({ data = {} }) => ({
                                items: data?.data ?? [], total: data?.total ?? 0
                        }),
                        pagination: {
                                currentKey: 'page_num',
                                pageSizeKey: 'page_size',
                        }
                })

                const handleOpenDialog = (mode = 'add', row = {}) => {
                        state.dialogConfig.visible = true
                        state.dialogConfig.mode = mode
                        state.dialogConfig.defaultData = row
                }

                const pagination = computed( () => ({
                        total: total.value,
                        current: current.value,
                        pageSize: pageSize.value,
                        showQuickJumper: true,
                        showSizeChanger: true,
                        showTotal: total => `共 ${total} 条`
                }))

                const refreshTableData = (isReload = true) => {
                        run ({
                                deviceSafetyId,
                                page_num: isReload ? 1 : pagination.value.current,
                                page_size: pagination.value?.pageSize,
                        })
                }

                const dataSource = computed(() =>  data.value?.items || [])

                const handleSave = async ({ mode, data, callback }) => {
                        const requestMethodEnum = {
                                add: addWafFloodProtection,
                                edit: updateWafFloodProtection
                        }
                        try {
                                const strategy = data.strategyName;
                                const [strategyName, strategyId] = strategy.split('#');
                                let insertData = {
                                        enable: data.enable,
                                        name: data.name,
                                        description: data.description,
                                        addrObject: data.addrObject,
                                        strategyId: parseInt(strategyId, 10),
                                        strategyName: strategyName
                                }
                                if( mode === 'edit' ) insertData.id = data.id
                                const res = await requestMethodEnum[mode]?.({
                                        ...insertData
                                })
                                if( res.code !== '00000' ) throw new Error(res.msg)
                                proxy.$message.success(mode === 'add' ? '新增成功！' : '更新成功！')
                                callback();
                                refreshTableData();
                        } catch (error) {
                                callback(true, error)
                        }
                }

                const handleDelete = (row) => {
                        proxy.$confirm({
                                title: '删除',
                                content: `确认删除该条数据吗?`,
                                icon: createVNode(ExclamationCircleOutlined),
                                okText: '确定',
                                okType: 'warning',
                                cancelText: '取消',
                                async onOk () {
                                        try {
                                                await deleteWafFloodProtection({ ids: [row.id] })
                                                proxy.$message.success('删除成功！')
                                                refreshTableData()
                                        } catch (err) {
                                                // proxy.$message.error('删除失败！')
                                        }
                                },
                                onCancel () {
                                        console.log('Cancel')
                                }
                        })
                }

                const handleChangeStatus = async (row = {}) => {
                        await updateWafFloodProtection( { ...row } );
                }

                onMounted(() => {
                        refreshTableData()
                })

                return {
                        ...toRefs(state),
                        pagination,
                        loading,
                        columns: getColumns({handleChangeStatus}),
                        dataSource,
                        handleOpenDialog,
                        handleSave,
                        handleDelete,
                }
        }
})

</script>