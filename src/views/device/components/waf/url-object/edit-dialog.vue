<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="800px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep } from 'lodash'

import { SCHEMA } from './config';

export default defineComponent({
props: {
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add'
  },
  defaultData: {
    type: Object,
    default: () => ({})
  }
},
emits: ['update:visible', 'on-success'],
setup (props, ctx) {
  const { proxy } = getCurrentInstance()
  const { visible, mode, defaultData } = toRefs(props)
  // const isAdd = computed(() => mode.value === 'add')
  const isView = computed(() => mode.value === 'view')
  const dialogVisible = computed(() => visible.value)
  const title = computed(() => {
    const titleMap = {
      add: '新增',
      edit: '编辑',
      view: '查看'
    }
    return `${titleMap[mode.value]}URL对象`
  })

  const state = reactive({
    loading: false
  })

  const form = createForm()

  watch(
    () => visible.value,
    async (val) => {
      if (!val) return
      await nextTick()
      await initForm()
    }
  )

  /**
   * 初始化表单数据
   */
  const initForm = async () => {
    const patternMap = {
      add: 'editable',
      view: 'readPretty',
      edit: 'editable'
    }
    const nameFieldMap = {
      add: 'editable',
      view: 'readPretty',
      edit: 'disabled'
    }
    const nameField = form.query('name').take()
    form.setPattern(patternMap[mode.value])
    nameField?.setPattern(nameFieldMap[mode.value])
    if (mode.value === 'add') return
    const formData = cloneDeep(defaultData.value)
    formData.url_list = formData.url?.map(item => ({url: item})) ?? []
    form.setValues(formData)
  }

  /**
   * 关闭弹窗处理
   * @param {boolean} hasError 是否有错误
   */
  const handleClose = async (hasError = false) => {
    state.loading = false
    if (hasError === true) return;
    await form.setValues(cloneDeep(form.initialValues), 'overwrite')
    await form.reset()
    ctx.emit('update:visible', false)
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    await form.validate()
    state.loading = true
    const params = cloneDeep(form.values)
    params.url_list = params.url_list.map(item => item.url)
    ctx.emit('on-success', {
      mode: mode.value,
      data: {...params},
      callback: handleClose
    })
  }

  return {
    title,
    isView,
    form,
    schema: SCHEMA,
    ...toRefs(state),
    dialogVisible,
    handleClose,
    handleSave
  }
}
})
</script>
