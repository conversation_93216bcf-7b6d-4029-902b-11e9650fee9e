import CellExpand from '@/components/cell-expand'

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name'
  },
  {
    title: '内容',
    dataIndex: 'url',
    key: 'url',
    customRender: ({ record }) => {
      if (!record.url?.length) return '-'
      return <CellExpand title="内容" data={record.url} />
    }
  },
  {
    title: '完全匹配',
    dataIndex: 'match_style',
    key: 'match_style',
    customRender: ({ record }) => {
      return record.match_style === 0 ? '关闭' : '开启'
    },
  },
  {
    title: '引用',
    dataIndex: 'ref',
    key: 'ref'
  },
  {
    title: '引用位置',
    dataIndex: 'ref_mods',
    key: 'ref_mods'
  },
  {
    title: '描述',
    dataIndex: 'desc',
    key: 'desc'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 220
  }
]

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          },
          'x-validator': [
            { required: true, message: '请输入名称' }
          ],
        },
        desc: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          }
        },
        match_style: {
          type: 'boolean',
          title: 'URL完全匹配',
          default: 0,
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 1,
            unCheckedValue: 0
          }
        },
        url_list: {
          type: 'array',
          'x-validator': [
            { required: true, message: '请添加URL信息' }
          ],
          title: 'URL信息',
          default: [],
          'x-decorator': 'FormItem',
          'x-component': 'ArrayTable',
          'x-component-props': {
            pagination: { pageSize: 10 },
            scroll: { x: '100%' },
            rowKey: 'url',
          },
          items: {
            type: 'object',
            properties: {
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: 'URL', key: 'url', dataIndex: 'url' },
                properties: {
                  url: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    'x-component': 'Input',
                    'x-component-props': {
                      placeholder: '请输入'
                    },
                  },
                }
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: '操作',
                  key: 'operations',
                  dataIndex: 'operations',
                  width: 120,
                  fixed: 'right'
                },
                properties: {
                  operations: {
                    type: 'void',
                    'x-component': 'FormItem',
                    properties: {
                      remove: {
                        type: 'void',
                        'x-component': 'ArrayTable.Remove',
                        'title': '删除'
                      }
                    }
                  }
                }
              }
            }
          },
          properties: {
            add: {
              type: 'void',
              'x-component': 'ArrayTable.Addition',
              title: '添加'
            }
          }
        },
      }
    }
  }
}

