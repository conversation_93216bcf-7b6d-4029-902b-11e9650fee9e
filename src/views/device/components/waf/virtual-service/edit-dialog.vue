<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="800px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema" :scope="formScope"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, inject, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep } from 'lodash'

import { getWafAddressObjectList, getWafServiceObjectList } from '@/request/api-device-waf'
import { SCHEMA } from './config';

export default defineComponent({
props: {
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add'
  },
  defaultData: {
    type: Object,
    default: () => ({})
  }
},
emits: ['update:visible', 'on-success'],
setup (props, ctx) {
  const { proxy } = getCurrentInstance()
  const { visible, mode, defaultData } = toRefs(props)
  const { deviceSafetyId } = inject('rowData')
  // const isAdd = computed(() => mode.value === 'add')
  const isView = computed(() => mode.value === 'view')
  const dialogVisible = computed(() => visible.value)
  const title = computed(() => {
    const titleMap = {
      add: '新增',
      edit: '编辑',
      view: '查看'
    }
    return `${titleMap[mode.value]}虚拟服务`
  })

  const state = reactive({
    loading: false
  })

  const form = createForm()

  watch(
    () => visible.value,
    async (val) => {
      if (!val) return
      await nextTick()
      await initForm()
    }
  )

  const initForm = async () => {
    const patternMap = {
      add: 'editable',
      view: 'readPretty',
      edit: 'editable'
    }
    const nameFieldMap = {
      add: 'editable',
      view: 'readPretty',
      edit: 'disabled'
    }
    const nameField = form.query('name').take()
    form.setPattern(patternMap[mode.value])
    nameField?.setPattern(nameFieldMap[mode.value])
    if (mode.value === 'add') return
    const formData = cloneDeep(defaultData.value)
    if (formData?.ssl_protect?.length) formData.ssl_protect = formData.ssl_protect?.map(url => ({url})) ?? []
    if (formData?.real_server_param?.length) formData.real_server = formData.real_server_param?.map(({addr_obj, real_server_protocol, serv_obj, ...args}) => ({...args, addr_obj, real_server_protocol, serv_obj}))
    form.setValues({...formData})
  }

  /**
   * 获取地址对象数据源
   * @returns {Function} 返回获取数据源的函数
   */
  let addressReqPromise = null
  const getAddressDataSource = () => {
    return async (field) => {
      let data = []
      try {
        if (!addressReqPromise) addressReqPromise = getWafAddressObjectList({deviceSafetyId})
        const res = await addressReqPromise;
        data = res?.data?.addrObject?.obj ?? []
      } finally {
        field?.setDataSource(data?.map(({name}) => ({label: name, value: name})) ?? [])
      }
    }
  }

  /**
   * 获取服务对象数据源
   * @returns {Function} 返回获取数据源的函数
   */
  let serviceReqPromise = null
  const getServiceDataSource = () => {
    return async (field) => {
      let data = []
      try {
        if (!serviceReqPromise) serviceReqPromise = getWafServiceObjectList({deviceSafetyId})
        const res = await serviceReqPromise;
        data = res?.data?.serviceObj?.sev ?? []
      } finally {
        field?.setDataSource(data?.map(({name}) => ({label: name, value: name})) ?? [])
      }
    }
  }

  /**
   * 关闭弹窗处理
   * @param {boolean} hasError 是否有错误
   */
  const handleClose = async (hasError = false) => {
    state.loading = false
    if (hasError === true) return
    await form.setValues(cloneDeep(form.initialValues), 'overwrite')
    await form.reset()
    ctx.emit('update:visible', false)
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    await form.validate()
    state.loading = true
    const params = cloneDeep(form.values)
    params.ssl_protect = params.ssl_protect?.map(item => item.url) ?? []
    ctx.emit('on-success', {
      mode: mode.value,
      data: { ...params },
      callback: handleClose
    })
  }

  onMounted(() => {
    getAddressDataSource()?.();
    getServiceDataSource()?.();
  })
  return {
    title,
    isView,
    form,
    formScope: {
      getAddressDataSource,
      getServiceDataSource
    },
    schema: SCHEMA,
    ...toRefs(state),
    dialogVisible,
    handleClose,
    handleSave
  }
}
})
</script>
