/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'searchtxt',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称进行检索',
    },
  },
]

/** @type {*} 运行模式名称枚举 */
const RUN_MODE_LABEL_ENUM = {
  BRIDGE: 0,
  PROXY: 1,
  SINGLE: 2,
  ROUTE: 3
}

/** @type {*} 运行模式名称映射 */
const RUN_MODE_LABEL_MAP = {
  [RUN_MODE_LABEL_ENUM.BRIDGE]: '桥模式',
  [RUN_MODE_LABEL_ENUM.PROXY]: '代理模式',
  [RUN_MODE_LABEL_ENUM.SINGLE]: '单臂模式',
  [RUN_MODE_LABEL_ENUM.ROUTE]: '路由模式'
}

/** @type {*} 排序枚举 */
export const SORT_ENUM = {
  UP: 1,
  DOWN: -1,
  TOP: 0
}

/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns ({ handleChangeStatus, pagination }) {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      fixed: 'left'
    },
    {
      title: '运行模式',
      dataIndex: 'mode',
      key: 'mode',
      width: 120,
      customRender: ({ record }) => {
        return RUN_MODE_LABEL_MAP[record.mode] ?? '-'
      }
    },
    {
      title: 'IP',
      dataIndex: 'ip',
      key: 'ip',
      width: 120,
    },
    {
      title: '端口',
      dataIndex: 'port',
      key: 'port',
      width: 100,
      customRender: ({ record }) => {
        return record.port ?? ''
      }
    },
    {
      title: '应用类型',
      dataIndex: 'protocal',
      key: 'protocal',
      width: 120,
    },
    {
      title: '站点',
      dataIndex: 'website_safe',
      key: 'website_safe',
      width: 100,
    },
    {
      title: '地址',
      dataIndex: 'addr_obj',
      key: 'addr_obj',
      width: 120,
      customRender: ({ record }) => {
        if(record.real_server_param?.length) return record.real_server_param?.[0]?.addr_obj ?? ''
        return record.addr_obj ?? '-'
      }
    },
    {
      title: '服务',
      dataIndex: 'serv_obj',
      key: 'serv_obj',
      width: 120,
      customRender: ({ record }) => {
        if(record.real_server_param?.length) return record.real_server_param?.[0]?.serv_obj ?? ''
        return record.serv_obj ?? '-'
      }
    },
    {
      title: '协议',
      dataIndex: 'real_server_protocol',
      key: 'real_server_protocol',
      width: 120,
      customRender: ({ record }) => {
        if(record.real_server_param?.length) return record.real_server_param?.[0]?.real_server_protocol ?? ''
        return record.real_server_protocol ?? '-'
      }
    },
    {
      title: '启用',
      dataIndex: 'disable',
      key: 'disable',
      width: 120,
      fixed: 'right',
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const status = record.disable === 1 ? 0 : 1;
            await handleChangeStatus?.({...record, status});
            record.disable = status;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.disable}
          loading={!!record.statusLoading}
          checkedValue={0}
          unCheckedValue={1}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 360,
      fixed: 'right'
    }
  ]
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          },
          'x-validator': [
            { required: true, message: '请输入名称' }
          ],
        },
        mode: {
          type: 'string',
          title: '运行模式',
          'x-validator': [
            { required: true, message: '请选择运行模式' }
          ],
          default: RUN_MODE_LABEL_ENUM.BRIDGE,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择'
          },
          enum: [
            { label: RUN_MODE_LABEL_MAP[RUN_MODE_LABEL_ENUM.BRIDGE], value: RUN_MODE_LABEL_ENUM.BRIDGE },
            { label: RUN_MODE_LABEL_MAP[RUN_MODE_LABEL_ENUM.PROXY], value: RUN_MODE_LABEL_ENUM.PROXY },
            { label: RUN_MODE_LABEL_MAP[RUN_MODE_LABEL_ENUM.SINGLE], value: RUN_MODE_LABEL_ENUM.SINGLE },
            { label: RUN_MODE_LABEL_MAP[RUN_MODE_LABEL_ENUM.ROUTE], value: RUN_MODE_LABEL_ENUM.ROUTE },
          ]
        },
        ip_ver: {
          type: 'string',
          title: 'IP类型',
          'x-validator': [
            { required: true, message: '请选择IP类型' }
          ],
          default: 'ipv4',
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          'x-component-props': {
            isGroup: true,
            options: [
              { label: 'IPV4', value: '4' },
              { label: 'IPV6', value: '6' },
            ]
          },
          'x-reactions': {
            dependencies: ['.mode'],
            fulfill: {
              state: {
                visible: `{{[1, 2].includes($deps[0])}}`,
              }
            }
          }
        },
        ip: {
          type: 'string',
          title: 'IP地址',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入IP'
          },
          'x-validator': [
            { required: true, message: '请输入IP' },
            {
              ip: true
            }
          ],
          'x-reactions': {
            dependencies: ['.mode'],
            fulfill: {
              state: {
                visible: `{{[1, 2].includes($deps[0])}}`,
              }
            }
          }
        },
        port: {
          type: 'number',
          title: '端口',
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          'x-component-props': {
            placeholder: '请输入端口'
          },
          'x-validator': [
            { required: true, message: '请输入端口' },
            {
              format: 'integer'
            },
            {
              port: true
            }
          ],
          'x-reactions': {
            dependencies: ['.mode'],
            fulfill: {
              state: {
                visible: `{{[1, 2].includes($deps[0])}}`,
              }
            }
          }
        },
        load_balance: {
          type: 'string',
          title: '负载均衡算法',
          default: 1,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择'
          },
          enum: [
            { label: '权重轮询', value: 1 },
            { label: '轮询', value: 2 },
            { label: '最少连接数', value: 3 },
          ],
          'x-reactions': {
            dependencies: ['.mode'],
            fulfill: {
              state: {
                visible: `{{[1, 2].includes($deps[0])}}`,
              }
            }
          }
        },
        protocal: {
          type: 'string',
          title: '应用类型',
          default: 'http',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择'
          },
          enum: [
            { label: 'HTTP', value: 'http' },
          ],
          'x-reactions': {
            dependencies: ['.mode'],
            fulfill: {
              state: {
                disabled: `{{3 === $deps[0]}}`,
              }
            }
          }
        },
        ssl_enable: {
          type: 'boolean',
          title: '私有数据保护',
          default: 0,
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 1,
            unCheckedValue: 0
          },
        },
        ssl_protect: {
          type: 'array',
          title: ' ',
          default: [],
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            colon: false
          },
          'x-component': 'ArrayTable',
          'x-component-props': {
            pagination: { pageSize: 10 },
            scroll: { x: '100%' },
            rowKey: 'url',
          },
          items: {
            type: 'object',
            properties: {
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: 'URL', key: 'url', dataIndex: 'url' },
                properties: {
                  url: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    'x-component': 'Input',
                    'x-component-props': {
                      placeholder: '请输入'
                    },
                  },
                }
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: '操作',
                  key: 'operations',
                  dataIndex: 'operations',
                  width: 120,
                  fixed: 'right'
                },
                properties: {
                  operations: {
                    type: 'void',
                    'x-component': 'FormItem',
                    properties: {
                      remove: {
                        type: 'void',
                        'x-component': 'ArrayTable.Remove',
                        'title': '删除'
                      }
                    }
                  }
                }
              }
            }
          },
          properties: {
            add: {
              type: 'void',
              'x-component': 'ArrayTable.Addition',
              title: '添加'
            }
          },
          'x-reactions': {
            dependencies: ['.ssl_enable'],
            fulfill: {
              state: {
                visible: `{{1 === $deps[0]}}`,
              }
            }
          }
        },
        real_server: {
          type: 'array',
          title: '真实服务器',
          default: [],
          'x-decorator': 'FormItem',
          'x-component': 'ArrayTable',
          'x-component-props': {
            pagination: { pageSize: 10 },
            scroll: { x: '100%' },
            rowKey: 'address',
          },
          items: {
            type: 'object',
            properties: {
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '地址对象', key: 'addr_obj', dataIndex: 'addr_obj' },
                properties: {
                  addr_obj: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '请选择'
                    },
                    'x-reactions': '{{getAddressDataSource()}}'
                  },
                }
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '服务对象', key: 'serv_obj', dataIndex: 'serv_obj' },
                properties: {
                  serv_obj: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '请选择'
                    },
                    'x-reactions': '{{getServiceDataSource()}}'
                  },
                }
              },
              column4: {
                type: 'void',
                visible: 'hidden',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '协议', key: 'protocal', width: 100, dataIndex: 'protocal' },
                properties: {
                  protocal: {
                    type: 'string',
                    default: 'http',
                    'x-decorator': 'FormItem',
                    'x-component': 'PreviewText.Input',
                    'x-component-props': {
                      placeholder: '请选择'
                    },
                  },
                }
              },
              column5: {
                type: 'void',
                visible: 'hidden',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '权重', key: 'weight', width: 100, dataIndex: 'weight' },
                properties: {
                  weight: {
                    type: 'number',
                    default: 0,
                    'x-decorator': 'FormItem',
                    'x-component': 'PreviewText.Input',
                    'x-component-props': {
                      placeholder: '请选择'
                    },
                  },
                }
              },
              column6: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: '操作',
                  key: 'operations',
                  dataIndex: 'operations',
                  width: 80,
                  fixed: 'right'
                },
                properties: {
                  operations: {
                    type: 'void',
                    'x-component': 'FormItem',
                    properties: {
                      remove: {
                        type: 'void',
                        'x-component': 'ArrayTable.Remove',
                        'title': '删除'
                      }
                    }
                  }
                }
              }
            }
          },
          properties: {
            add: {
              type: 'void',
              'x-component': 'ArrayTable.Addition',
              title: '添加'
            }
          }
        },
        safety: {
          type: 'void',
          title: '站点安全',
          'x-component': 'FormLayout',
          'x-decorator': 'FormItem',
          properties: {
            website_safe: {
              type: 'string',
              title: '',
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                colon: false,
              },
              'x-component': 'Select',
              'x-component-props': {
                placeholder: '请选择站点'
              },
              enum: [
                { label: 'default', value: 'default' },
              ]
            },
            doname: {
              type: 'string',
              title: '',
              'x-decorator-props': {
                colon: false
              },
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入域名'
              }
            },
          },
        },
      }
    }
  }
}

