<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="800px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema" :scope="formScope"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, inject, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep, keyBy } from 'lodash'

import { getWafAddressObjectList, getWafServiceObjectList } from '@/request/api-device-waf'
import { SCHEMA } from './config';

export default defineComponent({
props: {
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add'
  },
  defaultData: {
    type: Object,
    default: () => ({})
  }
},
emits: ['update:visible', 'on-success'],
setup (props, ctx) {
  const { proxy } = getCurrentInstance()
  const { visible, mode, defaultData } = toRefs(props)
  const { deviceSafetyId } = inject('rowData')
  // const isAdd = computed(() => mode.value === 'add')
  const isView = computed(() => mode.value === 'view')
  const dialogVisible = computed(() => visible.value)
  const title = computed(() => {
    const titleMap = {
      add: '新增',
      edit: '编辑',
      view: '查看'
    }
    return `${titleMap[mode.value]}IP黑名单`
  })

  const state = reactive({
    loading: false
  })

  const form = createForm()

  watch(
    () => visible.value,
    async (val) => {
      if (!val) return
      await nextTick()
      await initForm()
    }
  )

  const initForm = async () => {
    const patternMap = {
      add: 'editable',
      view: 'readPretty',
      edit: 'editable'
    }
    const nameFieldMap = {
      add: 'editable',
      view: 'readPretty',
      edit: 'disabled'
    }
    const nameField = form.query('name').take()
    form.setPattern(patternMap[mode.value])
    nameField?.setPattern(nameFieldMap[mode.value])
    if (mode.value === 'add') return
    const formData = cloneDeep(defaultData.value)
    let week_day = [], day_enable_time = [];
    if (formData.week_day) week_day = formData.week_day.split(',').filter(item => !!item)
    if (formData.day_enable_time) day_enable_time = formData.day_enable_time.split('-')
    form.setValues({...formData, week_day, day_enable_time})
  }

  // 获取地址对象数据源
  let addressReqPromise = null
  /**
   * 获取地址对象数据源
   * @returns {Function} 返回获取数据源的函数
   */
  const getAddressDataSource = () => {
    return async (field) => {
      let data = []
      try {
        if (!addressReqPromise) addressReqPromise = getWafAddressObjectList({deviceSafetyId})
        const res = await addressReqPromise;
        data = res?.data?.addrObject?.obj ?? []
      } finally {
        field.setDataSource(data?.map(({name}) => ({label: name, value: name})) ?? [])
      }
    }
  }

  // 获取服务对象数据源
  let serviceReqPromise = null
  /**
   * 获取服务对象数据源
   * @returns {Function} 返回获取数据源的函数
   */
  const getServiceDataSource = () => {
    return async (field) => {
      let data = []
      try {
        if (!serviceReqPromise) serviceReqPromise = getWafServiceObjectList({deviceSafetyId})
        const res = await serviceReqPromise;
        data = res?.data?.serviceObj?.sev ?? []
      } finally {
        field.setDataSource(data?.map(({name}) => ({label: name, value: name})) ?? [])
      }
    }
  }

  /**
   * 关闭弹窗处理
   * @param {boolean} hasError 是否有错误
   */
  const handleClose = async (hasError = false) => {
    state.loading = false
    if (hasError === true) return
    await form.setValues(cloneDeep(form.initialValues), 'overwrite')
    await form.reset()
    ctx.emit('update:visible', false)
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    await form.validate()
    state.loading = true
    const params = cloneDeep(form.values)
    if (params.week_day?.length) params.week_day = params.week_day?.join(',') ?? ''
    if (params.day_enable_time?.length) params.day_enable_time = params.day_enable_time?.join('-') ?? ''
    ctx.emit('on-success', {
      mode: mode.value,
      data: params,
      callback: handleClose
    })
  }

  return {
    title,
    isView,
    form,
    formScope: {
      getAddressDataSource,
      getServiceDataSource
    },
    schema: SCHEMA,
    ...toRefs(state),
    dialogVisible,
    handleClose,
    handleSave
  }
}
})
</script>
