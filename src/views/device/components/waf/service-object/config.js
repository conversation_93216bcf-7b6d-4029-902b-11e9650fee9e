import CellExpand from '@/components/cell-expand'

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'label',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 150
  },
  {
    title: '内容',
    dataIndex: 'item',
    key: 'item',
    customRender: ({ record }) => {
      if (!record.item?.length) return '-'
      return <CellExpand title="内容" data={record.item} />
    },
    width: 150
  },
  {
    title: '引用',
    dataIndex: 'ref',
    key: 'ref',
    width: 100
  },
  {
    title: '引用位置',
    dataIndex: 'ref_mods',
    key: 'ref_mods',
    width: 200
  },
  {
    title: '描述',
    dataIndex: 'desc',
    key: 'desc',
    width: 200
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 220
  }
]

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 85
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' }
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          }
        },
        desc: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          }
        },
        item: {
          type: 'array',
          'x-validator': [
            { required: true, message: '请添加服务信息' }
          ],
          title: '服务信息',
          default: [],
          'x-decorator': 'FormItem',
          'x-component': 'ArrayTable',
          'x-component-props': {
            pagination: { pageSize: 10 },
            scroll: { x: '100%' },
            rowKey: 'app',
          },
          items: {
            type: 'object',
            properties: {
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { width: 120, title: '协议', key: 'protocol', dataIndex: 'protocol' },
                properties: {
                  protocol: {
                    type: 'string',
                    default: 'TCP',
                    'x-decorator': 'FormItem',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '请选择'
                    },
                    enum: [
                      { label: 'TCP', value: 'TCP' },
                      { label: 'UDP', value: 'UDP' },
                      { label: 'ICMP', value: 'ICMP' },
                      { label: 'IP', value: 'IP' },
                    ],
                    'x-validator': [
                      { required: true, message: '请选择协议' }
                    ],
                  }
                }
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '对象信息', key: 'value', dataIndex: 'value' },
                properties: {
                  portRange: {
                    type: 'void',
                    'x-display': 'visible',
                    'x-component': 'Space',
                    properties: {
                      sport: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                          addonAfter: '-',
                        },
                        'x-component-props': {
                          placeholder: '1-65535',
                        },
                        'x-validator': [
                          { required: true, message: '请输入对象信息' },
                          { port: true }
                        ]
                      },
                      dport: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                        },
                        'x-component-props': {
                          placeholder: '1-65535',
                        },
                        'x-validator': [
                          { required: true, message: '请输入' },
                          { port: true }
                        ]
                      }
                    },
                    'x-reactions': {
                      dependencies: ['.protocol'],
                      fulfill: {
                        state: {
                          visible: `{{['TCP', 'UDP'].includes($deps[0])}}`
                        }
                      }
                    }
                  },
                  ICMP: {
                    type: 'void',
                    'x-display': 'none',
                    'x-component': 'Space',
                    properties: {
                      type: {
                        type: 'number',
                        'x-decorator': 'FormItem',
                        'x-component': 'InputNumber',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                        },
                        'x-component-props': {
                          placeholder: '类型:0-255',
                        },
                        'x-validator': [
                          { required: true, message: '请输入' },
                          { format: 'integer' },
                          { min: 0 },
                          { max: 255 }
                        ]
                      },
                      code: {
                        type: 'number',
                        'x-decorator': 'FormItem',
                        'x-component': 'InputNumber',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                        },
                        'x-component-props': {
                          placeholder: '代码:0-255',
                        },
                        'x-validator': [
                          { required: true, message: '请输入' },
                          { format: 'integer' },
                          { min: 0 },
                          { max: 255 }
                        ]
                      }
                    },
                    'x-reactions': {
                      dependencies: ['.protocol'],
                      fulfill: {
                        state: {
                          visible: `{{$deps[0] === 'ICMP'}}`
                        }
                      }
                    }
                  },
                  IP: {
                    type: 'void',
                    'x-display': 'none',
                    'x-component': 'Space',
                    properties: {
                      port: {
                        type: 'string',
                        'x-decorator': 'FormItem',
                        'x-component': 'Input',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                        },
                        'x-component-props': {
                          placeholder: '1-65535',
                        },
                        'x-validator': [
                          { required: true, message: '请输入' },
                          { port: true }
                        ]
                      },
                    },
                    'x-reactions': {
                      dependencies: ['.protocol'],
                      fulfill: {
                        state: {
                          visible: `{{$deps[0] === 'IP'}}`
                        }
                      }
                    }
                  },
                }
              },
              column3: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: '操作',
                  key: 'operations',
                  dataIndex: 'operations',
                  width: 120,
                  fixed: 'right'
                },
                properties: {
                  operations: {
                    type: 'void',
                    'x-component': 'FormItem',
                    properties: {
                      remove: {
                        type: 'void',
                        'x-component': 'ArrayTable.Remove',
                        'title': '删除'
                      }
                    }
                  }
                }
              }
            }
          },
          properties: {
            add: {
              type: 'void',
              'x-component': 'ArrayTable.Addition',
              title: '添加'
            }
          }
        },
      }
    }
  }
}

