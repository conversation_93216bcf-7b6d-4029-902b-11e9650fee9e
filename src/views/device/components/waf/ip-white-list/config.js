/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns ({ handleChangeStatus, pagination }) {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      fixed: 'left'
    },
    {
      title: '入接口',
      dataIndex: 'if_in',
      key: 'if_in',
      width: 100
    },
    {
      title: '源地址对象',
      dataIndex: 'src_addrobj',
      key: 'src_addrobj',
      width: 150
    },
    {
      title: '目的地址对象',
      dataIndex: 'dst_addrobj',
      key: 'dst_addrobj',
      width: 150
    },
    {
      title: '目的服务对象',
      dataIndex: 'dst_servobj',
      key: 'dst_servobj',
      width: 150
    },
    {
      title: '生效日期',
      dataIndex: 'week_day',
      key: 'week_day',
      width: 150
    },
    {
      title: '时间段',
      dataIndex: 'day_enable_time',
      key: 'day_enable_time',
      width: 150
    },
    {
      title: '启用',
      dataIndex: 'enable',
      key: 'enable',
      width: 100,
      fixed: 'right',
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const status = record.enable === 1 ? 0 : 1;
            await handleChangeStatus?.({...record, status});
            record.enable = status;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          checkedValue={1}
          unCheckedValue={0}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 220,
      fixed: 'right'
    }
  ]
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 130
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' }
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          }
        },
        if_in: {
          type: 'string',
          'x-validator': [
            { required: true, message: '请选择入接口' }
          ],
          title: '入接口',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择入接口'
          },
          enum: [
            { label: 'gev0/1', value: 'gev0/1' },
            { label: 'gev0/2', value: 'gev0/2' },
            { label: 'ANY', value: 'any' },
          ]
        },
        src_addrobj: {
          type: 'string',
          'x-validator': [
            { required: true, message: '请选择源地址对象' }
          ],
          title: '源地址对象',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择源地址对象'
          },
          'x-reactions': '{{getAddressDataSource()}}'
        },
        dst_addrobj: {
          type: 'string',
          'x-validator': [
            { required: true, message: '请选择目的地址对象' }
          ],
          title: '目的地址对象',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择目的地址对象'
          },
          'x-reactions': '{{getAddressDataSource()}}'
        },
        dst_servobj: {
          type: 'string',
          'x-validator': [
            { required: true, message: '请选择目的服务对象' }
          ],
          title: '目的服务对象',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择目的服务对象'
          },
          'x-reactions': '{{getServiceDataSource()}}'
        },
        log: {
          type: 'number',
          title: '日志',
          default: 0,
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 1,
            unCheckedValue: 0
          }
        },
        log_level: {
          type: 'string',
          title: '日志级别',
          'x-validator': [
            { required: true, message: '请选择日志级别' }
          ],
          default: 6,
          'x-display': 'hidden',
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          'x-component-props': {
            isGroup: true,
            options: [
              { label: '信息', value: 6 },
              { label: '通知', value: 5 },
              { label: '警示', value: 4 },
              { label: '告警', value: 1 }
            ]
          },
          'x-reactions': {
            dependencies: ['.log'],
            fulfill: {
              state: {
                visible: `{{$deps[0] === 1}}`
              }
            }
          }
        },
        set_periodic: {
          type: 'boolean',
          title: '日志生效时间设置',
          default: 0,
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 1,
            unCheckedValue: 0
          }
        },
        valid_time: {
          type: 'void',
          title: '',
          'x-display': 'hidden',
          'x-decorator': 'FormItem',
          'x-component': 'VoidField',
          'x-decorator-props': {
            colon: false
          },
          properties: {
            week_day: {
              type: 'array',
              'x-validator': [
                { required: true, message: '请选择' }
              ],
              title: '星期',
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                labelWidth: 130,
              },
              'x-component': 'Select',
              'x-component-props': {
                placeholder: '请选择',
                mode: 'multiple'
              },
              enum: [
                { label: '星期日', value: '7' },
                { label: '星期一', value: '1' },
                { label: '星期二', value: '2' },
                { label: '星期三', value: '3' },
                { label: '星期四', value: '4' },
                { label: '星期五', value: '5' },
                { label: '星期六', value: '6' }
              ]
            },
            day_enable_time: {
              type: 'array',
              'x-validator': [
                { required: true, message: '请选择' }
              ],
              title: '时间段',
              'x-decorator': 'FormItem',
              'x-decorator-props': {
                labelWidth: 130,
              },
              'x-component': 'TimeRangePicker',
              'x-component-props': {
                showTime: {format: 'HH'},
                placeholder: ['开始时间', '结束时间'],
                valueFormat: 'HH',
                format: "HH"
              },
            },
          },
          'x-reactions': {
            dependencies: ['.set_periodic'],
            fulfill: {
              state: {
                visible: `{{$deps[0] === 1}}`
              }
            }
          }
        }
      }
    }
  }
}

