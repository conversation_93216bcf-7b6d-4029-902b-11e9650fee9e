
/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    key: 'name',
    width: 150,
    fixed: 'left'
  },
  {
    title: '内容',
    dataIndex: 'period_time',
    key: 'period_time',
    width: 200
  },
  {
    title: '开始时间',
    dataIndex: 'begin_date',
    key: 'begin_date',
    width: 150
  },
  {
    title: '结束时间',
    dataIndex: 'end_date',
    key: 'end_date',
    width: 150
  },
  {
    title: '引用',
    dataIndex: 'ref',
    key: 'ref',
    width: 100
  },
  {
    title: '引用位置',
    dataIndex: 'ref_mods',
    key: 'ref_mods',
    width: 150
  },
  {
    title: '描述',
    dataIndex: 'desc',
    key: 'desc',
    width: 200
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 220,
    fixed: 'right'
  }
]

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 80
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          },
          'x-validator': [
            { required: true, message: '请输入名称' }
          ],
        },
        desc: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          }
        },
        period_time: {
          type: 'array',
          title: '循环日期',
          default: [],
          'x-decorator': 'FormItem',
          'x-component': 'ArrayTable',
          'x-component-props': {
            pagination: { pageSize: 10 },
            scroll: { x: '100%' },
            rowKey: 'url',
          },
          items: {
            type: 'object',
            properties: {
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '星期', key: 'week', dataIndex: 'week' },
                properties: {
                  week: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '请选择',
                      mode: 'multiple'
                    },
                    enum: [
                      { label: '星期日', value: '7' },
                      { label: '星期一', value: '1' },
                      { label: '星期二', value: '2' },
                      { label: '星期三', value: '3' },
                      { label: '星期四', value: '4' },
                      { label: '星期五', value: '5' },
                      { label: '星期六', value: '6' }
                    ]
                  },
                }
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '开始时间', width: 240, key: 'time_range', dataIndex: 'time_range' },
                properties: {
                  time_range: {
                    type: 'array',
                    'x-validator': [
                      { required: true, message: '请选择' }
                    ],
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      label: '',
                      colon: false,
                    },
                    'x-component': 'TimeRangePicker',
                    'x-component-props': {
                      showTime: {format: 'HH:mm'},
                      placeholder: ['开始时间', '结束时间'],
                      valueFormat: 'HH:mm',
                      format: "HH:mm"
                    },
                  },
                }
              },
              column3: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: '操作',
                  key: 'operations',
                  dataIndex: 'operations',
                  width: 80,
                  fixed: 'right'
                },
                properties: {
                  operations: {
                    type: 'void',
                    'x-component': 'FormItem',
                    properties: {
                      remove: {
                        type: 'void',
                        'x-component': 'ArrayTable.Remove',
                        'title': '删除'
                      }
                    }
                  }
                }
              }
            }
          },
          properties: {
            add: {
              type: 'void',
              'x-component': 'ArrayTable.Addition',
              title: '添加'
            }
          }
        },
        abs_enable: {
          type: 'boolean',
          title: '起止日期',
          default: 0,
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 1,
            unCheckedValue: 0
          },
          'x-decorator-props': {
            wrapperWidth: 50,
            addonAfter: '（不开启起止时间设置，表示一直生效）'
          }
        },
        dateRange: {
          type: 'array',
          title: ' ',
          'x-validator': [
            { required: true, message: '请选择' }
          ],
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            colon: false,
          },
          'x-component': 'DatePicker.RangePicker',
          'x-component-props': {
            showTime: { format: 'HH:mm' },
            placeholder: ['开始时间', '结束时间'],
            valueFormat: 'YYYY-MM-DD HH:mm',
            format: "YYYY-MM-DD HH:mm"
          },
          'x-reactions': {
            dependencies: ['.abs_enable'],
            fulfill: {
              state: {
                visible: `{{$deps[0] === 1}}`
              }
            }
          }
        }
      }
    }
  }
}

