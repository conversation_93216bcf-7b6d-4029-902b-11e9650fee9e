
export const columns = [
        {
                title: '名称',
                dataIndex: 'name',
                key: 'name',
                width: 150,
                fixed: 'left'
        },
        {
                title: '描述',
                dataIndex: 'description',
                key: 'description',
        },
        {
                title: 'TCP Flood防护',
                dataIndex: 'tcpFlood',
                key: 'tcpFlood',
                customRender: ({ record }) => {
                        return record.stcpEnable || record.dtcpEnable ? '启用' : '禁用'
                }
        },
        {
                title: 'UDP Flood防护',
                dataIndex: 'udpFlood',
                key: 'udpFlood',
                customRender: ({ record }) => {
                        return record.sudpEnable || record.dudpEnable ? '启用' : '禁用'
                }
        },
        {
                title: 'ICMP Flood防护',
                dataIndex: 'icmpFlood',
                key: 'icmpFlood',
                customRender: ({ record }) => {
                        return record.sicmpEnable || record.dicmpEnable ? '启用' : '禁用'
                }
        },
        {
                title: '响应动作',
                dataIndex: 'responseAction',
                key: 'responseAction',
        },
        {
                title: '日志',
                dataIndex: 'logEnable',
                key: 'logEnable',
                customRender: ({ record }) => {
                        return record.logEnable ? '启用' : '禁用'
                }
        },
        {
                title: '日志级别',
                dataIndex: 'logLevel',
                key: 'logLevel',
        },
        {
                title: '响应方式',
                dataIndex: 'responseWay',
                key: 'responseWay',
        },
        {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                width: 220,
                fixed: 'right'
        }
]

export const schema = {
        type: 'object',
        properties: {
                layout: {
                        type: 'void',
                        'x-component': 'FormLayout',
                        'x-component-props': {
                                labelWidth: 130
                        },
                        properties: {
                                name: {
                                        type: 'string',
                                        title: '名称',
                                        'x-decorator': 'FormItem',
                                        'x-component': 'Input',
                                        'x-component-props': {
                                                placeholder: '请输入名称'
                                        },
                                        'x-validator': [
                                                { required: true, message: '请输入名称' }
                                        ]
                                },
                                description: {
                                        type: 'string',
                                        title: '描述',
                                        default: '',
                                        'x-decorator': 'FormItem',
                                        'x-component': 'Input',
                                        'x-component-props': {
                                                rows: 1,
                                                placeholder: '请输入描述'
                                        }
                                },
                                tcpFlood: {
                                        type: 'object',
                                        title: 'TCP Flood防护',
                                        'x-decorator': 'FormItem',
                                        'x-decorator-props': {
                                                style: {
                                                        marginBottom: '0px'
                                                }
                                        },
                                        properties: {
                                                sourceHost: {
                                                        type: 'void',
                                                        'x-component': 'FormLayout',
                                                        'x-component-props': {
                                                                layout: 'inline',
                                                                style: {
                                                                        gap: '16px',
                                                                        display: 'flex',
                                                                        flexWrap: 'wrap',
                                                                        alignItems: 'center',
                                                                        width: '100%'
                                                                },
                                                                alignItems: 'center'
                                                        },
                                                        properties: {
                                                                stcpEnable: {
                                                                        type: 'boolean',
                                                                        default: false,
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'Checkbox',
                                                                        'x-content': '对每台源主机进行限流',
                                                                        'x-component-props': {
                                                                                checkedValue: true,
                                                                                unCheckedValue: false
                                                                        },
                                                                        'x-decorator-props': {
                                                                                style: {
                                                                                        whiteSpace: 'nowrap',
                                                                                        width: '200px'
                                                                                }
                                                                        }
                                                                },
                                                                'stcpLimit': {
                                                                        'type': 'number',
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'InputNumber',
                                                                        'x-decorator-props': {
                                                                                style: { flex: 1 }
                                                                        },
                                                                        'x-component-props': {
                                                                                min: 1,
                                                                                max: 10000,
                                                                                placeholder: '(1-10000)秒',
                                                                                style: { width: '100%' }
                                                                        },
                                                                        'x-reactions': {
                                                                                dependencies: ['tcpFlood.stcpEnable'],
                                                                                when: '{{ $deps[0] }}',
                                                                                fulfill: {
                                                                                        'state': {
                                                                                                'disabled': false
                                                                                        }
                                                                                },
                                                                                otherwise: {
                                                                                        'state': {
                                                                                                'disabled': true
                                                                                        }
                                                                                }
                                                                        },
                                                                }
                                                        }
                                                },
                                                destinationHost: {
                                                        type: 'void',
                                                        'x-component': 'FormLayout',
                                                        'x-component-props': {
                                                                layout: 'inline',
                                                                style: {
                                                                        gap: '16px',
                                                                        display: 'flex',
                                                                        flexWrap: 'wrap',
                                                                        alignItems: 'center',
                                                                        width: '100%'
                                                                },
                                                                alignItems: 'center'
                                                        },
                                                        properties: {
                                                                'dtcpEnable': {
                                                                        type: 'boolean',
                                                                        default: false,
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'Checkbox',
                                                                        'x-content': '对目标主机进行限流',
                                                                        'x-component-props': {
                                                                                checkedValue: true,
                                                                                unCheckedValue: false
                                                                        },
                                                                        'x-decorator-props': {
                                                                                style: {
                                                                                        whiteSpace: 'nowrap',
                                                                                        width: '200px'
                                                                                }
                                                                        }
                                                                },
                                                                'dtcpLimit': {
                                                                        'type': 'number',
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'InputNumber',
                                                                        'x-decorator-props': {
                                                                                style: { flex: 1 }
                                                                        },
                                                                        'x-component-props': {
                                                                                min: 1,
                                                                                max: 10000,
                                                                                placeholder: '(1-10000)秒',
                                                                                style: { width: '100%' }
                                                                        },
                                                                        'x-reactions': {
                                                                                dependencies: ['tcpFlood.dtcpEnable'],
                                                                                when: '{{ $deps[0] }}',
                                                                                fulfill: {
                                                                                        'state' : {
                                                                                                'disabled': false
                                                                                        }
                                                                                },
                                                                                otherwise: {
                                                                                        'state': {
                                                                                                'disabled': true
                                                                                        }
                                                                                }
                                                                        },
                                                                }
                                                        }
                                                }
                                        },
                                },
                                udpFlood: {
                                        type: 'object',
                                        title: 'UDP Flood防护',
                                        'x-decorator': 'FormItem',
                                        'x-decorator-props': {
                                                style: {
                                                        marginBottom: '0px'
                                                }
                                        },
                                        properties: {
                                                sourceHost: {
                                                        type: 'void',
                                                        'x-component': 'FormLayout',
                                                        'x-component-props': {
                                                                layout: 'inline',
                                                                style: {
                                                                        gap: '16px',
                                                                        display: 'flex',
                                                                        flexWrap: 'wrap',
                                                                        alignItems: 'center',
                                                                        width: '100%'
                                                                },
                                                                alignItems: 'center'
                                                        },
                                                        properties: {
                                                                'sudpEnable': {
                                                                        type: 'boolean',
                                                                        default: false,
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'Checkbox',
                                                                        'x-content': '对每台源主机进行限流',
                                                                        'x-component-props': {
                                                                                checkedValue: true,
                                                                                unCheckedValue: false
                                                                        },
                                                                        'x-decorator-props': {
                                                                                style: {
                                                                                        whiteSpace: 'nowrap',
                                                                                        width: '200px'
                                                                                }
                                                                        }
                                                                },
                                                                'sudpLimit': {
                                                                        'type': 'number',
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'InputNumber',
                                                                        'x-decorator-props': {
                                                                                style: { flex: 1 }
                                                                        },
                                                                        'x-component-props': {
                                                                                min: 1,
                                                                                max: 10000,
                                                                                placeholder: '(1-10000)秒',
                                                                                style: { width: '100%' }
                                                                        },
                                                                        'x-reactions': {
                                                                                dependencies: ['udpFlood.sudpEnable'],
                                                                                when: '{{ $deps[0] }}',
                                                                                fulfill: {
                                                                                        'state': {
                                                                                                'disabled': false
                                                                                        }
                                                                                },
                                                                                otherwise: {
                                                                                        'state': {
                                                                                                'disabled': true
                                                                                        }
                                                                                }
                                                                        },
                                                                }
                                                        }
                                                },
                                                destinationHost: {
                                                        type: 'void',
                                                        'x-component': 'FormLayout',
                                                        'x-component-props': {
                                                                layout: 'inline',
                                                                style: {
                                                                        gap: '16px',
                                                                        display: 'flex',
                                                                        flexWrap: 'wrap',
                                                                        alignItems: 'center',
                                                                        width: '100%'
                                                                },
                                                                alignItems: 'center'
                                                        },
                                                        properties: {
                                                                'dudpEnable': {
                                                                        type: 'boolean',
                                                                        default: false,
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'Checkbox',
                                                                        'x-content': '对目标主机进行限流',
                                                                        'x-component-props': {
                                                                                checkedValue: true,
                                                                                unCheckedValue: false
                                                                        },
                                                                        'x-decorator-props': {
                                                                                style: {
                                                                                        whiteSpace: 'nowrap',
                                                                                        width: '200px'
                                                                                }
                                                                        }
                                                                },
                                                                'dudpLimit': {
                                                                        'type': 'number',
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'InputNumber',
                                                                        'x-decorator-props': {
                                                                                style: { flex: 1 }
                                                                        },
                                                                        'x-component-props': {
                                                                                min: 1,
                                                                                max: 10000,
                                                                                placeholder: '(1-10000)秒',
                                                                                style: { width: '100%' }
                                                                        },
                                                                        'x-reactions': {
                                                                                dependencies: ['udpFlood.dudpEnable'],
                                                                                when: '{{ $deps[0] }}',
                                                                                fulfill: {
                                                                                        'state' : {
                                                                                                'disabled': false
                                                                                        }
                                                                                },
                                                                                otherwise: {
                                                                                        'state': {
                                                                                                'disabled': true
                                                                                        }
                                                                                }
                                                                        },
                                                                }
                                                        }
                                                }
                                        },
                                },
                                icmpFlood: {
                                        type: 'object',
                                        title: 'ICMP Flood防护',
                                        'x-decorator': 'FormItem',
                                        'x-decorator-props': {
                                                style: {
                                                        marginBottom: '0px'
                                                }
                                        },
                                        properties: {
                                                sourceHost: {
                                                        type: 'void',
                                                        'x-component': 'FormLayout',
                                                        'x-component-props': {
                                                                layout: 'inline',
                                                                style: {
                                                                        gap: '16px',
                                                                        display: 'flex',
                                                                        flexWrap: 'wrap',
                                                                        alignItems: 'center',
                                                                        width: '100%'
                                                                },
                                                                alignItems: 'center'
                                                        },
                                                        properties: {
                                                                'sicmpEnable': {
                                                                        type: 'boolean',
                                                                        default: false,
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'Checkbox',
                                                                        'x-content': '对每台源主机进行限流',
                                                                        'x-component-props': {
                                                                                checkedValue: true,
                                                                                unCheckedValue: false
                                                                        },
                                                                        'x-decorator-props': {
                                                                                style: {
                                                                                        whiteSpace: 'nowrap',
                                                                                        width: '200px'
                                                                                }
                                                                        }
                                                                },
                                                                'sicmpLimit': {
                                                                        'type': 'number',
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'InputNumber',
                                                                        'x-decorator-props': {
                                                                                style: { flex: 1 }
                                                                        },
                                                                        'x-component-props': {
                                                                                min: 1,
                                                                                max: 10000,
                                                                                placeholder: '(1-10000)秒',
                                                                                style: { width: '100%' }
                                                                        },
                                                                        'x-reactions': {
                                                                                dependencies: ['icmpFlood.sicmpEnable'],
                                                                                when: '{{ $deps[0] }}',
                                                                                fulfill: {
                                                                                        'state': {
                                                                                                'disabled': false
                                                                                        }
                                                                                },
                                                                                otherwise: {
                                                                                        'state': {
                                                                                                'disabled': true
                                                                                        }
                                                                                }
                                                                        },
                                                                }
                                                        }
                                                },
                                                destinationHost: {
                                                        type: 'void',
                                                        'x-component': 'FormLayout',
                                                        'x-component-props': {
                                                                layout: 'inline',
                                                                style: {
                                                                        gap: '16px',
                                                                        display: 'flex',
                                                                        flexWrap: 'wrap',
                                                                        alignItems: 'center',
                                                                        width: '100%'
                                                                },
                                                                alignItems: 'center'
                                                        },
                                                        properties: {
                                                                'dicmpEnable': {
                                                                        type: 'boolean',
                                                                        default: false,
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'Checkbox',
                                                                        'x-content': '对目标主机进行限流',
                                                                        'x-component-props': {
                                                                                checkedValue: true,
                                                                                unCheckedValue: false
                                                                        },
                                                                        'x-decorator-props': {
                                                                                style: {
                                                                                        whiteSpace: 'nowrap',
                                                                                        width: '200px'
                                                                                }
                                                                        }
                                                                },
                                                                'dicmpLimit': {
                                                                        'type': 'number',
                                                                        'x-decorator': 'FormItem',
                                                                        'x-component': 'InputNumber',
                                                                        'x-decorator-props': {
                                                                                style: { flex: 1 }
                                                                        },
                                                                        'x-component-props': {
                                                                                min: 1,
                                                                                max: 10000,
                                                                                placeholder: '(1-10000)秒',
                                                                                style: { width: '100%' }
                                                                        },
                                                                        'x-reactions': {
                                                                                dependencies: ['icmpFlood.dicmpEnable'],
                                                                                when: '{{ $deps[0] }}',
                                                                                fulfill: {
                                                                                        'state' : {
                                                                                                'disabled': false
                                                                                        }
                                                                                },
                                                                                otherwise: {
                                                                                        'state': {
                                                                                                'disabled': true
                                                                                        }
                                                                                }
                                                                        },
                                                                }
                                                        }
                                                }
                                        },
                                },
                                responseAction: {
                                        type: 'string',
                                        title: '响应动作',
                                        'x-decorator': 'FormItem',
                                        'x-component': 'Select',
                                        'x-component-props': {
                                                placeholder: '请选择响应动作'
                                        },
                                        'x-validator': [
                                                { required: true, message: '请选择响应动作' }
                                        ],
                                        'enum': [
                                                { label: '通过', value: '通过' },
                                                { label: '丢弃/阻断', value: '丢弃/阻断' }
                                        ],
                                },
                                logEnable: {
                                        type: 'boolean',
                                        title: '日志',
                                        default: false,
                                        'x-decorator': 'FormItem',
                                        'x-component': 'Switch',
                                        'x-component-props': {
                                                checkedValue: true,
                                                unCheckedValue: false
                                        }
                                },
                                logLevel: {
                                        type: 'string',
                                        title: '日志级别',
                                        'x-decorator': 'FormItem',
                                        'x-component': 'Select',
                                        'x-component-props': {
                                                placeholder: '请选择日志级别'
                                        },
                                        'x-validator': [
                                                { required: true, message: '请选择日志级别' }
                                        ],
                                        'x-reactions': [
                                                {
                                                        "dependencies": ["logEnable"],
                                                        "when": "{{$deps[0]}}",
                                                        "fulfill": {
                                                                "state": {
                                                                        "disabled": false
                                                                }
                                                        },
                                                        "otherwise": {
                                                                "state": {
                                                                        "disabled": true
                                                                }
                                                        }
                                                }
                                        ],
                                        'enum': [
                                                { label: '信息', value: '信息' },
                                                { label: '通知', value: '通知' },
                                                { label: '警示', value: '警示' },
                                                { label: '告警', value: '告警' }
                                        ]
                                },
                                responseWay: {
                                        type: 'array',
                                        title: '响应方式',
                                        'x-decorator': 'FormItem',
                                        'x-component': 'Checkbox.Group',
                                        "enum": [
                                                { label: '邮件', value: '邮件' },
                                                { label: '短信', value: '短信' }
                                        ]
                                }
                        }
                }
        }
}