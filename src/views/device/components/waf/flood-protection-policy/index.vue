<template>
        <div>
                <a-space align="start" class="waf-table-operate">
                        <a-button type="primary" @click="handleOpenDialog('add')">
                                新增
                        </a-button>
                </a-space>
                <a-table
                        :scroll="{y: 'calc(100vh - 330px)'}"
                        :data-source="dataSource"
                        :columns="columns"
                        :loading="loading"
                        :pagination="pagination"
                        @change="handleTableChange"
                >
                        <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'operation'">
                                        <a-button type="link" @click="handleOpenDialog('view', record)">详情</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" @click="handleOpenDialog('edit', record)">编辑</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" danger @click="handleDelete(record)">删除</a-button>
                                </template>
                        </template>
                </a-table>
                <EditDialog
                        v-model:visible="dialogConfig.visible"
                        v-bind="dialogConfig"
                        @on-success="handleSave"
                />
        </div>
</template>

<script>

import {
        addWafFloodProtectionStrategy,
        deleteWafFloodProtectionStrategy,
        getWafFloodProtectionStrategyList,
        updateWafFloodProtectionStrategy
} from '@/request/api-device-waf.js';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { computed, createVNode, defineComponent, getCurrentInstance, inject, onMounted, reactive, toRefs } from 'vue';
import { usePagination } from 'vue-request';
import { columns } from './config.js';
import EditDialog from './edit-dialog.vue';

export default defineComponent({
        components: {
                EditDialog,
        },
        setup() {
                const { proxy } = getCurrentInstance()
                const { deviceSafetyId } = inject('rowData')
                const state = reactive({
                        dialogConfig: {
                                visible: false,
                                mode: 'add',
                                defaultData: {}
                        }
                })
                
                const {
                        data,
                        run,
                        loading,
                        total,
                        current,
                        pageSize,
                        reload
                } = usePagination(getWafFloodProtectionStrategyList, {
                        manual: true,
                        defaultParams: {
                                deviceSafetyId
                        },
                        formatResult: ({data = {}}) => ({ items: data?.data ?? [], total: data?.total ?? 0 }),
                        pagination: {
                                currentKey: 'page_num',
                                pageSizeKey: 'page_size'
                        }
                })
                const handleOpenDialog = (mode = 'add', row = {}) => {
                        state.dialogConfig.visible = true;
                        state.dialogConfig.mode = mode;
                        state.dialogConfig.defaultData = row;
                }

                const pagination = computed(() => ({
                        total: total.value,
                        current: current.value,
                        pageSize: pageSize.value,
                        showQuickJumper: true,
                        showSizeChanger: true,
                        showTotal: total => `共 ${total} 条`
                }))

                const dataSource = computed(() => data.value?.items || [])

                const refreshTableData = (isReload = true) => { 
                        run({
                                deviceSafetyId,
                                page_num: isReload ? 1 : pagination.value.current,
                                page_size: pagination.value?.pageSize,
                        })
                }

                const handleSave = async ( { mode, data, callback } ) => {
                        const requestMethodEnum = {
                                add: addWafFloodProtectionStrategy,
                                edit: updateWafFloodProtectionStrategy
                        }
                        try {
                                let insertData = {
                                        name: data.name,
                                        responseAction: data.responseAction,
                                        stcpEnable: data.tcpFlood.stcpEnable,
                                        dtcpEnable: data.tcpFlood.dtcpEnable,
                                        sudpEnable: data.udpFlood.sudpEnable,
                                        dudpEnable: data.udpFlood.dudpEnable,
                                        sicmpEnable: data.icmpFlood.sicmpEnable,
                                        dicmpEnable: data.icmpFlood.dicmpEnable,
                                        logEnable: data.logEnable,
                                        responseWay: typeof data.responseWay === 'string' ? data.responseWay : `${data.responseWay.length > 0 ? data.responseWay.join(',') : ''}`
                                }
                                if( mode === 'edit' ) insertData.id = data.id
                                if( data.description.length > 0 ) insertData.description = data.description
                                if( data.tcpFlood.stcpEnable ) insertData.stcpLimit = data.tcpFlood.stcpLimit
                                if( data.tcpFlood.dtcpEnable ) insertData.dtcpLimit = data.tcpFlood.dtcpLimit
                                if( data.udpFlood.sudpEnable ) insertData.sudpLimit = data.udpFlood.sudpLimit
                                if( data.udpFlood.dudpEnable ) insertData.dudpLimit = data.udpFlood.dudpLimit
                                if( data.icmpFlood.sicmpEnable ) insertData.sicmpLimit = data.icmpFlood.sicmpLimit
                                if( data.icmpFlood.dicmpEnable ) insertData.dicmpLimit = data.icmpFlood.dicmpLimit
                                if( data.logEnable ) insertData.logLevel = data.logLevel
                                const res = await requestMethodEnum[mode]?.({
                                        ...insertData,
                                        deviceSafetyId
                                })
                                if( res.code !== '00000' ) throw new Error(res.msg)
                                proxy.$message.success(mode === 'add' ? '新增成功！' : '更新成功！')
                                callback();
                                refreshTableData();
                        } catch (error) {
                                callback(true, error)
                        }
                }

                const handleDelete = (row) => {
                        proxy.$confirm({
                                title: '删除',
                                content: `确认删除该条数据吗?`,
                                icon: createVNode(ExclamationCircleOutlined),
                                okText: '确定',
                                okType: 'warning',
                                cancelText: '取消',
                                async onOk () {
                                        try {
                                                await deleteWafFloodProtectionStrategy({ ids: [row.id] })
                                                proxy.$message.success('删除成功！')
                                                refreshTableData()
                                        } catch (err) {
                                                // proxy.$message.error('删除失败！')
                                        }
                                },
                                onCancel () {
                                        console.log('Cancel')
                                }
                        })
                }
                onMounted(() => {
                        refreshTableData();
                })

                return {
                        ...toRefs(state),
                        pagination,
                        loading,
                        dataSource,
                        columns,
                        handleOpenDialog,
                        handleSave,
                        handleDelete,
                };
        }
});

</script>