<template>
        <a-modal
                v-model:visible="dialogVisible"
                :title="title"
                width="1000px"
                :keyboard="false"
                :maskClosable="false"
                @cancel="handleClose"
        >
                <a-spin :spinning="loading">
                        <FormProvider :form="form">
                                <SchemaField :schema="schema"/>
                        </FormProvider>
                </a-spin>
                <template #footer>
                        <a-button
                                v-if="!isView"
                                type="primary"
                                :loading="loading"
                                @click="handleSave"
                        >
                                保存
                        </a-button>
                        <a-button
                                :type="isView ? 'primary' : 'default'"
                                @click="handleClose"
                        >
                                {{isView ? '关闭' : '取消'}}
                        </a-button>
                </template>
        </a-modal>
</template>

<script>
import { createForm } from '@formily/core';
import { cloneDeep } from 'lodash';
import { computed, defineComponent, nextTick, reactive, toRefs, watch } from 'vue';
import { schema } from './config';

export default defineComponent({
        name: 'EditDialog',
        props: {
                visible: {
                        type: Boolean,
                        default: false
                },
                mode: {
                        type: String,
                        default: 'add'
                },
                defaultData: {
                        type: Object,
                        default: () => ({})
                }
        },
        emits: ['update:visible', 'on-success'],
        setup(props, ctx) {
                const { visible, mode, defaultData } = toRefs(props)
                const isView = computed(() => mode.value === 'view')
                const dialogVisible = computed(() => visible.value)
                const title = computed(() => {
                        const titleMap = {
                                add: '新增',
                                detail: '详情',
                                edit: '编辑',
                                view: '查看'
                        }
                        return `${titleMap[mode.value]}Flood防护策略`;
                })
                const state = reactive({
                        loading: false
                })
                const form = createForm()
                watch(
                        () => visible.value,
                        async (val) => {
                                if (!val) return
                                await nextTick()
                                await initForm()
                        }
                )

                const initForm = async () => {
                        const patternMap = {
                                add: 'editable',
                                view: 'readPretty',
                                edit: 'editable'
                        }
                        const nameFieldMap = {
                                add: 'editable',
                                view: 'readPretty',
                                edit: 'disabled'
                        }
                        const nameField = form.query('name').take()
                        form.setPattern(patternMap[mode.value])
                        nameField.pattern = nameFieldMap[mode.value]
                        if (mode.value === 'add') return;
                        let formData = cloneDeep(defaultData.value)
                        formData.tcpFlood = {
                                stcpEnable : formData.stcpEnable,
                                dtcpEnable : formData.dtcpEnable,
                        }
                        formData.udpFlood = {
                                sudpEnable : formData.sudpEnable,
                                dudpEnable : formData.dudpEnable,
                        }
                        formData.icmpFlood = {
                                sicmpEnable : formData.sicmpEnable,
                                dicmpEnable : formData.dicmpEnable,
                        }
                        delete formData.stcpEnable
                        delete formData.dtcpEnable
                        delete formData.sudpEnable
                        delete formData.dudpEnable
                        delete formData.sicmpEnable
                        delete formData.dicmpEnable
                        if(formData.tcpFlood.stcpEnable) {
                                formData.tcpFlood.stcpLimit = formData.stcpLimit
                        }
                        if(formData.tcpFlood.dtcpEnable) {
                                formData.tcpFlood.dtcpLimit = formData.dtcpLimit
                        }
                        if(formData.udpFlood.sudpEnable) {
                                formData.udpFlood.sudpLimit = formData.sudpLimit
                        }
                        if(formData.udpFlood.dudpEnable) {
                                formData.udpFlood.dudpLimit = formData.dudpLimit
                        }
                        if(formData.icmpFlood.sicmpEnable) {
                                formData.icmpFlood.sicmpLimit = formData.sicmpLimit
                        }
                        if(formData.icmpFlood.dicmpEnable) {
                                formData.icmpFlood.dicmpLimit = formData.dicmpLimit
                        }
                        console.log('*************************************************************')
                        console.log(formData)
                        form.setValues(formData)
                }

                const handleClose = async (hasError = false) => {
                        state.loading = false
                        if (hasError === true) return;
                        await form.setValues(cloneDeep(form.initialValues), 'overwrite')
                        await form.reset()
                        ctx.emit('update:visible', false)
                }

                const handleSave = async () => {
                        await form.validate()
                        state.loading = true
                        const params = cloneDeep(form.values)
                        // 处理参数
                        ctx.emit('on-success', {
                                mode: mode.value,
                                data: {...params},
                                callback: handleClose
                        })
                }

                return {
                        title,
                        isView,
                        form,
                        schema,
                        ...toRefs(state),
                        dialogVisible,
                        handleClose,
                        handleSave
                }
        }
})

</script>