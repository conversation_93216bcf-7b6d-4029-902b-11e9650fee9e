import { getAddressObjectDetail, getBaseSecurityDomainList } from "@/request/api-device-fw"
import { useAsyncDataSource } from "@/utils/util"
import CellExpand from '@/components/cell-expand'

/**
 * 获取表格列配置
 * @param {Object} options - 配置选项
 * @param {Function} options.handleChangeStatus - 状态变更处理函数
 * @param {Object} options.pagination - 分页信息
 * @returns {Array} 列配置
 */
export const getColumns = ({ handleChangeStatus, pagination } = {}) => {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination?.pageSize * (pagination?.current - 1) + index + 1;
      }
    },
    {
      title: 'ID',
      dataIndex: 'id',
      ellipsis: true,
      key: 'id',
      width: 120
    },
    {
      title: '名称',
      dataIndex: 'name',
      ellipsis: true,
      key: 'name',
      width: 150,
      fixed: 'left'
    },
    {
      title: '入接口/安全域',
      dataIndex: 'securityDomain',
      ellipsis: true,
      key: 'securityDomain',
      width: 120,
      customRender: ({text}) => {
        const values = text?.split(',')
        return (<CellExpand title="入接口/安全域" data={values} />)
      }
    },
    {
      title: '模式',
      dataIndex: 'mode',
      ellipsis: true,
      key: 'mode',
      width: 120
    },
    {
      title: '源地址',
      dataIndex: 'sAddress',
      ellipsis: true,
      key: 'sAddress',
      width: 120,
      customRender: ({text}) => {
        const values = text?.split(',')
        return (<CellExpand title="源地址" data={values} />)
      }
    },
    {
      title: '目的地址',
      dataIndex: 'dAddress',
      ellipsis: true,
      key: 'dAddress',
      width: 120,
      customRender: ({text}) => {
        const values = text?.split(',')
        return (<CellExpand title="目的地址" data={values} />)
      }
    },
    {
      title: '启用',
      dataIndex: 'enable',
      ellipsis: true,
      key: 'enable',
      width: 80,
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const status = record.status ? false : true;
            await handleChangeStatus?.({...record, status});
            record.status = status;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '命中',
      dataIndex: 'hitCount',
      ellipsis: true,
      key: 'hitCount',
      width: 80
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 220
    }
  ]
}

/** @type {*} 表单配置 */
export const getSchema = (safetyId) => {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'boolean',
            title: '启用',
            default: true,
            'x-decorator': 'FormItem',
            'x-component': 'Switch'
          },
          name: {
            type: 'string',
            title: '名称',
            'x-validator': [
              { required: true, message: '请输入名称' },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入'
            }
          },
          securityDomain: {
            type: 'array',
            title: '入接口/安全域',
            default: ['any'],
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              mode:'multiple',
              placeholder: '请选择入接口/安全域'
            },
            'x-reactions': useAsyncDataSource(async () => {
              const { data } = await getBaseSecurityDomainList({safetyId});
              return data?.map(item => ({ label: item.name, value: item.name }));
            })
          },
          mode: {
            type: 'string',
            title: '模式',
            default: '客户流量检查',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              placeholder: '请选择模式'
            },
            enum: [
              { label: '客户流量检查', value: '客户流量检查' },
              { label: '服务器流量检查', value: '服务器流量检查' }
            ]
          },
          sAddress: {
            type: 'array',
            title: '源地址',
            default: ['any'],
            'x-decorator': 'FormItem',
            'x-component': 'TreeSelect',
            'x-component-props': {
              placeholder: '请选择源地址',
              multiple: true,
              'tree-default-expand-all': true
            },
            'x-reactions': useAsyncDataSource(async () => {
              const { data } = await getAddressObjectDetail({ type: 1, safetyId });
              const { lstAddrNode, lstAddrGroup } = data ?? {} ;
              return [
                {
                  label: '地址对象',
                  value: 'lstAddrNode',
                  disabled: true,
                  children: lstAddrNode?.map(item => ({ label: item.name, value: item.name }))
                },
                {
                  label: '地址组',
                  disabled: true,
                  value: 'lstAddrGroup',
                  children: lstAddrGroup?.map(item => ({ label: item.name, value: item.name }))
                }
              ]
            })
          },
          dAddress: {
            type: 'array',
            title: '目的地址',
            default: ['any'],
            'x-decorator': 'FormItem',
            'x-component': 'TreeSelect',
            'x-component-props': {
              placeholder: '请选择目的地址',
              multiple: true,
              'tree-default-expand-all': true
            },
            'x-reactions': useAsyncDataSource(async () => {
              const { data } = await getAddressObjectDetail({ type: 0, safetyId });
              const { lstAddrNode, lstAddrGroup } = data ?? {} ;
              return [
                {
                  label: '地址对象',
                  value: 'lstAddrNode',
                  disabled: true,
                  children: lstAddrNode?.map(item => ({ label: item.name, value: item.name }))
                },
                {
                  label: '地址组',
                  disabled: true,
                  value: 'lstAddrGroup',
                  children: lstAddrGroup?.map(item => ({ label: item.name, value: item.name }))
                }
              ]
            })
          },
          checkApp: {
            type: 'array',
            title: '检查应用',
            'x-decorator': 'FormItem',
            'x-component': 'Checkbox.Group',
            'x-component-props': {
              options: [
                { label: 'HTTPS', value: 'HTTPS' },
                { label: 'POP3S', value: 'POP3S' },
                { label: 'SMTPS', value: 'SMTPS' },
                { label: 'IMAPS', value: 'IMAPS' }
              ]
            }
          }
        }
      }
    }
  }
}


/** @type {*} 转换数据到请求格式 */
export const transformDataToReq = (data = {}) => {
  return {
    ...data,
    securityDomain: data.securityDomain?.join(','),
    sAddress: data.sAddress?.join(','),
    dAddress: data.dAddress?.join(','),
    checkApp: data.checkApp?.join(',')
  }
}

/** @type {*} 转换数据到表单格式 */
export const transformDataToForm = (data = {}) => {
  return {
    ...data,
    securityDomain: data.securityDomain?.split(','),
    sAddress: data.sAddress?.split(','),
    dAddress: data.dAddress?.split(','),
    checkApp: data.checkApp?.split(',')
  }
}