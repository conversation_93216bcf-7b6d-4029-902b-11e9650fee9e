<template>
  <a-tabs v-model:activeKey="activeKey" size="small">
    <a-tab-pane v-for="tab in TABS" :key="tab.label" :tab="tab.label">
      <div class="list--inner_tab">
        <component :is="tab.componentName" />
      </div>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import { defineComponent, ref } from 'vue';

import { TABS } from './config';
import Ipv4Policy from './ipv4-policy/index.vue';
import Ipv6Policy from './ipv6-policy/index.vue';
import Ipv4Whitelist from './ipv4-whitelist/index.vue';
import Ipv6Whitelist from './ipv6-whitelist/index.vue';

export default defineComponent({
  components: {
    Ipv4Policy,
    Ipv6Policy,
    Ipv4Whitelist,
    Ipv6Whitelist,
  },
  setup (props, ctx) {
    const activeKey = ref(TABS[0].label)
    return {
      TABS,
      activeKey
    }
  }
})
</script>

<style lang="less">
.list--inner_tab {
  margin-top: 8px;
}
</style> 