import { formatDate } from '@/utils/util'

/**
 * 获取表格列配置
 * @param {Object} options - 配置选项
 * @param {Function} options.handleChangeStatus - 状态变更处理函数
 * @param {Object} options.pagination - 分页信息
 * @returns {Array} 列配置
 */
export const getColumns = ({ handleChangeStatus, pagination } = {}) => {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination?.pageSize * (pagination?.current - 1) + index + 1;
      }
    },
    {
      title: 'ID',
      dataIndex: 'id',
      width: 50,
      fixed: 'left'
    },
    {
      title: '名称',
      dataIndex: 'name',
      ellipsis: true,
      key: 'name',
      width: 150,
      fixed: 'left'
    },
    {
      title: '目的地址',
      dataIndex: 'dIp',
      ellipsis: true,
      key: 'dIp',
      width: 150,
      fixed: 'left'
    },
    {
      title: 'TCP端口',
      dataIndex: 'tcpPort',
      ellipsis: true,
      key: 'tcpPort',
      width: 100
    },
    {
      title: '创建时间',
      dataIndex: 'createTime',
      ellipsis: true,
      key: 'createTime',
      width: 150,
      customRender: ({ text }) => {
        return text ? formatDate(text) : '-'
      }
    },
    {
      title: '过期时间',
      dataIndex: 'expireTime',
      ellipsis: true,
      key: 'expireTime',
      width: 150,
      customRender: ({ text }) => {
        return text ? formatDate(text) : '-'
      }
    },
    {
      title: '剩余时间',
      dataIndex: 'validPeriod',
      ellipsis: true,
      key: 'validPeriod',
      width: 120,
      customRender: ({ text, record }) => {
        if (record.validPeriod === 0) {
          return '永久有效'
        }
        return text || '-'
      }
    },
    {
      title: '加入原因',
      dataIndex: 'reason',
      ellipsis: true,
      key: 'reason',
      width: 150
    },
    {
      title: '启用',
      dataIndex: 'enabled',
      ellipsis: true,
      key: 'enabled',
      width: 80,
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const status = record.enabled === 1 ? 0 : 1;
            await handleChangeStatus?.({...record, status});
            record.enabled = status;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enabled}
          loading={!!record.statusLoading}
          checkedValue={1}
          unCheckedValue={0}
          onClick={handleClick}
        />
      }
    },
    {
      title: '命中',
      dataIndex: 'hitCount',
      ellipsis: true,
      key: 'hitCount',
      width: 80
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 220
    }
  ]
}

/** @type {*} 表单配置 */
export const getSchema = () => {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enabled: {
            type: 'boolean',
            title: '启用',
            default: true,
            'x-decorator': 'FormItem',
            'x-component': 'Switch'
          },
          name: {
            type: 'string',
            title: '名称',
            'x-validator': [
              { required: true, message: '请输入名称' },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入'
            }
          },
          dIp: {
            type: 'string',
            title: '目的地址',
            'x-validator': [
              { required: true, message: '请输入目的地址' },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入'
            }
          },
          tcpPort: {
            type: 'string',
            title: 'TCP端口',
            default: '443',
            'x-validator': [
              { required: true, message: '请输入TCP端口' },
            ],
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            'x-component-props': {
              placeholder: '请输入TCP端口'
            },
            'enum': [
              { label: '443', value: '443' },
              { label: '456', value: '456' },
              { label: '993', value: '993' },
              { label: '995', value: '995' }
            ]
          },
          validPeriod: {
            type: 'number',
            title: '有效时间',
            default: 0,
            'x-decorator': 'FormItem',
            'x-component': 'InputNumber',
            'x-component-props': {
              placeholder: '请输入有效时间',
              min: 0
            },
            'x-decorator-props': {
              addonAfter: '天'
            }
          }
        }
      }
    }
  }
}

export const transformDataToReq = (data) => {
  return {
    ...data,
  }
}

export const transformReqToData = (data) => {
  return {
    ...data,
  }
}