<template>
  <div>
    <a-space align="start" class="fw-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add')">
        新增
      </a-button>
    </a-space>
    <a-table
      :scroll="{y: 'calc(100vh - 320px)'}"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button type="link" @click="handleOpenDialog('view', record)">查看</a-button>
          <a-button type="link" @click="handleOpenDialog('edit', record)">
            编辑
          </a-button>
          <a-button type="link" danger @click="handleDelete(record)">
            删除
          </a-button>
        </template>
      </template>
    </a-table>
    <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="handleSave" />
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, inject, computed, onMounted, getCurrentInstance, createVNode } from 'vue'
import { usePagination } from 'vue-request'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

import { 
  getSslIpv4PolicyList, 
  addSslIpv4Policy, 
  updateSslIpv4Policy, 
  deleteSslIpv4Policy 
} from '@/request/api-device-fw'
import { getColumns } from './config'
import EditDialog from './edit-dialog.vue'

export default defineComponent({
  components: {
    EditDialog,
  },
  setup () {
    const { proxy } = getCurrentInstance()
    const safetyId = inject("deviceSafetyId");

    const state = reactive({
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })

    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
    } = usePagination(async (p) => getSslIpv4PolicyList({...p, safetyId: safetyId.value}), {
      formatResult: ({ data = {} }) => ({ items: data.data ?? [], total: data.total ?? 0 }),
      pagination: {
        currentKey: 'pageNum',
        pageSizeKey: 'pageSize'
      }
    })

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条`
    }))

    const dataSource = computed(() => {
      return data.value?.items || []
    })

    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        safetyId: safetyId.value,
        pageSize: pag.pageSize,
        pageNum: pag?.current,
      })
    }

    const refreshTableData = (isReload = true) => {
      run({
        safetyId: safetyId.value,
        pageNum: isReload ? 1 : pagination.value.current,
        pageSize: pagination.value?.pageSize,
      })
    }

    const handleSave = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addSslIpv4Policy,
        edit: updateSslIpv4Policy
      }
      try {
        await requestMethodEnum[mode]?.({...data, safetyId: safetyId.value})
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        refreshTableData()
      } catch (err) {
        callback(true, err)
      }
    }

    const handleDelete = async (row = {}) => {
      proxy.$confirm({
        title: '删除',
        content: `确认删除 ${row.name} 数据吗?`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteSslIpv4Policy({ id: row.id, safetyId: safetyId.value })
            proxy.$message.success('删除成功！')
            refreshTableData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel(){
          console.log('Cancel')
        }
      })
    }

    const handleChangeStatus = async ({status, ...row}) => {
      await updateSslIpv4Policy({...row, enabled: status, safetyId: safetyId.value})
    }

    const handleOpenDialog = (mode = 'add', row = {}) => {
      state.dialogConfig.visible = true
      state.dialogConfig.mode = mode
      state.dialogConfig.defaultData = row
    }

    const columns = computed(() => {
      return getColumns({handleChangeStatus, pagination: pagination.value});
    });

   

    return {
      ...toRefs(state),
      pagination,
      loading,
      dataSource,
      refreshTableData,
      handleOpenDialog,
      handleTableChange,
      handleSave,
      handleDelete,
      columns
    }
  }
})
</script> 