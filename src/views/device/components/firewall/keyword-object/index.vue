<template>
  <div>
    <!-- <DynamicSearch
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    /> -->
    <a-space align="start" class="fw-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add')">
        新建关键字
      </a-button>
    </a-space>
    <a-table
      :scroll="{y: 'calc(100vh - 370px)'}"
      :data-source="dataSource"
      rowKey="name"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button
            type="link"
            @click="handleOpenDialog('view', record)"
          >
            查看
          </a-button>
          <a-button
            type="link"
            @click="handleOpenDialog('edit', record)"
          >
            编辑
          </a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </template>
      </template>
    </a-table>
    <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="handleSave" />
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, computed, nextTick, getCurrentInstance, createVNode, inject, onMounted } from 'vue'
import { usePagination } from 'vue-request'
import { cloneDeep } from 'lodash'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

import DynamicSearch from "@/components/dynamic-search/index.vue";
import EditDialog from './edit-dialog.vue'

import { getKeywordObjectList, addKeywordObject, updateKeywordObject, deleteKeywordObject } from '@/request/api-device-fw'
import { getColumns, SEARCH_CONFIG } from './config';

export default defineComponent({
  name: 'KeywordObject',
  components: {
    DynamicSearch,
    EditDialog
  },
  setup () {
    const { proxy } = getCurrentInstance()
    const safetyId = inject("deviceSafetyId");

    const state = reactive({
      searchParams: { },
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })

    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(async (p) => getKeywordObjectList({...p, safetyId: safetyId.value}), {
      manual: true,
      formatResult: ({data = {}}) => ({ items: data.data ?? [], total: data.total ?? 0 }),
      pagination: {
        currentKey: 'page',
        pageSizeKey: 'size',
      }
    })

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条`
    }))

    const dataSource = computed(() => {
      return data.value?.items || []
    })

    /**
     * 处理表格分页变化
     * @param {Object} pag 分页参数
     */
    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        safetyId: safetyId.value,
        page: pag.current,
        size: pag?.pageSize,
        ...state.searchParams
      })
    }

    /**
     * 刷新表格数据
     * @param {boolean} isReload 是否重新加载第一页
     */
    const refreshTableData = (isReload = true) => {
      run({
        page: isReload ? 1 : pagination.value.current,
        size: pagination.value.pageSize,
       ...state.searchParams
      })
    }

    const handleSearch = async (params = {}) => {
      state.searchParams = cloneDeep(params);
      await nextTick();
      refreshTableData();
    }

    /**
     * 保存关键字对象数据
     * @param {Object} param 保存参数
     * @param {string} param.mode 操作模式
     * @param {Object} param.data 保存数据
     * @param {Function} param.callback 回调函数
     */
    const handleSave = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addKeywordObject,
        edit: updateKeywordObject
      }
      try {
        const res = await requestMethodEnum[mode]?.({...data, safetyId: safetyId.value})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        refreshTableData()
      } catch (err) {
        callback(true, err)
      }
    }

    /**
     * 删除关键字对象
     * @param {Object} row 行数据
     */
    const handleDelete = async (row) => {
      proxy.$confirm({
        title: '删除',
        content: `确认删除 ${row.name} 数据吗?`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteKeywordObject({...row, safetyId: safetyId.value})
            proxy.$message.success('删除成功！')
            refreshTableData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
    }

    /**
     * 打开编辑弹窗
     * @param {string} mode 操作模式
     * @param {Object} row 行数据
     */
    const handleOpenDialog = (mode = 'add', row = {}) => {
      state.dialogConfig.visible = true
      state.dialogConfig.mode = mode
      state.dialogConfig.defaultData = {...row, safetyId: safetyId.value}
    }

    onMounted(() => refreshTableData())

    return {
      SEARCH_CONFIG,
      ...toRefs(state),
      pagination,
      loading,
      dataSource,
      handleSearch,
      refreshTableData,
      handleOpenDialog,
      handleTableChange,
      handleSave,
      handleDelete,
      columns: getColumns({pagination})
    }
  }
})
</script> 