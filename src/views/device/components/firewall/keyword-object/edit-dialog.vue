<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="800px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider v-if="initReady" :form="form">
        <SchemaField :schema="schema"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave"
        >保存</a-button
      >
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">
        {{ isView ? "关闭" : "取消" }}
      </a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, nextTick, inject, shallowRef, ref } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep } from 'lodash'

import { getSchema } from './config';


export default defineComponent({
  name: 'KeywordObjectEditDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const safetyId = inject("deviceSafetyId");
    const { visible, mode, defaultData } = toRefs(props)
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新建关键字',
        edit: '编辑关键字',
        view: '查看关键字'
      }
      return titleMap[mode.value]
    })

    const state = reactive({
      loading: false
    })

    const form = shallowRef({});
    const schema = shallowRef({})

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick();
        await initForm()
      }
    )

    const enums = ref({});

    async function getKeywordObjectData() {
      // 这里可以添加获取关键字对象相关数据的逻辑
      // 比如获取关键字分类、模板等数据
      enums.value = {}
    }

    const initReady = ref(false)
    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      const formData = cloneDeep(defaultData.value)
      form.value = createForm({
        values: {...(formData??{}),keyword: formData?.keyword?.split(','), safetyId: safetyId.value}
      });
      schema.value = getSchema(safetyId.value, enums.value)
      nextTick(() => {
        form.value.setPattern(patternMap[mode.value])
      })
      initReady.value = true;
    }

    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return
      await form.value.setValues(cloneDeep(form.value.initialValues), 'overwrite')
      await form.value.reset()
      ctx.emit('update:visible', false)
      nextTick(() =>  initReady.value = false)
    }

    const handleSave = async () => {
      await form.value.validate()
      const params = cloneDeep(form.value.values)
      state.loading = true
      ctx.emit('on-success', {
        mode: mode.value,
        data: {
          ...params,
          keyword: params?.keyword?.join(','),
          safetyId: safetyId.value
        },
        callback: handleClose
      })
      nextTick(() =>  initReady.value = false)
    }

    return {
      title,
      isView,
      form,
      schema,
      initReady,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script> 