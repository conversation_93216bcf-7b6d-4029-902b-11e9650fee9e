/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    }
  },
  {
    title: '描述',
    name: 'description',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入描述',
    }
  }
]

/**
 * 获取表格列配置
 * @returns {Array} 列配置
 */
export const getColumns = ({pagination}) => {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
     {
      title: 'ID',
      dataIndex: 'id',
      ellipsis: true,
      key: 'id',
      width: 50
    },
    {
      title: '名称',
      dataIndex: 'name',
      ellipsis: true,
      key: 'name',
      width: 150
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
      key: 'description',
      width: 200
    },
    {
      title: '引用次数',
      dataIndex: 'citationNum',
      ellipsis: true,
      key: 'citationNum',
      width: 120,
      align: 'center',
      customRender: ({ text }) => {
        return text || 0
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 220
    }
  ]
}

/**
 * 获取表单schema
 * @param {string} safetyId 安全域ID
 * @param {Object} enums 枚举数据
 * @returns {Object} 表单schema
 */
export const getSchema = (safetyId, enums) => {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          name: {
            type: 'string',
            title: '名称',
            'x-decorator': 'FormItem',
            'x-component': 'Input',
            'x-component-props': {
              placeholder: '请输入名称'
            },
            'x-validator': [
              { required: true, message: '请输入名称' },
              { max: 63 }
            ]
          },
          description: {
            type: 'string',
            'x-component': 'Input.TextArea',
            'x-component-props': {
              rows: 3,
              placeholder: '请输入描述'
            },
            'x-decorator': 'FormItem',
            'x-decorator-props': {
              label: '描述'
            },
            'x-validator': [
              { max: 127 }
            ]
          },
          keyword: {
            type: 'array',
            'x-component': 'ArrayItems',
            'x-decorator': 'FormItem',
            title: '关键字列表',
            items: {
              type: 'void',
              'x-component': 'Space',
              properties: {
                input: {
                  type: 'string',
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                  'x-component-props': {
                    placeholder: '请输入关键字'
                  },
                  'x-validator': [
                    { required: true, message: '请输入关键字' },
                  ]
                },
                remove: {
                  type: 'void',
                  'x-decorator': 'FormItem',
                  'x-component': 'ArrayItems.Remove',
                },
              },
            },
            properties: {
              add: {
                type: 'void',
                title: '添加',
                'x-component': 'ArrayItems.Addition',
              },
            },
          },
          matchRule: {
            type: 'string',
            title: '匹配规则',
            'x-decorator': 'FormItem',
            'x-component': 'Select',
            enum: [
              {label: '精准匹配', value: 'exact'},
              {label: '模糊匹配', value: 'fuzzy'}
            ]
          }
        }
      }
    }
  }
} 