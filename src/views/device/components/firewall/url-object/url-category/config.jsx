/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
    key: 'name'
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    key: 'description'
  },
  {
    title: '引用次数',
    dataIndex: 'citationNum',
    ellipsis: true,
    key: 'citationNum'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 220
  }
]

/** @type {*} 新增URL分类表单schema */
export const URL_CATEGORY_SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 80
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入'
          },
          'x-validator': [
            { required: true, message: '请输入名称' },
            { max: 63 }
          ]
        },
        description: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            rows: 3,
            placeholder: '请输入'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            { max: 127 }
          ]
        },
        url: {
            type: 'array',
            'x-component': 'ArrayItems',
            'x-decorator': 'FormItem',
            title: 'URL列表',
            items: {
              type: 'void',
              'x-component': 'Space',
              properties: {
                input: {
                  type: 'string',
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                  'x-validator': [
                      { required: true, message: '请输入URL' }
                    ]
                },
                remove: {
                  type: 'void',
                  'x-decorator': 'FormItem',
                  'x-component': 'ArrayItems.Remove',
                },
              },
            },
            properties: {
              add: {
                type: 'void',
                title: '添加',
                'x-component': 'ArrayItems.Addition',
              },
            },
          },
      }
    }
  }
}

/** @type {*} 转换数据到请求格式 */
export const transformDataToReq = (data = {}) => {
  return {
    name: data.name,
    description: data.description,
    url: data.url?.join(',')
  }
}

/** @type {*} 转换数据到表单格式 */
export const transformDataToForm = (data = {}) => {
  return {
    name: data.name,
    description: data.description,
    url: data.url?.split(',')
  }
} 