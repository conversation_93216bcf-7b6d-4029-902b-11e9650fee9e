<template>
  <a-tabs v-model:activeKey="activeKey" size="small">
    <a-tab-pane v-for="tab in TABS" :key="tab.label" :tab="tab.label">
      <div class="list--inner_tab">
        <component :is="tab.componentName" />
      </div>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import { defineComponent, ref } from 'vue';

import { TABS } from './config';
import UrlCategory from './url-category/index.vue';
import UrlGroup from './url-group/index.vue';

export default defineComponent({
  components: {
    UrlCategory,
    UrlGroup,
  },
  setup (props, ctx) {
    const activeKey = ref(TABS[0].label)
    return {
      TABS,
      activeKey
    }
  }
})
</script>

<style lang="less">
.list--inner_tab {
  margin-top: 8px;
}
</style> 