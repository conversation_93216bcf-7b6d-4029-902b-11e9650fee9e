/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
    key: 'name'
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    key: 'description'  
  },
  {
    title: '引用次数',
    dataIndex: 'citationNum',
    ellipsis: true,
    key: 'citationNum'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 220
  }
]

/** @type {*} 新增URL组对象表单schema */
export const URL_GROUP_SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 80
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入'
          },
          'x-validator': [
            { required: true, message: '请输入名称' },
            { max: 63 }
          ]
        },
        description: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            rows: 3,
            placeholder: '请输入'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            { max: 127 }
          ]
        },
        content: {
          type: 'string',
          title: 'URL选定范围',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择',
            mode: "multiple",
            options: [
              { label: '在线交易[预定义]', value: 'online_transaction' },
              { label: '社交媒体[预定义]', value: 'social_media' },
              { label: '视频网站[预定义]', value: 'video_sites' },
              { label: '新闻资讯[预定义]', value: 'news' },
              { label: '购物网站[预定义]', value: 'shopping' },
              { label: '游戏网站[预定义]', value: 'gaming' },
              { label: '教育网站[预定义]', value: 'education' },
              { label: '金融网站[预定义]', value: 'finance' },
              { label: '医疗健康[预定义]', value: 'health' },
              { label: '政府网站[预定义]', value: 'government' },
              { label: '企业网站[预定义]', value: 'enterprise' },
              { label: '技术网站[预定义]', value: 'technology' },
              { label: '娱乐网站[预定义]', value: 'entertainment' },
              { label: '体育网站[预定义]', value: 'sports' },
              { label: '旅游网站[预定义]', value: 'travel' },
              { label: '汽车网站[预定义]', value: 'automotive' },
              { label: '房产网站[预定义]', value: 'real_estate' }
            ]
          },
        }
      }
    }
  }
}

/** @type {*} 转换数据到请求格式 */
export const transformDataToReq = (data = {}) => {
  return {
    name: data.name,
    description: data.description,
    content: data.content?.join(',')
  }
}

/** @type {*} 转换数据到表单格式 */
export const transformDataToForm = (data = {}) => {
  return {
    name: data.name,
    description: data.description,
    content: data.content?.split(',')
  }
} 