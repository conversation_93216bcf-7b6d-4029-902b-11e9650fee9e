<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="600px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema" :scope="formScope"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave"
        >提交</a-button
      >
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{
        isView ? "关闭" : "取消"
      }}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, inject, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep } from 'lodash'

import { SCHEMA } from './config';

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { visible, mode, defaultData } = toRefs(props)
    const { safetyId } = inject('rowData')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新建入侵防护',
        edit: '编辑入侵防护',
        view: '查看入侵防护'
      }
      return titleMap[mode.value]
    })

    const state = reactive({
      loading: false
    })

    const form = createForm()

    const formScope = {
      // 可以在这里添加表单作用域的方法
    }

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      form.setPattern(patternMap[mode.value])
      if (mode.value === 'add') return
      const formData = cloneDeep(defaultData.value)
      form.setValues({...formData})
    }

    const handleSave = async () => {
      try {
        state.loading = true
        const values = await form.submit()
        ctx.emit('on-success', {
          mode: mode.value,
          data: values,
          callback: (hasError, error) => {
            if (!hasError) {
              handleClose()
            }
          }
        })
      } catch (err) {
        console.error('表单验证失败:', err)
      } finally {
        state.loading = false
      }
    }

    const handleClose = () => {
      ctx.emit('update:visible', false)
      form.reset()
    }

    return {
      ...toRefs(state),
      dialogVisible,
      title,
      isView,
      form,
      formScope,
      schema: SCHEMA,
      handleSave,
      handleClose
    }
  }
})
</script>

<style lang="less" scoped>
.ant-modal-body {
  padding: 24px;
}
</style> 