/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: "名称",
    name: "name",
    type: "string",
    "x-component": "Input",
    "x-component-props": {
      placeholder: "请输入名称",
    },
  },
];

/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns({ handleChangeStatus, pagination }) {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: "left",
      customRender({ index }) {
        return (
          pagination.value.pageSize * (pagination.value.current - 1) + index + 1
        );
      },
    },
    {
      title: "ID",
      dataIndex: "id",
      key: "id",
      width: 50,
    },
    {
      title: "名称",
      dataIndex: "name",
      key: "name",
      fixed: "left",
      width: 200,
    },
    {
      title: "防护等级",
      dataIndex: "protectionLevel",
      key: "protectionLevel",
      width: 150,
      customRender: ({ record }) => {
        const levelMap = {
          1: "低",
          2: "中",
          3: "高",
        };
        return levelMap[record.protectionLevel] || record.protectionLevel || "-";
      },
    },
    {
      title: "描述",
      dataIndex: "description",
      key: "description",
      width: 300,
      customRender: ({ record }) => {
        return record.description || "-";
      },
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      fixed: "right",
      width: 220,
    },
  ];
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: "object",
  properties: {
    layout1: {
      type: "void",
      "x-component": "FormLayout",
      "x-component-props": {
        labelWidth: 120,
      },
      properties: {
        name: {
          type: "string",
          title: "名称",
          "x-validator": [{ required: true, message: "请输入名称" }],
          "x-component": "Input",
          "x-decorator": "FormItem",
          "x-component-props": {
            placeholder: "请输入",
          },
        },
        description: {
          type: "string",
          title: "描述",
          "x-component": "Input.TextArea",
          "x-decorator": "FormItem",
          "x-component-props": {
            placeholder: "请输入",
            rows: 3,
          },
        },
        protectionLevel: {
          type: "string",
          title: "防护等级",
          "x-component": "Select",
          "x-decorator": "FormItem",
          "x-component-props": {
            placeholder: "请选择防护等级",
            options: [
              { label: "低", value: "1" },
              { label: "中", value: "2" },
              { label: "高", value: "3" },
            ],
          },
        },
        autoUpdate: {
          type: "number",
          title: "自动更新",
          default: 1,
          "x-component": "Switch",
          "x-decorator": "FormItem",
          "x-component-props": {
            checkedValue: 1,
            unCheckedValue: 0,
          },
        },
        action: {
          type: "string",
          title: "行为",
          "x-component": "Select",
          "x-decorator": "FormItem",
          "x-component-props": {
            placeholder: "请选择行为",
            options: [
              { label: "放行", value: "allow" },
              { label: "阻止", value: "block" },
              { label: "隔离", value: "quarantine" },
            ],
          },
        },
        enable: {
          type: "number",
          title: "抓包启用",
          default: 1,
          "x-component": "Switch",
          "x-decorator": "FormItem",
          "x-component-props": {
            checkedValue: 1,
            unCheckedValue: 0,
          },
        },
        packetCount: {
          type: "number",
          title: "单条流抓包个数",
          default: 5,
          "x-component": "InputNumber",
          "x-decorator": "FormItem",
          "x-component-props": {
            placeholder: "请输入",
          },
        },
      },
    },
  },
}; 