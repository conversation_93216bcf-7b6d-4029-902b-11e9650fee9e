<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="600px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema" :scope="formScope"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, inject, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep } from 'lodash'

import {
  getTimeDefenseList
} from '@/request/api-device-fw'
import { SCHEMA } from './config';

export default defineComponent({
props: {
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add'
  },
  defaultData: {
    type: Object,
    default: () => ({})
  }
},
emits: ['update:visible', 'on-success'],
setup (props, ctx) {
  const { proxy } = getCurrentInstance()
  const { visible, mode, defaultData } = toRefs(props)
  const { safetyId } = inject('rowData')
  const isView = computed(() => mode.value === 'view')
  const dialogVisible = computed(() => visible.value)
  const title = computed(() => {
    const titleMap = {
      add: '新增',
      edit: '编辑',
      view: '查看'
    }
    return `${titleMap[mode.value]}域名白名单`
  })

  const state = reactive({
    loading: false
  })

  const form = createForm()

  watch(
    () => visible.value,
    async (val) => {
      if (!val) return
      await nextTick()
      await initForm()
    }
  )

  const initForm = async () => {
    const patternMap = {
      add: 'editable',
      view: 'readPretty',
      edit: 'editable'
    }
    form.setPattern(patternMap[mode.value])
    if (mode.value === 'add') return
    const formData = cloneDeep(defaultData.value)
    form.setValues({...formData})
  }

  // 获取时间数据源
  let timeReqPromise = null
  const getTimeDataSource = () => {
    return async (field) => {
      let data = []
      try {
        if (!timeReqPromise) timeReqPromise = getTimeDefenseList({safetyId})
        const res = await timeReqPromise;
        data = res?.data ?? {}
      } finally {
        const {lstTimeAbsolute, lstTimeCycle} = data ?? {} ;
        const dataSource = [
          {
            label: '绝对时间',
            value: 'lstTimeAbsolute',
            disabled: true,
            children: lstTimeAbsolute?.map(item => ({ label: item.name, value: item.name })) ?? []
          },
          {
            label: '周期时间',
            disabled: true,
            value: 'lstTimeCycle',
            children: lstTimeCycle?.map(item => ({ label: item.name, value: item.name })) ?? []
          }
        ]
        field?.setDataSource(dataSource)
      }
    }
  }

  const handleClose = async (hasError = false) => {
    state.loading = false
    if (hasError === true) return
    await form.setValues(cloneDeep(form.initialValues), 'overwrite')
    await form.reset()
    ctx.emit('update:visible', false)
  }

  const handleSave = async () => {
    await form.validate()
    state.loading = true
    const params = cloneDeep(form.values)
    ctx.emit('on-success', {
      mode: mode.value,
      data: params,
      callback: handleClose
    })
  }

  onMounted(async() => {
    const dataSourceFnList = [
      getTimeDataSource
    ];
    for (let fn of dataSourceFnList) {
      // 设备接口有问题，不能高并发
      await new Promise(resolve => setTimeout(resolve, 100))
      fn()?.()
    }
  })

  return {
    title,
    isView,
    form,
    formScope: {
      getTimeDataSource
    },
    schema: SCHEMA,
    ...toRefs(state),
    dialogVisible,
    handleClose,
    handleSave
  }
}
})
</script> 