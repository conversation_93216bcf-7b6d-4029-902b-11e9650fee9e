import { getAddressObjectDetail } from "@/request/api-device-fw";
import { useAsyncDataSource } from "@/utils/util";

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '请求域名',
    name: 'domain',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入请求域名',
    },
  },
]

/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns({ handleChangeStatus, pagination }) {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: '请求域名',
      dataIndex: 'domainName',
      key: 'domainName',
      fixed: 'left',
      width: 200
    },
    {
      title: '时间表',
      dataIndex: 'schedule',
      key: 'schedule',
      width: 150
    },
    {
      title: '命中数',
      dataIndex: 'hitCount',
      key: 'hitCount',
      width: 100
    },
    {
      title: '启用',
      dataIndex: 'enable',
      key: 'enable',
      width: 80,
      fixed: 'right',
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const status = record.enable ? false : true;
            await handleChangeStatus?.({ ...record, status });
            record.enable = status;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.statusLoading}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 220
    }
  ]
}

export function getSchema(safetyId) {
  return {
    type: 'object',
    properties: {
      layout1: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 120
        },
        properties: {
          enable: {
            type: 'number',
            title: '启用',
            default: 0,
            'x-component': 'Switch',
            'x-decorator': 'FormItem',
            'x-component-props': {
              checkedValue: 1,
              unCheckedValue: 0
            }
          },
          domainName: {
            type: 'string',
            title: '请求域名',
            'x-validator': [
              { required: true, message: '请输入请求域名' }
            ],
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入'
            }
          },
          sIp: {
            type: 'string',
            title: '源地址',
            default: ['any'],
            'x-decorator': 'FormItem',
            'x-component': 'TreeSelect',
            'x-component-props': {
              placeholder: '请选择目的地址',
              'tree-default-expand-all': true
            },
            // enum: enumData.addressList,
            'x-reactions': useAsyncDataSource(async () => {
              const { data } = await getAddressObjectDetail({ type: 0, safetyId });
              const { lstAddrNode, lstAddrGroup } = data ?? {};
              return [
                {
                  label: '地址对象',
                  value: 'lstAddrNode',
                  disabled: true,
                  children: lstAddrNode?.map(item => ({ label: item.name, value: item.name }))
                },
                {
                  label: '地址组',
                  disabled: true,
                  value: 'lstAddrGroup',
                  children: lstAddrGroup?.map(item => ({ label: item.name, value: item.name }))
                }
              ]
            })
          },
          dns: {
            type: 'string',
            title: 'DNS服务器',
            'x-component': 'Input',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请输入DNS服务器'
            }
          },
          schedule: {
            type: 'string',
            title: '时间表',
            'x-component': 'TreeSelect',
            'x-decorator': 'FormItem',
            'x-component-props': {
              placeholder: '请选择时间表',
              'tree-default-expand-all': true
            },
            'x-reactions': '{{getTimeDataSource()}}'
          }
        }
      }
    }
  }
}

