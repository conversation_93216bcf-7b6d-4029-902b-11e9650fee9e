<template>
  <div class="domain-blacklist--wrapper">
    <a-space align="center" class="fw-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add')">
        新增
      </a-button>
    </a-space>
    <a-table
      :scroll="{y: 'calc(100vh - 320px)'}"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button type="link" @click="handleOpenDialog('view', record)">查看</a-button>
          <a-button type="link" @click="handleOpenDialog('edit', record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </template>
      </template>
    </a-table>
    <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="handleSave" />
  </div>
</template>
<script>
import { defineComponent, inject, reactive, toRefs, computed, onMounted, getCurrentInstance, createVNode } from 'vue'
import { usePagination } from 'vue-request'
import { omit } from 'lodash'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

import { getDomainBlacklistList, addDomainBlacklist, updateDomainBlacklist, deleteDomainBlacklist } from '@/request/api-device-fw'
import { SEARCH_CONFIG, getColumns } from './config';

import EditDialog from './edit-dialog.vue'

export default defineComponent({
  components: {
    EditDialog,
  },
  setup () {
    const { proxy } = getCurrentInstance()
    const { safetyId } = inject('rowData')
    const state = reactive({
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })
    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getDomainBlacklistList, {
      manual: true,
      defaultParams: {
        safetyId,
      },
      formatResult: ({data = {}}) => {
        return { items: data?.data ?? [], total: data?.total ?? 0 }
      },
      pagination: {
        currentKey: 'page',
        pageSizeKey: 'size',
      }
    })

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条`
    }))

    const dataSource = computed(() => {
      return data.value?.items || []
    })

    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        safetyId,
        size: pag.pageSize,
        page: pag?.current,
      })
    }

    const refreshTableData = (isReload = true) => {
      run({
        safetyId,
        page: isReload ? 1 : pagination.value.current,
        size: pagination.value?.pageSize,
      })
    }

    const handleSave = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addDomainBlacklist,
        edit: updateDomainBlacklist
      }
      try {
        const res = await requestMethodEnum[mode]?.({...data, safetyId})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        refreshTableData()
      } catch (err) {
        callback(true, err)
      }
    }

    const handleChangeStatus = async ({status, ...row}) => {
      await updateDomainBlacklist({...row, enable: status, safetyId})
    }

    const handleDelete = async (row = {}) => {
      proxy.$confirm({
        title: '删除',
        content: `确认删除该条数据吗?`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteDomainBlacklist({ ...row, safetyId })
            proxy.$message.success('删除成功！')
            refreshTableData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
    }

    const handleOpenDialog = (mode = 'add', row = {}) => {
      state.dialogConfig.visible = true
      state.dialogConfig.mode = mode
      state.dialogConfig.defaultData = row
    }

    onMounted(() => {
      refreshTableData()
    })

    return {
      ...toRefs(state),
      SEARCH_CONFIG,
      pagination,
      loading,
      dataSource,
      refreshTableData,
      handleOpenDialog,
      handleTableChange,
      handleSave,
      handleDelete,
      columns: getColumns({handleChangeStatus, pagination})
    }
  }
})
</script>
<div lang="less" scoped>
  .domain-blacklist--wrapper {
    .drawer-table-operate {
      justify-content: space-between;
    }
  }
</div> 