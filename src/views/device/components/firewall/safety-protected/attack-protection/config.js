/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
    {
        title: "名称",
        name: "name",
        type: "string",
        "x-component": "Input",
        "x-component-props": {
            placeholder: "请输入名称",
        },
    },
];

/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns({ handleChangeStatus, pagination }) {
    return [
        {
            title: "序号",
            dataIndex: "num",
            width: 100,
            align: "center",
            fixed: "left",
            customRender({ index }) {
                return (
                    pagination.value.pageSize * (pagination.value.current - 1) + index + 1
                );
            },
        },
        {
            title: "名称",
            dataIndex: "name",
            key: "name",
            fixed: "left",
            width: 200,
        },
        {
            title: "描述",
            dataIndex: "description",
            key: "description",
            width: 300,
            customRender: ({ record }) => {
                return record.description || "-";
            },
        },
        {
            title: "操作",
            dataIndex: "operation",
            key: "operation",
            fixed: "right",
            width: 220,
        },
    ];
}

/** @type {*} 表单schema */
export const SCHEMA = {
    type: "object",
    properties: {
        layout1: {
            type: "void",
            "x-component": "FormLayout",
            "x-component-props": {
              labelWidth: 120,
            },
            properties: {
                name: {
                    type: "string",
                    title: "名称",
                    "x-validator": [{ required: true, message: "请输入名称" }],
                    "x-component": "Input",
                    "x-decorator": "FormItem",
                    "x-component-props": {
                        placeholder: "请输入",
                    },
                },
                description: {
                    type: "string",
                    title: "描述",
                    "x-component": "Input.TextArea",
                    "x-decorator": "FormItem",
                    "x-component-props": {
                        placeholder: "请输入",
                        rows: 3,
                    },
                },
                collapse: {
                    type: 'void',
                    'x-component': 'FormCollapse',
                    properties: {
                        tab1: {
                            type: 'void',
                            'x-component': 'FormCollapse.CollapsePanel',
                            'x-component-props': {
                                header: 'Anti-Flood Attack',
                            },
                            properties: {
                                antiFloodEnable: {
                                    type: "boolean",
                                    title: "启用",
                                    default: true,
                                    "x-component": "Switch",
                                    "x-decorator": "FormItem",
                                    'x-reactions': field => {
                                        field.form.setFieldState('*(tcpFloodSourceEnable, tcpFloodDestEnable, tcpFloodTotalEnable, udpFloodSourceEnable, udpFloodDestEnable, udpFloodTotalEnable, icmpFloodSourceEnable, icmpFloodDestEnable, icmpFloodTotalEnable)', (state) => {
                                            state.value = false;
                                            state.disabled = !field.value
                                        })
                                    }
                                },
                                // TCP Flood
                                tcpFloodSection: {
                                    type: "void",
                                    "x-component": "div",
                                    title: "TCP Flood",
                                    "x-decorator": "FormItem",
                                    "x-component-props": {
                                        labelWidth: 120,
                                    },
                                    properties: {
                                        tcpSourceSpace1: {
                                            type: "void",
                                            "x-component": "Space",
                                            properties: {
                                                tcpFloodSourceEnable: {
                                                    type: "boolean",
                                                    title: "每主机报文速率限制(源IP)",
                                                    default: false,
                                                    "x-component": "Switch",
                                                    "x-decorator": "FormItem",
                                                    'x-reactions': {
                                                        "target": "tcpFloodSourceRate",
                                                        "when": "{{$self.value}}",
                                                        "fulfill": {
                                                            "state": {
                                                                "disabled": false
                                                            }
                                                        },
                                                        "otherwise": {
                                                            "state": {
                                                                "disabled": true,
                                                                "value": ''
                                                            }
                                                        }
                                                    }
                                                },
                                                tcpFloodSourceRate: {
                                                    type: "number",
                                                    title: "",
                                                    default: 1000,
                                                    "x-component": "InputNumber",
                                                    "x-decorator": "FormItem",
                                                    "x-component-props": {
                                                        placeholder: "请输入",
                                                        min: 1,
                                                        max: 10000,
                                                        addonAfter: "/秒",
                                                    },
                                                },
                                            }
                                        },
                                        tcpSourceSpace2: {
                                            type: "void",
                                            "x-component": "Space",
                                            properties: {
                                                tcpFloodDestEnable: {
                                                    type: "boolean",
                                                    title: "每主机报文速率限制(目的IP)",
                                                    default: false,
                                                    "x-component": "Switch",
                                                    "x-decorator": "FormItem",
                                                    'x-reactions': {
                                                        "target": "tcpFloodDestRate",
                                                        "when": "{{$self.value}}",
                                                        "fulfill": {
                                                            "state": {
                                                                "disabled": false
                                                            }
                                                        },
                                                        "otherwise": {
                                                            "state": {
                                                                "disabled": true,
                                                                "value": ''
                                                            }
                                                        }
                                                    }
                                                },
                                                tcpFloodDestRate: {
                                                    type: "number",
                                                    title: "",
                                                    default: 1000,
                                                    "x-component": "InputNumber",
                                                    "x-decorator": "FormItem",
                                                    "x-component-props": {
                                                        placeholder: "请输入",
                                                        min: 1,
                                                        max: 10000,
                                                        addonAfter: "/秒",
                                                    },
                                                },
                                            }
                                        },
                                        tcpSourceSpace3: {
                                            type: "void",
                                            "x-component": "Space",
                                            properties: {
                                                tcpFloodTotalEnable: {
                                                    type: "boolean",
                                                    title: "总报文速率限制",
                                                    default: false,
                                                    "x-component": "Switch",
                                                    "x-decorator": "FormItem",
                                                    'x-reactions': {
                                                        "target": "tcpFloodTotalRate",
                                                        "when": "{{$self.value}}",
                                                        "fulfill": {
                                                            "state": {
                                                                "disabled": false
                                                            }
                                                        },
                                                        "otherwise": {
                                                            "state": {
                                                                "disabled": true,
                                                                "value": ''
                                                            }
                                                        }
                                                    }
                                                },
                                                tcpFloodTotalRate: {
                                                    type: "number",
                                                    title: "",
                                                    default: 20000,
                                                    "x-component": "InputNumber",
                                                    "x-decorator": "FormItem",
                                                    "x-component-props": {
                                                        placeholder: "请输入",
                                                        min: 1,
                                                        max: 100000,
                                                        addonAfter: "/秒",
                                                    },
                                                },
                                            }
                                        },
                                        tcpFloodAction: {
                                            type: "string",
                                            title: "动作",
                                            default: "block",
                                            "x-component": "Select",
                                            "x-decorator": "FormItem",
                                            "x-component-props": {
                                                placeholder: "请选择动作",
                                                options: [
                                                    { label: "阻断", value: "block" },
                                                    { label: "放行", value: "allow" },
                                                    { label: "隔离", value: "quarantine" },
                                                ],
                                            },
                                            'x-reactions': {
                                                "dependencies": ["tcpFloodSourceEnable", 'tcpFloodDestEnable', 'tcpFloodTotalEnable'], //依赖路径写法默认是取value，如果依赖的是字段的其他属性，可以使用 source#modified，用#分割取详细属性
                                                // "dependencies":{ aliasName:"source" }, //别名形式
                                                "fulfill": {
                                                    "schema": {
                                                        "x-disabled": "{{!$deps.includes(true)}}" //任意层次属性都支持表达式
                                                    }
                                                }
                                            }
                                        },
                                    },
                                },
                                // UDP Flood
                                udpFloodSection: {
                                    type: "void",
                                    "x-component": "div",
                                    title: "UDP Flood",
                                    "x-decorator": "FormItem",
                                    "x-component-props": {
                                        labelWidth: 120,
                                    },
                                    properties: {
                                        udpSourceSpace1: {
                                            type: "void",
                                            "x-component": "Space",
                                            properties: {
                                                udpFloodSourceEnable: {
                                                    type: "boolean",
                                                    title: "每主机报文速率限制(源IP)",
                                                    default: false,
                                                    "x-component": "Switch",
                                                    "x-decorator": "FormItem",
                                                    'x-reactions': {
                                                        "target": "udpFloodSourceRate",
                                                        "when": "{{$self.value}}",
                                                        "fulfill": {
                                                            "state": {
                                                                "disabled": false
                                                            }
                                                        },
                                                        "otherwise": {
                                                            "state": {
                                                                "disabled": true,
                                                                "value": ''
                                                            }
                                                        }
                                                    }
                                                },
                                                udpFloodSourceRate: {
                                                    type: "number",
                                                    title: "",
                                                    default: 1000,
                                                    "x-component": "InputNumber",
                                                    "x-decorator": "FormItem",
                                                    "x-component-props": {
                                                        placeholder: "请输入",
                                                        min: 1,
                                                        max: 10000,
                                                        addonAfter: "/秒",
                                                    },
                                                },
                                            }
                                        },
                                        udpSourceSpace2: {
                                            type: "void",
                                            "x-component": "Space",
                                            properties: {
                                                udpFloodDestEnable: {
                                                    type: "boolean",
                                                    title: "每主机报文速率限制(目的IP)",
                                                    default: false,
                                                    "x-component": "Switch",
                                                    "x-decorator": "FormItem",
                                                    'x-reactions': {
                                                        "target": "udpFloodDestRate",
                                                        "when": "{{$self.value}}",
                                                        "fulfill": {
                                                            "state": {
                                                                "disabled": false
                                                            }
                                                        },
                                                        "otherwise": {
                                                            "state": {
                                                                "disabled": true,
                                                                "value": ''
                                                            }
                                                        }
                                                    }
                                                },
                                                udpFloodDestRate: {
                                                    type: "number",
                                                    title: "",
                                                    default: 1000,
                                                    "x-component": "InputNumber",
                                                    "x-decorator": "FormItem",
                                                    "x-component-props": {
                                                        placeholder: "请输入",
                                                        min: 1,
                                                        max: 10000,
                                                        addonAfter: "/秒",
                                                    },
                                                },
                                            }
                                        },
                                        udpSourceSpace3: {
                                            type: "void",
                                            "x-component": "Space",
                                            properties: {
                                                udpFloodTotalEnable: {
                                                    type: "boolean",
                                                    title: "总报文速率限制",
                                                    default: false,
                                                    "x-component": "Switch",
                                                    "x-decorator": "FormItem",
                                                    'x-reactions': {
                                                        "target": "udpFloodTotalRate",
                                                        "when": "{{$self.value}}",
                                                        "fulfill": {
                                                            "state": {
                                                                "disabled": false
                                                            }
                                                        },
                                                        "otherwise": {
                                                            "state": {
                                                                "disabled": true,
                                                                "value": ''
                                                            }
                                                        }
                                                    }
                                                },
                                                udpFloodTotalRate: {
                                                    type: "number",
                                                    title: "",
                                                    default: 20000,
                                                    "x-component": "InputNumber",
                                                    "x-decorator": "FormItem",
                                                    "x-component-props": {
                                                        placeholder: "请输入",
                                                        min: 1,
                                                        max: 100000,
                                                        addonAfter: "/秒",
                                                    },
                                                },
                                            }
                                        },
                                        udpFloodAction: {
                                            type: "string",
                                            title: "动作",
                                            default: "block",
                                            "x-component": "Select",
                                            "x-decorator": "FormItem",
                                            "x-component-props": {
                                                placeholder: "请选择动作",
                                                options: [
                                                    { label: "阻断", value: "block" },
                                                    { label: "放行", value: "allow" },
                                                    { label: "隔离", value: "quarantine" },
                                                ],
                                            },
                                            'x-reactions': {
                                                "dependencies": ["udpFloodSourceEnable", 'udpFloodDestEnable', 'udpFloodTotalEnable'],
                                                "fulfill": {
                                                    "schema": {
                                                        "x-disabled": "{{!$deps.includes(true)}}"
                                                    }
                                                }
                                            }
                                        },
                                    },
                                },
                                // ICMP Flood
                                icmpFloodSection: {
                                    type: "void",
                                    "x-component": "div",
                                    title: "ICMP Flood",
                                    "x-decorator": "FormItem",
                                    "x-component-props": {
                                        labelWidth: 120,
                                    },
                                    properties: {
                                        icmpSourceSpace1: {
                                            type: "void",
                                            "x-component": "Space",
                                            properties: {
                                                icmpFloodSourceEnable: {
                                                    type: "boolean",
                                                    title: "每主机报文速率限制(源IP)",
                                                    default: false,
                                                    "x-component": "Switch",
                                                    "x-decorator": "FormItem",
                                                    'x-reactions': {
                                                        "target": "icmpFloodSourceRate",
                                                        "when": "{{$self.value}}",
                                                        "fulfill": {
                                                            "state": {
                                                                "disabled": false
                                                            }
                                                        },
                                                        "otherwise": {
                                                            "state": {
                                                                "disabled": true,
                                                                "value": ''
                                                            }
                                                        }
                                                    }
                                                },
                                                icmpFloodSourceRate: {
                                                    type: "number",
                                                    title: "",
                                                    default: 1000,
                                                    "x-component": "InputNumber",
                                                    "x-decorator": "FormItem",
                                                    "x-component-props": {
                                                        placeholder: "请输入",
                                                        min: 1,
                                                        max: 10000,
                                                        addonAfter: "/秒",
                                                    },
                                                },
                                            }
                                        },
                                        icmpSourceSpace2: {
                                            type: "void",
                                            "x-component": "Space",
                                            properties: {
                                                icmpFloodDestEnable: {
                                                    type: "boolean",
                                                    title: "每主机报文速率限制(目的IP)",
                                                    default: false,
                                                    "x-component": "Switch",
                                                    "x-decorator": "FormItem",
                                                    'x-reactions': {
                                                        "target": "icmpFloodDestRate",
                                                        "when": "{{$self.value}}",
                                                        "fulfill": {
                                                            "state": {
                                                                "disabled": false
                                                            }
                                                        },
                                                        "otherwise": {
                                                            "state": {
                                                                "disabled": true,
                                                                "value": ''
                                                            }
                                                        }
                                                    }
                                                },
                                                icmpFloodDestRate: {
                                                    type: "number",
                                                    title: "",
                                                    default: 1000,
                                                    "x-component": "InputNumber",
                                                    "x-decorator": "FormItem",
                                                    "x-component-props": {
                                                        placeholder: "请输入",
                                                        min: 1,
                                                        max: 10000,
                                                        addonAfter: "/秒",
                                                    },
                                                },
                                            }
                                        },
                                        icmpSourceSpace3: {
                                            type: "void",
                                            "x-component": "Space",
                                            properties: {
                                                icmpFloodTotalEnable: {
                                                    type: "boolean",
                                                    title: "总报文速率限制",
                                                    default: false,
                                                    "x-component": "Switch",
                                                    "x-decorator": "FormItem",
                                                    'x-reactions': {
                                                        "target": "icmpFloodTotalRate",
                                                        "when": "{{$self.value}}",
                                                        "fulfill": {
                                                            "state": {
                                                                "disabled": false
                                                            }
                                                        },
                                                        "otherwise": {
                                                            "state": {
                                                                "disabled": true,
                                                                "value": ''
                                                            }
                                                        }
                                                    }
                                                },
                                                icmpFloodTotalRate: {
                                                    type: "number",
                                                    title: "",
                                                    default: 20000,
                                                    "x-component": "InputNumber",
                                                    "x-decorator": "FormItem",
                                                    "x-component-props": {
                                                        placeholder: "请输入",
                                                        min: 1,
                                                        max: 100000,
                                                        addonAfter: "/秒",
                                                    },
                                                },
                                            }
                                        },
                                        icmpFloodAction: {
                                            type: "string",
                                            title: "动作",
                                            default: "block",
                                            "x-component": "Select",
                                            "x-decorator": "FormItem",
                                            "x-component-props": {
                                                placeholder: "请选择动作",
                                                options: [
                                                    { label: "阻断", value: "block" },
                                                    { label: "放行", value: "allow" },
                                                    { label: "隔离", value: "quarantine" },
                                                ],
                                            },
                                            'x-reactions': {
                                                "dependencies": ["icmpFloodSourceEnable", 'icmpFloodDestEnable', 'icmpFloodTotalEnable'],
                                                "fulfill": {
                                                    "schema": {
                                                        "x-disabled": "{{!$deps.includes(true)}}"
                                                    }
                                                }
                                            }
                                        },
                                    },
                                },
                            },
                        },
                        tab2: {
                            type: 'void',
                            'x-component': 'FormCollapse.CollapsePanel',
                            'x-component-props': {
                                header: '防扫描',
                            },
                            properties: {
                                antiScanEnable: {
                                    type: "boolean",
                                    title: "启用",
                                    "x-component": "Switch",
                                    "x-decorator": "FormItem",
                                    'x-reactions': field => {
                                        field.form.setFieldState('*(scanType,scanThreshold, hostSuppressionTime)', (state) => {
                                            state.value = false;
                                            state.disabled = !field.value
                                        })
                                    }
                                },
                                scanType: {
                                    type: 'array',
                                    title: '扫描类型',
                                    'x-decorator': 'FormItem',
                                    'x-component': 'Checkbox.Group',
                                    enum: [
                                        {
                                            label: 'TCP协议扫描',
                                            value: 'tcp',
                                        },
                                        {
                                            label: 'UDP协议扫描',
                                            value: 'udp',
                                        },
                                        {
                                            label: "PING扫描",
                                            value: 'ping'
                                        }
                                    ],
                                },
                                scanThreshold: {
                                    type: "number",
                                    title: "扫描识别阈值",
                                    default: 1000,
                                    "x-component": "InputNumber",
                                    "x-decorator": "FormItem",
                                    "x-component-props": {
                                        placeholder: "请输入",
                                        min: 10,
                                        max: 65535,
                                        addonAfter: "（10-65535）连接/秒",
                                    },
                                },
                                hostSuppressionTime: {
                                    type: "number",
                                    title: "主机抑制时长",
                                    default: 20,
                                    "x-component": "InputNumber",
                                    "x-decorator": "FormItem",
                                    "x-component-props": {
                                        placeholder: "请输入",
                                        min: 1,
                                        max: 65535,
                                        addonAfter: "（1-65535）秒",
                                    },
                                },
                            },
                        }
                    }
                },
            },
        },
    },
}; 