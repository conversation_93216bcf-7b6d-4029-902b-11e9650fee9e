/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: "名称",
    name: "name",
    type: "string",
    "x-component": "Input",
    "x-component-props": {
      placeholder: "请输入名称",
    },
  },
];

/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns({ handleChangeStatus, pagination }) {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: "left",
      customRender({ index }) {
        return (
          pagination.value.pageSize * (pagination.value.current - 1) + index + 1
        );
      },
    },
    {
      title: 'ID',
      dataIndex: 'id',
      key: 'id',
      width: 100
    },
    {
      title: "名称",
      dataIndex: "name",
      key: "name",
      fixed: "left",
      width: 200,
    },
    {
      title: "协议",
      dataIndex: "protocols",
      key: "protocols",
      width: 300,
    },
    {
      title: "行为",
      dataIndex: "action",
      key: "action",
      width: 150,
      customRender: ({ record }) => {
        const actionMap = {
          allow: "放行",
          block: "阻止",
          quarantine: "隔离",
        };
        return actionMap[record.action] || record.action || "-";
      },
    },
    {
      title: "操作",
      dataIndex: "operation",
      key: "operation",
      fixed: "right",
      width: 220,
    },
  ];
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: "object",
  properties: {
    layout1: {
      type: "void",
      "x-component": "FormLayout",
      "x-component-props": {
        labelWidth: 120,
      },
      properties: {
        name: {
          type: "string",
          title: "名称",
          "x-validator": [{ required: true, message: "请输入名称" }],
          "x-component": "Input",
          "x-decorator": "FormItem",
          "x-component-props": {
            placeholder: "请输入",
          },
        },
        protocols: {
          type: "array",
          title: "协议",
          "x-component": "Checkbox.Group",
          "x-decorator": "FormItem",
          "x-component-props": {
            options: [
              { label: "POP3", value: "POP3" },
              { label: "HTTP", value: "HTTP" },
              { label: "FTP", value: "FTP" },
              { label: "IMAP", value: "IMAP" },
              { label: "SMTP", value: "SMTP" },
            ],
          },
        },
        action: {
          type: "string",
          title: "行为",
          "x-component": "Select",
          "x-decorator": "FormItem",
          "x-component-props": {
            placeholder: "请选择行为",
            options: [
              { label: "放行", value: "allow" },
              { label: "阻止", value: "block" },
              { label: "隔离", value: "quarantine" },
            ],
          },
        },
      },
    },
  },
};


export const transformDataToReq = (data) => {
  return {
    ...data,
    protocols: data.protocols?.join(','),
  }
}

export const transformReqToData = (data) => {
  return {
    ...data,
    action: data.action || '-',
  }
}