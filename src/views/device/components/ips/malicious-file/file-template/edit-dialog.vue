<template>
  <FormProvider :form="form">
    <a-modal
      v-model:visible="dialogVisible"
      :title="title"
      width="850px"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleClose"
    >
      <a-spin :spinning="loading">
        <SchemaField :schema="SCHEMA" :scope="{formStep}"></SchemaField>
      </a-spin>
      <template #footer>
        <FormConsumer>
          <a-space>
            <a-button
              type="primary"
              ghost
              :disabled="!formStep.allowBack"
              @click="() => formStep.back()"
            >
              上一步
            </a-button>
            <a-button
              type="primary"
              ghost
              :disabled="!formStep.allowNext"
              @click="() => handleNextStep()"
            >
              下一步
            </a-button>
            <a-button
              v-if="!isView"
              :disabled="formStep.allowNext"
              type="primary"
              :loading="loading"
              @click="handleSave">保存</a-button>
            <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">
              {{isView ? '关闭' : '取消'}}
            </a-button>
          </a-space>
        </FormConsumer>
      </template>
    </a-modal>
  </FormProvider>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm, onFieldReact, onFieldValueChange } from '@formily/core'
import { FormStep } from '@formily/antdv-x3'
import { cloneDeep, keyBy } from 'lodash'

import { SCHEMA, getDirectionByProtocol, getActionsByProtocol, PROTOCOL_LIST } from './config';

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { visible, mode, defaultData } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}恶意文件模板`
    })

    const state = reactive({
      loading: false
    })

    const form = createForm({
      effects: () =>{
        onFieldReact('protocolList.*.direction', (field) => {
          if (!field) return;
          const protocol = field.query('.protocol').get('value');
          const flag = ['HTTP', 'FTP'].includes(protocol);
          field.dataSource = getDirectionByProtocol(protocol);
          if (flag) return;
          field.readPretty = true;
        })
        onFieldReact('protocolList.*.action', (field) => {
          const protocol = field.query('.protocol').get('value');
          const dataSource = getActionsByProtocol(protocol)
          field.setComponentProps({options: dataSource})
        })
        onFieldValueChange('sandBoxLinkageDetection', (field) => {
          const isOpen = !!field.value;
          const display = isOpen ? 'visible' : 'hidden';
          field.query('.fileDeliveryType')?.take()?.setDisplay(display)
          field.query('.fileExclude')?.take()?.setDisplay(display)
        })
      }
    })

    const formStep = FormStep.createFormStep()

    const handleNextStep = () => {
      if (!isView.value) {
        const selectedProtocol = form.values.protocolList.some(item => item.checked);
        if (!selectedProtocol) return proxy.$message.warning('请至少选择一项协议类型')
      }
      formStep.next()
    }

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      form.setPattern(patternMap[mode.value])
      if (mode === 'add') return;
      const formData = cloneDeep(defaultData.value);
      const protocolMap = keyBy(formData.protocolList, 'protocol');
      const protocolList = cloneDeep(PROTOCOL_LIST).map(item => {
        if (protocolMap[item.protocol]) return {...protocolMap[item.protocol], checked: true};
        return item
      });
      await nextTick();
      form.setValues({...formData, protocolList})
    }

    /**
     * 关闭弹窗处理
     * @param {boolean} hasError 是否有错误
     */
    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return;
      state.loading = false
      for (let i = 2; i >= 0; i--) { formStep.back() }
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    /**
     * 保存表单数据
     */
    const handleSave = async () => {
      await form.validate()
      const params = cloneDeep(form.values);
      if (!params.heuristicDetection && !params.sandBoxLinkageDetection) return proxy.$message.warning('至少启用一项检测方式')
      params.checkSandBox = 1;
      params.protocolList = params.protocolList?.filter(item => item.checked)
      state.loading = true
      ctx.emit('on-success', {
        mode: mode.value,
        data: params,
        callback: handleClose
      })
    }

    onMounted(() => {
    })

    return {
      title,
      isView,
      form,
      formStep,
      SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleNextStep,
      handleClose,
      handleSave
    }
  }
})
</script>
