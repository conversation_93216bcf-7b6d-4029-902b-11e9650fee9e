import { cloneDeep } from 'lodash';
import dayjs from 'dayjs'

/** @type {*} 模板定义类型枚举 */
export const TEMPLATE_TYPE_ENUM = {
  CUSTOMIZE: 1,
  PREDEFINE: 0
}

/** @type {*} 模板类型名称映射 */
export const TEMPLATE_TYPE_LABEL_MAP = {
  [TEMPLATE_TYPE_ENUM.CUSTOMIZE]: '自定义',
  [TEMPLATE_TYPE_ENUM.PREDEFINE]: '预定义'
}

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '模板名称',
    dataIndex: 'name',
    ellipsis: true,
    key: 'name',
    width: 200,
  },
  {
    title: '描述',
    dataIndex: 'desc',
    ellipsis: true,
    key: 'desc',
    width: 150,
  },
  {
    title: '内容',
    dataIndex: 'protocolList',
    ellipsis: true,
    key: 'protocolList',
    width: 150,
  },
  {
    title: '启发式检测',
    dataIndex: 'heuristicDetection',
    ellipsis: true,
    key: 'heuristicDetection',
    width: 120,
  },
  {
    title: '沙箱联动检测',
    dataIndex: 'sandBoxLinkageDetection',
    ellipsis: true,
    key: 'sandBoxLinkageDetection',
    width: 120,
  },
  {
    title: '引用次数',
    dataIndex: 'cite_count',
    ellipsis: true,
    key: 'cite_count',
    width: 100,
  },
  {
    title: '模板类型',
    dataIndex: 'type',
    ellipsis: true,
    key: 'type',
    width: 120,
    customRender: ({ record }) => {
      return TEMPLATE_TYPE_LABEL_MAP[record.type] ?? '-'
    }
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    ellipsis: true,
    key: 'create_time',
    width: 200,
    customRender: ({ text }) => {
      if (!text) return '-'
      return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 220
  }
]

/** @type {*} 文件投递类型列表 */
const FILE_TYPE_LIST = [
  { label: 'Office文档', value: 'office_file' },
  { label: '压缩文件', value: 'compress_file' },
  { label: '执行文件(Windows)', value: 'exe_win' },
  { label: '执行文件(Linux)', value: 'exe_linux' },
  { label: '脚本文件', value: 'script_file' },
  { label: '图形文件', value: 'draw_file' },
  { label: '邮件文件', value: 'mail_file' },
  { label: '移动终端', value: 'mobile_terminal' },
  { label: '网页文件', value: 'web_file' },
  { label: '快捷方式', value: 'link_style' },
  { label: '其他文件', value: 'other_file' }
]

/** @type {*} HTTP或FTP协议 - 方向数据源 */
const HTTP_DIRECTION_LIST = [
  { label: '双向', value: 'both' },
  { label: '上传', value: 'send' },
  { label: '下载', value: 'receive' }
];

/** @type {*} 协议数据源 */
export const PROTOCOL_LIST = [
  { checked: false, protocol: 'HTTP', direction: 'both', action: 'log' },
  { checked: false, protocol: 'FTP', direction: 'both', action: 'log' },
  { checked: false, protocol: 'SMTP', direction: 'send', action: 'log' },
  { checked: false, protocol: 'POP3', direction: 'receive', action: 'log' },
  { checked: false, protocol: 'IMAP', direction: 'receive', action: 'log' }
];

/**
 * 根据协议获取方向数据源
 *
 * @param {*} protocol
 * @return {*}
 */
export function getDirectionByProtocol(protocol) {
  const isFtpOrHttp = ['HTTP', 'FTP'].includes(protocol)
  if (!isFtpOrHttp) {
    return [
      { label: '双向', value: 'both' },
      { label: '发邮件', value: 'send' },
      { label: '收邮件', value: 'receive' }
    ]
  } else {
    return HTTP_DIRECTION_LIST
  }
}

/**
 * 根据协议获取病毒检测动作数据源
 *
 * @param {*} protocol
 * @return {*}
 */
export function getActionsByProtocol(protocol) {
  const actionLabelMap = {
    log: '仅记录日志',
    block: '阻断',
    block_remind: '阻断且提醒'
  }
  const protocolObj = {
    HTTP: ['log', 'block', 'block_remind'],
    FTP: ['log', 'block'],
    POP3: ['log'],
    SMTP: ['log', 'block', 'block_remind'],
    IMAP: ['log']
  };
  const actions = protocolObj[protocol]?.map(item => ({
    label: actionLabelMap[item],
    value: item
  })) ?? [];
  if ('POP3' === protocol) actions.push({label: '附件提醒', value: 'block_remind'})
  return actions;
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 110
      },
      properties: {
        step: {
          type: 'void',
          'x-component': 'FormStep',
          'x-component-props': {
            formStep: '{{formStep}}'
          },
          properties: {
            scanConfig: {
              type: 'void',
              'x-component': 'FormStep.StepPane',
              'x-component-props': {
                title: '文件扫描配置'
              },
              properties: {
                name: {
                  type: 'string',
                  title: '模板名称',
                  'x-validator': [
                    { required: true, message: '请输入模板名称' },
                  ],
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                  'x-component-props': {
                    placeholder: '请输入模板名称'
                  }
                },
                protocolList: {
                  type: 'array',
                  'x-validator': [
                    { required: true, message: '请选择' },
                  ],
                  title: '协议类型',
                  default: cloneDeep(PROTOCOL_LIST),
                  'x-decorator': 'FormItem',
                  'x-component': 'ArrayTable',
                  'x-component-props': {
                    pagination: { pageSize: 10 },
                    scroll: { x: '100%' },
                    rowKey: 'protocol',
                  },
                  items: {
                    type: 'object',
                    properties: {
                      column0: {
                        type: 'void',
                        'x-component': 'ArrayTable.Column',
                        'x-component-props': {
                          width: 40,
                          key: 'checked',
                          dataIndex: 'checked',
                          ellipsis: true,
                          title: ''
                        },
                        properties: {
                          checked: {
                            type: 'boolean',
                            'x-decorator': 'FormItem',
                            'x-component': 'Checkbox'
                          }
                        }
                      },
                      column1: {
                        type: 'void',
                        'x-component': 'ArrayTable.Column',
                        'x-component-props': { title: '协议', key: 'protocol', dataIndex: 'protocol' },
                        ellipsis: true,
                        properties: {
                          protocol: {
                            type: 'string',
                            'x-decorator': 'FormItem',
                            'x-component': 'PreviewText.Input'
                          }
                        }
                      },
                      column2: {
                        type: 'void',
                        'x-component': 'ArrayTable.Column',
                        'x-component-props': { title: '检测方向', key: 'direction', dataIndex: 'direction' },
                        ellipsis: true,
                        properties: {
                          direction: {
                            type: 'string',
                            'x-decorator': 'FormItem',
                            'x-component': 'Select',
                            enum: []
                          }
                        }
                      },
                      column3: {
                        type: 'void',
                        'x-component': 'ArrayTable.Column',
                        'x-component-props': { title: '病毒检测动作', key: 'action', dataIndex: 'action' },
                        ellipsis: true,
                        properties: {
                          action: {
                            type: 'string',
                            default: '1',
                            'x-decorator': 'FormItem',
                            'x-component': 'Radio.Group',
                            'x-component-props': {
                              isGroup: true,
                              optionType: "button",
                              options: []
                            }
                          },
                        }
                      }
                    }
                  }
                },
                desc: {
                  type: 'string',
                  'x-component': 'Input.TextArea',
                  'x-component-props': {
                    placeholder: '请输入备注'
                  },
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    label: '备注'
                  }
                }
              }
            },
            detection: {
              type: 'void',
              'x-component': 'FormStep.StepPane',
              'x-component-props': {
                title: '检测方式'
              },
              properties: {
                heuristicDetection: {
                  type: 'number',
                  default: 0,
                  'x-decorator': 'FormItem',
                  'x-component': 'Switch',
                  'x-component-props': {
                    checkedValue: 1,
                    unCheckedValue: 0
                  },
                  'x-decorator-props': {
                    label: '启发式检测',
                    wrapperWidth: 45,
                    addonAfter: '启用后，反恶意文件引擎将对网络流量中传输的文件进行病毒分析与检测。'
                  }
                },
                sandBoxLinkageDetection: {
                  type: 'number',
                  default: 0,
                  'x-decorator': 'FormItem',
                  'x-component': 'Switch',
                  'x-component-props': {
                    checkedValue: 1,
                    unCheckedValue: 0
                  },
                  'x-decorator-props': {
                    label: '沙箱联动检测',
                    addonAfter: '启用后，反恶意文件引擎检测到可疑文件时，将其投递到沙箱后运行检测。主要用于检测未知病毒。'
                  }
                },
                fileDeliveryType: {
                  type: 'array',
                  'x-display': 'hidden',
                  'x-validator': [
                    { required: true, message: '请选择文件投递类型' },
                  ],
                  default: FILE_TYPE_LIST.map(item => item.value),
                  'x-component': 'Checkbox.Group',
                  'x-component-props': {
                    isGroup: true,
                    options: FILE_TYPE_LIST
                  },
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    label: '文件投递类型'
                  }
                },
                fileExclude: {
                  type: 'void',
                  'x-display': 'hidden',
                  'x-component': 'FormLayout',
                  'x-decorator': 'FormItem',
                  title: '文件投递例外',
                  'x-component-props': {
                    labelWidth: 140
                  },
                  properties: {
                    fileNameKeyWordMatch: {
                      type: 'array',
                      'x-component': 'ArrayItems',
                      'x-decorator': 'FormItem',
                      title: '文件名',
                      items: {
                        type: 'void',
                        'x-component': 'Space',
                        properties: {
                          sort: {
                            type: 'void',
                            'x-decorator': 'FormItem',
                            'x-component': 'ArrayItems.SortHandle',
                          },
                          input: {
                            type: 'string',
                            'x-decorator': 'FormItem',
                            'x-component': 'Input',
                            'x-component-props': {
                              placeholder: '请输入文件名'
                            },
                            'x-validator': [
                              {
                                max: 31,
                              },
                              {
                                min: 0,
                              },
                            ],
                          },
                          remove: {
                            type: 'void',
                            'x-decorator': 'FormItem',
                            'x-component': 'ArrayItems.Remove',
                          },
                        },
                      },
                      properties: {
                        add: {
                          type: 'void',
                          title: '添加',
                          'x-component': 'ArrayItems.Addition',
                        },
                      },
                    },
                    sendAndReceiveAddressMatch: {
                      type: 'array',
                      'x-component': 'ArrayItems',
                      'x-decorator': 'FormItem',
                      title: '收件人/发件人过滤',
                      items: {
                        type: 'void',
                        'x-component': 'Space',
                        properties: {
                          sort: {
                            type: 'void',
                            'x-decorator': 'FormItem',
                            'x-component': 'ArrayItems.SortHandle',
                          },
                          input: {
                            type: 'string',
                            'x-decorator': 'FormItem',
                            'x-component': 'Input',
                            'x-component-props': {
                              placeholder: '请输入收件人/发件人过滤'
                            },
                            'x-validator': [
                              {
                                max: 31,
                              },
                              {
                                min: 0,
                              },
                            ],
                          },
                          remove: {
                            type: 'void',
                            'x-decorator': 'FormItem',
                            'x-component': 'ArrayItems.Remove',
                          },
                        },
                      },
                      properties: {
                        add: {
                          type: 'void',
                          title: '添加',
                          'x-component': 'ArrayItems.Addition',
                        },
                      },
                    }
                  }
                },
              }
            },
          }
        }
      }
    }
  }
}

