/** @type {*} 名单类型枚举 */
export const FILE_TYPE_ENUM = {
  BLACK: 'BLACK',
  WHITE: 'WHITE'
}

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '样本',
    name: 'search',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入样本名称或样本MD5',
    },
  },
]

/** @type {*} 检测状态枚举 */
export const CHECK_STATUS_ENUM = {
  FAILED: -1,
  PENDING: 1,
  WAITING: 0,
  SUCCESS: 2
}

/** @type {*} 检测状态文本映射 */
export const CHECK_STATUS_LABEL_ENUM = {
  [CHECK_STATUS_ENUM.FAILED]: '失败',
  [CHECK_STATUS_ENUM.PENDING]: '检测中',
  [CHECK_STATUS_ENUM.WAITING]: '待检测',
  [CHECK_STATUS_ENUM.SUCCESS]: '成功',
}

/** @type {*} 获取列表配置 */
export function getColumns({pagination}) {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: '样本MD5',
      dataIndex: 'file_md5',
      ellipsis: true,
      key: 'file_md5',
      width: 300
    },
    {
      title: '样本名称',
      dataIndex: 'name',
      ellipsis: true,
      key: 'name',
      width: 300
    },
    {
      title: '样本大小',
      dataIndex: 'file_size',
      ellipsis: true,
      key: 'file_size',
      width: 150
    },
    {
      title: '鉴定结果',
      dataIndex: 'detection_result',
      ellipsis: true,
      key: 'detection_result',
      width: 150,
      customRender: ({ record }) => {
        const result = record.detection_result === -1 ? '-' : record.detection_result;
        return <span>{result}</span>
      }
    },
    {
      title: '病毒名称',
      dataIndex: 'virus_name',
      ellipsis: true,
      key: 'virus_name',
      width: 150,
      customRender: ({ record }) => {
        const result = !record.virus_name ? '-' : record.virus_name;
        return <span>{result}</span>
      }
    },
    {
      title: '检测状态',
      dataIndex: 'detection_status',
      ellipsis: true,
      key: 'detection_status',
      width: 120,
      customRender: ({ record }) => {
        return CHECK_STATUS_LABEL_ENUM[record.detection_status] ?? '-'
      }
    },
    {
      title: '提交时间',
      dataIndex: 'gen_time',
      ellipsis: true,
      key: 'gen_time',
      width: 180,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 220
    }
  ]
}

/** @type {*} 表单配置 */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        file_md5: {
          type: 'string',
          title: '文件MD5',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入文件MD5'
          },
          'x-validator': [
            { required: true, message: '请输入文件MD5' },
            {
              max: 32,
            },
          ],
        },
        filename: {
          type: 'string',
          title: '文件名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入文件名称'
          },
          'x-validator': [
            {
              max: 128,
            },
            {
              validator:  `{{(value, rule)=> {
                if (!value) return ''
                return /^[a-zA-Z0-9\u4e00-\u9fa5-_./\\[\\]【】()]+$/.test(value)
              }}}`,
              message: '只允许输入 中文、数字、英文以及-._/[]【】()等字符',
            }
          ],
        },
        enable: {
          type: 'boolean',
          title: '启用',
          default: 0,
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 1,
            unCheckedValue: 0
          },
        },
        description: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            {
              max: 200,
            },
          ],
        }
      }
    }
  }
}
