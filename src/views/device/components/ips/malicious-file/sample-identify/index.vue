<template>
  <div>
    <DynamicSearch
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    />
    <a-space align="start" class="ips-drawer-table-operate">
      <a-button type="primary" @click="handleImport">
        导入样本
      </a-button>
      <a-button type="primary" @click="handleExport">
        导出
      </a-button>
    </a-space>
    <a-table
      :scroll="{y: 'calc(100vh - 420px)'}"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button type="link" @click="handleOpenDialog(FILE_TYPE_ENUM.BLACK, record)">加黑</a-button>
          <a-button type="link" @click="handleOpenDialog(FILE_TYPE_ENUM.WHITE, record)">加白</a-button>
          <a-button
            type="link"
            danger
            @click="handleDelete(record)"
            :disabled="CHECK_STATUS_ENUM.PENDING === record.detection_status"
          >删除</a-button>
        </template>
      </template>
    </a-table>
    <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="handleSave" />
    <ImportDialog
      v-model:visible="importDialogConfig.visible"
      @on-success="refreshTableData" />
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, computed, getCurrentInstance, createVNode, inject } from 'vue'
import { usePagination } from 'vue-request'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

import { getMaliciousFileSampleList, addMaliciousFileWhite, addMaliciousFileBlack, deleteMaliciousFileSample } from '@/request/api-device-ips'
import { getColumns, SEARCH_CONFIG, FILE_TYPE_ENUM, CHECK_STATUS_ENUM } from './config';

import DynamicSearch from "@/components/dynamic-search/index.vue";
import EditDialog from './edit-dialog.vue'
import ImportDialog from './import-dialog.vue'

import { downloadFile } from '@/utils/util'

export default defineComponent({
  name: 'MaliciousBlackList',
  components: {
    DynamicSearch,
    EditDialog,
    ImportDialog
  },
  setup () {
    const { proxy } = getCurrentInstance()
    const { deviceSafetyId } = inject('rowData')

    const state = reactive({
      searchParams: { search: '' },
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      },
      importDialogConfig: {
        visible: false,
      }
    })

    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getMaliciousFileSampleList, {
      manual: true,
      defaultParams: {
        deviceSafetyId,
        filter_data: state.searchParams
      },
      formatResult: ({data = {}}) => ({ items: data.data ?? [], total: data.total ?? 0 }),
      pagination: {
        currentKey: 'page_num',
        pageSizeKey: 'page_size',
      }
    })

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条`
    }))

    const dataSource = computed(() => {
      return data.value?.items || []
    })

    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        deviceSafetyId,
        page_size: pag.pageSize,
        page_num: pag?.current,
        filter_data: state.searchParams
      })
    }

    const handleSearch = (params = {}) => {
      state.searchParams = params;
      refreshTableData();
    }

    const refreshTableData = (isReload = true) => {
      run({
        deviceSafetyId,
        page_num: isReload ? 1 : pagination.value.current,
        page_size: pagination.value?.pageSize,
        filter_data: state.searchParams
      })
    }

    const handleSave = async ({ type, data, callback }) => {
      const isBlack = type === FILE_TYPE_ENUM.BLACK;
      const requestMethodEnum = {
        [FILE_TYPE_ENUM.BLACK]: addMaliciousFileBlack,
        [FILE_TYPE_ENUM.WHITE]: addMaliciousFileWhite
      }
      try {
        const res = await requestMethodEnum[type]?.({...data, deviceSafetyId})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isBlack ? '加黑成功！' : '加白成功！')
        callback()
        refreshTableData()
      } catch (err) {
        const errMsg = isBlack ? '加黑失败！' : '加白失败！';
        // proxy.$message.error(err.message || errMsg)
        callback(true, err)
      }
    }

    /**
     * 导入样本
     */
    const handleImport = () => {
      state.importDialogConfig.visible = true;
    }

    /**
     * 删除样本数据
     * @param {Object} row 行数据
     */
    const handleDelete = async (row = {}) => {
      proxy.$confirm({
        title: '删除',
        content: `确认删除 ${row.name} 数据吗?`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteMaliciousFileSample({ ...row, deviceSafetyId })
            proxy.$message.success('删除成功！')
            refreshTableData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
    }

    /**
     * 打开编辑弹窗
     * @param {string} type 操作类型
     * @param {Object} row 行数据
     */
    const handleOpenDialog = (type = FILE_TYPE_ENUM.BLACK, row = {}) => {
      state.dialogConfig.visible = true
      state.dialogConfig.type = type
      state.dialogConfig.defaultData = row
    }

    /**
     * 导出样本数据
     */
    const handleExport = async () => {
      const params = encodeURI(JSON.stringify({...state.searchParams, deviceSafetyId}));
      downloadFile(`/device-atomic/IPSQiming/sampleDetectionExport?params=${params}`)
    }

    return {
      FILE_TYPE_ENUM,
      SEARCH_CONFIG,
      CHECK_STATUS_ENUM,
      ...toRefs(state),
      pagination,
      loading,
      dataSource,
      handleSearch,
      refreshTableData,
      handleOpenDialog,
      handleTableChange,
      handleImport,
      handleSave,
      handleDelete,
      handleExport,
      columns: getColumns({pagination})
    }
  }
})
</script>
