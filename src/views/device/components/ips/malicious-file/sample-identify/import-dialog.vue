<template>
  <a-modal
    v-model:visible="dialogVisible"
    title="导入样本"
    width="800px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <a-upload-dragger
        v-model:fileList="fileList"
        :max-count="1"
        :beforeUpload="beforeUpload"
        :multiple="false"
        :data="{ deviceSafetyId }"
        action="/city/api/device-atomic/IPSQiming/sampleDetectionImport"
        with-credentials
      >
        <p class="ant-upload-drag-icon">
          <inbox-outlined></inbox-outlined>
        </p>
        <p class="ant-upload-text">将文件拖到此处，或点击上传</p>
        <p class="ant-upload-hint">
          文件大小不超过10M
        </p>
      </a-upload-dragger>
    </a-spin>
    <template #footer>
      <a-button
        type="primary"
        :loading="loading"
        @click="handleSave">提交</a-button>
      <a-button type="default" @click="handleClose">取消</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, getCurrentInstance, inject } from 'vue'
import { InboxOutlined } from '@ant-design/icons-vue'

import { importMaliciousFileSample } from '@/request/api-device-ips'

export default defineComponent({
  components: {
    InboxOutlined,
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { deviceSafetyId } = inject('rowData')
    const { visible } = toRefs(props)
    const dialogVisible = computed(() => visible.value)

    const state = reactive({
      fileList: [],
      loading: false
    })

    const beforeUpload = (file) => {
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        proxy.$message.error('文件大小不超过10M!');
      }
      return false;
    };

    /**
     * 关闭弹窗处理
     */
    const handleClose = async () => {
      state.fileList = []
      ctx.emit('update:visible', false)
    }

    /**
     * 提交导入的样本
     */
    const handleSave = async () => {
      const formData = new FormData();
      formData.append('deviceSafetyId', deviceSafetyId);
      for (const file of state.fileList) {
        formData.append('file', file.originFileObj);
      }
      try {
        state.loading = true;
        await importMaliciousFileSample(formData)
        proxy.$message.success('导入成功！')
        ctx.emit('on-success')
        handleClose()
      } catch(err) {
        console.log(err)
        proxy.$message.error('导入失败！')
      } finally {
        state.loading = false
      }
    }

    return {
      deviceSafetyId,
      ...toRefs(state),
      dialogVisible,
      beforeUpload,
      handleClose,
      handleSave
    }
  }
})
</script>
