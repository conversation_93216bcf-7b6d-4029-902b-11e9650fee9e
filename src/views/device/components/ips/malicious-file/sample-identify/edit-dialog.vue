<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="800px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep } from 'lodash'

import { SCHEMA, FILE_TYPE_ENUM } from './config'

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: FILE_TYPE_ENUM.BLACK
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { visible, type, defaultData } = toRefs(props)
    const dialogVisible = computed(() => visible.value)

    const title = computed(() => {
      const titleMap = {
        [FILE_TYPE_ENUM.BLACK]: '黑名单',
        [FILE_TYPE_ENUM.WHITE]: '白名单'
      }
      return `添加至恶意文件${titleMap[type.value]}`
    })
    const state = reactive({
      loading: false
    })

    const form = createForm()

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    const initForm = async () => {
      form.setValues(cloneDeep(defaultData.value))
    }

    /**
     * 关闭弹窗处理
     * @param {boolean} hasError 是否有错误
     */
    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    /**
     * 保存表单数据
     */
    const handleSave = async () => {
      await form.validate()
      state.loading = true
      const params = { ...form.values }
      ctx.emit('on-success', {
        type: type.value,
        data: params,
        callback: handleClose
      })
    }

    return {
      title,
      form,
      schema: SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script>
