import dayjs from 'dayjs'

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'search',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} 来源枚举 */
export const SOURCE_ENUM = {
  MANUAL: 'manual',
  IMPORT: 'import',
  CTI: 'cti',
  ALARM: 'alarm',
  BFA: 'bfa',
  TPP: 'tpp',
  WEB: 'web',
}

/** @type {*} 来源文本映射 */
export const SOURCE_LABEL_MAP = {
  [SOURCE_ENUM.MANUAL]: '手工添加',
  [SOURCE_ENUM.IMPORT]: '批量导入',
  [SOURCE_ENUM.CTI]: '威胁情报',
  [SOURCE_ENUM.ALARM]: '告警处置',
  [SOURCE_ENUM.BFA]: '暴力破解',
  [SOURCE_ENUM.TPP]: '三方平台',
  [SOURCE_ENUM.WEB]: 'WEB安全',
}

/** @type {*} 获取列表配置 */
export function getColumns({handleChangeStatus, pagination}) {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: '文件MD5',
      dataIndex: 'file_md5',
      ellipsis: true,
      key: 'file_md5'
    },
    {
      title: '文件名称',
      dataIndex: 'filename',
      ellipsis: true,
      key: 'filename'
    },
    {
      title: '创建时间',
      dataIndex: 'gen_time',
      ellipsis: true,
      width: 160,
      key: 'gen_time',
      customRender: ({ text }) => {
        if (!text) return '-'
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: '来源',
      dataIndex: 'source_type',
      ellipsis: true,
      key: 'source_type',
      customRender: ({ record }) => {
        return SOURCE_LABEL_MAP[record.source_type] ?? '-'
      }
    },
    {
      title: '描述',
      dataIndex: 'description',
      ellipsis: true,
      key: 'description'
    },
    {
      title: '启用',
      dataIndex: 'enable',
      ellipsis: true,
      key: 'enable',
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.enableLoading = true;
            const enable = record.enable === 1 ? 0 : 1;
            await handleChangeStatus?.({...record, enable});
            record.enable = enable;
          } finally {
            record.enableLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.enableLoading}
          checkedValue={1}
          unCheckedValue={0}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 220
    }
  ]
}

/** @type {*} 表单配置 */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        file_md5: {
          type: 'string',
          title: '文件MD5',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入文件MD5'
          },
          'x-validator': [
            { required: true, message: '请输入文件MD5' },
            {
              max: 32,
            },
          ],
        },
        file_sha1: {
          type: 'string',
          title: '文件SHA1',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入文件SHA1'
          },
          'x-validator': [
            {
              max: 40,
            },
          ],
        },
        filename: {
          type: 'string',
          title: '文件名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入文件名称'
          },
          'x-validator': [
            {
              max: 128,
            },
            {
              validator:  `{{(value, rule)=> {
                if (!value) return ''
                return /^[a-zA-Z0-9\u4e00-\u9fa5-_./\\[\\]【】()]+$/.test(value)
              }}}`,
              message: '只允许输入 中文、数字、英文以及-._/[]【】()等字符',
            }
          ],
        },
        enable: {
          type: 'boolean',
          title: '启用',
          default: 0,
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 1,
            unCheckedValue: 0
          },
        },
        description: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入备注'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '备注'
          },
          'x-validator': [
            {
              max: 200,
            },
          ],
        }
      }
    }
  }
}
