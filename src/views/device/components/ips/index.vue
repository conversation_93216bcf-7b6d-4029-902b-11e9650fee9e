<!-- 入侵防御系统 配置弹窗 -->
<template>
  <a-drawer
    v-model:visible="dialogVisible"
    placement="right"
    width="80%"
    :keyboard="false"
    :maskClosable="false"
    :onClose="handleClose"
  >
    <template #title>
      <a-tabs type="card" v-model:activeKey="activeKey">
        <a-tab-pane v-for="tab in IPS_TABS" :key="tab.componentName" :tab="tab.label">
        </a-tab-pane>
      </a-tabs>
    </template>
    <a-spin :spinning="loading">
      <component :is="activeKey" />
    </a-spin>
  </a-drawer>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, provide, onMounted, shallowRef } from 'vue'
import { IPS_TABS, dictService } from './config';
import AppManage from './app-manage/index.vue';
import BlackList from './black-list/index.vue';
import WhiteList from './white-list/index.vue';
import FilterManage from './filter-manage/index.vue';
import MaliciousFile from './malicious-file/index.vue';
import IntrusionPrevention from './intrusion-prevention/index.vue';
import SecurityPolicy from './security-policy/index.vue';
import ObjectManage from './object-manage/index.vue';

export default defineComponent({
  name: 'Ips',
  components: {
    AppManage,
    BlackList,
    WhiteList,
    FilterManage,
    ObjectManage,
    MaliciousFile,
    IntrusionPrevention,
    SecurityPolicy
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible'],
  setup (props, ctx) {
    const { visible, rowData } = toRefs(props)
    const dialogVisible = computed(() => visible.value)

    const state = reactive({
      loading: false,
      activeKey: IPS_TABS[0].componentName
    })

    const singleDictService = shallowRef({})

    const init = async () => {
      try {
        state.loading = true
        singleDictService.value = await dictService({deviceSafetyId: rowData.value.id})
        console.log(singleDictService.value)
      } finally {
        state.loading = false
      }
    }

    watch(
      () => visible.value,
      (val) => {
        console.log('ips-drawer-visible: ', val)
        if (val) {
          init()
        }
      },
      { immediate: true }
    )

    const handleClose = async () => {
      ctx.emit('update:visible', false)
    }

    provide('rowData', {
      rowData,
      deviceSafetyId: rowData.value.id,
      singleDictService
    })

    return {
      ...toRefs(state),
      IPS_TABS,
      dialogVisible,
      handleClose
    }
  }
})
</script>

<style lang="less">
.ips-drawer-table-operate {
  display: flex;
  justify-content: flex-end;
  margin: 8px 0;
}
</style>
