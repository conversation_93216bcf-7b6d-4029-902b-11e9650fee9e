import dayjs from 'dayjs'

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: 'IP',
    name: 'ip',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} 封禁类型枚举 */
export const BLOCK_TYPE_ENUM = {
  SRC: 'src_ip',
  DST: 'dst_ip',
  ALL: 'src_ip_and_dst_ip'
}

/** @type {*} 封禁类型文本映射 */
export const BLOCK_TYPE_LABEL_MAP = {
  [BLOCK_TYPE_ENUM.SRC]: '仅源IP',
  [BLOCK_TYPE_ENUM.DST]: '仅目的IP',
  [BLOCK_TYPE_ENUM.ALL]: '源IP + 目的IP'
}

/** @type {*} 协议类型枚举 */
export const PROTOCOL_TYPE_ENUM = {
  TCP: 'tcp',
  UDP: 'udp',
  ANY: 'any',
  OTHER: 'other'
}

/** @type {*} 协议类型文本映射 */
export const PROTOCOL_TYPE_LABEL_MAP = {
  [PROTOCOL_TYPE_ENUM.TCP]: 'TCP',
  [PROTOCOL_TYPE_ENUM.UDP]: 'UDP',
  [PROTOCOL_TYPE_ENUM.ANY]: 'ANY',
  [PROTOCOL_TYPE_ENUM.OTHER]: 'OTHER'
}

/** @type {*} 来源枚举 */
export const SOURCE_ENUM = {
  MANUAL: 'manual',
  IMPORT: 'import',
  CTI: 'cti',
  ALARM: 'alarm',
  BFA: 'bfa',
  TPP: 'tpp',
  WEB: 'web',
}

/** @type {*} 来源文本映射 */
export const SOURCE_LABEL_MAP = {
  [SOURCE_ENUM.MANUAL]: '手工添加',
  [SOURCE_ENUM.IMPORT]: '批量导入',
  [SOURCE_ENUM.CTI]: '威胁情报',
  [SOURCE_ENUM.ALARM]: '告警处置',
  [SOURCE_ENUM.BFA]: '暴力破解',
  [SOURCE_ENUM.TPP]: '三方平台',
  [SOURCE_ENUM.WEB]: 'WEB安全',
}

/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns ({ handleChangeStatus, pagination }) {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: '加白方式',
      dataIndex: 'blocking_method',
      ellipsis: true,
      key: 'blocking_method',
      width: 150,
      fixed: 'left',
      customRender: ({ record }) => {
        return <span>{BLOCK_TYPE_LABEL_MAP[record.blocking_method] ?? '-'}</span>
      }
    },
    {
      title: '源IP',
      dataIndex: 'src_ip',
      ellipsis: true,
      width: 150,
      key: 'src_ip'
    },
    {
      title: '目的IP',
      dataIndex: 'dst_ip',
      ellipsis: true,
      width: 150,
      key: 'dst_ip'
    },
    {
      title: '协议',
      dataIndex: 'protocol',
      ellipsis: true,
      key: 'protocol',
      width: 100,
      customRender: ({ record }) => {
        return <span>{PROTOCOL_TYPE_LABEL_MAP[record.protocol] ?? '-'}</span>
      }
    },
    {
      title: '起始时间',
      dataIndex: 'effective_start_time',
      ellipsis: true,
      width: 160,
      key: 'effective_start_time',
      customRender: ({ text }) => {
        if (!text) return '-'
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: '结束时间',
      dataIndex: 'effective_end_time',
      ellipsis: true,
      width: 160,
      key: 'effective_end_time'
    },
    {
      title: '来源',
      dataIndex: 'source',
      ellipsis: true,
      key: 'source',
      width: 100,
      customRender: ({ record }) => {
        return SOURCE_LABEL_MAP[record.source_type] ?? '-'
      }
    },
    {
      title: '备注',
      dataIndex: 'description',
      ellipsis: true,
      width: 180,
      key: 'description'
    },
    {
      title: '启用',
      dataIndex: 'status',
      ellipsis: true,
      key: 'status',
      fixed: 'right',
      width: 100,
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const status = record.status === 'ON' ? 'OFF' : 'ON';
            await handleChangeStatus?.({...record, status});
            record.status = status;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.status}
          loading={!!record.statusLoading}
          checkedValue={'ON'}
          unCheckedValue={'OFF'}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 220
    }
  ]
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        blocking_method: {
          type: 'string',
          title: '加白方式',
          'x-validator': [
            { required: true, message: '请选择加白方式' },
          ],
          default: BLOCK_TYPE_ENUM.SRC,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择加白方式',
            showSearch: true,
            filterOption: (input, option = {}) => {
              if (!input) return true;
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          },
          enum: [
            { label: BLOCK_TYPE_LABEL_MAP[BLOCK_TYPE_ENUM.SRC], value: BLOCK_TYPE_ENUM.SRC },
            { label: BLOCK_TYPE_LABEL_MAP[BLOCK_TYPE_ENUM.DST], value: BLOCK_TYPE_ENUM.DST },
            { label: BLOCK_TYPE_LABEL_MAP[BLOCK_TYPE_ENUM.ALL], value: BLOCK_TYPE_ENUM.ALL }
          ]
        },
        src_ip: {
          type: 'string',
          title: '源IP',
          'x-decorator': 'FormItem',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            rows: 5,
            placeholder: `支持IPv4、IPv6,不同数据请换行，格式及示例如下:
  ************
  ************/24
  ************-************`
          },
          'x-validator': [
            { required: true, message: '请输入源IP' },
            { multipleIp: true }
          ],
          'x-reactions': {
            dependencies: ['.blocking_method'],
            fulfill: {
              state: {
                visible: `{{['src_ip', 'src_ip_and_dst_ip'].includes($deps[0])}}`
              }
            }
          }
        },
        dst_ip: {
          type: 'string',
          title: '目的IP',
          'x-decorator': 'FormItem',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            rows: 5,
            placeholder: `支持IPv4、IPv6,不同数据请换行，格式及示例如下:
  ************
  ************/24
  ************-************`
          },
          'x-validator': [
            { required: true, message: '请输入目的IP' },
            { multipleIp: true }
          ],
          'x-reactions': {
            dependencies: ['.blocking_method'],
            fulfill: {
              state: {
                visible: `{{['dst_ip', 'src_ip_and_dst_ip'].includes($deps[0])}}`
              }
            }
          }
        },
        protocol: {
          type: 'string',
          title: '协议',
          'x-validator': [
            { required: true, message: '请选择协议' },
          ],
          default: PROTOCOL_TYPE_ENUM.ANY,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择协议',
            showSearch: true,
            filterOption: (input, option = {}) => {
              if (!input) return true;
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          },
          enum: [
            { label: PROTOCOL_TYPE_LABEL_MAP[PROTOCOL_TYPE_ENUM.ANY], value: PROTOCOL_TYPE_ENUM.ANY },
            { label: PROTOCOL_TYPE_LABEL_MAP[PROTOCOL_TYPE_ENUM.TCP], value: PROTOCOL_TYPE_ENUM.TCP },
            { label: PROTOCOL_TYPE_LABEL_MAP[PROTOCOL_TYPE_ENUM.UDP], value: PROTOCOL_TYPE_ENUM.UDP },
            { label: PROTOCOL_TYPE_LABEL_MAP[PROTOCOL_TYPE_ENUM.OTHER], value: PROTOCOL_TYPE_ENUM.OTHER }
          ]
        },
        validity_period: {
          type: 'object',
          title: '',
          'x-component': 'VoidField',
          'x-decorator-props': {
            colon: false
          },
          properties: {
            type: {
              type: 'string',
              title: '有效期',
              'x-validator': [
                { required: true, message: '请选择有效期' },
              ],
              default: 'limit',
              'x-decorator': 'FormItem',
              'x-component': 'Radio.Group',
              'x-component-props': {
                isGroup: true,
                options: [
                  { label: '限制时长', value: 'limit' },
                  { label: '时间范围', value: 'range' },
                  { label: '永久有效', value: 'forever' }
                ]
              }
            },
            limit: {
              type: 'void',
              title: ' ',
              'x-display': 'hidden',
              'x-decorator': 'FormItem',
              'x-component': 'Space',
              'x-decorator-props': {
                colon: false,
                feedbackLayout: 'terse',
              },
              properties: {
                validity_period_time: {
                  type: 'number',
                  'x-decorator': 'FormItem',
                  'x-component': 'InputNumber',
                  'x-component-props': {
                    placeholder: `请输入`
                  },
                  'x-decorator-props': {
                    colon: false,
                    addonBefore: '从当前时间起'
                  },
                  'x-validator': [
                    { required: true, message: '请输入' },
                    {
                      format: 'integer'
                    },
                    {
                      minimum: 1
                    }
                  ]
                },
                validity_period_unit: {
                  type: 'string',
                  title: '',
                  default: 'minute',
                  'x-decorator': 'FormItem',
                  'x-component': 'Select',
                  'x-component-props': {
                    placeholder: '请选择',
                    showSearch: true,
                    filterOption: (input, option = {}) => {
                      if (!input) return true;
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    },
                  },
                  enum: [
                    { label: '分钟', value: 'minute' },
                    { label: '小时', value: 'hour' },
                    { label: '天', value: 'day' }
                  ],
                  'x-decorator-props': {
                    colon: false,
                    addonAfter: '内有效'
                  },
                }
              },
              'x-reactions': {
                dependencies: ['.type'],
                fulfill: {
                  state: {
                    visible: `{{$deps[0] === 'limit'}}`
                  }
                }
              }
            },
            forever: {
              type: 'void',
              title: ' ',
              'x-display': 'hidden',
              'x-decorator': 'FormItem',
              'x-component': 'VoidField',
              'x-decorator-props': {
                colon: false
              },
              properties: {
                timeRange: {
                  type: 'array',
                  title: '',
                  'x-validator': [
                    { required: true, message: '请选择' },
                  ],
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    colon: false
                  },
                  'x-component': 'DatePicker.RangePicker',
                  'x-component-props': {
                    showTime: {format: 'HH:mm:ss'},
                    placeholder: ['开始时间', '结束时间'],
                    valueFormat: 'YYYY-MM-DD HH:mm:ss',
                    format: "YYYY-MM-DD HH:mm:ss"
                  },
                },
              },
              'x-reactions': {
                dependencies: ['.type'],
                fulfill: {
                  state: {
                    visible: `{{$deps[0] === 'range'}}`
                  }
                }
              }
            }
          }
        },
        status: {
          type: 'boolean',
          title: '启用',
          'x-validator': [
            { required: true, message: '请选择是否启用' },
          ],
          default: 'ON',
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 'ON',
            unCheckedValue: 'OFF'
          }
        },
        description: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          }
        }
      }
    }
  }
}
