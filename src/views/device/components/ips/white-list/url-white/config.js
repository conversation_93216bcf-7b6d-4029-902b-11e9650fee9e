import dayjs from 'dayjs'

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: 'URL',
    name: 'url',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入',
    },
  },
]

/** @type {*} 来源枚举 */
export const SOURCE_ENUM = {
  MANUAL: 'manual',
  IMPORT: 'import',
  CTI: 'cti',
  ALARM: 'alarm',
  BFA: 'bfa',
  TPP: 'tpp',
  WEB: 'web',
}

/** @type {*} 来源文本映射 */
export const SOURCE_LABEL_MAP = {
  [SOURCE_ENUM.MANUAL]: '手工添加',
  [SOURCE_ENUM.IMPORT]: '批量导入',
  [SOURCE_ENUM.CTI]: '威胁情报',
  [SOURCE_ENUM.ALARM]: '告警处置',
  [SOURCE_ENUM.BFA]: '暴力破解',
  [SOURCE_ENUM.TPP]: '三方平台',
  [SOURCE_ENUM.WEB]: 'WEB安全',
}

/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns ({ handleChangeStatus, pagination }) {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: 'URL',
      dataIndex: 'url',
      ellipsis: true,
      key: 'url'
    },
    {
      title: '起始时间',
      dataIndex: 'effective_start_time',
      ellipsis: true,
      key: 'effective_start_time',
      customRender: ({ text }) => {
        if (!text) return '-'
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: '结束时间',
      dataIndex: 'effective_end_time',
      ellipsis: true,
      key: 'effective_end_time',
      customRender: ({ record }) => {
        const label = record.validity_period.type === 'forever' && !record.effective_end_time ? '永久有效' : record.effective_end_time
        return <span>{label}</span>
      }
    },
    {
      title: '来源',
      dataIndex: 'source',
      ellipsis: true,
      key: 'source',
      customRender: ({ record }) => {
        return SOURCE_LABEL_MAP[record.source_type] ?? '-'
      }
    },
    {
      title: '备注',
      dataIndex: 'description',
      ellipsis: true,
      key: 'description'
    },
    {
      title: '启用',
      dataIndex: 'status',
      ellipsis: true,
      key: 'status',
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const status = record.status === 'ON' ? 'OFF' : 'ON';
            await handleChangeStatus?.({...record, status});
            record.status = status;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.status}
          loading={!!record.statusLoading}
          checkedValue={'ON'}
          unCheckedValue={'OFF'}
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 180
    }
  ]
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        url: {
          type: 'string',
          title: 'URL',
          'x-decorator': 'FormItem',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            rows: 5,
            placeholder: `不同数据请换行，支持格式及示例如下：
www.example.com/index.html
192.168.1.1/index.html`
          },
          'x-validator': [
            { required: true, message: '请输入URL' },
            { format: 'customUrl' }
          ]
        },
        validity_period: {
          type: 'object',
          title: '',
          'x-component': 'VoidField',
          'x-decorator-props': {
            colon: false
          },
          properties: {
            type: {
              type: 'string',
              title: '有效期',
              'x-validator': [
                { required: true, message: '请选择有效期' },
              ],
              default: 'limit',
              'x-decorator': 'FormItem',
              'x-component': 'Radio.Group',
              'x-component-props': {
                isGroup: true,
                options: [
                  { label: '限制时长', value: 'limit' },
                  { label: '时间范围', value: 'range' },
                  { label: '永久有效', value: 'forever' }
                ]
              }
            },
            limit: {
              type: 'void',
              title: ' ',
              'x-display': 'hidden',
              'x-decorator': 'FormItem',
              'x-component': 'Space',
              'x-decorator-props': {
                colon: false,
              },
              properties: {
                validity_period_time: {
                  type: 'number',
                  'x-decorator': 'FormItem',
                  'x-component': 'InputNumber',
                  'x-component-props': {
                    placeholder: `请输入`
                  },
                  'x-decorator-props': {
                    colon: false,
                    addonBefore: '从当前时间起'
                  },
                  'x-validator': [
                    { required: true, message: '请输入' },
                    {
                      format: 'integer'
                    },
                    {
                      minimum: 1
                    }
                  ]
                },
                validity_period_unit: {
                  type: 'string',
                  title: '',
                  'x-validator': [
                    { required: true, message: '请选择' },
                  ],
                  default: 'minute',
                  'x-decorator': 'FormItem',
                  'x-component': 'Select',
                  'x-component-props': {
                    placeholder: '请选择',
                    showSearch: true,
                    filterOption: (input, option = {}) => {
                      if (!input) return true;
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    },
                  },
                  enum: [
                    { label: '分钟', value: 'minute' },
                    { label: '小时', value: 'hour' },
                    { label: '天', value: 'day' }
                  ],
                  'x-decorator-props': {
                    colon: false,
                    addonAfter: '内有效'
                  },
                }
              },
              'x-reactions': {
                dependencies: ['.type'],
                fulfill: {
                  state: {
                    visible: `{{$deps[0] === 'limit'}}`
                  }
                }
              }
            },
            forever: {
              type: 'void',
              title: ' ',
              'x-display': 'hidden',
              'x-decorator': 'FormItem',
              'x-component': 'VoidField',
              'x-decorator-props': {
                colon: false
              },
              properties: {
                timeRange: {
                  type: 'array',
                  title: '',
                  'x-validator': [
                    { required: true, message: '请选择' },
                  ],
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    colon: false
                  },
                  'x-component': 'DatePicker.RangePicker',
                  'x-component-props': {
                    showTime: {format: 'HH:mm:ss'},
                    placeholder: ['开始时间', '结束时间'],
                    valueFormat: 'YYYY-MM-DD HH:mm:ss',
                    format: "YYYY-MM-DD HH:mm:ss"
                  },
                },
              },
              'x-reactions': {
                dependencies: ['.type'],
                fulfill: {
                  state: {
                    visible: `{{$deps[0] === 'range'}}`
                  }
                }
              }
            }
          }
        },
        status: {
          type: 'boolean',
          title: '启用',
          'x-validator': [
            { required: true, message: '请选择是否启用' },
          ],
          default: 'ON',
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 'ON',
            unCheckedValue: 'OFF'
          }
        },
        description: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          }
        }
      }
    }
  }
}
