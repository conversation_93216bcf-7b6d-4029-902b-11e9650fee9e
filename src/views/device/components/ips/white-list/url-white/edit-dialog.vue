<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="800px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="SCHEMA"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, inject } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep, keyBy } from 'lodash'
import dayjs from 'dayjs'

import { SCHEMA } from './config'

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    titleSuffix: {
      type: String,
      default: ''
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()

    const { visible, mode, defaultData, titleSuffix } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}${titleSuffix.value}`
    })

    const state = reactive({
      threatTypeMap: {},
      loading: false
    })

    const form = createForm()

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      form.setPattern(patternMap[mode.value])
      const urlComponentMap = {
        TEXTAREA: {
          component: 'Input.TextArea',
          componentProps: {
            rows: 5,
            placeholder: `不同数据请换行，支持格式及示例如下：
www.example.com/index.html
192.168.1.1/index.html`
          }
        },
        INPUT: {
          component: 'Input',
          componentProps: {
            placeholder: '例如：192.168.1.1/index.html'
          }
        }
      }
      const urlField = form.query('url').take()
      const componentObj = mode.value === 'add' ? urlComponentMap.TEXTAREA : urlComponentMap.INPUT
      urlField?.setComponent(componentObj.component)
      urlField?.setComponentProps(componentObj.componentProps)
      if (mode.value === 'add') return
      const formData = cloneDeep(defaultData.value)
      if (formData.validity_period.type !== 'forever') {
        formData.validity_period.timeRange = [formData.validity_period.start_time, formData.validity_period.end_time]
      }
      form.setValues(formData)
    }

    /**
     * 关闭弹窗处理
     * @param {boolean} hasError 是否有错误
     */
    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return;
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    /**
     * 保存表单数据
     */
    const handleSave = async () => {
      await form.validate()
      state.loading = true
      const params = cloneDeep(form.values)
      const validity_period = {
        type: 'forever',
        start_time: '',
        end_time: '',
        validity_period_unit: '',
        validity_period_time: ''
      }
      if (mode.value === 'add') {
        params.url = params.url?.split('\n') ?? [];
      }
      if (params.validity_period.type === 'limit') {
        const {validity_period_unit, validity_period_time} = params.validity_period
        const current = dayjs()
        validity_period.type = 'range'
        validity_period.start_time = current.format()
        validity_period.end_time = current.add(validity_period_time, validity_period_unit).format()
        validity_period.validity_period_time = validity_period_time
        validity_period.validity_period_unit = validity_period_unit
      } else if (params.validity_period.type === 'range') {
        validity_period.type = 'range'
        validity_period.start_time = dayjs(params.timeRange?.[0]).format()
        validity_period.end_time = dayjs(params.timeRange?.[1]).format()
      }
      ctx.emit('on-success', {
        mode: mode.value,
        data: {...params, validity_period},
        callback: handleClose
      })
    }

    return {
      title,
      isView,
      form,
      SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script>
