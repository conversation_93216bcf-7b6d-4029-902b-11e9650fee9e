<!-- 入侵防御系统 白名单配置 -->

<template>
  <a-tabs v-model:activeKey="activeKey" size="small">
    <a-tab-pane v-for="tab in TABS" :key="tab.label" :tab="tab.label">
      <div class="white-list--inner_tab">
        <component :is="tab.componentName" :title="tab.label" type="WHITE"/>
      </div>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import { defineComponent, ref } from 'vue'
import { TABS } from './config'
import IpWhite from './ip-white/index.vue'
import DomainWhite from './domain-white/index.vue'
import SessionWhite from './session-white/index.vue'
import UrlWhite from './url-white/index.vue'

export default defineComponent({
  components: {
    IpWhite,
    DomainWhite,
    SessionWhite,
    UrlWhite
  },
  setup (props, ctx) {
    const activeKey = ref(TABS[0].label)
    return {
      TABS,
      activeKey
    }
  }
})
</script>

<style lang="less">
.white-list--inner_tab {
  margin-top: 8px;
}
</style>
