<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="700px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema" :scope="formScope"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, inject, shallowRef, reactive, watch, getCurrentInstance, onMounted, nextTick } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep, pick, keyBy } from 'lodash'

import { SCHEMA } from './config';
import { getKeywordAllList, getFileFingerprintList } from '@/request/api-device-ips'


export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { visible, mode, defaultData } = toRefs(props)
    const { deviceSafetyId } = inject('rowData')
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}Web过滤`
    })

    const dataSourceMap = shallowRef({
      fileFingerprintMap: {},
      keywordMap: {}
    })

    const state = reactive({
      loading: false
    })

    const form = createForm()

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    /**
     * 初始化表单数据
     */
    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      form.setPattern(patternMap[mode.value])
      if (mode.value === 'add') return
      const formData = cloneDeep(defaultData.value)
      formData.file_content_fingerprint = formData.file_content_fingerprint?.id ?? null
      formData.file_content_keyword = formData.file_content_keyword?.id ?? null
      formData.content = formData.content?.id ?? null
      formData.file_name = formData.file_name?.id ?? null
      form.setValues(formData)
    }

    // 获取关键字数据源
    let keywordReqPromise = null
    const getKeywordDataSource = () => {
      return async (field) => {
        let data = []
        try {
          if (!keywordReqPromise) keywordReqPromise = getKeywordAllList({deviceSafetyId})
          const res = await keywordReqPromise;
          data = res.data ?? []
          dataSourceMap.value.keywordMap = keyBy(data, 'id')
        } finally {
          field.setDataSource(data?.map(({name, id}) => ({label: name, value: id})) ?? [])
        }
      }
    }

    // 获取文件指纹
    const getFileFingerprintDataSource = async (field) => {
      let data = []
      try {
        const { data: fileFingerprints = [] } = await getFileFingerprintList({deviceSafetyId})
        dataSourceMap.value.fileFingerprintMap = keyBy(fileFingerprints, 'id')
        data = fileFingerprints?.map(({name, id}) => ({label: name, value: id})) ?? []
      } finally {
        field.setDataSource(data)
      }
    }

    const getObjByKeys = (obj = {}, key = '', fieldKeys = []) => {
      if (!key || !obj[key] || !fieldKeys.length) return {}
      return pick(obj[key], fieldKeys);
    }

    /**
     * 关闭弹窗处理
     * @param {boolean} hasError 是否有错误
     */
    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    /**
     * 保存表单数据
     */
    const handleSave = async () => {
      await form.validate()
      state.loading = true
      const params = { ...form.values, enable: 1 }
      params.file_content_fingerprint = getObjByKeys(dataSourceMap.value.fileFingerprintMap, params.file_content_fingerprint, ['id', 'name'])
      params.file_content_keyword = getObjByKeys(dataSourceMap.value.keywordMap, params.file_content_keyword, ['id', 'name'])
      params.content = getObjByKeys(dataSourceMap.value.keywordMap, params.content, ['id', 'name'])
      params.file_name = getObjByKeys(dataSourceMap.value.keywordMap, params.file_name, ['id', 'name'])

      ctx.emit('on-success', {
        mode: mode.value,
        data: params,
        callback: handleClose
      })
    }

    return {
      title,
      isView,
      form,
      formScope: {
        getKeywordDataSource,
        getFileFingerprintDataSource
      },
      schema: SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script>
