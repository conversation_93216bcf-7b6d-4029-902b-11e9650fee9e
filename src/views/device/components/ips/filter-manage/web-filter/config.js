import dayjs from 'dayjs'

/** @type {*} 模板定义类型枚举 */
export const TEMPLATE_TYPE_ENUM = {
  CUSTOMIZE: 'customize',
  PREDEFINE: 'predefine'
}

/** @type {*} 模板类型名称映射 */
export const TEMPLATE_TYPE_LABEL_MAP = {
  [TEMPLATE_TYPE_ENUM.CUSTOMIZE]: '自定义',
  [TEMPLATE_TYPE_ENUM.PREDEFINE]: '预定义'
}

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 80,
    align: "center",
    fixed: 'left',
    customRender({index}){
      return index+1
    }
  },
  {
    title: '模板名称',
    dataIndex: 'name',
    ellipsis: true,
    key: 'name'
  },
  {
    title: '引用次数',
    dataIndex: 'cite_count',
    ellipsis: true,
    key: 'cite_count'
  },
  {
    title: '模板类型',
    dataIndex: 'template_type',
    ellipsis: true,
    key: 'template_type',
    customRender: ({ record }) => {
      return TEMPLATE_TYPE_LABEL_MAP[record.template_type] ?? '-'
    }
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    ellipsis: true,
    key: 'create_time',
    width: 160,
    customRender: ({ text }) => {
      if (!text) return '-'
      return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
    }
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 220
  }
]

/** @type {*} Web过滤表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        name: {
          type: 'string',
          title: '模板名称',
          'x-validator': [
            { required: true, message: '请输入模板名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入模板名称'
          }
        },
        web_action: {
          type: 'string',
          title: '动作',
          required: true,
          default: 'LOG',
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          'x-component-props': {
            isGroup: true,
            optionType: "button",
            options: [
              { label: '仅记录日志', value: 'LOG' },
              { label: '敏感信息替换', value: 'TOUCHY' }
            ]
          }
        },
        content: {
          type: 'string',
          title: "内容",
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择',
            showSearch: true,
            filterOption: (input, option = {}) => {
              if (!input) return true;
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          },
          'x-decorator-props': {
            addonAfter: '（关键字）'
          },
          'x-reactions': '{{getKeywordDataSource()}}'
        },
        file_name: {
          type: 'string',
          title: "文件名称",
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择',
            showSearch: true,
            filterOption: (input, option = {}) => {
              if (!input) return true;
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          },
          'x-decorator-props': {
            addonAfter: '（关键字）'
          },
          'x-reactions': '{{getKeywordDataSource()}}'
        },
        file_content_keyword: {
          type: 'string',
          title: "文件内容",
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择',
            showSearch: true,
            filterOption: (input, option = {}) => {
              if (!input) return true;
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          },
          'x-decorator-props': {
            addonAfter: '（关键字）'
          },
          'x-reactions': '{{getKeywordDataSource()}}'
        },
        file_content_fingerprint: {
          type: 'string',
          title: " ",
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择',
            showSearch: true,
            filterOption: (input, option = {}) => {
              if (!input) return true;
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          },
          'x-decorator-props': {
            colon: false,
            addonAfter: '（文件指纹）'
          },
          'x-reactions': '{{getFileFingerprintDataSource}}'
        },
        data_restore: {
          type: 'string',
          title: '数据还原',
          default: 0,
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          'x-component-props': {
            isGroup: true,
            options: [
              { label: '是', value: 1 },
              { label: '否', value: 0 }
            ]
          },
          'x-decorator-props': {
            wrapperWidth: 110,
            addonAfter: '（是否对命中WEB过滤防护的数据报文进行抓取）'
          },
        },
        encryption_check: {
          type: 'string',
          title: '加密检查',
          default: 0,
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          'x-component-props': {
            isGroup: true,
            options: [
              { label: '是', value: 1 },
              { label: '否', value: 0 }
            ]
          },
          'x-decorator-props': {
            wrapperWidth: 110,
            addonAfter: '（是否对WEB应用中所携带的加密文件进行检查）'
          },
        },
      }
    }
  }
}

