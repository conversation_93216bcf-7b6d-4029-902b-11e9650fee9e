import dayjs from 'dayjs'

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} 来源枚举 */
export const SOURCE_ENUM = {
  MANUAL: 'manual'
}

/** @type {*} 来源文本映射 */
export const SOURCE_LABEL_MAP = {
  [SOURCE_ENUM.MANUAL]: '手工添加'
}

/** @type {*} 获取防护模板列表配置 */
export function getColumns ({ handleChangeStatus }){
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({index}){
        return index+1
      }
    },
    {
      title: '规则ID',
      dataIndex: 'rule_id',
      ellipsis: true,
      key: 'rule_id',
      width: 100
    },
    {
      title: '规则名称',
      dataIndex: 'rule_name',
      ellipsis: true,
      key: 'rule_name',
      width: 250
    },
    {
      title: '源IP',
      dataIndex: 'src_ip',
      ellipsis: true,
      key: 'src_ip',
      width: 150
    },
    {
      title: '目的IP',
      dataIndex: 'dst_ip',
      ellipsis: true,
      key: 'dst_ip',
      width: 150
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      ellipsis: true,
      key: 'create_time',
      width: 180,
      customRender: ({ text }) => {
        if (!text) return '-'
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: '来源',
      dataIndex: 'source',
      ellipsis: true,
      key: 'source',
      width: 120,
      customRender: ({ record }) => {
        return SOURCE_LABEL_MAP[record.source] ?? '-'
      }
    },
    {
      title: '备注',
      dataIndex: 'remark',
      ellipsis: true,
      key: 'remark',
      width: 200,
    },
    {
      title: '启用',
      dataIndex: 'enable',
      ellipsis: true,
      key: 'enable',
      width: 80,
      fixed: 'right',
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.enableLoading = true;
            const enable = record.enable === 'ON' ? 'OFF' : 'ON';
            await handleChangeStatus({...record, enable});
            record.enable = enable;
          } finally {
            record.enableLoading = false;
          }
        }
        return <a-switch
          checked={record.enable}
          loading={!!record.enableLoading}
          checkedValue="ON"
          unCheckedValue="OFF"
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 220
    }
  ]
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        rule_id: {
          type: 'string',
          title: '规则ID/名称',
          'x-validator': [
            { required: true, message: '请选择规则' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            allowClear: true,
            showSearch: true,
            filterOption: (input, option = {}) => {
              if (!input) return true;
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
            placeholder: '支持规则名称、id模糊查询',
          },
          'x-reactions': '{{getPredefineRule}}'
        },
        src_ip: {
          type: 'string',
          title: '源IP',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '不输入默认为ANY'
          }
        },
        dst_ip: {
          type: 'string',
          title: '目的IP',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '不输入默认为ANY'
          }
        },
        enable: {
          type: 'boolean',
          title: '启用',
          required: true,
          default: 'ON',
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 'ON',
            unCheckedValue: 'OFF'
          }
        },
        remark: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            { max: 200 }
          ]
        },
      }
    }
  }
}
