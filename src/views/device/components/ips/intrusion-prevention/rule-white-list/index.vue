<template>
  <div>
    <a-space align="start" class="ips-drawer-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add')">
        新增
      </a-button>
      <a-button type="primary" @click="handleExport">
        导出
      </a-button>
    </a-space>
    <a-table
      :scroll="{y: 'calc(100vh - 420px)'}"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button type="link" @click="handleOpenDialog('view', record)">查看</a-button>
          <a-button type="link" @click="handleOpenDialog('edit', record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </template>
      </template>
    </a-table>
    <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="handleSave" />
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, inject, onMounted, getCurrentInstance, createVNode } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

import { getRuleWhiteAllList, addRuleWhite, updateRuleWhite, deleteRuleWhite, updateRuleWhiteStatus } from '@/request/api-device-ips'
import { getColumns } from './config';
import { downloadFile } from '@/utils/util'

import EditDialog from './edit-dialog.vue'

export default defineComponent({
  components: {
    EditDialog
  },
  setup () {
    const { proxy } = getCurrentInstance()
    const { deviceSafetyId } = inject('rowData')

    const state = reactive({
      dataSource: [],
      loading: false,
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })

    const refreshTableData = async () => {
      try {
        state.loading = true
        const { data = [] } = await getRuleWhiteAllList({deviceSafetyId})
        state.dataSource = data
      } finally {
        state.loading = false
      }
    }

    const handleSave = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addRuleWhite,
        edit: updateRuleWhite
      }

      try {
        const res = await requestMethodEnum[mode]?.({...data, deviceSafetyId})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback?.()
        refreshTableData()
      } catch (err) {
        // const errMsg = isAdd ? '新增失败！' : '更新失败！';
        // proxy.$message.error(err.message || errMsg)
        callback?.(true, err)
      }
    }

    const handleDelete = async (row = {}) => {
      proxy.$confirm({
        title: '删除',
        content: `确认删除 ${row.rule_name} 数据吗？`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteRuleWhite({ ...row, deviceSafetyId })
            proxy.$message.success('删除成功！')
            refreshTableData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
    }


    const handleOpenDialog = (mode = 'add', row = {}) => {
      state.dialogConfig.visible = true
      state.dialogConfig.mode = mode
      state.dialogConfig.defaultData = row
    }

    const handleChangeStatus = async (row = {}) => {
      await updateRuleWhiteStatus({...row, deviceSafetyId})
    }

    const handleExport = async () => {
      const params = encodeURI(JSON.stringify({deviceSafetyId}));
      downloadFile(`/device-atomic/IPSQiming/ruleWhiteListExport?params=${params}`)
    }

    onMounted(() => {
      refreshTableData();
    })

    return {
      ...toRefs(state),
      refreshTableData,
      handleOpenDialog,
      handleSave,
      handleDelete,
      handleExport,
      columns: getColumns({handleChangeStatus})
    }
  }
})
</script>
