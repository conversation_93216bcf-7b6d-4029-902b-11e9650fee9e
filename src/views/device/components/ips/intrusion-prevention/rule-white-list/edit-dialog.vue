<template>
    <a-modal
      v-model:visible="dialogVisible"
      :title="title"
      width="800px"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleClose"
    >
      <a-spin :spinning="loading">
        <FormProvider :form="form">
          <SchemaField :schema="SCHEMA" :scope="formScope"></SchemaField>
        </FormProvider>
      </a-spin>
      <template #footer>
        <a-button
          v-if="!isView"
          type="primary"
          :loading="loading"
          @click="handleSave">保存</a-button>
        <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
      </template>
    </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, inject, shallowRef } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep, keyBy } from 'lodash'

import { getPredefineRuleAllList } from '@/request/api-device-ips.js'
import { SCHEMA } from './config';

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { deviceSafetyId } = inject('rowData')
    const { visible, mode, defaultData } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}规则白名单`
    })
    const predefineRuleMap = shallowRef({})
    const state = reactive({
      loading: false
    })

    const form = createForm()

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      form.setPattern(patternMap[mode.value])
      if (mode.value === 'add') return
      const formData = cloneDeep(defaultData.value)
      form.setValues(formData)
    }

    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return;
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    const handleSave = async () => {
      await form.validate()
      state.loading = true
      const params = cloneDeep(form.values)
      params.rule_name = predefineRuleMap.value[params.rule_id]?.name ?? ''
      ctx.emit('on-success', {
        mode: mode.value,
        data: params,
        callback: handleClose
      })
    }

    const getPredefineRule = async (field) => {
      let data = []
      try {
        state.loading = true
        const res = await getPredefineRuleAllList({deviceSafetyId})
        data = res.data ?? []
        predefineRuleMap.value = keyBy(data, 'id')
      } finally {
        state.loading = false
        field.setDataSource(data.map(({id, name}) => ({label: `【${id}】${name}`, value: id})))
      }
    }

    return {
      title,
      isView,
      form,
      formScope: {
        getPredefineRule
      },
      SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script>
