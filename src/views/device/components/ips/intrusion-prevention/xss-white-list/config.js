import dayjs from 'dayjs'

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '白名单内容',
    name: 'content',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入白名单内容',
    },
  },
]

/** @type {*} 检测点枚举 */
export const CHECKPOINT_ENUM = {
  ANY: 'any',
  URL: 'url',
  XFORWARD: 'xforward',
  USERAGENT: 'useragent',
  FORM: 'form',
  REFERENCE: 'reference',
  COOKIE: 'cookie'
}

/** @type {*} 检测点文本映射 */
export const CHECKPOINT_LABEL_MAP = {
  [CHECKPOINT_ENUM.ANY]: 'ANY',
  [CHECKPOINT_ENUM.URL]: 'URL',
  [CHECKPOINT_ENUM.XFORWARD]: 'XFORWARD',
  [CHECKPOINT_ENUM.USERAGENT]: 'USERAGENT',
  [CHECKPOINT_ENUM.FORM]: 'FORM',
  [CHECKPOINT_ENUM.REFERENCE]: 'REFERENCE',
  [CHECKPOINT_ENUM.COOKIE]: 'COOKIE',
}

/** @type {*} 来源枚举 */
export const SOURCE_ENUM = {
  MANUAL: 'manual',
  IMPORT: 'import',
  CTI: 'cti',
  ALARM: 'alarm',
  BFA: 'bfa',
  TPP: 'tpp',
  WEB: 'web',
}

/** @type {*} 来源文本映射 */
export const SOURCE_LABEL_MAP = {
  [SOURCE_ENUM.MANUAL]: '手工添加',
  [SOURCE_ENUM.IMPORT]: '批量导入',
  [SOURCE_ENUM.CTI]: '威胁情报',
  [SOURCE_ENUM.ALARM]: '告警处置',
  [SOURCE_ENUM.BFA]: '暴力破解',
  [SOURCE_ENUM.TPP]: '三方平台',
  [SOURCE_ENUM.WEB]: 'WEB安全',
}

/** @type {*} 获取防护模板列表配置 */
export function getColumns ({ handleChangeStatus, pagination }){
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: '白名单内容',
      dataIndex: 'content',
      ellipsis: true,
      key: 'content'
    },
    {
      title: '检测点',
      dataIndex: 'checkpoints',
      ellipsis: true,
      key: 'checkpoints',
      customRender: ({ record }) => {
        return CHECKPOINT_LABEL_MAP[record.checkpoints] ?? '-'
      }
    },
    {
      title: '来源',
      dataIndex: 'source',
      ellipsis: true,
      key: 'source',
      customRender: ({ record }) => {
        return SOURCE_LABEL_MAP[record.source] ?? '-'
      }
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      ellipsis: true,
      key: 'create_time',
      width: 160,
      customRender: ({ text }) => {
        if (!text) return '-'
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: '启用',
      dataIndex: 'status',
      ellipsis: true,
      key: 'status',
      width: 100,
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const status = record.status === 'ON' ? 'OFF' : 'ON';
            await handleChangeStatus({...record, status});
            record.status = status;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.status}
          loading={!!record.statusLoading}
          checkedValue="ON"
          unCheckedValue="OFF"
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 220
    }
  ]
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        content: {
          type: 'string',
          title: '白名单内容',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入白名单内容'
          },
          'x-validator': [
            { required: true, message: '请输入白名单内容' },
            { min: 5 },
            { max: 512 }
          ]
        },
        checkpoints: {
          type: 'string',
          title: '检测点',
          'x-validator': [
            { required: true, message: '请选择检测点' },
          ],
          default: CHECKPOINT_ENUM.ANY,
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择检测点',
            showSearch: true,
            filterOption: (input, option = {}) => {
              if (!input) return true;
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          },
          enum: [
            { label: CHECKPOINT_LABEL_MAP[CHECKPOINT_ENUM.COOKIE], value: CHECKPOINT_ENUM.COOKIE },
            { label: CHECKPOINT_LABEL_MAP[CHECKPOINT_ENUM.REFERENCE], value: CHECKPOINT_ENUM.REFERENCE },
            { label: CHECKPOINT_LABEL_MAP[CHECKPOINT_ENUM.FORM], value: CHECKPOINT_ENUM.FORM },
            { label: CHECKPOINT_LABEL_MAP[CHECKPOINT_ENUM.USERAGENT], value: CHECKPOINT_ENUM.USERAGENT },
            { label: CHECKPOINT_LABEL_MAP[CHECKPOINT_ENUM.XFORWARD], value: CHECKPOINT_ENUM.XFORWARD },
            { label: CHECKPOINT_LABEL_MAP[CHECKPOINT_ENUM.URL], value: CHECKPOINT_ENUM.URL },
            { label: CHECKPOINT_LABEL_MAP[CHECKPOINT_ENUM.ANY], value: CHECKPOINT_ENUM.ANY }
          ]
        },
        status: {
          type: 'boolean',
          title: '启用',
          'x-validator': [
            { required: true, message: '请选择是否启用' },
          ],
          default: 'ON',
          'x-decorator': 'FormItem',
          'x-component': 'Switch',
          'x-component-props': {
            checkedValue: 'ON',
            unCheckedValue: 'OFF'
          }
        },
      }
    }
  }
}
