import { toRefs, inject, reactive, computed, watch } from 'vue'
import { without, uniq } from 'lodash'

import {
  getAllRuleIds,
} from '@/request/api-device-ips'

const ruleState = reactive({
  allRuleIds: [],
  selectedRules: [],
})

const selectedIds = computed(() => {
  return ruleState.selectedRules?.map(item => item.id) ?? []
})


const unSelectedIds = computed(() => {
  return without(ruleState.allRuleIds, ...(selectedIds.value)) ?? []
})

/**
 * 获取所有规则ID
 *
 */
/**
 * 获取所有规则ID
 * @param {Object} params 查询参数
 */
const getAllRuleIdsByParams = async (params = {}) => {
  const { data = [] } = await getAllRuleIds({...params, no_paging: true})
  ruleState.allRuleIds = data?.[0]?.list ?? []
}

const setSelectedRules = (rules = []) => {
  ruleState.selectedRules = rules ?? [];
}

const resetSelectedRules = () => {
  if (ruleState.allRuleIds.length) ruleState.allRuleIds = []
  if (ruleState.selectedRules.length) ruleState.selectedRules = []
}

/**
 * 规则选择Hook
 *
 * @export
 */
export default function useRuleHook () {

  return {
    getAllRuleIdsByParams,
    selectedIds,
    unSelectedIds,
    setSelectedRules,
    resetSelectedRules,
    ...toRefs(ruleState)
  }
}
