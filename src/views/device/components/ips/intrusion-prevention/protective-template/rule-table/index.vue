<template>
  <a-spin :spinning="loading">
    <a-tabs type="card" v-model:activeKey="activeKey">
      <a-tab-pane key="selected" :tab="`已包含规则(${selectedIds.length})`">
        <SelectedRuleTable class="list--inner_tab" :mode="mode" />
      </a-tab-pane>
      <a-tab-pane key="unSelected" :tab="`未包含规则(${unSelectedIds.length})`" v-if="mode !== 'view'">
        <UnselectedRuleTable class="list--inner_tab" :mode="mode" />
      </a-tab-pane>
    </a-tabs>
  </a-spin>
</template>

<script>
import { defineComponent, ref, inject, onMounted, toRefs } from 'vue'

import useRuleHook from '../rule-table-hook.js';
import SelectedRuleTable from './selected-rule-table.vue'
import UnselectedRuleTable from './unselected-rule-table.vue'

export default defineComponent({
  components: {
    SelectedRuleTable,
    UnselectedRuleTable
  },
  props: {
    mode: {
      type: String,
      default: 'add'
    }
  },
  setup (props, ctx) {
    const activeKey = ref('selected')
    const loading = ref(false)
    const { deviceSafetyId } = inject('rowData')
    const { getAllRuleIdsByParams, selectedIds, unSelectedIds } = useRuleHook()

    onMounted(async () => {
      try {
        loading.value = true
        await getAllRuleIdsByParams({deviceSafetyId})
      } finally {
        loading.value = false
      }
    })

    return {
      activeKey,
      loading,
      selectedIds,
      unSelectedIds
    }
  }
})
</script>

<style lang="less" scoped>
.list--inner_tab {
  margin-top: 8px;
}
</style>
