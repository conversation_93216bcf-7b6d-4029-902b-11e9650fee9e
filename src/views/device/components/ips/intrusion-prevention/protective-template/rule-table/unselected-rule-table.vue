<template>
  <DynamicSearch
    :config="RULE_SEARCH_CONFIG"
    @search="handleSearch"
    @reset="handleSearch"
  />
  <a-space align="start" class="ips-drawer-table-operate">
    <a-button type="primary" @click="handleAdd(selectedRows)">
      批量添加
    </a-button>
  </a-space>
  <a-table
    rowKey="id"
      :scroll="{y: 'calc(100vh - 420px)'}"
    :data-source="dataSource"
    :columns="columns"
    :loading="loading"
    :pagination="pagination"
    @change="handleTableChange"
    :row-selection="{
      selectedRowKeys: selectedRowKeys,
      onChange: (...selection) => handleSelectionChange(...selection)
    }"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'operation'">
        <a-button type="link" @click="handleAdd([record])">添加</a-button>
      </template>
    </template>
  </a-table>
</template>

<script>
import { defineComponent, computed, toRefs, watch, nextTick, reactive, getCurrentInstance, inject } from 'vue'
import { usePagination } from 'vue-request'
import { slice, cloneDeep } from 'lodash'

import {
  getRuleByIds,
} from '@/request/api-device-ips'

import DynamicSearch from "@/components/dynamic-search/index.vue";

import useRuleHook from '../rule-table-hook';
import{ RULE_SEARCH_CONFIG, getRuleColumns } from './config';

export default defineComponent({
  props: {
    mode: {
      type: String,
      default: 'add'
    }
  },
  components: {
    DynamicSearch
  },
  setup (props, ctx) {
    const { mode } = toRefs(props)
    const { proxy } = getCurrentInstance()
    const { deviceSafetyId, singleDictService } = inject('rowData')
    const { getAllRuleIdsByParams, selectedRules, unSelectedIds, setSelectedRules } = useRuleHook()
    const state = reactive({
      selectedRows: [],
      selectedRowKeys: [],
      searchParams: {},
    })

    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getRuleByIds, {
      manual: true,
      defaultParams: {
        deviceSafetyId
      },
      formatResult: ({ data = [] }) => ( {
        items: data ?? [],
        total: unSelectedIds.value.length ?? 0
      }),
      pagination: {
        currentKey: 'page_num',
        pageSizeKey: 'page_size',
      }
    })

    const pagination = computed(() => ({
      total: unSelectedIds.value.length ?? 0,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条`
    }))

    const dataSource = computed(() => {
      return data.value?.items || []
    })

    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      state.selectedRows = []
      state.selectedRowKeys = []
      const page = pag?.current ?? 1
      const size = pag.pageSize
      const start = (page - 1) * size ?? 0
      const end = start + size
      const data = slice(unSelectedIds.value, start, end)
      run({
        deviceSafetyId,
        page_size: pag.pageSize,
        page_num: pag?.current,
        data,
        filter_data: state.searchParams
      })
    }

    const handleSelectionChange = (selectionKeys = [], selectionRows = []) => {
      state.selectedRowKeys = cloneDeep(selectionKeys) ?? []
      state.selectedRows = cloneDeep(selectionRows) ?? []
    }

    const handleSearch = async (params = {}, operationType) => {
      state.searchParams = params;
      if (operationType === 'manual') {
        try {
          loading.value = true
          await getAllRuleIdsByParams({...params, deviceSafetyId})
        } finally {
          loading.value = false
        }
      }
      refreshTableData();
    }

    const refreshTableData = (isReload = true) => {
      state.selectedRows = []
      state.selectedRowKeys = []
      const page = isReload ? 1 : pagination.value.current
      const size = pagination.value?.pageSize
      const start = (page - 1) * size ?? 0
      const end = start + size
      const data = slice(unSelectedIds.value, start, end)
      run({
        deviceSafetyId,
        page_num: isReload ? 1 : pagination.value.current,
        page_size: pagination.value?.pageSize,
        data,
        filter_data: state.searchParams
      })
    }

    // 添加规则
    /**
     * 添加规则
     * @param {Array} rows 要添加的规则数组
     */
    const handleAdd = async (rows = []) => {
      if (!rows?.length) return proxy.$message.warning('至少要选中一项')
      setSelectedRules([...selectedRules.value, ...cloneDeep(rows)])
      await nextTick()
      refreshTableData();
    }

    watch(
      () => unSelectedIds.value,
      (val) => {
        if (!val.length) {
          if (!data.value ) return
          data.value.items = []
          data.value.total = 0
          return
        }
        refreshTableData()
      }
    )

    return {
      RULE_SEARCH_CONFIG,
      ...toRefs(state),
      pagination,
      loading,
      dataSource,
      handleSearch,
      refreshTableData,
      handleTableChange,
      handleSelectionChange,
      handleAdd,
      columns: getRuleColumns({showOperation: mode.value !== 'view', dictService: singleDictService.value})
    }
  }
})
</script>
