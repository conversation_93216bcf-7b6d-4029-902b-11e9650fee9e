
/** @type {*} 规则列表搜索配置 */
export const RULE_SEARCH_CONFIG = [
  {
    title: '规则名称',
    name: 'rule_name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入规则名称',
    },
  },
]

/** @type {*} 获取规则列表配置 */
export function getRuleColumns({dictService, showOperation = true}) {
  const attackTypeMap = dictService.transformToEnum('IPS_ATTACK_TYPE_LIST')
  const actionMap = dictService.transformToEnum('IPS_RESPONSE_ACTION')
  const levelMap = dictService.transformToEnum('IPS_THREAT_LEVEL_LIST')
  const systemMap = dictService.transformToEnum('IPS_AFFECTED_SYSTEM_LIST')
  const columns = [
    {
      title: '规则ID',
      dataIndex: 'id',
      ellipsis: true,
      key: 'id',
      width: 100
    },
    {
      title: '规则名称',
      dataIndex: 'name',
      ellipsis: true,
      key: 'name',
      width: 200
    },
    {
      title: '启用状态',
      dataIndex: 'enable',
      ellipsis: true,
      key: 'enable',
      width: 100
    },
    {
      title: '威胁级别',
      dataIndex: 'level',
      ellipsis: true,
      key: 'level',
      width: 100,
      customRender: ({ record }) => {
        return levelMap[record.level] ?? '-'
      }
    },
    {
      title: '动作',
      dataIndex: 'block_action',
      ellipsis: true,
      key: 'block_action',
      width: 100,
      customRender: ({ record }) => {
        return actionMap[record.block_action] ?? '-'
      }
    },
    {
      title: '威胁类型',
      dataIndex: 'attack_type',
      ellipsis: true,
      key: 'attack_type',
      width: 120,
      customRender: ({ record }) => {
        return attackTypeMap[record.attack_type] ?? '-'
      }
    },
    {
      title: '影响系统',
      dataIndex: 'affected_system',
      ellipsis: true,
      key: 'affected_system',
      width: 150,
      customRender: ({ record }) => {
        return systemMap[record.affected_system] ?? '-'
      }
    },
    {
      title: '发布时间',
      dataIndex: 'time_release',
      ellipsis: true,
      key: 'time_release',
      width: 155
    }
  ]
  const operateColumn = {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 100
  }
  return showOperation ? [...columns, operateColumn] : columns
}
