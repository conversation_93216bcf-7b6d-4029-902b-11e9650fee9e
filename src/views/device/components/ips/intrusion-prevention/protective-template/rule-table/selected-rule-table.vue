<template>
  <a-table
      :scroll="{y: 'calc(100vh - 370px)'}"
    :data-source="dataSource"
    :columns="columns"
    :loading="loading"
    :pagination="pagination"
    @change="handleTableChange"
  >
    <template #bodyCell="{ column, record }">
      <template v-if="column.key === 'operation'">
        <a-button type="link" danger @click="handleRemove([record.id])">移除</a-button>
      </template>
    </template>
  </a-table>
</template>

<script>
import { defineComponent, computed, watch, getCurrentInstance, inject, toRefs } from 'vue'
import { usePagination } from 'vue-request'
import { filter, slice, cloneDeep } from 'lodash'

import {
  getRuleByIds,
} from '@/request/api-device-ips'

import useRuleHook from '../rule-table-hook.js';
import{ getRuleColumns } from './config';

export default defineComponent({
  props: {
    mode: {
      type: String,
      default: 'add'
    }
  },
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { mode } = toRefs(props)

    const { deviceSafetyId, singleDictService } = inject('rowData')

    const { selectedRules, selectedIds, setSelectedRules } = useRuleHook()

    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      changeCurrent,
      reload
    } = usePagination(getRuleByIds, {
      manual: true,
      defaultParams: {
        deviceSafetyId
      },
      formatResult: ({ data = [] }) => ({
        items: data ?? [],
        total: selectedIds.value.length ?? 0
      }),
      pagination: {
        currentKey: 'page_num',
        pageSizeKey: 'page_size',
      }
    })

    const pagination = computed(() => ({
      total: selectedIds.value.length ?? 0,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条`
    }))

    const dataSource = computed(() => {
      return data.value?.items || []
    })

    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      let page = pag?.current ?? 1
      const size = pag.pageSize
      let start = (page - 1) * size ?? 0
      if (start === selectedIds.value.length) {
        start = start - size
        page = page - 1
      }
      const end = start + size
      const data = slice(selectedIds.value, start, end)
      run({
        deviceSafetyId,
        page_size: size,
        page_num: page,
        data
      })
    }

    const refreshTableData = () => {
      let page = pagination.value.current
      const size = pagination.value?.pageSize
      let start = (page - 1) * size ?? 0
      if (start === selectedIds.value.length) {
        start = start - size
        page = page - 1
      }
      const end = start + size
      const data = slice(selectedIds.value, start, end)
      run({
        deviceSafetyId,
        data,
        page_size: size,
        page_num: page,
      })
    }

    // 移除已选规则
    /**
     * 移除已选规则
     * @param {Array} removeRules 要移除的规则ID数组
     */
    const handleRemove = async (removeRules = []) => {
      const currentRules = filter(selectedRules.value, item => !removeRules.includes(item.id))
      setSelectedRules(currentRules)
    }

    watch(
      () => selectedIds.value,
      (val) => {
        if (!val.length) {
          if (!data.value) return
          data.value.items = []
          data.value.total = 0
          return
        }
        refreshTableData()
      }
    )

    return {
      pagination,
      loading,
      dataSource,
      refreshTableData,
      handleTableChange,
      handleRemove,
      columns: getRuleColumns({showOperation: mode.value !== 'view', dictService: singleDictService.value})
    }
  }
})
</script>
