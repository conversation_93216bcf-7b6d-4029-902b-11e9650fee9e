import { sum, values } from 'lodash'

/** @type {*} 模板定义类型枚举 */
export const TEMPLATE_TYPE_ENUM = {
  CUSTOMIZE: 'customize',
  PREDEFINE: 'predefine'
}

/** @type {*} 模板类型名称映射 */
export const TEMPLATE_TYPE_LABEL_MAP = {
  [TEMPLATE_TYPE_ENUM.CUSTOMIZE]: '自定义',
  [TEMPLATE_TYPE_ENUM.PREDEFINE]: '预定义'
}

/** @type {*} 防护模式枚举 */
export const MODE_ENUM = {
  DEFEND: 'defend',
  ALARM: 'alarm'
}

/** @type {*} 防护模式名称映射 */
export const MODE_LABEL_MAP = {
  [MODE_ENUM.DEFEND]: '防护模式',
  [MODE_ENUM.ALARM]: '告警模式'
}

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '模板名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入模板名称',
    },
  },
]

/** @type {*} 获取防护模板列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '模板名称',
    dataIndex: 'name',
    ellipsis: true,
    key: 'name',
    width: 200
  },
  {
    title: '描述',
    dataIndex: 'desc',
    ellipsis: true,
    key: 'desc',
    width: 200
  },
  {
    title: '响应模式',
    dataIndex: 'mode',
    ellipsis: true,
    key: 'mode',
    width: 120,
    customRender: ({ record }) => {
      return MODE_LABEL_MAP[record.mode] ?? '-'
    }
  },
  {
    title: '规则数量（条）',
    dataIndex: 'level_sum',
    ellipsis: true,
    key: 'level_sum',
    width: 150,
    customRender: ({ record }) => {
      return sum(values(record.level_sum)) ?? 0
    }
  },
  {
    title: '引用次数',
    dataIndex: 'cite_count',
    ellipsis: true,
    key: 'cite_count',
    width: 120
  },
  {
    title: '模板类型',
    dataIndex: 'type',
    ellipsis: true,
    key: 'type',
    width: 120,
    customRender: ({ record }) => {
      return TEMPLATE_TYPE_LABEL_MAP[record.type] ?? '-'
    }
  },
  {
    title: '创建时间',
    dataIndex: 'create_time',
    ellipsis: true,
    key: 'create_time',
    width: 155,
    customRender: ({ record }) => {
      return record.create_time ?? '-'
    }
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 220
  }
]


/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        name: {
          type: 'string',
          title: '模板名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入模板名称'
          },
          'x-validator': [
            { required: true, message: '请输入模板名称' },
            { min: 1 },
            { max: 31 },
            { ipsName: true }
          ]
        },
        mode: {
          type: 'string',
          required: true,
          default: 'defend',
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          'x-component-props': {
            isGroup: true,
            optionType: "button",
            options: [
              { label: MODE_LABEL_MAP[MODE_ENUM.DEFEND], value: MODE_ENUM.DEFEND },
              { label: MODE_LABEL_MAP[MODE_ENUM.ALARM], value: MODE_ENUM.ALARM }
            ]
          },
          'x-decorator-props': {
            label: '响应模式',
            // addonAfter: '(防护模式下，匹配到规则的流量将按照规则动作给予响应和防护。)',
            // wrapperWidth: 150
          }
        },
        desc: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          }
        },
        'x-validator': [
          { max: 200 }
        ]
      }
    }
  }
}

