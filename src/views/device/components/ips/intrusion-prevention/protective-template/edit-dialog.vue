<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="900px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="SCHEMA"></SchemaField>
      </FormProvider>
      <RuleTable v-if="dialogVisible" :mode="mode" />
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, nextTick, inject } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep, pick } from 'lodash'

import RuleTable from './rule-table/index.vue'
import useRuleHook from './rule-table-hook';
import { getProtectiveTemplateDetail } from '@/request/api-device-ips'
import { SCHEMA } from './config';

export default defineComponent({
  components: {
    RuleTable
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { deviceSafetyId } = inject('rowData')
    const { visible, mode, defaultData } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}入侵防护模板`
    })

    const { resetSelectedRules, selectedRules, setSelectedRules } = useRuleHook()

    const state = reactive({
      ruleDetails: {},
      loading: false
    })

    const form = createForm()

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      form.setPattern(patternMap[mode.value])
      if (mode.value === 'add') return
      let details = cloneDeep(defaultData.value)
      try {
        state.loading = true
        const res = await getProtectiveTemplateDetail({deviceSafetyId, id: defaultData.value.id})
        details = res.data ?? details;
      } finally {
        state.loading = false
        form.setValues(cloneDeep(details))
        if (details.rules?.length) setSelectedRules(cloneDeep(details.rules))
      }
    }

    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return;
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      state.ruleDetails = {}
      ctx.emit('update:visible', false)
      resetSelectedRules()
    }

    const handleSave = async () => {
      await form.validate()
      const params = cloneDeep(form.values)
      if (!selectedRules.value?.length) proxy.$message.warning('必须至少包含一项规则！')
      params.rules = selectedRules.value?.map(item => pick(item, ['id', 'level', 'enable', 'block_action', 'log', 'merge_type', 'raw_pkt_enable'])) ?? []
      state.loading = true
      ctx.emit('on-success', {
        mode: mode.value,
        data: params,
        callback: handleClose
      })
    }

    return {
      title,
      isView,
      form,
      SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script>
