<!-- 入侵防御系统 入侵防御配置 -->

<template>
  <a-tabs v-model:activeKey="activeKey" size="small">
    <a-tab-pane v-for="tab in TABS" :key="tab.label" :tab="tab.label">
      <div class="list--inner_tab">
        <component :is="tab.componentName" v-bind="tab.componentProps ?? {}" />
      </div>
    </a-tab-pane>
  </a-tabs>
</template>

<script>

import { defineComponent, ref } from 'vue'
import { TABS } from './config'
import ProtectiveTemplate from './protective-template/index.vue'
import XssWhiteList from './xss-white-list/index.vue'
import SqlWhiteList from './sql-white-list/index.vue'
import RuleWhiteList from './rule-white-list/index.vue'

export default defineComponent({
  components: {
    ProtectiveTemplate,
    XssWhiteList,
    Sq<PERSON><PERSON><PERSON><PERSON><PERSON>ist,
    RuleWhiteList
  },
  setup (props, ctx) {
    const activeKey = ref(TABS[0].label)
    return {
      TABS,
      activeKey
    }
  }
})
</script>

<style lang="less">
.list--inner_tab {
  margin-top: 8px;
}
</style>
