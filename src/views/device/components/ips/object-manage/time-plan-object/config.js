/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} 定义类型枚举 */
export const TYPE_LABEL_ENUM = {
  CUSTOMIZE: 'customize',
  PREDEFINE: 'predefine'
}

/** @type {*} 定义类型映射 */
export const TYPE_LABEL_MAP = {
  [TYPE_LABEL_ENUM.CUSTOMIZE]: '自定义',
  [TYPE_LABEL_ENUM.PREDEFINE]: '预定义'
}

/** @type {*} 时间计划类型枚举 */
export const TIME_PLAN_ENUM = {
  CYCLE: 'CYCLE',
  ABSOLUTE: 'ABSOLUTE'
}

/** @type {*} 时间计划类型映射 */
export const TIME_PLAN_LABEL_MAP = {
  [TIME_PLAN_ENUM.CYCLE]: '周期计划',
  [TIME_PLAN_ENUM.ABSOLUTE]: '绝对计划'
}

/** @type {*} 时间计划状态枚举 */
export const STATUS_ENUM = {
  ACTIVE: 'active',
  ENDED: 'ended',
  NOT_STARTED: 'not_started'
}


/** @type {*} 时间计划状态映射 */
export const STATUS_LABEL_MAP = {
  [STATUS_ENUM.ACTIVE]: '活跃中',
  [STATUS_ENUM.ENDED]: '已过期',
  [STATUS_ENUM.NOT_STARTED]: '未开始',
}

/** @type {*} 周期计划类型枚举 */
export const CYCLE_TYPE_ENUM = {
  EVERYDAY: 'EVERYDAY',
  FEW_DAYS_WEEK: 'FEW_DAYS_WEEK',
  PERIOD_OF_TIME_A_WEEK: 'PERIOD_OF_TIME_A_WEEK'
}

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
    key: 'name',
    width: 100,
  },
  {
    title: '类型',
    dataIndex: 'category',
    ellipsis: true,
    key: 'category',
    width: 120,
    customRender: ({ record }) => {
      const category = TIME_PLAN_LABEL_MAP[record.category] ?? '-';
      return <span>{category}</span>
    }
  },
  {
    title: '状态',
    dataIndex: 'status',
    ellipsis: true,
    key: 'status',
    width: 80,
    customRender: ({ record }) => {
      const status = STATUS_LABEL_MAP[record.status] ?? '-';
      return <span>{status}</span>
    }
  },
  {
    title: '起止时间',
    dataIndex: 'start_end_time',
    ellipsis: true,
    key: 'start_end_time',
    customRender: ({ record }) => {
      if (!record.start_end_time?.[0]?.start && !record.start_end_time?.[0]?.end) return <span>永久有效</span>
      const label = `${record.start_end_time?.[0]?.start} 到 ${record.start_end_time?.[0]?.end}`
      return <span>{label}</span>
    }
  },
  {
    title: '引用次数',
    dataIndex: 'cite_count',
    ellipsis: true,
    key: 'cite_count',
    width: 100,
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    key: 'description'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    fixed: 'right',
    width: 220
  }
]

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 90
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          }
        },
        category: {
          type: 'string',
          title: '计划类型',
          'x-validator': [
            { required: true, message: '请选择计划类型' },
          ],
          default: TIME_PLAN_ENUM.CYCLE,
          'x-decorator': 'FormItem',
          'x-component': 'Radio.Group',
          'x-component-props': {
            isGroup: true,
            optionType: "button",
            options: [
              { label: TIME_PLAN_LABEL_MAP.CYCLE, value: TIME_PLAN_ENUM.CYCLE },
              { label: TIME_PLAN_LABEL_MAP.ABSOLUTE, value: TIME_PLAN_ENUM.ABSOLUTE },
            ]
          }
        },
        cycle_plan: {
          type: 'array',
          title: '周期设置',
          'x-validator': [
            { required: true, message: '请添加周期设置' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'ArrayTable',
          'x-component-props': {
            rowKey: 'index',
            pagination: { pageSize: 10 },
            scroll: { x: '100%' },
          },
          items: {
            type: 'object',
            properties: {
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '类型', width: 160, key: 'type', dataIndex: 'type' },
                ellipsis: true,
                properties: {
                  type: {
                    type: 'string',
                    'x-validator': [
                      { required: true, message: '请选择类型' }
                    ],
                    default: 'EVERYDAY',
                    'x-decorator': 'FormItem',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '请选择',
                      showSearch: true,
                      filterOption: (input, option = {}) => {
                        if (!input) return true;
                        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      },
                    },
                    enum: [
                      { label: '每天', value: CYCLE_TYPE_ENUM.EVERYDAY },
                      { label: '每周的某几天', value: CYCLE_TYPE_ENUM.FEW_DAYS_WEEK },
                      { label: '每周的一段时间', value: CYCLE_TYPE_ENUM.PERIOD_OF_TIME_A_WEEK },
                    ]
                  }
                }
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '时间', key: 'content', dataIndex: 'content' },
                ellipsis: true,
                properties: {
                  EVERYDAY: {
                    type: 'void',
                    'x-component': 'VoidField',
                    'x-display': 'visible',
                    properties: {
                      day_time: {
                        type: 'array',
                        'x-validator': [
                          { required: true, message: '请选择时间' }
                        ],
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          label: '',
                          colon: false
                        },
                        'x-component': 'TimeRangePicker',
                        'x-component-props': {
                          showTime: {format: 'HH:mm:ss'},
                          placeholder: ['开始时间', '结束时间'],
                          valueFormat: 'HH:mm:ss',
                          format: "HH:mm:ss"
                        },
                      }
                    },
                    'x-reactions': {
                      dependencies: ['.type'],
                      fulfill: {
                        state: {
                          visible: `{{$deps[0] === 'EVERYDAY'}}`
                        }
                      }
                    }
                  },
                  FEW_DAYS_WEEK: {
                    type: 'void',
                    'x-display': 'none',
                    'x-component': 'Space',
                    properties: {
                      days_week: {
                        type: 'array',
                        'x-validator': [
                          { required: true, message: '请选择' }
                        ],
                        'x-decorator': 'FormItem',
                        'x-component': 'Select',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                          wrapperWidth: 360,
                        },
                        'x-component-props': {
                          placeholder: '请选择',
                          mode: 'multiple'
                        },
                        enum: [
                          { label: '周一', value: 1 },
                          { label: '周二', value: 2 },
                          { label: '周三', value: 3 },
                          { label: '周四', value: 4 },
                          { label: '周五', value: 5 },
                          { label: '周六', value: 6 },
                          { label: '周日', value: 7 },
                        ]
                      },
                      week_time: {
                        type: 'array',
                        'x-validator': [
                          { required: true, message: '请选择' }
                        ],
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                        },
                        'x-component': 'TimeRangePicker',
                        'x-component-props': {
                          showTime: {format: 'HH:mm:ss'},
                          placeholder: ['开始时间', '结束时间'],
                          valueFormat: 'HH:mm:ss',
                          format: "HH:mm:ss"
                        },
                      }
                    },
                    'x-reactions': {
                      dependencies: ['.type'],
                      fulfill: {
                        state: {
                          visible: `{{$deps[0] === 'FEW_DAYS_WEEK'}}`
                        }
                      }
                    }
                  },
                  PERIOD_OF_TIME_A_WEEK: {
                    type: 'void',
                    'x-display': 'none',
                    'x-component': 'Space',
                    properties: {
                      start_day: {
                        type: 'string',
                        'x-validator': [
                          { required: true, message: '请选择' }
                        ],
                        'x-decorator': 'FormItem',
                        'x-component': 'Select',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                          wrapperWidth: 140,
                        },
                        'x-component-props': {
                          placeholder: '请选择',
                        },
                        enum: [
                          { label: '周一', value: 1 },
                          { label: '周二', value: 2 },
                          { label: '周三', value: 3 },
                          { label: '周四', value: 4 },
                          { label: '周五', value: 5 },
                          { label: '周六', value: 6 },
                          { label: '周日', value: 7, disabled: true },
                        ]
                      },
                      start_time: {
                        type: 'string',
                        'x-validator': [
                          { required: true, message: '请选择' }
                        ],
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                          wrapperWidth: 145,
                        },
                        'x-component': 'TimePicker',
                        'x-component-props': {
                          showTime: {format: 'HH:mm:ss'},
                          placeholder: ['起始时间'],
                          valueFormat: 'HH:mm:ss'
                        },
                      },
                      end_day: {
                        type: 'string',
                        'x-validator': [
                          { required: true, message: '请选择' }
                        ],
                        'x-decorator': 'FormItem',
                        'x-component': 'Select',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                          wrapperWidth: 140,
                        },
                        'x-component-props': {
                          placeholder: '请选择',
                        },
                        enum: [
                          { label: '周一', value: 1, disabled: true },
                          { label: '周二', value: 2 },
                          { label: '周三', value: 3 },
                          { label: '周四', value: 4 },
                          { label: '周五', value: 5 },
                          { label: '周六', value: 6 },
                          { label: '周日', value: 7 },
                        ]
                      },
                      end_time: {
                        type: 'string',
                        'x-validator': [
                          { required: true, message: '请选择' }
                        ],
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          label: '',
                          colon: false,
                          wrapperWidth: 140,
                        },
                        'x-component': 'TimePicker',
                        'x-component-props': {
                          showTime: {format: 'HH:mm:ss'},
                          placeholder: ['结束时间'],
                          valueFormat: 'HH:mm:ss'
                        },
                      }
                    },
                    'x-reactions': {
                      dependencies: ['.type'],
                      fulfill: {
                        state: {
                          visible: `{{$deps[0] ==='PERIOD_OF_TIME_A_WEEK'}}`
                        }
                      }
                    }
                  }
                }
              },
              column3: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: '操作',
                  dataIndex: 'operations',
                  ellipsis: true,
                  width: 70,
                  fixed: 'right'
                },
                properties: {
                  item: {
                    type: 'void',
                    'x-component': 'FormItem',
                    properties: {
                      remove: {
                        type: 'void',
                        'x-component': 'ArrayTable.Remove'
                      }
                    }
                  }
                }
              }
            }
          },
          properties: {
            add: {
              type: 'void',
              'x-component': 'ArrayTable.Addition',
              title: '添加'
            }
          }
        },
        start_end_time: {
          type: 'array',
          title: '起止时间',
          'x-decorator': 'FormItem',
          'x-component': 'DatePicker.RangePicker',
          'x-component-props': {
            showTime: {format: 'HH:mm:ss'},
            placeholder: ['开始时间', '结束时间'],
            valueFormat: 'YYYY-MM-DD HH:mm:ss',
            format: "YYYY-MM-DD HH:mm:ss"
          },
        },
        description: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            { max: 200 }
          ]
        },
      }
    }
  }
}


