<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="1000px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, nextTick } from 'vue'
import { createForm, onFieldValueChange, onFieldInputValueChange } from '@formily/core'
import { cloneDeep, values } from 'lodash'

import { SCHEMA, CYCLE_TYPE_ENUM, TIME_PLAN_ENUM } from './config';

const DEFAULT_FORM = {
  name: '',
  category: TIME_PLAN_ENUM.CYCLE,
  cycle_plan: [],
  start_end_time: [],
  description: ''
};

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { visible, mode, defaultData } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}时间计划`
    })

    const state = reactive({
      loading: false
    })

    const form = createForm({
      initialValues: cloneDeep(DEFAULT_FORM),
      effects: () => {
        onFieldValueChange('category', (field, form) => {
          const categoryValue = field.value
          const isAbsoluteTime = categoryValue === TIME_PLAN_ENUM.ABSOLUTE
          const timeRangeField = form.query('.start_end_time')?.take()
          timeRangeField?.setRequired(isAbsoluteTime)
          const arrayField = form.query('.cycle_plan')?.take()
          arrayField?.setDisplay(isAbsoluteTime ? 'hidden' : 'visible')
        })
      }
    })

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    /**
     * 初始化表单数据
     */
    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      const nameFieldMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'disabled'
      }
      const nameField = form.query('name').take()
      form.setPattern(patternMap[mode.value])
      nameField?.setPattern(nameFieldMap[mode.value])
      if (mode.value === 'add') return form.setValues(cloneDeep(DEFAULT_FORM))
      const formData = cloneDeep(defaultData.value)
      formData.cycle_plan = formData.cycle_plan?.map(item => {
        const planObj = {
          type: item.type,
          id: item.id
        }
        if (item.type === CYCLE_TYPE_ENUM.FEW_DAYS_WEEK) {
          planObj.days_week = item.days_week
          planObj.week_time = [item.start_time, item.end_time]
        } else if (item.type === CYCLE_TYPE_ENUM.PERIOD_OF_TIME_A_WEEK) {
          planObj.start_day = item.start_day
          planObj.end_day = item.end_day
          planObj.start_time = item.start_time
          planObj.end_time = item.end_time
        } else {
          planObj.day_time = [item.start_time, item.end_time]
        }
        return planObj
      }) ?? []
      if (formData.start_end_time?.length) {
        formData.start_end_time = [formData.start_end_time[0].start, formData.start_end_time[0].end]
      }
      form.setValues(formData)
    }

    /**
     * 关闭弹窗处理
     * @param {boolean} hasError 是否有错误
     */
    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return;
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    /**
     * 保存表单数据
     */
    const handleSave = async () => {
      await form.validate()
      state.loading = true
      const params = cloneDeep(form.values)
      if (params.start_end_time?.length) {
        params.start_end_time = [{start: params.start_end_time[0], end: params.start_end_time[1] }]
      }
      if (params.category === TIME_PLAN_ENUM.CYCLE) {
        params.cycle_plan = params.cycle_plan.map(item => {
          const planObj = {
            type: item.type,
            days_month: [],
            days_week: [],
            start_day: 1,
            end_day: 7,
          }
          if (item.type === CYCLE_TYPE_ENUM.FEW_DAYS_WEEK) {
            planObj.days_week = item.days_week
            planObj.time = item.day_time
            planObj.start_time = item.week_time[0]
            planObj.end_time = item.week_time[1]
          } else if (item.type === CYCLE_TYPE_ENUM.PERIOD_OF_TIME_A_WEEK) {
            planObj.start_day = item.start_day
            planObj.end_day = item.end_day
            planObj.start_time = item.start_time
            planObj.end_time = item.end_time
          } else {
            planObj.start_time = item.day_time[0]
            planObj.end_time = item.day_time[1]
          }
          return planObj
        })
      }
      ctx.emit('on-success', {
        mode: mode.value,
        data: params,
        callback: handleClose
      })
    }

    return {
      title,
      isView,
      form,
      schema: SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script>
