<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="900px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm, onFieldReact } from '@formily/core'
import { cloneDeep, omit } from 'lodash'

import { SCHEMA } from './config';

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { visible, mode, defaultData } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}关键字`
    })

    const state = reactive({
      loading: false
    })

    const form = createForm({
      effects: () => {
        onFieldReact('member.*.content', async field => {
          const matchtype = field.query('.matchtype')?.take()?.value
          const componentMap = {
              full: {
                component: 'Input',
                componentProps: {
                  placeholder: '请输入关键字'
                },
                defaultValue: field.value ?? ''
              },
              regex: {
                component: 'Select',
                componentProps: {
                  placeholder: '请选择',
                  options: [
                    { label: '手机正则', value: '手机正则' },
                    { label: '身份证正则', value: '身份证正则' },
                    { label: '银行卡/信用卡正则', value: '银行卡/信用卡正则' },
                  ]
                },
                defaultValue: field.value ?? null
              }
            }
            field?.setComponent(componentMap[matchtype]?.component, componentMap[matchtype]?.componentProps)
            await nextTick()
            field?.setValue(componentMap[matchtype]?.defaultValue || null)
        })
      }
    })

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    /**
     * 初始化表单数据
     */
    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      const nameFieldMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'disabled'
      }
      const nameField = form.query('name').take()
      form.setPattern(patternMap[mode.value])
      nameField?.setPattern(nameFieldMap[mode.value])
      if (mode.value === 'add') return
      const formData = cloneDeep(defaultData.value)
      form.setValues(formData)
    }

    /**
     * 关闭弹窗处理
     * @param {boolean} hasError 是否有错误
     */
    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return;
      console.log(cloneDeep({...form.initialValues, member: []}))
      await form.setValues({}, 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    /**
     * 保存表单数据
     */
    const handleSave = async () => {
      await form.validate()
      state.loading = true
      const params = cloneDeep(form.values)
      ctx.emit('on-success', {
        mode: mode.value,
        data: omit(params, ['code', 'type']),
        callback: handleClose
      })
    }

    return {
      title,
      isView,
      form,
      schema: SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script>
