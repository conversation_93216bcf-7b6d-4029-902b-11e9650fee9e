/** @type {*} 定义类型枚举 */
export const TYPE_LABEL_ENUM = {
  CUSTOMIZE: 1,
  PREDEFINE: 0
}

/** @type {*} 定义类型映射 */
export const TYPE_LABEL_MAP = {
  [TYPE_LABEL_ENUM.CUSTOMIZE]: '自定义',
  [TYPE_LABEL_ENUM.PREDEFINE]: '预定义'
}

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({index}){
      return index+1
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
    key: 'name'
  },
  {
    title: '类型',
    dataIndex: 'type',
    ellipsis: true,
    key: 'type',
    customRender: ({ record }) => {
      const type = TYPE_LABEL_MAP[record.type] ?? '-';
      return <span>{type}</span>
    }
  },
  {
    title: '描述',
    dataIndex: 'desc',
    ellipsis: true,
    key: 'desc'
  },
  {
    title: '引用次数',
    dataIndex: 'cite_count',
    ellipsis: true,
    key: 'cite_count'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 220
  }
]

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 80
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          }
        },
        desc: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            { max: 200 }
          ]
        },
        member: {
          type: 'array',
          'x-validator': [
            { required: true, message: '请添加关键字' },
          ],
          title: '关键字',
          default: [],
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            colon: false
          },
          'x-component': 'ArrayTable',
          'x-component-props': {
            pagination: { pageSize: 10 },
            scroll: { x: '100%' },
          },
          items: {
            type: 'object',
            properties: {
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '类型', width: 150, key: 'matchtype', dataIndex: 'matchtype' },
                ellipsis: true,
                properties: {
                  matchtype: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    default: 'full',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '请选择',
                      showSearch: true,
                      filterOption: (input, option = {}) => {
                        if (!input) return true;
                        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      },
                    },
                    enum: [
                      { label: '关键字匹配', value: 'full' },
                      { label: '正则匹配', value: 'regex' },
                    ]
                  }
                }
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '关键字内容', key: 'content', dataIndex: 'content' },
                ellipsis: true,
                properties: {
                  content: {
                    type: 'string',
                    'x-validator': [
                      { required: true, message: '请输入关键字' }
                    ],
                    default: null,
                    'x-component': 'Input',
                    'x-decorator': 'FormItem',
                    'x-component-props': {
                      placeholder: '请输入关键字',
                    },
                    'x-decorator-props': {
                      label: '',
                      colon: false,
                    },
                  }
                }
              },
              column3: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '白名单', key: 'white', dataIndex: 'white' },
                ellipsis: true,
                properties: {
                  white: {
                    type: 'array',
                    'x-display': 'hidden',
                    'x-component': 'Select',
                    default: [],
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      label: '',
                      colon: false,
                    },
                    'x-component-props': {
                      placeholder: '非必填,请输入并选中',
                      mode: 'tags',
                      showSearch: true,
                      filterOption: (input, option = {}) => {
                        if (!input) return true;
                        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      },
                    },
                    enum: [],
                    'x-reactions': {
                      dependencies: ['.matchtype'],
                      fulfill: {
                        state: {
                          visible: `{{$deps[0] === 'regex'}}`
                        }
                      }
                    }
                  }
                }
              },
              column4: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '命中阈值', key: 'limit', dataIndex: 'limit', width: 140 },
                ellipsis: true,
                properties: {
                  limit: {
                    type: 'number',
                    'x-component': 'InputNumber',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      label: '',
                      colon: false,
                    },
                    'x-component-props': {
                      placeholder: '1~255'
                    },
                    'x-validator': [
                      { format: 'integer' },
                      {
                        minimum: 1
                      },
                      {
                        maximum: 255
                      }
                    ]
                  }
                }
              },
              column5: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: '操作',
                  dataIndex: 'operations',
                  ellipsis: true,
                  width: 100,
                  fixed: 'right'
                },
                properties: {
                  item: {
                    type: 'void',
                    'x-component': 'FormItem',
                    properties: {
                      remove: {
                        type: 'void',
                        'x-component': 'ArrayTable.Remove'
                      }
                    }
                  }
                }
              }
            }
          },
          properties: {
            add: {
              type: 'void',
              'x-component': 'ArrayTable.Addition',
              title: '添加'
            }
          }
        },
      }
    }
  }
}


