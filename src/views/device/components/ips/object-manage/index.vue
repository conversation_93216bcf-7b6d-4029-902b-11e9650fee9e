<!-- 入侵防御系统 对象管理 -->

<template>
  <a-tabs v-model:activeKey="activeKey" size="small">
    <a-tab-pane v-for="tab in TABS" :key="tab.label" :tab="tab.label">
      <div class="list--inner_tab">
        <component :is="tab.componentName" />
      </div>
    </a-tab-pane>
  </a-tabs>
</template>

<script>
import { defineComponent, ref } from 'vue';

import { TABS } from './config';
import IpAddress from './ip-address/index.vue';
import ServiceObject from './service-object/index.vue';
import CustomApp from './custom-app/index.vue';
import KeywordManage from './keyword-manage/index.vue';
import TimePlanObject from './time-plan-object/index.vue';

export default defineComponent({
  components: {
    IpAddress,
    ServiceObject,
    CustomApp,
    KeywordManage,
    TimePlanObject
  },
  setup (props, ctx) {
    const activeKey = ref(TABS[0].label)
    return {
      TABS,
      activeKey
    }
  }
})
</script>

<style lang="less">
.list--inner_tab {
  margin-top: 8px;
}
</style>
