
/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} 服务或服务组枚举 */
export const CATEGORY_ENUM = {
  SERVICE: 'SERVICE',
  GROUP: 'GROUP'
}

/** @type {*} 服务类型Label枚举 */
export const CATEGORY_LABEL_MAP = {
  SERVICE: '服务',
  GROUP: '服务组'
}

/** @type {*} 定义类型映射 */
export const TYPE_LABEL_MAP = {
  customize: '自定义',
  predefine: '预定义'
}

/** @type {*} 定义类型枚举 */
export const TYPE_LABEL_ENUM = {
  CUSTOMIZE: 'customize',
  PREDEFINE: 'predefine'
}

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
    key: 'name'
  },
  {
    title: '服务类型',
    dataIndex: 'category',
    ellipsis: true,
    key: 'category',
    customRender: ({ record }) => {
      const category = CATEGORY_LABEL_MAP[record.category] ?? '-';
      return <span>{category}</span>
    }
  },
  {
    title: '类别',
    dataIndex: 'type',
    ellipsis: true,
    key: 'type',
    customRender: ({ record }) => {
      const type = TYPE_LABEL_MAP[record.type] ?? '-';
      return <span>{type}</span>
    }
  },
  {
    title: '引用次数',
    dataIndex: 'cite_count',
    ellipsis: true,
    key: 'cite_count'
  },
  {
    title: '描述',
    dataIndex: 'desc',
    ellipsis: true,
    key: 'desc'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 220
  }
]

/** @type {*} 新增服务表单schema */
export const SERVICE_SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          }
        },
        protocol_type: {
          type: 'string',
          title: "协议类型",
          'x-validator': [
            { required: true, message: '请选择协议类型' },
          ],
          default: 'tcp/udp',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择',
            showSearch: true,
            filterOption: (input, option = {}) => {
              if (!input) return true;
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          },
          enum: [
            { label: 'TCP/UDP', value: 'tcp/udp' },
            { label: 'ICMP(ICMPV6)', value: 'icmp' }
          ]
        },
        members: {
          type: 'array',
          'x-validator': [
            { required: true, message: '请添加成员' },
          ],
          title: '成员列表',
          default: [],
          'x-decorator': 'FormItem',
          'x-component': 'ArrayTable',
          'x-component-props': {
            pagination: { pageSize: 10 },
            scroll: { x: '100%' },
          },
          items: {
            type: 'object',
            properties: {
              // column0: {
              //   type: 'void',
              //   'x-component': 'ArrayTable.Column',
              //   'x-component-props': {
              //     width: 50,
              //     title: '序号',
              //     align: 'center',
              //   },
              //   properties: {
              //     index: {
              //       type: 'void',
              //       'x-component': 'ArrayTable.Index',
              //     },
              //   },
              // },
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '协议类型', width: 150, key: 'protocol', dataIndex: 'protocol' },
                ellipsis: true,
                properties: {
                  protocol: {
                    type: 'string',
                    'x-decorator': 'FormItem',
                    default: 'tcp',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '请选择',
                      showSearch: true,
                      filterOption: (input, option = {}) => {
                        if (!input) return true;
                        return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                      },
                    },
                    enum: [
                      { label: 'TCP', value: 'tcp' },
                      { label: 'UDP', value: 'udp' },
                    ]
                  }
                }
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '源端口', key: 'src_port', dataIndex: 'src_port' },
                ellipsis: true,
                properties: {
                  src_port: {
                    type: 'void',
                    'x-component': 'Space',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      label: '',
                      colon: false,
                    },
                    properties: {
                      src_port_start: {
                        type: 'number',
                        'x-component': 'InputNumber',
                        default: 1,
                        'x-decorator': 'FormItem',
                        'x-component-props': {
                          placeholder: '起始端口号'
                        },
                        'x-decorator-props': {
                          addonAfter: '-'
                        },
                        'x-validator': [
                          { required: true, message: '请输入' },
                          {
                            format: 'integer'
                          },
                          {
                            port: true
                          }
                        ]
                      },
                      src_port_end: {
                        type: 'number',
                        'x-component': 'InputNumber',
                        'x-decorator': 'FormItem',
                        default: 65535,
                        'x-component-props': {
                          placeholder: '截止端口号'
                        },
                        'x-validator': [
                          {
                            format: 'integer'
                          },
                          {
                            port: true
                          }
                        ]
                      },
                    }
                  }
                }
              },
              column3: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '目的端口', key: 'dst_port', dataIndex: 'dst_port' },
                ellipsis: true,
                properties: {
                  dst_port: {
                    type: 'void',
                    'x-component': 'Space',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      label: ' ',
                      colon: false,
                      feedbackLayout: 'none'
                    },
                    properties: {
                      dst_port_start: {
                        type: 'number',
                        'x-component': 'InputNumber',
                        'x-decorator': 'FormItem',
                        'x-component-props': {
                          placeholder: '起始端口号'
                        },
                        'x-decorator-props': {
                          addonAfter: '-'
                        },
                        'x-validator': [
                          { required: true, message: '请输入' },
                          {
                            format: 'integer'
                          },
                          {
                            port: true
                          }
                        ]
                      },
                      dst_port_end: {
                        type: 'number',
                        'x-component': 'InputNumber',
                        'x-decorator': 'FormItem',
                        'x-component-props': {
                          placeholder: '截止端口号'
                        },
                        'x-validator': [
                          {
                            format: 'integer'
                          },
                          {
                            port: true
                          }
                        ]
                      },
                    }
                  }
                }
              }
            }
          },
          properties: {
            add: {
              type: 'void',
              'x-component': 'ArrayTable.Addition',
              title: '添加'
            }
          }
        },
        type: {
          type: 'number',
          'x-display': 'hidden',
          'x-component': 'InputNumber',
          'x-component-props': {
            placeholder: '请输入'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '类型(Type)',
            addonAfter: '范围(0-255)'
          },
          'x-validator': [
            {
              format: 'integer',
            },
            {
              minimum: 0
            },
            {
              maximum: 255
            }
          ],
        },
        code: {
          type: 'number',
          'x-display': 'hidden',
          'x-component': 'InputNumber',
          'x-component-props': {
            placeholder: '请输入'
          },
          'x-validator': [
            {
              format: 'integer',
            },
            {
              minimum: 0
            },
            {
              maximum: 255
            }
          ],
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '代码(Code)',
            addonAfter: '范围(0-255)'
          }
        },
        desc: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            { max: 200 }
          ]
        },
      }
    }
  }
}

/** @type {*} 新增服务组表单schema */
export const SERVICE_GROUP_SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 80
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          }
        },
        description: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            { max: 200 }
          ]
        },
        members: {
          type: 'array',
          'x-validator': [
            { required: true, message: '请添加成员' },
          ],
          title: '成员',
          enum: [],
          'x-decorator': 'FormItem',
          'x-component': 'Transfer',
          'x-component-props': {
            render: (item) => item.title,
          },
        },
      }
    }
  }
}

