<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="800px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm, onFieldValueChange } from '@formily/core'
import { cloneDeep, omit } from 'lodash'

import { SERVICE_SCHEMA, CATEGORY_ENUM } from './config';

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { visible, mode, defaultData } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}服务`
    })

    const state = reactive({
      loading: false
    })

    const form = createForm({
      effects: () => {
        onFieldValueChange('protocol_type', (field) => {
          const isICMP = field.value === 'icmp'
          field.query('*(type,code)')?.forEach(item => {
            item?.setState({
              display: isICMP ? 'visible' : 'hidden',
              required: isICMP
            })
          })
          field.query('members')?.take()?.setState({
            display: isICMP ? 'hidden' : 'visible',
            required: !isICMP
          })
        })
      }
    })

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    /**
     * 初始化表单数据
     */
    const initForm = async () => {
      form.setPattern(isView.value ? 'readPretty' : 'editable')
      if (mode === 'add') return;
      const formData = cloneDeep(defaultData.value)
      if (formData.protocol_type === 'icmp') {
        formData.code = formData.members?.[0]?.code ?? null
        formData.type = formData.members?.[0]?.type ?? null
      } else {
        formData.members = formData.members?.map(item => {
          return {
            protocol: item.protocol,
            src_port_start: item?.src_port?.[0] ?? null,
            src_port_end: item?.src_port?.[1] ?? null,
            dst_port_start: item?.dst_port?.[0] ?? null,
            dst_port_end: item?.dst_port?.[1] ?? null
          }
        })
      }
      form.setValues(formData)
    }

    /**
     * 关闭弹窗处理
     * @param {boolean} hasError 是否有错误
     */
    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return;
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    /**
     * 保存表单数据
     */
    const handleSave = async () => {
      await form.validate()
      state.loading = true
      const params = cloneDeep(form.values)
      if (params.protocol_type === 'icmp') {
        params.members = [
          {
            protocol: 'icmp',
            code: params.code,
            type: params.type,
          }
        ]
      } else {
        params.members = params.members?.map(item => {
          return {
            protocol: item.protocol,
            src_port: item?.src_port_end ? [item?.src_port_start, item?.src_port_end] : [item?.src_port_start],
            dst_port: item?.dst_port_end ? [item?.dst_port_start, item?.dst_port_end] : [item?.dst_port_start],
          }
        }) ?? []
      }
      ctx.emit('on-success', {
        mode: mode.value,
        category: CATEGORY_ENUM.SERVICE,
        data: omit(params, ['code', 'type']),
        callback: handleClose
      })
    }

    return {
      title,
      isView,
      form,
      schema: SERVICE_SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script>
