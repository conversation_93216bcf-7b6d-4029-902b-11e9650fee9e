<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="800px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, onMounted, nextTick } from 'vue'
import { createForm, onFieldValueChange } from '@formily/core'
import { cloneDeep, omit } from 'lodash'

import { SCHEMA } from './config'

export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { visible, mode, defaultData } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}自定义应用`
    })

    const state = reactive({
      loading: false
    })

    const form = createForm({

      effects: () => {
        onFieldValueChange('type', (field) => {
          field.query('name')?.take()?.setComponentProps({
            addonBefore: field.value ? `${field.value}_` : ''
          })
        })
      }
    })

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    /**
     * 初始化表单数据
     */
    const initForm = async () => {
      const formData = cloneDeep(defaultData.value)
      formData.origin_match = atob(formData.origin_match)
      formData.origin_match_node = JSON.parse(atob(formData.origin_match_node || ""))
      form.setValues(formData)
    }

    /**
     * 关闭弹窗处理
     * @param {boolean} hasError 是否有错误
     */
    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    /**
     * 保存表单数据
     */
    const handleSave = async () => {
      await form.validate()
      state.loading = true
      const params = { ...form.values, define_type: 'expert' }
      params.origin_match = btoa(params.origin_match)
      params.origin_match_node = btoa(JSON.stringify("base" === params.define_type ? params.origin_match_node : []))
      ctx.emit('on-success', {
        mode: mode.value,
        data: omit(params, ['origin_match_tip']),
        callback: handleClose
      })
    }

    return {
      title,
      isView,
      form,
      schema: SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleClose,
      handleSave
    }
  }
})
</script>
