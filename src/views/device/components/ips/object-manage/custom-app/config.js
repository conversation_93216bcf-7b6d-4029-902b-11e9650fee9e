import { name } from "dayjs/locale/zh-cn"

/** @type {*} 获取列表配置 */
export function getColumns() {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({index}){
        return index+1
      }
    },
    {
      title: '应用名称',
      dataIndex: 'name',
      ellipsis: true,
      key: 'name',
      width: 100,
    },
    {
      title: '协议类型',
      dataIndex: 'type',
      ellipsis: true,
      key: 'type',
      width: 110,
    },
    {
      title: '特征定义',
      dataIndex: 'origin_match',
      ellipsis: true,
      key: 'origin_match',
      width: 150,
      customRender: ({ record }) => {
        return record.origin_match ? atob(record.origin_match) : '-'
      }
    },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      ellipsis: true,
      key: 'create_time',
      width: 155,
    },
    {
      title: '描述',
      dataIndex: 'desc',
      ellipsis: true,
      key: 'desc',
      width: 100,
    },
    {
      title: '引用次数',
      dataIndex: 'cite_count',
      ellipsis: true,
      key: 'cite_count',
      width: 100,
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 130,
      fixed: 'right'
    }
  ]
}

/** @type {*} 表单配置 */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        type: {
          type: 'string',
          title: '协议类型',
          'x-validator': [
            { required: true, message: '请选择协议类型' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择',
            showSearch: true,
            filterOption: (input, option = {}) => {
              if (!input) return true;
              return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
            },
          },
          enum: [
            { label: 'TCP', value: 'TCP' },
            { label: 'UDP', value: 'UDP' },
            { label: 'HTTP', value: 'HTTP' },
            { label: 'DNS', value: 'DNS' },
          ]
        },
        name: {
          type: 'string',
          title: '规则名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入规则名称'
          },
          'x-validator': [
            { required: true, message: '请输入规则名称' },
            {
              max: 32,
            },
          ],
        },
        origin_match: {
          type: 'string',
          'x-validator': [
            { required: true, message: '请输入特征定义' },
          ],
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '示例：tcp.port == 8000 && http.cookie *^ "shell"'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '特征定义'
          },
        },
        origin_match_tip: {
          type: 'string',
          title: ' ',
          // default: ,
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            colon: false
          },
          'x-component': () => {
            return <a-alert
              message={`注意：不能填写tcp.srcport==1000 或者 http.url^sarray("abc", "def")等规则，规则过于简单，需结合字符串类型规则使用，如 tcp.srcport==1000&&tcp.clip0^"abc"`}
              type="warning"
              show-icon
            />
          },
        },
        desc: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            {
              max: 200,
            },
          ],
        }
      }
    }
  }
}
