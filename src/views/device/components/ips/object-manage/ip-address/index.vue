<template>
  <div>
    <DynamicSearch
      :config="SEARCH_CONFIG"
      @search="handleSearch"
      @reset="handleSearch"
    />
    <a-space align="start" class="ips-drawer-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add', CATEGORY_ENUM.IP)">
        新增地址
      </a-button>
      <a-button type="primary" @click="handleOpenDialog('add', CATEGORY_ENUM.IP_POOL)">
        新增地址组
      </a-button>
      <a-button type="primary" @click="handleExport">
        导出
      </a-button>
    </a-space>
    <a-table
      :scroll="{y: 'calc(100vh - 420px)'}"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button type="link" @click="handleOpenDialog('view', record.category, record)">查看</a-button>
          <a-button
            type="link"
            :disabled="TYPE_ENUM.predefine === record.type"
            @click="handleOpenDialog('edit', record.category, record)"
          >
            编辑
          </a-button>
          <a-button
            type="link"
            :disabled="TYPE_ENUM.predefine === record.type"
            danger
            @click="handleDelete(record)"
          >
            删除
          </a-button>
        </template>
      </template>
    </a-table>
    <EditAddressDialog
      v-model:visible="addressDialogConfig.visible"
      v-bind="addressDialogConfig"
      @on-success="handleSave" />
    <EditAddressGroupDialog
      v-model:visible="groupDialogConfig.visible"
      v-bind="groupDialogConfig"
      @on-success="handleSave" />
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, inject, computed, onMounted, getCurrentInstance, createVNode } from 'vue'
import { usePagination } from 'vue-request'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

import { getIpAddressList, addIpAddress, updateIpAddress, deleteIpAddress } from '@/request/api-device-ips'
import { columns as COLUMNS, SEARCH_CONFIG, CATEGORY_ENUM, TYPE_ENUM } from './config';
import { downloadFile } from '@/utils/util'

import DynamicSearch from "@/components/dynamic-search/index.vue";
import EditAddressDialog from './edit-address-dialog.vue'
import EditAddressGroupDialog from './edit-address-group-dialog.vue'

export default defineComponent({
  components: {
    DynamicSearch,
    EditAddressDialog,
    EditAddressGroupDialog
  },
  setup () {
    const { proxy } = getCurrentInstance()
    const { deviceSafetyId } = inject('rowData')

    const state = reactive({
      searchParams: {},
      addressDialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      },
      groupDialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })

    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
    } = usePagination(getIpAddressList, {
      manual: true,
      defaultParams: {
        deviceSafetyId,
        filter_data: state.searchParams
      },
      formatResult: ({ data = {} }) => ({ items: data.data ?? [], total: data.total ?? 0 }),
      pagination: {
        currentKey: 'page_num',
        pageSizeKey: 'page_size'
      }
    })

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: total => `共 ${total} 条`
    }))

    const dataSource = computed(() => {
      return data.value?.items || []
    })

    /**
     * 处理表格分页变化
     * @param {Object} pag 分页参数
     */
    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        deviceSafetyId,
        page_size: pag.pageSize,
        page_num: pag?.current,
        filter_data: state.searchParams
      })
    }

    /**
     * 处理搜索
     * @param {Object} params 搜索参数
     */
    const handleSearch = (params = {}) => {
      state.searchParams = params;
      refreshTableData();
    }

    /**
     * 刷新表格数据
     * @param {boolean} isReload 是否重新加载第一页
     */
    const refreshTableData = (isReload = true) => {
      run({
        deviceSafetyId,
        page_num: isReload ? 1 : pagination.value.current,
        page_size: pagination.value?.pageSize,
        filter_data: state.searchParams
      })
    }

    /**
     * 保存IP地址数据
     * @param {Object} options 保存选项
     */
    const handleSave = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      try {
        if (isAdd) {
          await addIpAddress({...data, deviceSafetyId})
        } else {
          await updateIpAddress({...data, deviceSafetyId})
        }
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        refreshTableData()
      } catch (err) {
        callback(true, err)
      }
    }

    /**
     * 删除IP地址
     * @param {Object} row 行数据
     */
    const handleDelete = async (row = {}) => {
      proxy.$confirm({
        title: '删除',
        content: `确认删除 ${row.name} 数据吗?`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteIpAddress({ id: row.id, deviceSafetyId })
            proxy.$message.success('删除成功！')
            refreshTableData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
    }

    /**
     * 打开编辑弹窗
     * @param {string} mode 操作模式
     * @param {string} category 地址类型
     * @param {Object} row 行数据
     */
    const handleOpenDialog = (mode = 'add', category = CATEGORY_ENUM.IP, row = {}) => {
      const isGroup = category === CATEGORY_ENUM.IP_POOL
      if (isGroup) {
        state.groupDialogConfig.visible = true
        state.groupDialogConfig.mode = mode
        state.groupDialogConfig.defaultData = row
      } else {
        state.addressDialogConfig.visible = true
        state.addressDialogConfig.mode = mode
        state.addressDialogConfig.defaultData = row
      }
    }

    /**
     * 导出IP地址数据
     */
    const handleExport = async () => {
      const params = encodeURI(JSON.stringify({...state.searchParams, deviceSafetyId}));
      downloadFile(`/device-atomic/IPSQiming/addressExport?params=${params}`)
    }

    const columns = computed(() => {
      return COLUMNS.map(column => {
        if (column.customRender) {
          return {
            ...column,
            customRender: (args) => column.customRender(args, pagination.value)
          };
        }
        return column;
      });
    });

    return {
      CATEGORY_ENUM,
      SEARCH_CONFIG,
      TYPE_ENUM,
      ...toRefs(state),
      pagination,
      loading,
      dataSource,
      handleSearch,
      refreshTableData,
      handleOpenDialog,
      handleTableChange,
      handleSave,
      handleDelete,
      handleExport,
      columns
    }
  }
})
</script>
