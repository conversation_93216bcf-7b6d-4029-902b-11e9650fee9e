
/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} ip或ip地址池枚举 */
export const CATEGORY_ENUM = {
  IP: 'NODE',
  IP_POOL: 'GROUP'
}

/** @type {*} 地址类型Label枚举 */
export const CATEGORY_LABEL_MAP = {
  NODE: '地址',
  GROUP: '地址组'
}

/** @type {*} 对象录入类型枚举 */
export const TYPE_ENUM = {
  customize: 'customize',
  predefine: 'predefine'
}

/** @type {*} 对象录入类型值映射 */
export const TYPE_LABEL_ENUM = {
  customize: '自定义',
  predefine: '预定义'
}

/** @type {*} 获取列表配置 */
export const columns = [
  {
    title: "序号",
    dataIndex: "num",
    width: 100,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '名称',
    dataIndex: 'name',
    ellipsis: true,
    key: 'name'
  },
  {
    title: '地址类型',
    dataIndex: 'category',
    ellipsis: true,
    key: 'category',
    customRender: ({ record }) => {
      const category = CATEGORY_LABEL_MAP[record.category] ?? '-';
      return <span>{category}</span>
    }
  },
  {
    title: '类别',
    dataIndex: 'type',
    ellipsis: true,
    key: 'type',
    customRender: ({ record }) => {
      const type = TYPE_LABEL_ENUM[record.type] ?? '-';
      return <span>{type}</span>
    }
  },
  {
    title: '引用次数',
    dataIndex: 'cite_count',
    ellipsis: true,
    key: 'cite_count'
  },
  {
    title: '描述',
    dataIndex: 'description',
    ellipsis: true,
    key: 'description'
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
    width: 220
  }
]

/** @type {*} 新增地址表单schema */
export const ADDRESS_SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 80
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          }
        },
        members: {
          type: 'string',
          title: '地址池',
          'x-decorator': 'FormItem',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            rows: 5,
            placeholder: `不同IP请换行，支持换行及示例如下
************
************/24
************-************`
          },
          'x-validator': [
            { required: true, message: '请输入地址池' },
            { multipleIp: true }
          ]
        },
        description: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            { max: 200 }
          ]
        },
      }
    }
  }
}


/** @type {*} 新增地址组表单schema */
export const ADDRESS_GROUP_SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 80
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          }
        },
        description: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          },
          'x-validator': [
            { max: 200 }
          ]
        },
        members: {
          type: 'array',
          'x-validator': [
            { required: true, message: '请选择成员' },
          ],
          title: '成员',
          enum: [],
          'x-decorator': 'FormItem',
          'x-component': 'Transfer',
          'x-component-props': {
            titles: [' 可选地址', ' 已选地址'],
            render: (item) => item.title,
          },
        },
      }
    }
  }
}

