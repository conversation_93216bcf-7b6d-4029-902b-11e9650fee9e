import CellExpand from '@/components/cell-expand'

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'policy_name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/** @type {*} 动作枚举 */
export const ACTION_ENUM = {
  SAFETY_PROTECTION: 'SAFETY_PROTECTION',
  RELEASE: 'RELEASE',
  BLOCK: 'BLOCK'
}

/** @type {*} 动作枚举 */
const ACTION_LABEL_MAP = {
  [ACTION_ENUM.SAFETY_PROTECTION]: '安全防护',
  [ACTION_ENUM.RELEASE]: '放行',
  [ACTION_ENUM.BLOCK]: '阻断'
}

/** @type {*} 动作提示枚举 */
export const ACTION_TIP_ENUM = {
  [ACTION_ENUM.SAFETY_PROTECTION]: '动作为"安全防护"，经安全检测后确认为无害的流量被放行，有害流量根据命中防护模板中的规则动作进行阻断、告警等响应，防护模板在"下一步"中设置。',
  [ACTION_ENUM.RELEASE]: '动作为"放行"，符合当前策略的所有流量，无论是否具有威胁都将被转发。',
  [ACTION_ENUM.BLOCK]: '动作为"阻断"，符合当前策略的所有流量，都将禁止通过。'
}

/** @type {*} 获取安全策略列表配置 */
export const getColumns = ({handleChangeStatus, pagination}) => {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: '策略名称',
      dataIndex: 'policy_name',
      key: 'policy_name',
      width: 100,
      fixed: 'left'
    },
    {
      title: '动作',
      dataIndex: 'action',
      key: 'action',
      width: 100,
      customRender: ({ record }) => {
        return <span>{ACTION_LABEL_MAP[record.action]}</span>
      }
    },
    {
      title: '源安全区',
      dataIndex: 'src_safe_zone',
      key: 'src_safe_zone',
      width: 100,
      customRender: ({ record }) => {
        return <span>{record?.src_safe_zone?.name ?? ''}</span>
      }
    },
    {
      title: '源地址',
      dataIndex: 'src_address_object',
      key: 'src_address_object',
      width: 160,
      customRender: ({ record }) => {
        if (!record.src_address_object?.length) return '-'
        const data = record?.src_address_object?.map(item => item.name) ?? []
        return <CellExpand title="源地址" data={data} />
      }
    },
    {
      title: '目的安全区',
      dataIndex: 'dst_safe_zone',
      key: 'dst_safe_zone',
      width: 120,
      customRender: ({ record }) => {
        return <span>{record?.dst_safe_zone?.name ?? ''}</span>
      }
    },
    {
      title: '目的地址',
      dataIndex: 'dst_address_object',
      key: 'dst_address_object',
      width: 160,
      customRender: ({ record }) => {
        if (!record.dst_address_object?.length) return '-'
        const data = record?.dst_address_object?.map(item => item.name) ?? []
        return <CellExpand title="目的地址" data={data} />
      }
    },
    {
      title: '时间计划',
      dataIndex: 'time_table',
      key: 'time_table',
      width: 100,
      customRender: ({ record }) => {
        return <span>{record?.time_table?.name ?? ''}</span>
      }
    },
    {
      title: '服务',
      dataIndex: 'service_object',
      key: 'service_object',
      width: 140,
      customRender: ({ record }) => {
        if (!record.service_object?.length) return '-'
        const data = record?.service_object?.map(item => item.name) ?? []
        return <CellExpand title="服务" data={data} />
      }
    },
    {
      title: '安全模板',
      dataIndex: 'config_file_type',
      key: 'config_file_type',
      width: 100
    },
    {
      title: '命中次数',
      dataIndex: 'triggers_number',
      key: 'triggers_number',
      width: 100
    },
    {
      title: '阻断次数',
      dataIndex: 'interceptions_number',
      key: 'interceptions_number',
      width: 100
    },
    {
      title: '启用',
      dataIndex: 'status',
      key: 'status',
      fixed: 'right',
      width: 80,
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const status = record.status === 'ON' ? 'OFF' : 'ON';
            await handleChangeStatus({...record, status});
            record.status = status;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.status}
          loading={!!record.statusLoading}
          checkedValue="ON"
          unCheckedValue="OFF"
          onClick={handleClick}
        />
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 220
    }
  ]
}

/** @type {*} 表单配置 */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        step: {
          type: 'void',
          'x-component': 'FormStep',
          'x-component-props': {
            formStep: '{{formStep}}'
          },
          properties: {
            baseConfig: {
              type: 'void',
              'x-component': 'FormStep.StepPane',
              'x-component-props': {
                title: '基础配置'
              },
              properties: {
                policy_name: {
                  type: 'string',
                  title: '策略名称',
                  'x-validator': [
                    { required: true, message: '请输入策略名称' },
                  ],
                  'x-decorator': 'FormItem',
                  'x-component': 'Input',
                  'x-component-props': {
                    placeholder: '请输入策略名称'
                  }
                },
                src_safe_zone: {
                  type: 'string',
                  title: '源安全区',
                  'x-validator': [
                    { required: true, message: '请选择源安全区' },
                  ],
                  'x-decorator': 'FormItem',
                  'x-component': 'Select',
                  'x-component-props': {
                    placeholder: '请选择源安全区',
                    showSearch: true,
                    filterOption: (input, option = {}) => {
                      if (!input) return true;
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    },
                  },
                  'x-reactions': '{{getSafetyZoneDataSource}}'
                },
                src_address_object: {
                  type: 'array',
                  title: '源地址',
                  'x-validator': [
                    { required: true, message: '请选择源地址' },
                  ],
                  'x-decorator': 'FormItem',
                  'x-component': 'Select',
                  'x-component-props': {
                    mode: 'multiple',
                    placeholder: '请选择源地址',
                    showSearch: true,
                    filterOption: (input, option = {}) => {
                      if (!input) return true;
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    },
                  },
                  'x-reactions': '{{getAddressDataSource()}}'
                },
                dst_safe_zone: {
                  type: 'string',
                  title: '目的安全区',
                  'x-disabled': true,
                  'x-decorator': 'FormItem',
                  'x-component': 'Select',
                  'x-component-props': {
                    placeholder: '请选择目的安全区',
                    showSearch: true,
                    filterOption: (input, option = {}) => {
                      if (!input) return true;
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    },
                  },
                  'x-reactions': [
                    '{{getSafetyZoneDataSource}}',
                    {
                      dependencies: ['.src_safe_zone'],
                      fulfill: {
                        state: {
                          value: `{{$deps[0]}}`
                        }
                      }
                    }
                  ]
                },
                dst_address_object: {
                  type: 'array',
                  title: '目的地址',
                  'x-validator': [
                    { required: true, message: '请选择目的地址' },
                  ],
                  'x-decorator': 'FormItem',
                  'x-component': 'Select',
                  'x-component-props': {
                    mode: 'multiple',
                    placeholder: '请选择目的地址',
                    showSearch: true,
                    filterOption: (input, option = {}) => {
                      if (!input) return true;
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    },
                  },
                  'x-reactions': '{{getAddressDataSource()}}'
                },
                service_object: {
                  type: 'array',
                  title: '服务',
                  'x-validator': [
                    { required: true, message: '请选择服务' },
                  ],
                  'x-decorator': 'FormItem',
                  'x-component': 'Select',
                  'x-component-props': {
                    mode: 'multiple',
                    placeholder: '请选择服务',
                    showSearch: true,
                    filterOption: (input, option = {}) => {
                      if (!input) return true;
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    },
                  },
                  'x-reactions': '{{getServiceDataSource}}'
                },
                time_table: {
                  type: 'string',
                  title: '时间计划',
                  'x-validator': [
                    { required: true, message: '请选择时间计划' },
                  ],
                  'x-decorator': 'FormItem',
                  'x-component': 'Select',
                  'x-component-props': {
                    placeholder: '请选择时间计划',
                    showSearch: true,
                    filterOption: (input, option = {}) => {
                      if (!input) return true;
                      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                    },
                  },
                  'x-reactions': '{{getTimePlanDataSource}}'
                },
                action: {
                  type: 'string',
                  title: '动作',
                  default: 'SAFETY_PROTECTION',
                  'x-decorator': 'FormItem',
                  'x-component': 'Radio.Group',
                  'x-component-props': {
                    isGroup: true,
                    optionType: "button",
                    options: [
                      { label: '安全防护', value: ACTION_ENUM.SAFETY_PROTECTION },
                      { label: '放行', value: ACTION_ENUM.RELEASE },
                      { label: '阻断', value: ACTION_ENUM.BLOCK }
                    ]
                  }
                },
                action_tip: {
                  type: 'string',
                  title: ' ',
                  default: ACTION_TIP_ENUM.SAFETY_PROTECTION,
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    colon: false
                  },
                  'x-component': (props) => {
                    return <a-alert
                      message={props.value}
                      type="warning"
                      show-icon
                    />
                  },
                },
                status: {
                  type: 'boolean',
                  title: '启用',
                  default: 'ON',
                  'x-decorator': 'FormItem',
                  'x-component': 'Switch',
                  'x-component-props': {
                    checkedValue: 'ON',
                    unCheckedValue: 'OFF'
                  },
                },
                log_status: {
                  type: 'boolean',
                  title: '记录日志',
                  default: 'ON',
                  'x-decorator': 'FormItem',
                  'x-component': 'Switch',
                  'x-component-props': {
                    checkedValue: 'ON',
                    unCheckedValue: 'OFF'
                  },
                  'x-reactions': [
                    {
                      dependencies: ['.action'],
                      fulfill: {
                        state: {
                          display: `{{$deps[0] === 'BLOCK' ? 'visible' : 'none'}}`
                        }
                      }
                    }
                  ]
                },
                desc: {
                  type: 'string',
                  'x-component': 'Input.TextArea',
                  'x-component-props': {
                    placeholder: '请输入备注'
                  },
                  'x-decorator': 'FormItem',
                  'x-decorator-props': {
                    label: '备注'
                  }
                }
              }
            },
            securityConfig: {
              type: 'void',
              'x-component': 'FormStep.StepPane',
              'x-component-props': {
                title: '安全防护'
              },
              properties: {
                space: {
                  type: 'void',
                  'x-component': 'VoidField',
                  'x-reactions': [
                    {
                      dependencies: ['.action'],
                      fulfill: {
                        state: {
                          display: `{{$deps[0] === 'SAFETY_PROTECTION' ? 'visible' : 'none'}}`
                        }
                      }
                    }
                  ],
                  properties: {
                    intrusion_prevention: {
                      type: 'string',
                      title: '入侵防护',
                      'x-decorator': 'FormItem',
                      'x-component': 'Select',
                      'x-component-props': {
                        placeholder: '请选择入侵防护',
                        showSearch: true,
                        filterOption: (input, option = {}) => {
                          if (!input) return true;
                          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        },
                      },
                      'x-reactions': '{{getSafetyTemplateDataSource("IPS")}}'
                    },
                    malicious_software: {
                      type: 'string',
                      title: '恶意文件',
                      'x-decorator': 'FormItem',
                      'x-component': 'Select',
                      'x-component-props': {
                        placeholder: '请选择恶意文件',
                        showSearch: true,
                        filterOption: (input, option = {}) => {
                          if (!input) return true;
                          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        },
                      },
                      'x-reactions': '{{getSafetyTemplateDataSource("Malice")}}'
                    },
                    web_security: {
                      type: 'string',
                      title: 'WEB安全',
                      'x-decorator': 'FormItem',
                      'x-component': 'Select',
                      'x-component-props': {
                        placeholder: '请选择WEB安全',
                        showSearch: true,
                        filterOption: (input, option = {}) => {
                          if (!input) return true;
                          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        },
                      },
                      'x-reactions': '{{getSafetyTemplateDataSource("Web")}}'
                    },
                    threat: {
                      type: 'string',
                      title: '威胁情报',
                      'x-decorator': 'FormItem',
                      'x-component': 'Select',
                      'x-component-props': {
                        placeholder: '请选择威胁情报',
                        showSearch: true,
                        filterOption: (input, option = {}) => {
                          if (!input) return true;
                          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        },
                      },
                      'x-reactions': '{{getSafetyTemplateDataSource("Threat")}}'
                    },
                    web_filtering: {
                      type: 'string',
                      title: 'WEB过滤',
                      'x-decorator': 'FormItem',
                      'x-component': 'Select',
                      'x-component-props': {
                        placeholder: '请选择WEB过滤',
                        showSearch: true,
                        filterOption: (input, option = {}) => {
                          if (!input) return true;
                          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        },
                      },
                      'x-reactions': '{{getSafetyTemplateDataSource("FilterWeb")}}'
                    },
                    mail_filtering: {
                      type: 'string',
                      title: '邮件过滤',
                      'x-decorator': 'FormItem',
                      'x-component': 'Select',
                      'x-component-props': {
                        placeholder: '请选择邮件过滤',
                        showSearch: true,
                        filterOption: (input, option = {}) => {
                          if (!input) return true;
                          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        },
                      },
                      'x-reactions': '{{getSafetyTemplateDataSource("FilterMail")}}'
                    },
                    app_manage: {
                      type: 'string',
                      title: '应用管理',
                      'x-decorator': 'FormItem',
                      'x-component': 'Select',
                      'x-component-props': {
                        placeholder: '请选择应用管理',
                        showSearch: true,
                        filterOption: (input, option = {}) => {
                          if (!input) return true;
                          return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
                        },
                      },
                      'x-reactions': '{{getSafetyTemplateDataSource("Protect")}}'
                    },
                  }
                }
              }
            },
          }
        }
      }
    }
  }
}
