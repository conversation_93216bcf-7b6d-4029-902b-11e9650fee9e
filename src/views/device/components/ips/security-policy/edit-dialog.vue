<template>
  <FormProvider :form="form">
    <a-modal
      v-model:visible="dialogVisible"
      :title="title"
      width="800px"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleClose"
    >
      <a-spin :spinning="loading">
        <SchemaField :schema="SCHEMA" :scope="{...formScope, formStep}"></SchemaField>
      </a-spin>
      <template #footer>
        <FormConsumer>
          <template #default="{ form }">
            <a-space>
              <a-button
                v-if="form.values.action === ACTION_ENUM.SAFETY_PROTECTION"
                type="primary"
                ghost
                :disabled="!formStep.allowBack"
                @click="() => formStep.back()"
              >
                上一步
              </a-button>
              <a-button
                v-if="form.values.action === ACTION_ENUM.SAFETY_PROTECTION"
                type="primary"
                ghost
                :disabled="!formStep.allowNext"
                @click="() => handleNextStep()"
              >
                下一步
              </a-button>
              <a-button
                v-if="!isView"
                :disabled="form.values.action === ACTION_ENUM.SAFETY_PROTECTION && formStep.allowNext"
                type="primary"
                :loading="loading"
                @click="handleSave">保存</a-button>
              <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">
                {{isView ? '关闭' : '取消'}}
              </a-button>
            </a-space>
          </template>
        </FormConsumer>
      </template>
    </a-modal>
  </FormProvider>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, onBeforeUnmount, nextTick, inject, shallowRef } from 'vue'
import { createForm, onFieldValueChange } from '@formily/core'
import { FormStep } from '@formily/antdv-x3'
import { cloneDeep, partition, keyBy, pick, omit, values } from 'lodash'

import { getIpsPolicySafetyZone, getIpAddressAllList, getTimePlanAllList, getServiceObjectAllList, getIpsPolicySafetyTemplate } from '@/request/api-device-ips.js'
import { SCHEMA, ACTION_TIP_ENUM, ACTION_ENUM } from './config';

export default defineComponent({
  name: 'EditIpsPolicy',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { deviceSafetyId } = inject('rowData')
    const { visible, mode, defaultData } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}策略`
    })

    const dataSourceMap = shallowRef({
      addressMap: {},
      serviceMap: {},
      timePlanMap: {},
      safetyZoneMap: {}
    })

    const state = reactive({
      loading: false
    })

    const form = createForm({
      effects() {
        onFieldValueChange('action', (field) => {
          const actionTipField = field.query('.action_tip').take()
          if (actionTipField) actionTipField.value = ACTION_TIP_ENUM[field.value] ?? ''
        })
      },
    })

    const formStep = FormStep.createFormStep()

    const handleNextStep = () => {
      formStep.next()
    }

    const formatAddressDataSource = (data = []) => {
      if (!data?.length) return []
      if (isView.value) return data.map(({name, id}) => ({label: name, value: id}))
      const [groups = [], nodes = []] = partition(data, (item) => item.category === 'GROUP');
      return [
        { label: '地址', options: nodes.map(({name, id}) => ({label: name, value: id})) },
        { label: '地址组', options: groups.map(({name, id}) => ({label: name, value: id})) }
      ]
    }

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      form.setPattern(patternMap[mode.value])
      // 源地址和目的地址数据源由于需要分组，分组后readPretty模式回显有问题，所以数据源区分场景
      const addressDataSource = formatAddressDataSource(values(dataSourceMap.value.addressMap));
      form.query('.src_address_object').take()?.setDataSource(addressDataSource)
      form.query('.dst_address_object').take()?.setDataSource(addressDataSource)
      if (mode.value === 'add') return
      const formData = cloneDeep(defaultData.value)
      formData.service_object = formData?.service_object?.map(item => item.id) ?? []
      formData.dst_address_object = formData?.dst_address_object?.map(item => item.id) ?? []
      formData.src_address_object = formData?.src_address_object?.map(item => item.id) ?? []
      formData.dst_safe_zone = formData?.dst_safe_zone.id ?? null
      formData.src_safe_zone = formData?.src_safe_zone.id ?? null
      formData.time_table = formData?.time_table.id ?? null
      if (formData.action === ACTION_ENUM.SAFETY_PROTECTION) {
        formData.intrusion_prevention = formData.intrusion_prevention?.id ?? null
        formData.malicious_software = formData.malicious_software?.id ?? null
        formData.web_security = formData.web_security?.id ?? null
        formData.web_filtering = formData.web_filtering?.id ?? null
        formData.mail_filtering = formData.mail_filtering?.id ?? null
        formData.app_manage = formData.app_manage?.id ?? null
      }
      form.setValues(formData)
    }

    // 获取IP地址、地址组数据源
    let addressReqPromise = null
    const getAddressDataSource = () => {
      return async (field) => {
        let data = []
        try {
          if (!addressReqPromise) addressReqPromise = getIpAddressAllList({deviceSafetyId})
          const res = await addressReqPromise;
          data = res.data ?? []
          dataSourceMap.value.addressMap = keyBy(data, 'id')
        } finally {
          field.setDataSource(formatAddressDataSource(data))
        }
      }
    }

    // 获取安全区数据源
    const getSafetyZoneDataSource = async (field) => {
      let data = []
      try {
        const { data: safetyZones = [] } = await getIpsPolicySafetyZone({deviceSafetyId})
        dataSourceMap.value.safetyZoneMap = keyBy(safetyZones, 'id')
        data = safetyZones?.map(({name, id}) => ({label: name, value: id})) ?? []
      } finally {
        field.setDataSource(data)
      }
    }

    // 获取服务数据源
    const getServiceDataSource = async (field) => {
      let data = []
      try {
        const { data: services = [] } = await getServiceObjectAllList({deviceSafetyId})
        dataSourceMap.value.serviceMap = keyBy(services, 'id')
        data = services?.map(({name, id}) => ({label: name, value: id})) ?? []
      } finally {
        field.setDataSource(data)
      }
    }

    let SafetyTemplateReqPromise = null
    // 获取安全防护数据源
    const getSafetyTemplateDataSource = (fieldKey) => {
      return async (field) => {
        let data = {}
        try {
          if (!SafetyTemplateReqPromise) SafetyTemplateReqPromise = getIpsPolicySafetyTemplate({deviceSafetyId})
          const res = await SafetyTemplateReqPromise;
          data = res?.data?.[0] ?? {}
        } finally {
          dataSourceMap.value[fieldKey] = keyBy(data[fieldKey], 'id');
          const dataSource = data[fieldKey]?.map(({name, id}) => ({label: name, value: id})) ?? []
          field.setDataSource(dataSource)
        }
      }
    }

    // 获取时间计划数据源
    const getTimePlanDataSource = async (field) => {
      let data = []
      try {
        const { data: timePlans = [] } = await getTimePlanAllList({deviceSafetyId})
        dataSourceMap.value.timePlanMap = keyBy(timePlans, 'id')
        data = timePlans?.map(({name, id}) => ({label: name, value: id})) ?? []
      } finally {
        field.setDataSource(data)
      }
    }

    /**
     * 关闭弹窗处理
     * @param {boolean} hasError 是否有错误
     */
    const handleClose = async (hasError = false) => {
      state.loading = false
      if (hasError === true) return
      for (let i = 2; i >= 0; i--) { formStep.back() }
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    const getObjByKeys = (obj = {}, key = '', fieldKeys = []) => {
      if (!key || !obj[key] || !fieldKeys.length) return {}
      return pick(obj[key], fieldKeys);
    }

    /**
     * 保存表单数据
     */
    const handleSave = async () => {
      await form.validate()
      const params = cloneDeep(form.values)
      const service_object = params.service_object?.map(item => {
        return getObjByKeys(dataSourceMap.value.serviceMap, item, ['id', 'name', 'category'])
      }) ?? []
      const dst_address_object = params.dst_address_object?.map(item => {
        return getObjByKeys(dataSourceMap.value.addressMap, item, ['id', 'name'])
      }) ?? []
      const src_address_object = params.src_address_object?.map(item => {
        return getObjByKeys(dataSourceMap.value.addressMap, item, ['id', 'name'])
      }) ?? []
      const dst_safe_zone = getObjByKeys(dataSourceMap.value.safetyZoneMap, params.dst_safe_zone, ['id', 'name', 'category'])
      const src_safe_zone = getObjByKeys(dataSourceMap.value.safetyZoneMap, params.src_safe_zone, ['id', 'name', 'category'])
      const time_table = getObjByKeys(dataSourceMap.value.timePlanMap, params.time_table, ['id', 'name'])
      if (params.action === ACTION_ENUM.SAFETY_PROTECTION) {
        const isValid = ['intrusion_prevention', 'malicious_software', 'web_security', 'web_filtering', 'mail_filtering', 'app_manage'].some(item => !!params[item])
        if (!isValid) return proxy.$message.warning('请选择一个防护模板')
        params.intrusion_prevention = getObjByKeys(dataSourceMap.value.IPS, params.intrusion_prevention, ['id', 'name'])
        params.malicious_software = getObjByKeys(dataSourceMap.value.Malice, params.malicious_software, ['id', 'name'])
        params.web_security = getObjByKeys(dataSourceMap.value.Web, params.web_security, ['id', 'name', 'group_no'])
        params.web_filtering = getObjByKeys(dataSourceMap.value.FilterWeb, params.web_filtering, ['id', 'name'])
        params.mail_filtering = getObjByKeys(dataSourceMap.value.FilterMail, params.mail_filtering, ['id', 'name'])
        params.app_manage = getObjByKeys(dataSourceMap.value.Protect, params.app_manage, ['id', 'name', 'group_no'])
      } else {
        params.intrusion_prevention = null
        params.malicious_software = null
        params.web_security = null
        params.web_filtering = null
        params.mail_filtering = null
        params.app_manage = null
      }
      state.loading = true
      ctx.emit('on-success', {
        mode: mode.value,
        data: {
          ...params,
          service_object,
          dst_address_object,
          src_address_object,
          dst_safe_zone,
          src_safe_zone,
          time_table,
        },
        callback: handleClose
      })
    }

    onBeforeUnmount(() => {
      SafetyTemplateReqPromise = null
      addressReqPromise = null
    })

    return {
      ACTION_ENUM,
      title,
      isView,
      form,
      formStep,
      formScope: {
        getAddressDataSource,
        getSafetyZoneDataSource,
        getServiceDataSource,
        getTimePlanDataSource,
        getSafetyTemplateDataSource
      },
      SCHEMA,
      ...toRefs(state),
      dialogVisible,
      handleNextStep,
      handleClose,
      handleSave
    }
  }
})
</script>
