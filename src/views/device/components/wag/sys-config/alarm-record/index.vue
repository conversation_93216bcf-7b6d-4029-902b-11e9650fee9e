<template>
        <div>
                <a-form
                        ref="searchFormRef"
                        @submit="handleSearch"
                >
                        <a-row
                                :gutter="[16, 16]"
                                :style="{
                                        marginTop: '25px'
                                }"
                        >
                                <a-col :span="6">
                                        <a-form-item
                                                label="告警类型"
                                                :label-col="{
                                                        span: 6,
                                                        style: {
                                                                width: '150px',
                                                                textAlign: 'right'
                                                        }
                                                }"
                                                :wrapper-col="{
                                                        span: 18
                                                }"
                                        >
                                                <a-select
                                                        placeholder="请选择告警类型"
                                                        v-model:value="searchFormData.alarmType"
                                                >
                                                        <a-select-option value="always">全部</a-select-option>
                                                        <a-select-option value="tamper">Tamper</a-select-option>
                                                        <a-select-option value="sqlInjection">SQLInjection</a-select-option>
                                                        <a-select-option value="linkTheft">LinkTheft</a-select-option>
                                                        <a-select-option value="system">System</a-select-option>
                                                </a-select>
                                        </a-form-item>
                                </a-col>
                                <a-col :span="6">
                                        <a-form-item
                                                label="通知方式"
                                                :label-col="{
                                                        span: 6,
                                                        style: {
                                                                width: '150px',
                                                                textAlign: 'right'
                                                        }
                                                }"
                                                :wrapper-col="{
                                                        span: 18
                                                }"
                                        >
                                                <a-select
                                                        placeholder="请选择通知方式"
                                                        v-model:value="searchFormData.alarmWay"
                                                >
                                                        <a-select-option value="always">全部</a-select-option>
                                                        <a-select-option value="mail">Mail</a-select-option>
                                                        <a-select-option value="sms">SMS</a-select-option>
                                                </a-select>
                                        </a-form-item>
                                </a-col>
                                <a-col :span="6">
                                        <a-form-item
                                                label="通知地址"
                                                :label-col="{
                                                        span: 6,
                                                        style: {
                                                                width: '150px',
                                                                textAlign: 'right'
                                                        }
                                                }"
                                                :wrapper-col="{
                                                        span: 18
                                                }"
                                        >
                                                <a-input
                                                        placeholder="请输入通知地址"
                                                        v-model:value="searchFormData.alarmAddr"
                                                />
                                        </a-form-item>
                                </a-col>
                                <a-col
                                        :span="6"
                                        :style = "{
                                                display: 'flex',
                                                justifyContent: 'flex-end'
                                        }"
                                > 
                                        <a-form-item>
                                                <a-button
                                                        type="primary"
                                                        html-type="submit"
                                                >
                                                        搜索
                                                </a-button>
                                        </a-form-item>
                                </a-col>
                        </a-row>
                        <a-row
                                :gutter="[16, 16]"
                        >
                                <a-col :span="6">
                                        <a-form-item
                                                label="通知内容"
                                                :label-col="{ span: 6, style: { width: '150px', textAlign: 'right' } }"
                                                :wrapper-col="{ span: 18 }"
                                        >
                                                <a-input
                                                        placeholder="请输入通知内容"
                                                        v-model:value="searchFormData.alarmContent"
                                                />
                                        </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                        <a-form-item
                                                label="篡改时间"
                                                :label-col="{ span: 3, style: { width: '150px', textAlign: 'right' } }"
                                                :wrapper-col="{ span: 21 }"
                                        >
                                                <a-range-picker
                                                        v-model:value="searchFormData.tamperTime"
                                                        :style="{ width: '100%' }"
                                                />
                                        </a-form-item>
                                </a-col>
                        </a-row>
                </a-form>
                <div class="list-operator">
                        <a-space>
                                <a-button type="primary" @click="handleExport">导出</a-button>
                        </a-space>
                </div>
                <a-table
                        :scroll="{y: 'calc(100vh - 330px)'}"
                        :data-source="dataSource"
                        :columns="columns"
                        :loading="loading"
                        :pagination="pagination"
                        :row-selection="rowSelection"
                        :row-key="record => record.id"
                        @change="handleTableChange"
                >
                        <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'operation'">
                                        <a-button type="link" @click="handleOpenDialog('view', record)">查看</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" @click="handleOpenDialog('edit', record)">编辑</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" danger @click="handleDelete(record)">删除</a-button>
                                </template>
                        </template>
                </a-table>
        </div>
</template>

<script>
import DynamicSearch from '@/components/dynamic-search/index.vue';
import {
        deleteWagAlarmRecord,
        getWagAlarmRecordList
} from '@/request/api-device-wag.js';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { computed, createVNode, defineComponent, getCurrentInstance, inject, onMounted, reactive, ref, toRefs } from 'vue';
import { usePagination } from 'vue-request';
import { columns } from './config';

export default defineComponent({
        name: 'AlarmRecord',
        components: {
                DynamicSearch,
        },
        props: {
        },
        setup (props, ctx) {

                const dynamicSearchRef = ref(null);
                let searchParams = {};

                const searchFormRef = ref()

                const searchFormData = reactive({
                        alarmType: 'always',
                        alarmWay: 'always',
                        alarmAddr: '',
                        alarmContent: '',
                        tamperTime: []
                })

                const handleSearch = (values) => {
                        
                        // 在这里执行搜索逻辑
                        refreshTableData()
                        resetSearchForm()
                }

                // 重置表单函数
                const resetSearchForm = () => {
                        // 重置表单数据
                        searchFormData.alarmType = 'always'
                        searchFormData.alarmWay = 'always'
                        searchFormData.alarmAddr = ''
                        searchFormData.alarmContent = ''
                        searchFormData.tamperTime = []
                        // 重新加载数据
                        refreshTableData()
                }


                const { proxy } = getCurrentInstance()
                const { deviceSafetyId } = inject('rowData')
                const state = reactive({
                        dialogConfig: {
                                visible: false,
                                mode: 'add',
                                defaultData: {}
                        }
                })

                const selectedRowKeys = ref([])
                const selectedRows = ref([])

                const rowSelection = {
                        selectedRowKeys: selectedRowKeys.value,
                        onChange: (selectedKeys, rows) => {
                                selectedRowKeys.value = selectedKeys
                                selectedRows.value = rows
                        },
                        preserveSelectedRowKeys: true,
                        columnWidth: '50px'
                }

                const {
                        data,
                        run,
                        loading,
                        total,
                        current,
                        pageSize,
                        reload
                } = usePagination(getWagAlarmRecordList, {
                        manual: true,
                        defaultParams: {
                                deviceSafetyId,
                        },
                        formatResult: ({ data = {} }) => ({
                                items: data?.data ?? [], total: data?.total ?? 0
                        }),
                        pagination: {
                                currentKey: 'page_num',
                                pageSizeKey: 'page_size',
                        }
                })

                const dataSource = computed(() => data.value?.items || [])

                const pagination = computed( () => ({
                        total: total.value,
                        current: current.value,
                        pageSize: pageSize.value,
                        showQuickJumper: true,
                        showSizeChanger: true,
                        showTotal: total => `共 ${total} 条`
                }))

                const refreshTableData = (isReload = true) => {
                        run ({
                                deviceSafetyId,
                                page_num: isReload ? 1 : pagination.value.current,
                                page_size: pagination.value?.pageSize,
                        })
                }

                const handleEnable = ( isEnable ) => {
                        
                }

                const handleOpenDialog = (mode = 'add', row = {}) => {
                        state.dialogConfig.visible = true
                        state.dialogConfig.mode = mode
                        state.dialogConfig.defaultData = row
                }

                const handleDelete = (row) => {
                        proxy.$confirm({
                                title: '删除',
                                content: `确认删除该条数据吗?`,
                                icon: createVNode(ExclamationCircleOutlined),
                                okText: '确定',
                                okType: 'warning',
                                cancelText: '取消',
                                async onOk () {
                                        try {
                                                await deleteWagAlarmRecord({ ids: [row.id] })
                                                proxy.$message.success('删除成功！')
                                                refreshTableData()
                                        } catch (err) {
                                                // proxy.$message.error('删除失败！')
                                        }
                                },
                                onCancel () {
                                        console.log('Cancel')
                                }
                        })
                }

                const handleImport = () => {
                        
                }

                const handleExport = () => {
                        
                }

                onMounted(() => {
                        refreshTableData()
                })
                
                return {
                        ...toRefs(state),
                        dynamicSearchRef,
                        dataSource,
                        pagination,
                        loading,
                        columns,
                        rowSelection,
                        searchFormData,
                        searchFormRef,
                        handleExport,
                        handleOpenDialog,
                        handleSearch,
                        handleDelete
                }
        }
})

</script>

<style lang="less" scoped>

.list-operator {
        margin-bottom: 16px;
        display: flex;
        justify-content: flex-end;
}

</style>