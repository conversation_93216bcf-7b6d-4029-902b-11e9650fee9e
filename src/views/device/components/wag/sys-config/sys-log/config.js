export const SEARCH_CONFIG = [
	{
		title: '级别',
		name: 'level',
		type: 'string',
		'x-component': 'Select',
		'x-component-props': {
			placeholder: '请选择级别'
		},
                enum: [
			{
				label: '全部',
				value: 'all'
			},
			{
				label: 'Info',
				value: 'Info'
			},
			{
				label: 'Warning',
				value: 'Warning'
			},
			{
				label: 'Error',
				value: 'Error'
			},
			{
				label: 'Critcal',
				value: 'Critcal'
			}
		],
		default: 'all'
	},
	{
		title: '时间范围',
		name: 'timeRange',
		type: 'array',
		'x-component': 'DatePicker.RangePicker',
		'x-component-props': {
			placeholder: ['开始时间', '结束时间'],
			showTime: true,
			format: 'YYYY-MM-DD HH:mm:ss'
		}
	}
]


export const columns = [
	{
		title: '名称',
		dataIndex: 'name',
		key: 'name',
		width: 160,
		align: 'center',
	},
	{
                title: 'ID',
                dataIndex: 'id',
                key: 'id',
        },
	{
		title: '描述',
		dataIndex: 'description',
		key: 'description',
	},
	{
		title: '用户名',
		dataIndex: 'userName',
		key: 'userName',
	},
	{
		title: 'IP',
		dataIndex: 'ip',
		key: 'ip',
	},
	{
		title: '级别',
		dataIndex: 'level',
		key: 'level',
	},
	{
		title: '操作时间',
		dataIndex: 'operationTime',
		key: 'operationTime',
	},
	{
		title: '操作',
		dataIndex: 'operation',
		key: 'operation',
	},
]