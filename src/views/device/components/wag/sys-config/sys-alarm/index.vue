<template>
        <div>
                <DynamicSearch
                        ref="dynamicSearchRef"
                        :config="SEARCH_CONFIG"
                        @search="handleSearch"
                        @reset="handleSearch"
                />
                <div class="list-operator">
                        <a-space>
                                <a-button type="primary" @click="handleExport">导出</a-button>
                        </a-space>
                </div>
                <a-table
                        :scroll="{y: 'calc(100vh - 330px)'}"
                        :data-source="dataSource"
                        :columns="columns"
                        :loading="loading"
                        :pagination="pagination"
                        :row-selection="rowSelection"
                        :row-key="record => record.id"
                        @change="handleTableChange"
                >
                        <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'operation'">
                                        <a-button type="link" @click="handleOpenDialog('view', record)">查看</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" @click="handleOpenDialog('edit', record)">编辑</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" danger @click="handleDelete(record)">删除</a-button>
                                </template>
                        </template>
                </a-table>
        </div>
</template>

<script>
import DynamicSearch from '@/components/dynamic-search/index.vue';
import {
        deleteWagSysAlarm,
        getWagSysAlarmList
} from '@/request/api-device-wag.js';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { computed, createVNode, defineComponent, getCurrentInstance, inject, onMounted, reactive, ref, toRefs } from 'vue';
import { usePagination } from 'vue-request';
import { SEARCH_CONFIG, columns } from './config';

export default defineComponent({
        name: 'SysAlarm',
        components: {
                DynamicSearch,
        },
        props: {
                
        },
        setup (props, ctx) {

                const dynamicSearchRef = ref(null);
                let searchParams = {};

                const { proxy } = getCurrentInstance()
                const { deviceSafetyId } = inject('rowData')
                const state = reactive({
                        dialogConfig: {
                                visible: false,
                                mode: 'add',
                                defaultData: {}
                        }
                })

                const selectedRowKeys = ref([])
                const selectedRows = ref([])

                const rowSelection = {
                        selectedRowKeys: selectedRowKeys.value,
                        onChange: (selectedKeys, rows) => {
                                selectedRowKeys.value = selectedKeys
                                selectedRows.value = rows
                        },
                        preserveSelectedRowKeys: true,
                        columnWidth: '50px'
                }

                const {
                        data,
                        run,
                        loading,
                        total,
                        current,
                        pageSize,
                        reload
                } = usePagination(getWagSysAlarmList, {
                        manual: true,
                        defaultParams: {
                                deviceSafetyId,
                        },
                        formatResult: ({ data = {} }) => ({
                                items: data?.data ?? [], total: data?.total ?? 0
                        }),
                        pagination: {
                                currentKey: 'page_num',
                                pageSizeKey: 'page_size',
                        }
                })

                const dataSource = computed(() => data.value?.items || [])

                const pagination = computed( () => ({
                        total: total.value,
                        current: current.value,
                        pageSize: pageSize.value,
                        showQuickJumper: true,
                        showSizeChanger: true,
                        showTotal: total => `共 ${total} 条`
                }))

                const refreshTableData = (isReload = true) => {
                        run ({
                                deviceSafetyId,
                                page_num: isReload ? 1 : pagination.value.current,
                                page_size: pagination.value?.pageSize,
                        })
                }

                const handleEnable = ( isEnable ) => {
                        
                }

                const handleOpenDialog = (mode = 'add', row = {}) => {
                        state.dialogConfig.visible = true
                        state.dialogConfig.mode = mode
                        state.dialogConfig.defaultData = row
                }

                const handleDelete = (row) => {
                        proxy.$confirm({
                                title: '删除',
                                content: `确认删除该条数据吗?`,
                                icon: createVNode(ExclamationCircleOutlined),
                                okText: '确定',
                                okType: 'warning',
                                cancelText: '取消',
                                async onOk () {
                                        try {
                                                await deleteWagSysAlarm({ ids: [row.id] })
                                                proxy.$message.success('删除成功！')
                                                refreshTableData()
                                        } catch (err) {
                                                // proxy.$message.error('删除失败！')
                                        }
                                },
                                onCancel () {
                                        console.log('Cancel')
                                }
                        })
                }

                const handleImport = () => {
                        
                }

                const handleExport = () => {
                        
                }

                const handleSearch = () => {

                }

                onMounted(() => {
                        refreshTableData()
                })

                return {
                        ...toRefs(state),
                        dynamicSearchRef,
                        SEARCH_CONFIG,
                        dataSource,
                        pagination,
                        loading,
                        columns,
                        rowSelection,
                        handleExport,
                        handleOpenDialog,
                        handleSearch,
                        handleDelete
                }
        }
})

</script>

<style lang="less" scoped>

.list-operator {
        margin-bottom: 16px;
        display: flex;
        justify-content: flex-end;
}

</style>