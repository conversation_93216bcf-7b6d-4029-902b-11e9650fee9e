export const SEARCH_CONFIG = [
	{
		title: '过期时间',
		name: 'timeRange',
		type: 'array',
		'x-component': 'DatePicker.RangePicker',
		'x-component-props': {
			placeholder: ['开始时间', '结束时间'],
			showTime: true,
			format: 'YYYY-MM-DD HH:mm:ss'
		}
	}
]


export const columns = [
        {
                title: '系统类型',
                dataIndex: 'systemType',
                key: 'systemType',
                width: 120,
                align: 'center',
        },
	{
                title: 'ID',
                dataIndex: 'id',
                key: 'id',
        },
	{
                title: '服务器数量',
                dataIndex: 'serverCount',
                key: 'serverCount',
        },
	{
                title: '过期时间',
                dataIndex: 'expireTime',
                key: 'expireTime',
        },
	{
		title: '授予时间',
		dataIndex: 'grantTime',
		key: 'grantTime',
	},
	{
		title: '导入时间',
		dataIndex: 'importTime',
		key: 'importTime',
	},
	{
		title: '操作',
		dataIndex: 'operation',
		key: 'operation',
		width: 120,
	},
]