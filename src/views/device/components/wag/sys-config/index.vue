<template>
        <a-tabs
                v-model:activeKey="activeKey"
                size="small"
        >
                <a-tab-pane v-for="tab in TABS" :key="tab.label" :tab="tab.label">
                        <div class="sys-config-tab">
                                <component :is="tab.componentName" :title="tab.label" />
                        </div>
                </a-tab-pane>
        </a-tabs>
</template>

<script>
import { defineComponent, ref } from 'vue';
import AlarmRecord from './alarm-record/index.vue';
import Authorize from './authorize/index.vue';
import { TABS } from './config.js';
import SysAlarm from './sys-alarm/index.vue';
import SysLog from './sys-log/index.vue';

export default defineComponent({
        name: 'SysConfig',
        components: {
                AlarmRecord,
                Authorize,
                SysAlarm,
                SysLog
        },
        props: {
        },
        setup (props, ctx) {
                const activeKey = ref(TABS[0].label)
                return {
                        TABS,
                        activeKey,
                }
        }
})

</script>

<style lang="less" scoped>
.sys-config-tab {
        margin-top: 8px;
}
</style>