<!-- WAG 配置弹窗 -->

<template>
  <a-drawer
    v-model:visible="dialogVisible"
    placement="right"
    width="80%"
    :keyboard="false"
    :maskClosable="false"
    :onClose="handleClose"
  >
    <template #title>
      <a-tabs type="card" v-model:activeKey="activeKey">
        <a-tab-pane v-for="tab in TABS" :key="tab.componentName" :tab="tab.label">
        </a-tab-pane>
      </a-tabs>
    </template>
    <component :is="activeKey" />
  </a-drawer>
</template>

<script lang="js">
import { computed, defineComponent, provide, reactive, toRefs, watch } from 'vue';
import { TABS } from './config';
import CustomerManage from './customer-manage/index.vue';
import FileRelease from './file-release/index.vue';
import SysConfig from './sys-config/index.vue';
import TamperProtection from './tamper-protection/index.vue';

export default defineComponent({
  name: 'Wag',
  components: {
    CustomerManage,
    FileRelease,
    TamperProtection,
    SysConfig
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible'],
  setup (props, ctx) {
    const { visible, rowData } = toRefs(props)
    const dialogVisible = computed(() => visible.value)

    const state = reactive({
      activeKey: TABS[0].componentName
    })

    provide('rowData', {
      rowData,
      safetyId: rowData.value.id
    })

    watch(
      () => visible.value,
      async (val) => {
        console.log('wag-drawer-visible', val)
      },
      { immediate: true }
    )

    const handleClose = async () => {
      ctx.emit('update:visible', false)
    }

    return {
      ...toRefs(state),
      TABS,
      dialogVisible,
      handleClose
    }
  }
})
</script>

<style lang="less">
.wag-table-operate {
  display: flex;
  justify-content: flex-end;
  margin: 8px 0;
}
</style>
