<template>
        <div>
                <a-form
                        ref="searchFormRef"
                        @submit="handleSearch"
                >
                        <a-row
                                :gutter="[16, 16]"
                                :style="{
                                        marginTop: '25px'
                                }"
                        >
                                <a-col :span="6">
                                        <a-form-item
                                                label="服务器名称"
                                                :label-col="{
                                                        span: 6,
                                                        style: {
                                                                width: '150px',
                                                                textAlign: 'right'
                                                        }
                                                }"
                                                :wrapper-col="{
                                                        span: 18
                                                }"
                                        >
                                                <a-input
                                                        placeholder="请输入服务器名称"
                                                        v-model:value="searchFormData.serverName"
                                                />
                                        </a-form-item>
                                </a-col>
                                <a-col :span="6">
                                        <a-form-item
                                                label="操作系统"
                                                :label-col="{
                                                        span: 6,
                                                        style: {
                                                                width: '150px',
                                                                textAlign: 'right'
                                                        }
                                                }"
                                                :wrapper-col="{
                                                        span: 18
                                                }"
                                        >
                                                <a-input
                                                        placeholder="请输入操作系统"
                                                        v-model:value="searchFormData.operatingSystem"
                                                />
                                        </a-form-item>
                                </a-col>
                                <a-col :span="6">
                                        <a-form-item
                                                label="篡改文件"
                                                :label-col="{
                                                        span: 6,
                                                        style: {
                                                                width: '150px',
                                                                textAlign: 'right'
                                                        }
                                                }"
                                                :wrapper-col="{
                                                        span: 18
                                                }"
                                        >
                                                <a-input
                                                        placeholder="请输入篡改文件"
                                                        v-model:value="searchFormData.tamperFile"
                                                />
                                        </a-form-item>
                                </a-col>
                                <a-col
                                        :span="6"
                                        :style = "{
                                                display: 'flex',
                                                justifyContent: 'flex-end'
                                        }"
                                > 
                                        <a-form-item>
                                                <a-button
                                                        type="primary"
                                                        html-type="submit"
                                                >
                                                        搜索
                                                </a-button>
                                        </a-form-item>
                                </a-col>
                        </a-row>
                        <a-row
                                :gutter="[16, 16]"
                        >
                                <a-col :span="6">
                                        <a-form-item
                                                label="篡改类型"
                                                :label-col="{ span: 6, style: { width: '150px', textAlign: 'right' } }"
                                                :wrapper-col="{ span: 18 }"
                                        >
                                                <a-select
                                                        placeholder="请选择篡改类型"
                                                        v-model:value="searchFormData.tamperType"
                                                >
                                                        <a-select-option value="always">全部</a-select-option>
                                                        <a-select-option value="add">添加</a-select-option>
                                                        <a-select-option value="delete">删除</a-select-option>
                                                        <a-select-option value="modify">修改</a-select-option>
                                                        <a-select-option value="newDir">新建文件夹</a-select-option>
                                                        <a-select-option value="rename">重命名</a-select-option>
                                                </a-select>
                                        </a-form-item>
                                </a-col>
                                <a-col :span="12">
                                        <a-form-item
                                                label="篡改时间"
                                                :label-col="{ span: 3, style: { width: '150px', textAlign: 'right' } }"
                                                :wrapper-col="{ span: 21 }"
                                        >
                                                <a-range-picker
                                                        v-model:value="searchFormData.tamperTime"
                                                        :style="{ width: '100%' }"
                                                />
                                        </a-form-item>
                                </a-col>
                        </a-row>
                </a-form>
                <div class="list-operator">
                        <a-space>
                                <a-button type="primary" @click="handleOpenDialog">新增</a-button>
                                <a-button type="primary" @click="handleExport">导出</a-button>
                        </a-space>
                </div>
                <a-table
                        :scroll="{y: 'calc(100vh - 330px)'}"
                        :data-source="dataSource"
                        :columns="columns"
                        :loading="loading"
                        :pagination="pagination"
                        :row-key="record => record.id"
                        @change="handleTableChange"
                >
                        <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'operation'">
                                        <a-button type="link" @click="handleOpenDialog('view', record)">查看</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" @click="handleOpenDialog('edit', record)">编辑</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" danger @click="handleDelete(record)">删除</a-button>
                                </template>
                        </template>
                </a-table>
                <EditDialog
                        v-model:visible="dialogConfig.visible"
                        v-bind="dialogConfig"
                        @on-success="handleSave"
                />
        </div>
</template>

<script>
import {
        addWagEventAlarm,
        deleteWagEventAlarm,
        getWagEventAlarmList,
        updateWagEventAlarm
} from '@/request/api-device-wag.js';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { computed, createVNode, defineComponent, getCurrentInstance, inject, onMounted, reactive, ref, toRefs } from 'vue';
import { usePagination } from 'vue-request';
import { columns } from './config.js';
import EditDialog from './edit-dialog.vue';

export default defineComponent({
        name: 'EventAlarm',
        components: {
                EditDialog
        },
        props: {
        },
        setup () {
                const { proxy } = getCurrentInstance()
                const { deviceSafetyId } = inject('rowData')
                const state = reactive({
                        dialogConfig: {
                                visible: false,
                                mode: 'add',
                                defaultData: {}
                        }
                })

                const searchFormRef = ref()

                const searchFormData = reactive({
                        serverName: '',
                        operatingSystem: '',
                        tamperFile: '',
                        tamperType: 'always',
                        tamperTime: []
                })

                const handleSearch = (values) => {
                        console.log('搜索表单数据:', {
                                serverName: searchFormData.serverName,
                                operatingSystem: searchFormData.operatingSystem,
                                tamperFile: searchFormData.tamperFile,
                                tamperType: searchFormData.tamperType,
                                tamperTime: searchFormData.tamperTime
                        })
                        // 在这里执行搜索逻辑
                        refreshTableData()
                        resetSearchForm()
                }

                // 重置表单函数
                const resetSearchForm = () => {
                        // 重置表单数据
                        searchFormData.serverName = ''
                        searchFormData.operatingSystem = ''
                        searchFormData.tamperFile = ''
                        searchFormData.tamperType = ''
                        searchFormData.tamperTime = []
                        // 重新加载数据
                        refreshTableData()
                }

                const {
                        data,
                        run,
                        loading,
                        total,
                        current,
                        pageSize,
                        reload
                } = usePagination(getWagEventAlarmList, {
                        manual: true,
                        defaultParams: {
                                deviceSafetyId,
                        },
                        formatResult: ({ data = {} }) => ({
                                items: data?.data ?? [], total: data?.total ?? 0
                        }),
                        pagination: {
                                currentKey: 'page_num',
                                pageSizeKey: 'page_size',
                        }
                })

                const dataSource = computed(() => data.value?.items || [])

                const pagination = computed( () => ({
                        total: total.value,
                        current: current.value,
                        pageSize: pageSize.value,
                        showQuickJumper: true,
                        showSizeChanger: true,
                        showTotal: total => `共 ${total} 条`
                }))

                const refreshTableData = (isReload = true) => {
                        run ({
                                deviceSafetyId,
                                page_num: isReload ? 1 : pagination.value.current,
                                page_size: pagination.value?.pageSize,
                        })
                }

                const handleEnable = ( isEnable ) => {
                        
                }

                const handleOpenDialog = (mode = 'add', row = {}) => {
                        state.dialogConfig.visible = true
                        state.dialogConfig.mode = mode
                        state.dialogConfig.defaultData = row
                }

                const handleDelete = (row) => {
                        proxy.$confirm({
                                title: '删除',
                                content: `确认删除该条数据吗?`,
                                icon: createVNode(ExclamationCircleOutlined),
                                okText: '确定',
                                okType: 'warning',
                                cancelText: '取消',
                                async onOk () {
                                        try {
                                                await deleteWagEventAlarm({ ids: [row.id] })
                                                proxy.$message.success('删除成功！')
                                                refreshTableData()
                                        } catch (err) {
                                                // proxy.$message.error('删除失败！')
                                        }
                                },
                                onCancel () {
                                        console.log('Cancel')
                                }
                        })
                }

                const handleImport = () => {
                        
                }

                const handleExport = () => {
                        
                }

                const handleSave = async ({ mode, data, callback }) => {
                        const requestMethodEnum = {
                                add: addWagEventAlarm,
                                edit: updateWagEventAlarm
                        }
                        try {
                                
                                let insertData = {
                                        webServerIp: data?.webServerIp,
                                        serverName: data?.serverName,
                                        os: data?.os,
                                        fileLocation: data?.fileLocation,
                                        tamperType: data?.tamperType,
                                        tamperTime: data?.tamperTime
                                }
                                if( mode === 'edit' ) insertData.id = data.id
                                const res = await requestMethodEnum[mode]?.({
                                        ...insertData
                                })
                                if( res.code !== '00000' ) throw new Error(res.msg)
                                proxy.$message.success(mode === 'add' ? '新增成功！' : '更新成功！')
                                callback();
                                refreshTableData();
                        } catch (error) {
                                callback(true, error)
                        }
                }

                onMounted(() => {
                        refreshTableData()
                })

                return {
                        ...toRefs(state),
                        dataSource,
                        pagination,
                        loading,
                        columns,
                        searchFormData,
                        searchFormRef,
                        handleEnable,
                        handleOpenDialog,
                        handleImport,
                        handleExport,
                        handleDelete,
                        handleSave,
                        handleSearch
                }
        }
})

</script>

<style lang="less" scoped>

.list-operator {
        margin-bottom: 16px;
        display: flex;
        justify-content: flex-end;
}

</style>