
export const columns = [
        {
                title: '序号',
                dataIndex: 'index',
                key: 'index',
                width: 80,
                align: 'center',
        },
        {
                title: 'ID',
                dataIndex: 'id',
                key: 'id',
        },
        {
                title: 'web服务器IP',
                dataIndex: 'webServerIp',
                key: 'webServerIp'
        },
        {
                title: '服务器名称',
                dataIndex: 'serverName',
                key: 'serverName'
        },
        {
                title: '操作系统',
                dataIndex: 'os',
                key: 'os',
        },
        {
                title: '文件位置',
                dataIndex: 'fileLocation',
                key: 'fileLocation',
        },
        {
                title: '篡改类型',
                dataIndex: 'tamperType',
                key: 'tamperType',
        },
        {
                title: '篡改时间',
                dataIndex: 'tamperTime',
                key: 'tamperTime',
        },
        {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                width: 120,
        }
]

export const getSchema = (deviceSafetyId) => {
        return {
                type: 'object',
                properties: {
                        layout: {
                                type: 'void',
                                'x-component': 'FormGrid',
                                'x-component-props': {
                                        maxColumns: 2,
                                        minWidth: 300,
                                        columnGap: 24,
                                        rowGap: 0
                                },
                                properties: {
                                        webServerIp: {
                                                type: 'string',
                                                title: 'web服务器IP',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'Input',
                                                'x-component-props': {
                                                        placeholder: '请输入web服务器IP'
                                                },
                                                'x-validator': [
                                                        { required: true, message: '请输入web服务器IP' }
                                                ],
                                                'x-decorator-props': {
                                                        labelWidth: 120,
                                                        labelAlign: 'right'
                                                }
                                        },
                                        serverName: {
                                                type: 'string',
                                                title: '服务器名称',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'Input',
                                                'x-component-props': {
                                                        placeholder: '请输入服务器名称'
                                                },
                                                'x-validator': [
                                                        { required: true, message: '请输入服务器名称' }
                                                ],
                                                'x-decorator-props': {
                                                        labelWidth: 120,
                                                        labelAlign: 'right'
                                                }
                                        },
                                        os: {
                                                type: 'string',
                                                title: '操作系统',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'Input',
                                                'x-component-props': {
                                                        placeholder: '请输入操作系统'
                                                },
                                                'x-validator': [
                                                        { required: true, message: '请输入操作系统' }
                                                ],
                                                'x-decorator-props': {
                                                        labelWidth: 120,
                                                        labelAlign: 'right'
                                                }
                                        },
                                        fileLocation: {
                                                type: 'string',
                                                title: '文件位置',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'Input',
                                                'x-component-props': {
                                                        placeholder: '请输入文件位置'
                                                },
                                                'x-validator': [
                                                        { required: true, message: '请输入文件位置' }
                                                ],
                                                'x-decorator-props': {
                                                        labelWidth: 120,
                                                        labelAlign: 'right'
                                                }
                                        },
                                        tamperType: {
                                                type: 'string',
                                                title: '篡改类型',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'Select',
                                                'x-component-props': {
                                                        placeholder: '请选择篡改类型'
                                                },
                                                'x-validator': [
                                                        { required: true, message: '请选择篡改类型' }
                                                ],
                                                'x-decorator-props': {
                                                        labelWidth: 120,
                                                        labelAlign: 'right'
                                                },
                                                enum: [
                                                        { label: '添加', value: 'add' },
                                                        { label: '删除', value: 'delete' },
                                                        { label: '修改', value: 'modify' },
                                                        { label: '新建文件夹', value: 'newDir' },
                                                        { label: '重命名', value: 'rename' },
                                                ],
                                                default: 'add'
                                        },
                                        tamperTime: {
                                                type: 'string',
                                                title: '篡改时间',
                                                'x-decorator': 'FormItem',
                                                'x-component': 'DatePicker',
                                                'x-component-props': {
                                                        placeholder: '请选择篡改时间'
                                                },
                                                'x-validator': [
                                                        { required: true, message: '请选择篡改时间' }
                                                ],
                                                'x-decorator-props': {
                                                        labelWidth: 120,
                                                        labelAlign: 'right'
                                                },
                                        }
                                }
                        }
                }
        }
}