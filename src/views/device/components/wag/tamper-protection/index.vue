<template>
        <a-tabs
                v-model:activeKey="activeKey"
                size="small"
        >
                <a-tab-pane v-for="tab in TABS" :key="tab.label" :tab="tab.label">
                        <div class="tamper-protection-tab">
                                <component :is="tab.componentName" :title="tab.label" />
                        </div>
                </a-tab-pane>
        </a-tabs>
</template>

<script>
import { defineComponent, getCurrentInstance, ref, toRefs } from 'vue';
import { TABS } from './config';
import EventAlarm from './event-alarm/index.vue';

export default defineComponent({
        name: 'TamperProtection',
        components: {
                EventAlarm,
        },
        props: {
                visible: {
                        type: Boolean,
                        default: false
                },
                rowData: {
                        type: Object,
                        default: () => ({})
                }
        },
        setup (props, ctx) {
                const { visible, rowData } = toRefs(props)
                const { proxy } = getCurrentInstance()
                const activeKey = ref(TABS[0].label)
                return {
                        TABS,
                        activeKey
                }
        },
        
})
</script>

<style lang="less">
.tamper-protection-tab {
        margin-top: 8px;
}
</style>