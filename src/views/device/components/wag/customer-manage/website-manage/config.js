import CellExpand from '@/components/cell-expand'

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns () {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({index}){
        return index+1
      }
    },
    {
      title: '站点名称',
      dataIndex: 'groupPath',
      key: 'groupPath',
      width: 150,
      fixed: 'left'
    },
    {
      title: '服务器类型',
      dataIndex: 'webServer',
      key: 'webServer',
      width: 120
    },
    {
      title: '描述',
      dataIndex: 'describe',
      key: 'describe',
      width: 150
    },
    {
      title: '站点路径',
      dataIndex: 'siteDocRoot',
      key: 'siteDocRoot',
      width: 150
    },
    {
      title: '自动同步',
      dataIndex: 'autoPublish',
      key: 'autoPublish',
      width: 100,
      customRender: ({ record }) => {
        return record.autoPublish ? '是' : '否'
      }
    },
    {
      title: '忽略文件/目录',
      dataIndex: 'protectIgnore',
      key: 'protectIgnore',
      width: 150,
      customRender: ({ record }) => {
        if (!record.protectIgnore?.length) return '-'
        return <CellExpand title="忽略文件/目录" data={record.protectIgnore} />
      }
    },
    {
      title: '忽略进程',
      dataIndex: 'processIgnore',
      key: 'processIgnore',
      width: 100,
      customRender: ({ record }) => {
        if (!record.processIgnore?.length) return '-'
        return <CellExpand title="忽略进程" data={record.processIgnore} />
      }
    },
    {
      title: '开启保护',
      dataIndex: 'protected',
      key: 'protected',
      width: 100,
      customRender: ({ record }) => {
        return record.protected ? '是' : '否'
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 220,
      fixed: 'right'
    }
  ]
}

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 160
      },
      properties: {
        groupPath: {
          type: 'string',
          title: '发布目录（服务器）',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择'
          },
          'x-reactions': '{{getWagPublishPathList()}}',
          'x-validator': [
            { required: true, message: '请选择发布目录（服务器）' }
          ]
        },
        webServer: {
          type: 'string',
          title: '服务器类型',
          'x-validator': [
            { required: true, message: '请输入服务器类型' }
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入'
          }
        },
        describe: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          }
        },
        siteDocRoot: {
          type: 'string',
          title: '站点目录（客户端）',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入'
          },
          'x-validator': [
            { required: true, message: '请输入站点目录（客户端）' }
          ],
        },
        autoPublish: {
          type: 'boolean',
          default: false,
          title: '自动同步',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          enum: [
            { label: '是', value: true },
            { label: '否', value: false }
          ]
        },
        protectIgnore: {
          type: 'array',
          default: [],
          'x-component': 'ArrayItems',
          'x-decorator': 'FormItem',
          title: '忽略文件/目录',
          items: {
            type: 'void',
            'x-component': 'Space',
            properties: {
              input: {
                type: 'string',
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-component-props': {
                  placeholder: '请输入'
                }
              },
              remove: {
                type: 'void',
                'x-decorator': 'FormItem',
                'x-component': 'ArrayItems.Remove',
              },
            },
          },
          properties: {
            add: {
              type: 'void',
              title: '添加',
              'x-component': 'ArrayItems.Addition',
            },
          },
        },
        processIgnore: {
          type: 'array',
          default: [],
          'x-component': 'ArrayItems',
          'x-decorator': 'FormItem',
          title: '忽略进程',
          items: {
            type: 'void',
            'x-component': 'Space',
            properties: {
              input: {
                type: 'string',
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-component-props': {
                  placeholder: '请输入'
                }
              },
              remove: {
                type: 'void',
                'x-decorator': 'FormItem',
                'x-component': 'ArrayItems.Remove',
              },
            },
          },
          properties: {
            add: {
              type: 'void',
              title: '添加',
              'x-component': 'ArrayItems.Addition',
            },
          },
        },
        isProtected: {
          type: 'boolean',
          default: false,
          title: '开启保护',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          enum: [
            { label: '是', value: true },
            { label: '否', value: false }
          ]
        },
      }
    }
  }
}

