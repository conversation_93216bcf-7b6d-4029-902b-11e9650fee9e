<template>
  <div class="website-manage--wrapper">
    <a-space align="center" class="wag-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add')">
        新增
      </a-button>
    </a-space>
    <a-table
      :scroll="{x: 1410, y: 'calc(100vh - 350px)'}"
      :data-source="dataSource"
      :columns="columns"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button type="link" @click="handleOpenDialog('view', record)">查看</a-button>
          <a-button type="link" @click="handleOpenDialog('edit', record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete(record)">删除</a-button>
        </template>
      </template>
    </a-table>
    <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="handleSave" />
  </div>
</template>
<script>
import { defineComponent, inject, reactive, toRefs, computed, onMounted, getCurrentInstance, createVNode } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'

import { addWagWebsite, updateWagWebsite, deleteWagWebsite } from '@/request/api-device-wag'
import { getColumns } from './config';

import EditDialog from './edit-dialog.vue'

export default defineComponent({
  components: {
    EditDialog,
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['refresh'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { data } = toRefs(props)
    const { safetyId } = inject('rowData')
    const state = reactive({
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })

    const dataSource = computed(() => {
      return data.value?.lstSiteData ?? []
    })

    /**
     * 刷新表格数据
     */
    const refreshTableData = () => {
      ctx.emit('refresh')
    }

    /**
     * 保存站点数据
     * @param {Object} options 保存选项
     */
    const handleSave = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add'
      const requestMethodEnum = {
        add: addWagWebsite,
        edit: updateWagWebsite
      }
      try {
        const res = await requestMethodEnum[mode]?.({
          ...data,
          webServerId: props.data.id,
          safetyId
        })
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        refreshTableData()
      } catch (err) {
        // const errMsg = isAdd ? '新增失败！' : '更新失败！';
        // proxy.$message.error(err.message || errMsg)
        callback(true, err)
      }
    }

    /**
     * 删除站点
     * @param {Object} row 行数据
     */
    const handleDelete = async (row = {}) => {
      proxy.$confirm({
        title: '删除',
        content: `确认删除该条数据吗?`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteWagWebsite({ id: row.id, webServerId: props.data.id,  safetyId })
            proxy.$message.success('删除成功！')
            refreshTableData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
    }

    /**
     * 打开编辑弹窗
     * @param {string} mode 操作模式
     * @param {Object} row 行数据
     */
    const handleOpenDialog = (mode = 'add', row = {}) => {
      state.dialogConfig.visible = true
      state.dialogConfig.mode = mode
      state.dialogConfig.defaultData = row
    }

    return {
      ...toRefs(state),
      dataSource,
      refreshTableData,
      handleOpenDialog,
      handleSave,
      handleDelete,
      columns: getColumns()
    }
  }
})
</script>
