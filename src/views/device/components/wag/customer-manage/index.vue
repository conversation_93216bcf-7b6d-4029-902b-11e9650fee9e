<template>
  <a-spin :spinning="loading">
    <div class="customer-manage--wrapper">
      <div class="content-left">
        <a-input v-model:value="searchValue" style="margin-bottom: 8px" placeholder="请输入" />
        <a-tree
          v-model:expanded-keys="expandedKeys"
          v-model:selected-keys="selectedKeys"
          :tree-data="clientTreeData"
          :field-names="{title: 'name', key: 'id'}"
          :filterTreeNode="filterTreeNode"
          @select="handleSelectTreeNode"
        >
          <template #title="{ name, id, data }">
            <div class="node-item">
              <span class="node-item-label">{{ name }}</span>
              <span class="node-item-btn">
                <a-tooltip v-if="id === '-1'">
                  <template #title>新增</template>
                  <a-button type="link" @click="handleOpenDialog('add')">
                    <PlusSquareOutlined />
                  </a-button>
                </a-tooltip>
                <template v-else>
                  <a-tooltip>
                    <template #title>编辑</template>
                    <a-button type="link" @click="handleOpenDialog('edit', data)">
                      <FormOutlined />
                    </a-button>
                  </a-tooltip>
                  <a-tooltip>
                    <template #title>删除</template>
                    <a-button type="link" danger @click="handleDelete(data)">
                      <DeleteOutlined />
                    </a-button>
                  </a-tooltip>
                </template>
              </span>
            </div>
          </template>
        </a-tree>
      </div>
      <div class="content-right">
        <a-tabs type="card" v-model:activeKey="activeTabKey">
          <a-tab-pane v-for="tab in TABS" :key="tab.componentName" :tab="tab.label">
          </a-tab-pane>
        </a-tabs>
        <component :is="activeTabKey" :data="activeTreeNode" @refresh="fetchClientData" />
      </div>
      <EditDialog
        v-model:visible="dialogConfig.visible"
        v-bind="dialogConfig"
        @on-success="handleSaveClient" />
    </div>
  </a-spin>
</template>


<script lang="js">
import { defineComponent, createVNode, inject, toRefs, onMounted, getCurrentInstance, computed, reactive, watch } from 'vue'
import { PlusSquareOutlined, FormOutlined, DeleteOutlined, ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { find, lowerCase } from 'lodash'

import WebsiteManage from './website-manage/index.vue';
import BasicInfo from './basic-info.vue';
import EditDialog from './edit-client-dialog.vue'

import { TABS } from './config';

import {
  getWagClientList,
  deleteWagClient,
  updateWagClient,
  addWagClient
} from '@/request/api-device-wag'

export default defineComponent({
  components: {
    PlusSquareOutlined,
    FormOutlined,
    DeleteOutlined,
    WebsiteManage,
    BasicInfo,
    EditDialog,
  },
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { safetyId } = inject('rowData')
    const state = reactive({
      loading: false,
      searchValue: '',
      activeTabKey: TABS[0].componentName,
      expandedKeys: ['-1'],
      clientTreeData: [],
      selectedKeys: [],
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })

    const activeTreeNode = computed(() => {
      const selectedKeys = state.selectedKeys
      if (!selectedKeys?.length) return {}
      const allNodes = state.clientTreeData?.[0]?.children ?? []
      return find(allNodes, ['id', selectedKeys[0]])
    })

    const fetchClientData = async () => {
      try {
        state.loading = true
        const { data = [] } = await getWagClientList({safetyId})
        state.clientTreeData = [{ name: '客户端管理', id: '-1', disabled: true, children: data ?? [] }]
        let selectedKeys = data?.[0] ? [data[0]?.id] : []
        if (state.selectedKeys?.length && find(data, ['id', state.selectedKeys[0]])) {
          selectedKeys = state.selectedKeys
        }
        state.selectedKeys = selectedKeys
      } finally {
        state.loading = false
      }
    }

    const filterTreeNode = (node = {}) => {
      if (!state.searchValue) return false
      return lowerCase(node.name)?.indexOf(lowerCase(state.searchValue)) > -1
    }

    const handleSelectTreeNode = (selectedKeys = []) => {
      state.selectedKeys = selectedKeys
    }

    const handleOpenDialog = (mode = 'add', row = {}) => {
      state.dialogConfig.visible = true
      state.dialogConfig.mode = mode
      state.dialogConfig.defaultData = row
    }

    const handleSaveClient = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addWagClient,
        edit: updateWagClient
      }
      try {
        const res = await requestMethodEnum[mode]?.({...data, safetyId})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        fetchClientData()
      } catch (err) {
        // const errMsg = isAdd ? '新增失败！' : '更新失败！';
        // proxy.$message.error(err.message || errMsg)
        callback(true, err)
      }
    }

    const handleDelete = async (node = {}) => {
      proxy.$confirm({
        title: '删除',
        content: `确认删除该条数据吗?`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteWagClient({ id: node.id, safetyId })
            proxy.$message.success('删除成功！')
            fetchClientData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
    }

    onMounted(() => {
      fetchClientData()
    })

    return {
      TABS,
      ...toRefs(state),
      filterTreeNode,
      activeTreeNode,
      fetchClientData,
      handleSelectTreeNode,
      handleOpenDialog,
      handleSaveClient,
      handleDelete
    }
  }
})
</script>

<style lang="less" scoped>
.customer-manage--wrapper {
  display: flex;
  height: inherit;

  .content-left {
    width: 250px;
    margin-right: 16px;
    padding: 16px;
    border: 1px solid #ccc;

    ::v-deep .ant-tree-treenode {
      width: 100%;

      .ant-tree-node-content-wrapper {
        flex: 1;
      }
    }
  }

  .node-item {

    &-btn {
      display: none;

      .ant-btn {
        height: auto;
        padding: 0;
        margin-left: 8px;
      }
    }

    &:hover {
      .node-item-btn {
        display: inline-block;
      }
    }
  }

  .content-right {
    flex: 1;
    overflow: hidden;
  }
}
</style>

