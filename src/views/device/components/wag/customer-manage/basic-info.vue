<template>
  <div class="wag-client-basic">
    <div class="title-bar">
      基础信息
    </div>
    <LayoutTable :column-count="2" title-width="130px" emptyText="-">
      <LayoutTableItem title="名称">{{data.name}}</LayoutTableItem>
      <LayoutTableItem title="IP">{{data.ip}}</LayoutTableItem>
      <LayoutTableItem title="同步文件端口">{{data.publishBindPort}}</LayoutTableItem>
      <LayoutTableItem title="保护程序安装目录">{{data.protected ? '是' : '否'}}</LayoutTableItem>
      <LayoutTableItem title="描述" :span="2">{{data.describe}}</LayoutTableItem>
    </LayoutTable>
    <div class="system-info">
      <div class="software-info">
        <div class="title-bar">
          软件信息
        </div>
        <LayoutTable :column-count="1" title-width="130px" emptyText="-">
          <LayoutTableItem title="操作系统">{{systemInfo.platform}}</LayoutTableItem>
          <LayoutTableItem title="启动时间">{{systemInfo.time}}</LayoutTableItem>
          <LayoutTableItem title="最后在线时间">{{systemInfo.lastOnlineTime}}</LayoutTableItem>
        </LayoutTable>
      </div>
      <div class="hardware-info">
        <div class="title-bar">
          硬件信息
        </div>
        <LayoutTable :column-count="1" title-width="130px" emptyText="-">
          <LayoutTableItem title="CPU">{{systemInfo.cpu}}</LayoutTableItem>
          <LayoutTableItem title="内存">{{systemInfo.memory}}</LayoutTableItem>
          <LayoutTableItem title="硬盘">{{systemInfo.disk}}</LayoutTableItem>
        </LayoutTable>
      </div>
    </div>
  </div>
</template>


<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch } from 'vue'
import LayoutTable from '@/components/layout-table/index.vue'
import LayoutTableItem from '@/components/layout-table/layout-table-item.vue'

export default defineComponent({
  components: {
    LayoutTable,
    LayoutTableItem
  },
  props: {
    data: {
      type: Object,
      default: () => ({})
    }
  },
  setup (props, ctx) {
    /**
     * 计算系统信息
     * @returns {Object} 返回系统信息对象
     */
    const systemInfo = computed(() => {
      return props.data.systemInfo ?? {}
    })

    return {
      systemInfo
    }
  }
})
</script>

<style lang="less" scoped>
.wag-client-basic {
  .title-bar {
    display: flex;
    align-items: center;

    &::before {
      width: 3px;
      height: 16px;
      margin: 12px 8px 12px 0;
      content: ' ';
      background-color: rgba(2, 167, 240, 1);
      display: inline-block;
    }
  }

  .system-info {
    display: flex;
    justify-content: space-between;

    .software-info,
    .hardware-info {
      width: calc(50% - 4px);
    }
  }
}
</style>
