/** @type {*} tab列表 */
export const TABS = [
  {
    label: '基础信息',
    componentName: 'BasicInfo'
  },
  {
    label: '站点管理',
    componentName: 'WebsiteManage'
  },
]

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 150
      },
      properties: {
        name: {
          type: 'string',
          title: '名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          },
          'x-validator': [
            { required: true, message: '请输入名称' }
          ],
        },
        ipObj: {
          type: 'void',
          title: 'IP',
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            asterisk: true,
            feedbackLayout: 'none',
          },
          'x-component': 'FormGrid',
          properties: {
            isIpv6: {
              type: 'boolean',
              default: false,
              'x-decorator': 'FormItem',
              'x-component': 'Select',
              enum: [
                { label: 'IPV4', value: false },
                { label: 'IPV6', value: true }
              ],
              'x-validator': [
                { required: true, message: '请选择' }
              ],
            },
            ip: {
              type: 'string',
              'x-validator': [
                { required: true, message: '请输入' }
              ],
              'x-decorator': 'FormItem',
              'x-component': 'Input',
              'x-component-props': {
                placeholder: '请输入',
              },
            },
          },
        },
        describe: {
          type: 'string',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
          'x-decorator': 'FormItem',
          'x-decorator-props': {
            label: '描述'
          }
        },
        publishBindPort: {
          type: 'number',
          title: '同步文件端口',
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          'x-component-props': {
            placeholder: '请输入'
          },
          'x-validator': [
            { required: true, message: '请输入同步文件端口' },
            { port: true },
            { format: 'integer' }
          ]
        },
        sbiBindPort: {
          type: 'number',
          title: '通知端口',
          'x-decorator': 'FormItem',
          'x-component': 'InputNumber',
          'x-component-props': {
            placeholder: '请输入'
          },
          'x-validator': [
            { required: true, message: '请输入通知端口' },
            { port: true },
            { format: 'integer' }
          ]
        },
        isProtected: {
          type: 'boolean',
          title: '保护程序安装目录',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          enum: [
            { label: '是', value: true },
            { label: '否', value: false }
          ]
        },
      }
    }
  }
}
