<template>
        <div>
                <div
                        class="list-operator"
                        :style="{ marginTop: '8px' }"
                >
                        <a-space class="left-space">
                                <a-button type="primary" @click="handleEnable(true)">启用</a-button>
                                <a-button type="primary" @click="handleEnable(false)">禁用</a-button>
                        </a-space>
                        <a-space class="right-space">
                                <a-button type="primary" @click="handleOpenDialog">新增</a-button>
                                <a-button type="primary" @click="handleImport">导入</a-button>
                                <a-button type="primary" @click="handleExport">导出</a-button>
                        </a-space>
                </div>
                <a-table
                        :scroll="{y: 'calc(100vh - 330px)'}"
                        :data-source="dataSource"
                        :columns="columns"
                        :loading="loading"
                        :pagination="pagination"
                        :row-selection="rowSelection"
                        :row-key="record => record.id"
                        @change="handleTableChange"
                >
                        <template #bodyCell="{ column, record }">
                                <template v-if="column.key === 'operation'">
                                        <a-button type="link" @click="handleOpenDialog('view', record)">查看</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" @click="handleOpenDialog('edit', record)">编辑</a-button>
                                        <a-button type="link" :disabled="['always'].includes(record.name)" danger @click="handleDelete(record)">删除</a-button>
                                </template>
                        </template>
                </a-table>
                <EditDialog
                        v-model:visible="dialogConfig.visible"
                        v-bind="dialogConfig"
                        @on-success="handleSave"
                />
        </div>
</template>

<script >
import {
        addWagManualSyncTask,
        deleteWagManualSyncTask,
        getWagManualSyncTaskList,
        updateWagManualSyncTask
} from '@/request/api-device-wag.js';
import { ExclamationCircleOutlined } from '@ant-design/icons-vue';
import { computed, createVNode, defineComponent, getCurrentInstance, inject, onMounted, reactive, ref, toRefs } from 'vue';
import { usePagination } from 'vue-request';
import { columns } from './config.js';
import EditDialog from './edit-dialog.vue';

export default defineComponent({
        name: 'SyncTasks',
        components: {
                EditDialog,
        },
        props: {
                
        },
        setup (props, ctx) {
                const { proxy } = getCurrentInstance()
                const { deviceSafetyId } = inject('rowData')

                const selectedRowKeys = ref([])
                const selectedRows = ref([])

                const rowSelection = {
                        selectedRowKeys: selectedRowKeys.value,
                        onChange: (selectedKeys, rows) => {
                                selectedRowKeys.value = selectedKeys
                                selectedRows.value = rows
                        },
                        preserveSelectedRowKeys: true,
                        columnWidth: '50px'
                }

                const state = reactive({
                        dialogConfig: {
                                visible: false,
                                mode: 'add',
                                defaultData: {}
                        }
                })

                const {
                        data,
                        run,
                        loading,
                        total,
                        current,
                        pageSize,
                        reload
                } = usePagination(getWagManualSyncTaskList, {
                        manual: true,
                        defaultParams: {
                                deviceSafetyId,
                        },
                        formatResult: ({ data = {} }) => ({
                                items: data?.data ?? [], total: data?.total ?? 0
                        }),
                        pagination: {
                                currentKey: 'page_num',
                                pageSizeKey: 'page_size',
                        }
                })

                const dataSource = computed(() => data.value?.items || [])

                const pagination = computed( () => ({
                        total: total.value,
                        current: current.value,
                        pageSize: pageSize.value,
                        showQuickJumper: true,
                        showSizeChanger: true,
                        showTotal: total => `共 ${total} 条`
                }))

                const refreshTableData = (isReload = true) => {
                        run ({
                                deviceSafetyId,
                                page_num: isReload ? 1 : pagination.value.current,
                                page_size: pagination.value?.pageSize,
                        })
                }

                const handleEnable = ( isEnable ) => {
                        
                }

                const handleOpenDialog = (mode = 'add', row = {}) => {
                        state.dialogConfig.visible = true
                        state.dialogConfig.mode = mode
                        state.dialogConfig.defaultData = row
                }

                const handleDelete = (row) => {
                        proxy.$confirm({
                                title: '删除',
                                content: `确认删除该条数据吗?`,
                                icon: createVNode(ExclamationCircleOutlined),
                                okText: '确定',
                                okType: 'warning',
                                cancelText: '取消',
                                async onOk () {
                                        try {
                                                await deleteWagManualSyncTask({ ids: [row.id] })
                                                proxy.$message.success('删除成功！')
                                                refreshTableData()
                                        } catch (err) {
                                                // proxy.$message.error('删除失败！')
                                        }
                                },
                                onCancel () {
                                        console.log('Cancel')
                                }
                        })
                }

                const handleImport = () => {
                        
                }

                const handleExport = () => {
                        
                }

                 // 添加选择列的表格列配置
                const columnsWithSelection = computed(() => {
                        // 在原列前添加选择列
                        return [
                                {
                                        key: 'selection',
                                        ...rowSelection
                                },
                                ...columns
                        ]
                })

                const handleClearSelection = () => {
                        selectedRowKeys.value = []
                        selectedRows.value = []
                }

                const handleSave = async ({ mode, data, callback }) => {
                        const requestMethodEnum = {
                                add: addWagManualSyncTask,
                                edit: updateWagManualSyncTask
                        }
                        try {
                                const strategy = data.strategyName;
                                const [strategyName, strategyId] = strategy.split('#');
                                let insertData = {
                                        enable: data.enable,
                                        name: data.name,
                                        description: data.description,
                                        addrObject: data.addrObject,
                                        strategyId: parseInt(strategyId, 10),
                                        strategyName: strategyName
                                }
                                if( mode === 'edit' ) insertData.id = data.id
                                const res = await requestMethodEnum[mode]?.({
                                        ...insertData
                                })
                                if( res.code !== '00000' ) throw new Error(res.msg)
                                proxy.$message.success(mode === 'add' ? '新增成功！' : '更新成功！')
                                callback();
                                refreshTableData();
                        } catch (error) {
                                callback(true, error)
                        }
                }


                onMounted(() => {
                        refreshTableData()
                })

                return {
                        ...toRefs(state),
                        dataSource,
                        pagination,
                        loading,
                        columns,
                        rowSelection,
                        handleEnable,
                        handleOpenDialog,
                        handleImport,
                        handleExport,
                        handleDelete,
                        handleSave
                }
        },
})

</script>

<style lang="less" scoped>

.list-operator {
        margin-bottom: 16px;
        display: flex;
        justify-content: space-between;
}

</style>