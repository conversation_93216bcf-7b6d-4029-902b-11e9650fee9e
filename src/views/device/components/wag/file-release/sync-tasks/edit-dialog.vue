<template>
        <a-modal
                v-model:visible="dialogVisible"
                :title="title"
                width="800px"
                :keyboard="false"
                :maskClosable="false"
                @cancel="handleClose"
        >
                <a-spin :spinning="loading">
                        <FormProvider :form="form">
                                <SchemaField
                                        :schema="schema"
                                        :scope="formScope"
                                />
                        </FormProvider>
                </a-spin>
                <template #footer>
                        <a-button
                                v-if="!isView"
                                type="primary"
                                :loading="loading"
                                @click="handleSave"
                        >
                                保存
                        </a-button>
                        <a-button
                                :type="isView ? 'primary' : 'default'"
                                @click="handleClose"
                        >
                                {{ isView ? '关闭' : '取消' }}
                        </a-button>
                </template>
        </a-modal>
</template>

<script>
import {
        getWagClientList,
        getWagFileSyncRootDirList,
        getWagSubFileListByDir
} from '@/request/api-device-wag';
import { createForm } from '@formily/core';
import { cloneDeep } from 'lodash';
import { computed, defineComponent, getCurrentInstance, inject, nextTick, reactive, toRefs, watch } from 'vue';
import { SCHEMA } from './config';

export default defineComponent({
        props: {
                visible: {
                        type: Boolean,
                        default: false
                },
                mode: {
                        type: String,
                        default: 'add'
                },
                defaultData: {
                        type: Object,
                        default: () => ({})
                }
        },
        emits: ['update:visible', 'on-success'],
        setup (props, ctx) {
                const { proxy } = getCurrentInstance()
                const { visible, mode, defaultData } = toRefs(props)
                const { safetyId } = inject('rowData')
                const isView = computed(() => mode.value === 'view')
                const dialogVisible = computed(() => visible.value)
                const title = computed(() => {
                        const titleMap = {
                                add: '新增',
                                edit: '编辑',
                                view: '查看'
                        }
                        return `${titleMap[mode.value]}同步任务`
                })

                const state = reactive({
                        loading: false
                })

                const form = createForm()

                watch(
                        () => visible.value,
                        async (val) => {
                                if (!val) return
                                await nextTick()
                                await initForm()
                        }
                )

                const initForm = async () => {
                        const patternMap = {
                                add: 'editable',
                                view: 'readPretty',
                                edit: 'editable'
                        }
                        form.setPattern(patternMap[mode.value])
                        if (mode.value === 'add') return
                        const formData = cloneDeep(defaultData.value)
                        form.setValues({...formData})
                }

                const formatTreeDataSource = (treeData = [], subTreeData = [], pid) => {
                        treeData = cloneDeep(treeData)
                        if (!treeData.length || !pid || !subTreeData.length) return treeData
                        const recursion = (data = []) => {
                                for (const node of data) {
                                        if ( node.id === pid ) {
                                                node.children = subTreeData;
                                                break;
                                        } else if ( !node.isLeaf && node.children?.length ) {
                                                recursion(node.children)
                                        }
                                }
                                return data
                        }
                        return recursion(treeData)
                }

                // 获取备份目录数据源-一级目录
                let rootDirPromise = null
                const getWagFileSyncDirDataSource = () => {
                        return async ( field ) => {
                                let data = []
                                const loadData = async({value}) => {
                                        const { data = [] } = await getWagSubFileListByDir({safetyId, dir: value})
                                        const subTreeData = data?.map(item => {
                                                return {
                                                        label: item.name,
                                                        value: item.realPath,
                                                        id: item.realPath,
                                                        isLeaf: item.type === 'f',
                                                        pid: value
                                                }
                                        }) ?? []
                                        if (!subTreeData.length) return
                                        const treeDataSource = formatTreeDataSource(field.dataSource, subTreeData, value);
                                        field.setDataSource(treeDataSource)
                                }
                                field.setComponentProps({ loadData })
                                try {
                                        if (!rootDirPromise) rootDirPromise = getWagFileSyncRootDirList({safetyId})
                                        const res = await rootDirPromise;
                                        data = res?.data ?? []
                                } finally {
                                        field.setDataSource(data?.map(({name}) => {
                                                let value = name
                                                if (!value.startsWith('/')) value = `/${value}`
                                                return {label: name, value, id: value, isLeaf: false}
                                        }) ?? [])
                                }
                        }
                }

                // 获取web服务器数据源
                let webServerReqPromise = null
                const getWagWebServerDataSource = () => {
                        return async (field) => {
                                let data = []
                                try {
                                        if (!webServerReqPromise) webServerReqPromise = getWagClientList({safetyId})
                                        const res = await webServerReqPromise;
                                        data = res?.data ?? []
                                } finally {
                                        field.setDataSource(data?.map(({name, ip}) => ({label: `${name} (${ip})`, value: name})) ?? [])
                                }
                        }
                }

                const handleClose = async (hasError = false) => {
                        state.loading = false
                        if (hasError === true) return
                        await form.setValues(cloneDeep(form.initialValues), 'overwrite')
                        await form.reset()
                        ctx.emit('update:visible', false)
                }

                const handleSave = async () => {
                        await form.validate()
                        state.loading = true
                        const params = cloneDeep(form.values)
                        ctx.emit('on-success', {
                                mode: mode.value,
                                data: params,
                                callback: handleClose
                        })
                }

                return {
                        title,
                        isView,
                        form,
                        formScope: {
                                getWagFileSyncDirDataSource,
                                getWagWebServerDataSource
                        },
                        schema: SCHEMA,
                        ...toRefs(state),
                        dialogVisible,
                        handleClose,
                        handleSave
                }
        }
})
</script>
