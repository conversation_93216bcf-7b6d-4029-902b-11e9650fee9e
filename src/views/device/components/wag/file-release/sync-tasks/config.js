
export const columns = [
        {
                title: '任务名称',
                dataIndex: 'taskName',
                key: 'taskName',
                width: 100,
                align: 'center',
        },
        {
                title: 'ID',
                dataIndex: 'id',
                key: 'id',
        },
        {
                title: '文件数量',
                dataIndex: 'fileCount',
                key: 'fileCount',
        },
        {
                title: '文件大小',
                dataIndex: 'fileSize',
                key: 'fileSize',
        },
        {
                title: '原路径',
                dataIndex: 'srcPath',
                key: 'srcPath',
        },
        {
                title: '目标站点',
                dataIndex: 'dstSite',
                key: 'dstSite',
        },
        {
                title: '客户端名称',
                dataIndex: 'clientName',
                key: 'clientName',
        },
        {
                title: '发布忽略',
                dataIndex: 'ignoreRelease',
                key: 'ignoreRelease',
        },
        {
                title: '是否增量同步',
                dataIndex: 'isIncremental',
                key: 'isIncremental',
        },
        {
                title: '下发时间',
                dataIndex: 'publishTime',
                key: 'publishTime',
        },
        {
                title: '结束时间',
                dataIndex: 'endTime',
                key: 'endTime',
        },
        {
                title: '状态',
                dataIndex: 'status',
                key: 'status',
        },
        {
                title: '操作',
                dataIndex: 'operation',
                key: 'operation',
                width: 220,
                fixed: 'right',
        }
]

export const SCHEMA = {
        type: 'object',
        properties: {
                layout: {
                        type: 'void',
                        'x-component': 'FormLayout',
                        'x-component-props': {
                                labelWidth: 120
                        },
                        properties: {
                                name: {
                                        type: 'string',
                                        title: '任务名称',
                                        'x-decorator': 'FormItem',
                                        'x-component': 'Input',
                                        'x-component-props': {
                                                placeholder: '请输入任务名称'
                                        },
                                        'x-validator': [
                                                {
                                                        required: true,
                                                        message: '请输入任务名称'
                                                }
                                        ]
                                },
                                pathArray: {
                                        type: 'array',
                                        title: '备份目录',
                                        'x-decorator': 'FormItem',
                                        'x-component': 'TreeSelect',
                                        'x-component-props': {
                                                placeholder: '请选择',
                                                multiple: true,
                                                treeCheckable: true,
                                        },
                                        'x-reactions': '{{getWagFileSyncDirDataSource()}}',
                                        'x-validator': [
                                                {
                                                        required: true,
                                                        message: '请选择备份目录'
                                                }
                                        ]
                                },
                                webServerArray: {
                                        type: 'array',
                                        title: 'WEB服务器',
                                        'x-decorator': 'FormItem',
                                        'x-component': 'Select',
                                        'x-component-props': {
                                                mode: "multiple",
                                                placeholder: '请选择WEB服务器'
                                        },
                                        'x-reactions': '{{getWagWebServerDataSource()}}',
                                        'x-validator': [
                                                {
                                                        required: true,
                                                        message: '请选择WEB服务器'
                                                }
                                        ]
                                },
                                publishIgnore: {
                                        type: 'array',
                                        default: [],
                                        'x-component': 'ArrayItems',
                                        'x-decorator': 'FormItem',
                                        title: '忽略目录',
                                        items: {
                                                type: 'void',
                                                'x-component': 'Space',
                                                properties: {
                                                        input: {
                                                                type: 'string',
                                                                'x-decorator': 'FormItem',
                                                                'x-component': 'Input',
                                                                'x-component-props': {
                                                                        placeholder: '请输入忽略目录',
                                                                        style: {
                                                                                width: '460px'
                                                                        }
                                                                }
                                                        },
                                                        remove: {
                                                                type: 'void',
                                                                'x-decorator': 'FormItem',
                                                                'x-component': 'ArrayItems.Remove'
                                                        },
                                                },
                                        },
                                        properties: {
                                                add: {
                                                        type: 'void',
                                                        title: '添加',
                                                        'x-component': 'ArrayItems.Addition',
                                                },
                                        },
                                },
                                isIncrement: {
                                        type: 'boolean',
                                        default: false,
                                        title: '开启保护',
                                        'x-decorator': 'FormItem',
                                        'x-component': 'Select',
                                        enum: [
                                                { label: '是', value: true },
                                                { label: '否', value: false }
                                        ]
                                }
                        }
                }
        }
}