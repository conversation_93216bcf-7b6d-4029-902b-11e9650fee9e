/** 导入单元格展开组件 */
import CellExpand from '@/components/cell-expand'

/**
 * 获取列表配置
 * 定义文件同步任务列表的列配置
 *
 * @export
 * @return {*}
 */
export function getColumns ({pagination}) {
  return [
    {
      // 序号列
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      // 任务名称列
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      width: 150,
      fixed: 'left'
    },
    {
      title: '文件数量',
      dataIndex: 'fileCount',
      key: 'fileCount',
      width: 100
    },
    {
      title: '文件大小',
      dataIndex: 'fileSize',
      key: 'fileSize',
      width: 150
    },
    {
      title: '原路径',
      dataIndex: 'srcPaths',
      key: 'srcPaths',
      width: 150,
      customRender: ({ record }) => {
        if (!record.srcPaths?.length) return '-'
        return <CellExpand title="原路径" data={record.srcPaths} />
      }
    },
    {
      title: '目标站点',
      dataIndex: 'siteDocRoot',
      key: 'siteDocRoot',
      width: 150
    },
    {
      title: '客户端名称',
      dataIndex: 'clientName',
      key: 'clientName',
      width: 150
    },
    {
      title: '发布忽略',
      dataIndex: 'publishIgnore',
      key: 'publishIgnore',
      width: 150,
      customRender: ({ record }) => {
        if (!record.publishIgnore?.length) return '-'
        return <CellExpand title="发布忽略" data={record.publishIgnore} />
      }
    },
    {
      title: '是否增量同步',
      dataIndex: 'increment',
      key: 'increment',
      width: 150,
      customRender: ({ record }) => {
        return record.increment ? '是' : '否'
      }
    },
    {
      title: '下发时间',
      dataIndex: 'startTime',
      key: 'startTime',
      width: 160
    },
    {
      title: '结束时间',
      dataIndex: 'endTime',
      key: 'endTime',
      width: 160
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 150
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      width: 120,
      fixed: 'right'
    }
  ]
}

/**
 * 编辑弹窗表单配置
 * 定义文件同步任务编辑表单的字段配置
 */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 130
      },
      properties: {
        // 任务名称字段
        name: {
          type: 'string',
          title: '任务名称',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入任务名称'
          },
          'x-validator': [
            { required: true, message: '请输入任务名称' }
          ]
        },
        pathArray: {
          type: 'array',
          title: '备份目录',
          'x-decorator': 'FormItem',
          'x-component': 'TreeSelect',
          'x-component-props': {
            placeholder: '请选择',
            multiple: true,
            treeCheckable: true,
          },
          'x-reactions': '{{getWagFileSyncDirDataSource()}}',
          'x-validator': [
            { required: true, message: '请选择备份目录' }
          ]
        },
        webServerArray: {
          type: 'array',
          title: 'WEB服务器',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          'x-component-props': {
            mode: "multiple",
            placeholder: '请选择'
          },
          'x-reactions': '{{getWagWebServerDataSource()}}',
          'x-validator': [
            { required: true, message: '请选择WEB服务器' }
          ]
        },
        publishIgnore: {
          type: 'array',
          default: [],
          'x-component': 'ArrayItems',
          'x-decorator': 'FormItem',
          title: '忽略目录',
          items: {
            type: 'void',
            'x-component': 'Space',
            properties: {
              input: {
                type: 'string',
                'x-decorator': 'FormItem',
                'x-component': 'Input',
                'x-component-props': {
                  placeholder: '请输入'
                }
              },
              remove: {
                type: 'void',
                'x-decorator': 'FormItem',
                'x-component': 'ArrayItems.Remove',
              },
            },
          },
          properties: {
            add: {
              type: 'void',
              title: '添加',
              'x-component': 'ArrayItems.Addition',
            },
          },
        },
        isIncrement: {
          type: 'boolean',
          default: false,
          title: '开启保护',
          'x-decorator': 'FormItem',
          'x-component': 'Select',
          enum: [
            { label: '是', value: true },
            { label: '否', value: false }
          ]
        },
      }
    }
  }
}

