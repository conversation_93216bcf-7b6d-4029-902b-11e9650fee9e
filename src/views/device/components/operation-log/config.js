// 审计日志配置

// 功能模块选项
export const MODULE_OPTIONS = [
  { label: '资产管理', value: '资产管理' },
  { label: '网络管理', value: '网络管理' },
  { label: '基础设置', value: '基础设置' },
  { label: '策略管理', value: '策略管理' },
  { label: '日志共享', value: '日志共享' },
  { label: '系统管理', value: '系统管理' }
];

// 操作类型选项
export const OPERATE_TYPE_OPTIONS = [
  { label: '新增', value: '新增' },
  { label: '修改', value: '修改' },
  { label: '删除', value: '删除' },
  { label: '导入', value: '导入' },
  { label: '导出', value: '导出' },
  { label: '查看', value: '查看' },
  { label: '登录', value: '登录' },
  { label: '登出', value: '登出' }
];

// 搜索配置
export const SEARCH_CONFIG = [
  {
    title: '功能模块',
    name: 'moduleName',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入功能模块',
    },
  },
  {
    title: 'IP地址',
    name: 'operateIp',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入IP地址',
    },
  }
];

// 表格列配置
export function getTableColumns() {
  return [
    {
      title: "ID",
      dataIndex: "id",
      width: 80,
      align: "center",
      fixed: 'left',
    },
    {
      title: '功能模块',
      dataIndex: 'moduleName',
      key: 'moduleName',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '操作类型',
      dataIndex: 'operateType',
      key: 'operateType',
      width: 100,
      align: "center",
    },
    {
      title: '操作内容',
      dataIndex: 'content',
      key: 'content',
      width: 300,
      align: "left",
      ellipsis: true,
    },
    {
      title: '操作人',
      dataIndex: 'operator',
      key: 'operator',
      width: 120,
      align: "center",
      ellipsis: true,
    },
    {
      title: '操作IP',
      dataIndex: 'operateIp',
      key: 'operateIp',
      width: 140,
      align: "center",
    },
    {
      title: '操作时间',
      dataIndex: 'operateTime',
      key: 'operateTime',
      width: 160,
      align: "center",
      customRender({ text }) {
        return text ? new Date(text).toLocaleString('zh-CN') : '-';
      }
    }
  ];
}
