# 1，检测配置管理

## 1.1，特征检测策略库

### 1.1.1，新增数据

* 请求路径：`POST /ids/detectionStrategy/insert`

* 请求参数示例：

  ```json
  {
    "name": "敏感信息泄露防护",
    "strategyType": "内容识别",
    "eventName": ["敏感数据外发"],
    "remark": "检测包含身份证、手机号等敏感信息的传输行为"
  }
  
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.1.2，修改数据

* 请求路径：`PUT /ids/detectionStrategy/modify`

* 请求参数示例：

  ```json
  {
      "id": 3,
    "name": "敏感信息泄露防护",
    "strategyType": "内容识别",
    "eventName": ["敏感数据外发"],
    "remark": "检测包含身份证、手机号等敏感信息的传输行为"
  }
  
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.1.3，删除数据

* 请求路径：`DELETE /ids/detectionStrategy/delete`

* 请求参数示例：

  ```json
  {
    "id": [1, 2]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.1.4，分页查询数据

* 请求路径：`POST /ids/detectionStrategy/pageData`

* 请求参数示例：

  ```json
  {
      "page": 1,
      "size": 1,
      "name": "恶意软件",
      "startTime": "2000-07-20 00:00:00", // 非必填
      "endTime": "2026-07-27 23:00:00" // 非必填
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": {
          "data": [
              {
                  "createTime": 1754204629000,
                  "createdBy": "684b4e34ce7645309d3972d57227d9f6",
                  "eventName": [
                      "恶意代码执行",
                      "可疑文件传输",
                      "异常进程创建"
                  ],
                  "id": 2,
                  "name": "恶意软件检测策略集",
                  "remark": "针对终端设备的恶意软件行为特征检测",
                  "strategyType": "特征匹配",
                  "updateTime": 1754204629000,
                  "updatedBy": "684b4e34ce7645309d3972d57227d9f6"
              }
          ],
          "offset": 0,
          "page": 1,
          "size": 1,
          "total": 1,
          "totalPage": 1
      },
      "msg": "一切ok"
  }
  ```

### 1.1.5，导出数据

* 请求路径：`GET /ids/detectionStrategy/export`

* 请求参数示例：

  ```json
  http://127.0.0.1:8900/ids/detectionStrategy/export?ids=&name=%E6%81%B6%E6%84%8F%E8%BD%AF%E4%BB%B6&startTime=2000-01-01%2000:00:00&endTime=2050-01-01%2000:00:00
  ```

* 响应参数示例

  ```json
  文件流
  ```

### 1.1.6，导入数据

* 请求路径：`POST /ids/detectionStrategy/import`

* 请求参数示例：

  ```json
  form-data
  file : 文件流
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": "导入成功",
      "msg": "一切ok"
  }
  ```

### 1.1.7，合并数据

* 请求路径：`POST /ids/detectionStrategy/combine`

* 请求参数示例

  ```json
  {
      "ids": [
          3,
          4
      ],
      "name": "敏感信息泄露防护",
      "strategyType": "内容识别",
      "remark": "检测包含身份证、手机号等敏感信息的传输行为"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

## 1.2，策略模板

### 1.2.1，新增数据

* 请求路径：`POST /ids/detectionStrategyTemplate/insert`

* 请求参数示例：

  ```json
  {
    "name": "访客网络隔离模板",
    "templateType": "用户",
    "remark": "用于临时访客网络的安全隔离检测",
    "ipRange": ["*************/24"],
    "responseWay": ["日志", "报警"]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.2.2，修改数据

* 请求路径：`PUT /ids/detectionStrategyTemplate/modify`

* 请求参数示例：

  ```json
  {
      "id": 1,
    "name": "访客网络隔离模板",
    "templateType": "用户",
    "remark": "用于临时访客网络的安全隔离检测",
    "ipRange": ["*************/24"],
    "responseWay": ["日志", "报警"]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.2.3，删除数据

* 请求路径：`DELETE /ids/detectionStrategyTemplate/delete`

* 请求参数示例：

  ```json
  {
    "id": [1, 2]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.2.4，分页查询数据

* 请求路径：`POST /ids/detectionStrategyTemplate/pageData`

* 请求参数示例：

  ```json
  {
      "page": 1,
      "size": 1,
      "name": "",
      "startTime": "2000-07-20 00:00:00", // 非必填
      "endTime": "2026-07-27 23:00:00" // 非必填
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": {
          "data": [
              {
                  "createTime": 1754317544000,
                  "createdBy": "684b4e34ce7645309d3972d57227d9f6",
                  "id": 4,
                  "ipRange": [
                      "*************/24"
                  ],
                  "name": "访客网络隔离模板",
                  "remark": "用于临时访客网络的安全隔离检测",
                  "responseWay": [
                      "日志",
                      "报警"
                  ],
                  "templateType": "用户",
                  "updateTime": 1754317544000,
                  "updatedBy": "684b4e34ce7645309d3972d57227d9f6"
              }
          ],
          "offset": 0,
          "page": 1,
          "size": 1,
          "total": 4,
          "totalPage": 4
      },
      "msg": "一切ok"
  }
  ```

### 1.2.5，导出数据

* 请求路径：`GET /ids/detectionStrategyTemplate/export`

* 请求参数示例：

  ```json
  /ids/detectionStrategyTemplate/export?ids=
  ```

* 响应参数示例

  ```json
  文件流
  ```

### 1.2.6，导入数据

* 请求路径：`POST /ids/detectionStrategyTemplate/import`

* 请求参数示例：

  ```json
  form-data
  file : 文件流
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": "导入成功",
      "msg": "一切ok"
  }
  ```

### 1.2.7，合并数据

* 请求路径：`POST /ids/detectionStrategyTemplate/combine`

* 请求参数示例：

  ```json
  {
      "ids": [1, 2],
    "name": "访客网络隔离模板-EDIT",
    "templateType": "用户",
    "remark": "用于临时访客网络的安全隔离检测"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

## 1.3，拒绝服务与扫描类

### 1.3.1，新增数据

* 请求路径：`POST /ids/detectionRefuseServer/insert`

* 请求参数示例：

  ```json
  {
    "name": "SYN Flood攻击检测",
    "remark": "检测大量伪造源IP的SYN报文，导致目标服务器连接耗尽",
    "eventAlias": "SYN洪水攻击",
    "eventLevel": "高危",
    "protocol": "TCP",
    "securityType": "其他攻击",
    "influenceSystem": "Windows Server、Linux",
    "influenceDevice": "Web服务器、数据库服务器",
    "prevalence": "流行"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.3.2，修改数据

* 请求路径：`PUT /ids/detectionRefuseServer/modify`

* 请求参数示例：

  ```json
  {
      "id": 1,
    "name": "SYN Flood攻击检测",
    "remark": "检测大量伪造源IP的SYN报文，导致目标服务器连接耗尽",
    "eventAlias": "SYN洪水攻击",
    "eventLevel": "高危",
    "protocol": "TCP",
    "securityType": "其他攻击",
    "influenceSystem": "Windows Server、Linux",
    "influenceDevice": "Web服务器、数据库服务器",
    "prevalence": "流行"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.3.3，删除数据

* 请求路径：`DELETE /ids/detectionRefuseServer/delete`

* 请求参数示例：

  ```json
  {
    "id": [1, 2]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.3.4，分页查询数据

* 请求路径：`POST /ids/detectionRefuseServer/pageData`

* 请求参数示例：

  ```json
  {
      "page": 1,
      "size": 1,
      "name": "",
      "startTime": "2000-07-20 00:00:00", // 非必填
      "endTime": "2026-07-27 23:00:00" // 非必填
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": {
          "data": [
              {
                  "createTime": 1754317825000,
                  "createdBy": "684b4e34ce7645309d3972d57227d9f6",
                  "eventAlias": "SYN洪水攻击",
                  "eventLevel": "高危",
                  "id": 1,
                  "influenceDevice": "Web服务器、数据库服务器",
                  "influenceSystem": "Windows Server、Linux",
                  "name": "SYN Flood攻击检测",
                  "prevalence": "流行",
                  "protocol": "TCP",
                  "remark": "检测大量伪造源IP的SYN报文，导致目标服务器连接耗尽",
                  "securityType": "其他攻击",
                  "updateTime": 1754317825000,
                  "updatedBy": "684b4e34ce7645309d3972d57227d9f6"
              }
          ],
          "offset": 0,
          "page": 1,
          "size": 1,
          "total": 1,
          "totalPage": 1
      },
      "msg": "一切ok"
  }
  ```

### 1.3.5，导出数据

* 请求路径：`GET /ids/detectionRefuseServer/export`

* 请求参数示例：

  ```json
  /ids/detectionRefuseServer/export?ids=
  ```

* 响应参数示例

  ```json
  文件流
  ```

### 1.3.6，导入数据

* 请求路径：`POST /ids/detectionRefuseServer/import`

* 请求参数示例：

  ```json
  form-data
  file : 文件流
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": "导入成功",
      "msg": "一切ok"
  }
  ```

## 1.4，弱口令配置

### 1.4.1，新增数据

* 请求路径：`POST /ids/detectionWeakPwd/insert`

* 请求参数示例：

  ```json
  {
    "name": "通用系统弱口令集合",
    "remark": "适用于各类操作系统的常见弱口令列表",
    "prevalence": "流行",
    "weakPwd": ["admin", "root", "password", "123456", "12345678"]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.4.2，修改数据

* 请求路径：`PUT /ids/detectionWeakPwd/modify`

* 请求参数示例：

  ```json
  {
      "id": 1,
    "name": "通用系统弱口令集合",
    "remark": "适用于各类操作系统的常见弱口令列表",
    "prevalence": "流行",
    "weakPwd": ["admin", "root", "password", "123456", "12345678"]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.4.3，删除数据

* 请求路径：`DELETE /ids/detectionWeakPwd/delete`

* 请求参数示例：

  ```json
  {
    "id": [1, 2]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.4.4，分页查询数据

* 请求路径：`POST /ids/detectionWeakPwd/pageData`

* 请求参数示例：

  ```json
  {
      "page": 1,
      "size": 1,
      "name": "",
      "startTime": "2000-07-20 00:00:00", // 非必填
      "endTime": "2026-07-27 23:00:00" // 非必填
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": {
          "data": [
              {
                  "createTime": 1754318019000,
                  "createdBy": "684b4e34ce7645309d3972d57227d9f6",
                  "id": 2,
                  "name": "通用系统弱口令集合",
                  "prevalence": "流行",
                  "remark": "适用于各类操作系统的常见弱口令列表",
                  "updateTime": 1754318019000,
                  "updatedBy": "684b4e34ce7645309d3972d57227d9f6",
                  "weakPwd": [
                      "admin",
                      "root",
                      "password",
                      "123456",
                      "12345678"
                  ]
              }
          ],
          "offset": 0,
          "page": 1,
          "size": 1,
          "total": 1,
          "totalPage": 1
      },
      "msg": "一切ok"
  }
  ```

### 1.4.5，导出数据

* 请求路径：`GET /ids/detectionWeakPwd/export`

* 请求参数示例：

  ```json
  /ids/detectionWeakPwd/export?ids=
  ```

* 响应参数示例

  ```json
  文件流
  ```

### 1.4.6，导入数据

* 请求路径：`POST /ids/detectionWeakPwd/import`

* 请求参数示例：

  ```json
  form-data
  file : 文件流
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": "导入成功",
      "msg": "一切ok"
  }
  ```

## 1.5，病毒检测配置

### 1.5.1，新增数据

* 请求路径：`POST /ids/detectionVirus/insert`

* 请求参数示例：

  ```json
  {
    "name": "企业终端病毒防护配置",
    "engineIp": "*************",
    "engineName": "企业级杀毒引擎V4.2",
    "engineRemark": "支持实时监控、定时扫描和恶意代码隔离的综合引擎",
    "remark": "针对办公终端的病毒防护策略，包含U盘防护和邮件附件扫描",
    "strategyGroup": "实时防护策略,定时全盘扫描策略,恶意邮件拦截策略"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.5.2，修改数据

* 请求路径：`PUT /ids/detectionVirus/modify`

* 请求参数示例：

  ```json
  {
      "id": 1,
    "name": "企业终端病毒防护配置",
    "engineIp": "*************",
    "engineName": "企业级杀毒引擎V4.2",
    "engineRemark": "支持实时监控、定时扫描和恶意代码隔离的综合引擎",
    "remark": "针对办公终端的病毒防护策略，包含U盘防护和邮件附件扫描",
    "strategyGroup": "实时防护策略,定时全盘扫描策略,恶意邮件拦截策略"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.5.3，删除数据

* 请求路径：`DELETE /ids/detectionVirus/delete`

* 请求参数示例：

  ```json
  {
    "id": [1, 2]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.5.4，分页查询数据

* 请求路径：`POST /ids/detectionVirus/pageData`

* 请求参数示例：

  ```json
  {
      "page": 1,
      "size": 1,
      "name": "",
      "startTime": "2000-07-20 00:00:00", // 非必填
      "endTime": "2026-07-27 23:00:00" // 非必填
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": {
          "data": [
              {
                  "createTime": 1754318143000,
                  "createdBy": "684b4e34ce7645309d3972d57227d9f6",
                  "engineIp": "*************",
                  "engineName": "企业级杀毒引擎V4.2",
                  "engineRemark": "支持实时监控、定时扫描和恶意代码隔离的综合引擎",
                  "id": 1,
                  "name": "企业终端病毒防护配置",
                  "remark": "针对办公终端的病毒防护策略，包含U盘防护和邮件附件扫描",
                  "strategyGroup": "实时防护策略,定时全盘扫描策略,恶意邮件拦截策略",
                  "updateTime": 1754318143000,
                  "updatedBy": "684b4e34ce7645309d3972d57227d9f6"
              }
          ],
          "offset": 0,
          "page": 1,
          "size": 1,
          "total": 1,
          "totalPage": 1
      },
      "msg": "一切ok"
  }
  ```

### 1.5.5，导出数据

* 请求路径：`GET /ids/detectionVirus/export`

* 请求参数示例：

  ```json
  /ids/detectionVirus/export?ids=
  ```

* 响应参数示例

  ```json
  文件流
  ```

### 1.5.6，导入数据

* 请求路径：`POST /ids/detectionVirus/import`

* 请求参数示例：

  ```json
  form-data
  file : 文件流
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": "导入成功",
      "msg": "一切ok"
  }
  ```

## 1.6，隐藏信道库配置

### 1.6.1，新增数据

* 请求路径：`POST /ids/detectionChannelLibrary/insert`

* 请求参数示例：

  ```json
  {
    "name": "可疑URL检测规则",
    "type": "URL",
    "url": "http://suspicious-site.com/secret.php",
    "characteristic": "包含隐藏数据传输功能的可疑页面",
    "md5": "f1e2d3c4b5a6f7e8d9c0b1a2f3e4d5c6",
    "issueStatus": "未下发"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.6.2，修改数据

* 请求路径：`PUT /ids/detectionChannelLibrary/modify`

* 请求参数示例：

  ```json
  {
      "id": 1,
    "name": "可疑URL检测规则",
    "type": "URL",
    "url": "http://suspicious-site.com/secret.php",
    "characteristic": "包含隐藏数据传输功能的可疑页面",
    "md5": "f1e2d3c4b5a6f7e8d9c0b1a2f3e4d5c6",
    "issueStatus": "未下发"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.6.3，删除数据

* 请求路径：`DELETE /ids/detectionChannelLibrary/delete`

* 请求参数示例：

  ```json
  {
    "id": [1, 2]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.6.4，分页查询数据

* 请求路径：`POST /ids/detectionChannelLibrary/pageData`

* 请求参数示例：

  ```json
  {
      "page": 1,
      "size": 1,
      "name": "",
      "startTime": "2000-07-20 00:00:00", // 非必填
      "endTime": "2026-07-27 23:00:00" // 非必填
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": {
          "data": [
              {
                  "characteristic": "包含隐藏数据传输功能的可疑页面",
                  "createTime": 1754318196000,
                  "createdBy": "684b4e34ce7645309d3972d57227d9f6",
                  "id": 1,
                  "issueStatus": "未下发",
                  "md5": "f1e2d3c4b5a6f7e8d9c0b1a2f3e4d5c6",
                  "name": "可疑URL检测规则",
                  "type": "URL",
                  "updateTime": 1754318196000,
                  "updatedBy": "684b4e34ce7645309d3972d57227d9f6",
                  "url": "http://suspicious-site.com/secret.php"
              }
          ],
          "offset": 0,
          "page": 1,
          "size": 1,
          "total": 1,
          "totalPage": 1
      },
      "msg": "一切ok"
  }
  ```

### 1.6.5，导出数据

* 请求路径：`GET /ids/detectionChannelLibrary/export`

* 请求参数示例：

  ```json
  /ids/detectionChannelLibrary/export?ids=
  ```

* 响应参数示例

  ```json
  文件流
  ```

### 1.6.6，导入数据

* 请求路径：`POST /ids/detectionChannelLibrary/import`

* 请求参数示例：

  ```json
  form-data
  file : 文件流
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": "导入成功",
      "msg": "一切ok"
  }
  ```

### 1.6.7，下发数据

* 请求路径：`POST /ids/detectionChannelLibrary/issue`

* 请求参数示例

  ```json
  {
    "id": 1,
    "engineName": "123",
    "engineIp": "*******"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

## 1.7，组件配置

### 1.7.1，新增数据

* 请求路径：`POST /ids/detectionComponent/insert`

* 请求参数示例：

  ```json
  {
    "name": "入侵检测引擎A",
    "type": "引擎",
    "ip": "*********",
    "remark": "负责核心业务区域的入侵行为检测，支持实时流量分析",
    "authorize": "正式授权",
    "strategyGroup": "全量的策略集"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.7.2，修改数据

* 请求路径：`PUT /ids/detectionComponent/modify`

* 请求参数示例：

  ```json
  {
      "id": 1,
    "name": "入侵检测引擎A",
    "type": "引擎",
    "ip": "*********",
    "remark": "负责核心业务区域的入侵行为检测，支持实时流量分析",
    "authorize": "正式授权",
    "strategyGroup": "全量的策略集"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.7.3，删除数据

* 请求路径：`DELETE /ids/detectionComponent/delete`

* 请求参数示例：

  ```json
  {
    "id": [1, 2]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.7.4，分页查询数据

* 请求路径：`POST /ids/detectionComponent/pageData`

* 请求参数示例：

  ```json
  {
      "page": 1,
      "size": 1,
      "name": "",
      "startTime": "2000-07-20 00:00:00", // 非必填
      "endTime": "2026-07-27 23:00:00" // 非必填
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": {
          "data": [
              {
                  "authorize": "正式授权",
                  "createTime": 1754318440000,
                  "createdBy": "684b4e34ce7645309d3972d57227d9f6",
                  "id": 1,
                  "ip": "*********",
                  "name": "入侵检测引擎A",
                  "remark": "负责核心业务区域的入侵行为检测，支持实时流量分析",
                  "strategyGroup": "全量的策略集",
                  "type": "引擎",
                  "updateTime": 1754318440000,
                  "updatedBy": "684b4e34ce7645309d3972d57227d9f6"
              }
          ],
          "offset": 0,
          "page": 1,
          "size": 1,
          "total": 1,
          "totalPage": 1
      },
      "msg": "一切ok"
  }
  ```

### 1.7.5，导出数据

* 请求路径：`GET /ids/detectionComponent/export`

* 请求参数示例：

  ```json
  /ids/detectionComponent/export?ids=
  ```

* 响应参数示例

  ```json
  文件流
  ```

### 1.7.6，导入数据

* 请求路径：`POST /ids/detectionComponent/import`

* 请求参数示例：

  ```json
  form-data
  file : 文件流
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": "导入成功",
      "msg": "一切ok"
  }
  ```

## 1.8，动态引擎配置

### 1.8.1，新增数据

* 请求路径：`POST /ids/detectionEngine/insert`

* 请求参数示例：

  ```json
  {
    "name": "智能威胁检测引擎V3.0",
    "engineIp": "************",
    "remark": "支持AI动态分析的新一代威胁检测引擎，可实时更新病毒库",
    "username": "engine_admin",
    "password": "EncryptedPwd@2024",
    "connectStatus": "在线"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.8.2，修改数据

* 请求路径：`PUT /ids/detectionEngine/modify`

* 请求参数示例：

  ```json
  {
      "id": 1,
    "name": "智能威胁检测引擎V3.0",
    "engineIp": "************",
    "remark": "支持AI动态分析的新一代威胁检测引擎，可实时更新病毒库",
    "username": "engine_admin",
    "password": "EncryptedPwd@2024",
    "connectStatus": "在线"
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.8.3，删除数据

* 请求路径：`DELETE /ids/detectionEngine/delete`

* 请求参数示例：

  ```json
  {
    "id": [1, 2]
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "msg": "一切ok"
  }
  ```

### 1.8.4，分页查询数据

* 请求路径：`POST /ids/detectionEngine/pageData`

* 请求参数示例：

  ```json
  {
      "page": 1,
      "size": 1,
      "name": "",
      "startTime": "2000-07-20 00:00:00", // 非必填
      "endTime": "2026-07-27 23:00:00" // 非必填
  }
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": {
          "data": [
              {
                  "connectStatus": "在线",
                  "createTime": 1754318544000,
                  "createdBy": "684b4e34ce7645309d3972d57227d9f6",
                  "engineIp": "************",
                  "id": 1,
                  "name": "智能威胁检测引擎V3.0",
                  "password": "EncryptedPwd@2024",
                  "remark": "支持AI动态分析的新一代威胁检测引擎，可实时更新病毒库",
                  "updateTime": 1754318544000,
                  "updatedBy": "684b4e34ce7645309d3972d57227d9f6",
                  "username": "engine_admin"
              }
          ],
          "offset": 0,
          "page": 1,
          "size": 1,
          "total": 1,
          "totalPage": 1
      },
      "msg": "一切ok"
  }
  ```

### 1.8.5，导出数据

* 请求路径：`GET /ids/detectionEngine/export`

* 请求参数示例：

  ```json
  /ids/detectionEngine/export?ids=
  ```

* 响应参数示例

  ```json
  文件流
  ```

### 1.8.6，导入数据

* 请求路径：`POST /ids/detectionEngine/import`

* 请求参数示例：

  ```json
  form-data
  file : 文件流
  ```

* 响应参数示例

  ```json
  {
      "code": "00000",
      "data": "导入成功",
      "msg": "一切ok"
  }
  ```

# 2，事件管理

# 3，文件检测配置

# 4，URL信誉库

# 5，任务管理

