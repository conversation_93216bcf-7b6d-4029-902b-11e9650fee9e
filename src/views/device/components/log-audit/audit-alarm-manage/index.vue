<template>
  <div class="right-content">
    <DynamicSearch :config="SEARCH_CONFIG" @search="handleSearch" @reset="handleSearch" />
    <a-space align="end" class="table-operate">
      <a-button type="primary" @click="handleDownload">
        导出
      </a-button>
    </a-space>
    <a-table
      :scroll="{y: 'calc(100vh - 370px)', x: 1445}"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button type="link" @click="handleOpenDialog('view', record)"
            >查看</a-button
          >
          <a-button type="link" @click="handleOpenDialog('edit', record)"
            >编辑</a-button
          >
          <a-button type="link" danger @click="handleDelete(record)"
            >删除</a-button
          >
        </template>
      </template>
    </a-table>
  </div>
</template>

<script>
import DynamicSearch from "@/components/dynamic-search/index.vue";
import { SEARCH_CONFIG, COLUMNS } from "./config";
import { usePagination } from "vue-request";
import { computed, getCurrentInstance, reactive, createVNode, toRefs, ref, inject } from "vue";
import { getDeviceLogAuditAlarmList } from "@/request/api-device-log";
import { downloadFile } from '@/utils/util';

export default {
  components: {
    DynamicSearch,
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const { safetyId } = inject("rowData");

    const state = reactive({
      searchParams: {label: '', timeRange: []},
    })

    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getDeviceLogAuditAlarmList,{
      manual: true,
      defaultParams: {
        safetyId,
        ...state.searchParams
      },
      formatResult: ({data = {}}) => {
        console.log(data)
        return { items: data?.data ?? [], total: data.total ?? 0 }
      },
      pagination: {
        currentKey: "page",
        pageSizeKey: "size"
      },
    });

    /**
     * 计算分页配置
     */
    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`,
    }));

    /**
     * 计算表格数据源
     */
    const dataSource = computed(() => {
      return data.value?.items || [];
    });

    /**
     * 处理表格分页变化
     * @param {Object} pag 分页参数
     */
    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      run({
        safetyId,
        size: pag.pageSize,
        page: pag?.current,
        ...state.searchParams
      })
    }

    /**
     * 处理搜索
     * @param {Object} params 搜索参数
     */
    const handleSearch = (params = {}) => {
      state.searchParams = params;
      refreshTableData();
    }

    /**
     * 刷新表格数据
     * @param {boolean} isReload 是否重新加载第一页
     */
    const refreshTableData = (isReload = true) => {
      run({
        safetyId,
        page: isReload ? 1 : pagination.value.current,
        size: pagination.value?.pageSize,
        ...state.searchParams
      })
    }

    /**
     * 导出数据
     */
    const handleDownload = () => {
      downloadFile(`/device-atomic/log/qm/export/exportAlarm?safetyId=${safetyId}`)
    }

    const columns = computed(() => {
      return COLUMNS.map(column => {
        if (column.customRender) {
          return {
            ...column,
            customRender: (args) => column.customRender(args, pagination.value)
          };
        }
        return column;
      });
    });

    return {
      ...toRefs(state),
      SEARCH_CONFIG,
      columns,
      loading,
      dataSource,
      pagination,
      handleTableChange,
      handleSearch,
      handleDownload
    };
  },
};
</script>

<style lang="less" scoped>
.table-operate {
  margin: 8px 0;
  display: flex;
  justify-content: flex-end;
}
</style>
