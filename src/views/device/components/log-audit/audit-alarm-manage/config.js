import dayjs from 'dayjs'
import { useAsyncDataSource } from "@/utils/util"

export const SEARCH_CONFIG = [
  {
    title: '告警类型',
    name: 'label',
    default: '',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入告警类型',
    },
  },
  {
    title: '告警发生时间',
    name: 'timeRange',
    default: [],
    type: 'array',
    'x-component': 'DatePicker.RangePicker',
    'x-component-props': {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss',
      placeholder: ['开始时间', '结束时间']
    },
  },
]


export const COLUMNS = [
  {
    title: "序号",
    dataIndex: "num",
    width: 80,
    align: "center",
    fixed: 'left',
    customRender({ index }, pagination) {
      return pagination.pageSize * (pagination.current - 1) + index + 1;
    }
  },
  {
    title: '告警名称',
    dataIndex: 'alertName',
    key: 'alertName',
    width: 150,
    fixed: 'left'
  },
  {
    title: '告警分类',
    dataIndex: 'alertType',
    key: 'alertType',
    width: 120
  },
  {
    title: '告警级别',
    dataIndex: 'level',
    key: 'level',
    width: 120,
    customRender: ({ record }) => {
      // 高:5 中高:4, 中:3, 中低:2, 低:1
      const map = {
        1: '低',
        2: '中低',
        3: '中',
        4: '中高',
        5: '高'
      }
      return <span>{map[record.level]}</span>
    }
  },
  {
    title: '告警发生时间',
    dataIndex: 'startTime',
    key: 'startTime',
    width: 165,
    customRender: ({ record }) => {
      const startTime = record.startTime ? dayjs().format('YYYY-MM-DD HH:mm:ss') : ''
      return <span>{startTime}</span>
    }
  },
  {
    title: '设备地址',
    dataIndex: 'devIp',
    key: 'devIp',
    width: 120,
  },
  {
    title: '设备类型',
    dataIndex: 'devType',
    key: 'devType',
    width: 120,
  },
  {
    title: '告警状态',
    dataIndex:'status',
    key:'status',
    width: 120,
    customRender: ({ record }) => {
      // (0-待定,1-已确认,2已处理)
      const map = {
        0: '待定',
        1: '已确认',
        2: '已处理'
      }
      return <span>{map[record.status]}</span>
    }
  },
  {
    title: '报送次数',
    dataIndex: 'alertCount',
    key: 'alertCount',
    width: 120,
  },
  {
    title: '是否升级',
    dataIndex: 'isUpgrade',
    key: 'isUpgrade',
    width: 120,
    customRender: ({ record }) => {
      const category = record.isUpgrade? '是' : '否'
      return <span>{category}</span>
    }
  },
  {
    title: '告警内容描述',
    dataIndex: 'description',
    key: 'description',
    width: 300,
  }
]

/** @type {*} 表单schema */
export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        name: {
          type: 'string',
          title: '策略名称',
          'x-validator': [
            { required: true, message: '请输入策略名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入策略名称'
          }
        },
        serverType: {
          type: 'string',
          title: '服务器对象',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '服务器对象'
          },
          'x-decorator': 'FormItem',
          'x-validator': [
            { required: true, message: '请选择' },
          ],
          'x-reactions': useAsyncDataSource(async () => {
            return []
          })
        },
        addressType: {
          type: 'string',
          title: '源IP地址对象',
          'x-validator': [
            { required: true, message: '请选择' },
          ],
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请输入源IP地址对象'
          },
          'x-decorator': 'FormItem',
          'x-reactions': useAsyncDataSource(async () => {
            return []
          })
        },
        timeObject: {
          type: 'string',
          title: '时间对象',
          'x-component': 'Select',
          'x-validator': [
            { required: true, message: '请选择' },
          ],
          'x-component-props': {
            placeholder: '请输入时间对象'
          },
          'x-decorator': 'FormItem',
          'x-reactions': useAsyncDataSource(async () => {
            return []
          })
        },
        eventLevel: {
          type: 'string',
          title: '事件级别',
          'x-validator': [
            { required: true, message: '请选择' },
          ],
          'x-component': 'Radio.Group',
          'x-component-props': {
            placeholder: '请选择事件级别'
          },
          'x-decorator': 'FormItem',
          enum: [
            { label: '低', value: '低' },
            { label: '中', value: '中' },
            { label: '高', value: '高' },
            { label: '极高', value: '极高' },
          ]
        },
        periods: {
          type: 'array',
          'x-validator': [
            { required: true, message: '请添加规则对象' }
          ],
          title: '规则对象',
          'x-decorator': 'FormItem',
          'x-component': 'ArrayTable',
          'x-component-props': {
            rowKey: 'index',
            pagination: { pageSize: 10 },
            scroll: { x: '100%' },
          },
          items: {
            type: 'object',
            properties: {
              column1: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '规则名称', key: 'name', dataIndex: 'name' },
                ellipsis: true,
                properties: {
                  name: {
                    type: 'string',
                    'x-validator': [
                      { required: true, message: '请选择规则' }
                    ],
                    'x-decorator': 'FormItem',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '规则名称',
                    },
                    'x-reactions': useAsyncDataSource(async () => {
                      return []
                    })
                  }
                }
              },
              column2: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': { title: '规则分类名称', width: 240, key: 'classifyName', dataIndex: 'classifyName' },
                ellipsis: true,
                properties: {
                  classifyName: {
                    type: 'string',
                    'x-validator': [
                      { required: true, message: '请选择规则分类' }
                    ],
                    'x-decorator': 'FormItem',
                    'x-component': 'Select',
                    'x-component-props': {
                      placeholder: '规则分类分类',
                    },
                    'x-reactions': useAsyncDataSource(async () => {
                      return []
                    })
                  }
                }
              },
              column3: {
                type: 'void',
                'x-component': 'ArrayTable.Column',
                'x-component-props': {
                  title: '操作',
                  dataIndex: 'operations',
                  ellipsis: true,
                  width: 70,
                  fixed: 'right'
                },
                properties: {
                  item: {
                    type: 'void',
                    'x-component': 'FormItem',
                    properties: {
                      remove: {
                        type: 'void',
                        'x-component': 'ArrayTable.Remove'
                      }
                    }
                  }
                }
              }
            }
          },
          properties: {
            add: {
              type: 'void',
              'x-component': 'ArrayTable.Addition',
              title: '添加'
            }
          }
        },
        responseMode: {
          type: 'string',
          title: '响应方式',
          'x-validator': [
            { required: true, message: '请选择响应方式' },
          ],
          'x-component': 'Checkbox.Group',
          'x-component-props': {
            placeholder: '请选择响应方式'
          },
          'x-decorator': 'FormItem',
          enum: [
            { label: '告警', value: '告警' },
            { label: '会话阻断', value: '会话阻断' },
          ]
        },
        alarmNotice: {
          type: 'string',
          title: '告警通知',
          'x-validator': [
            { required: true, message: '请选择告警通知' },
          ],
          'x-component': 'Checkbox.Group',
          'x-component-props': {
            placeholder: '请选择告警通知'
          },
          'x-decorator': 'FormItem',
          enum: [
            { label: 'Email通知', value: 'Email通知' },
            { label: '短信通知', value: '短信通知' },
          ]
        },
        alarmReceiver: {
          type: 'string',
          title: '收件人',
          'x-validator': [
            { required: true, message: '请输入收件人' },
          ],
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入收件人'
          },
          'x-decorator': 'FormItem',
        },
        emailSubject: {
          type: 'string',
          title: '邮件主题',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入邮件主题'
          },
          'x-decorator': 'FormItem',
        },
        telePhone: {
          type: 'string',
          title: '手机号',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入手机号'
          },
          'x-decorator': 'FormItem',
        },
        masterId: {
          type: 'string',
          title: '等保级别',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择等保级别'
          },
          'x-decorator': 'FormItem',
        },
        sharedConfig: {
          type: 'string',
          title: '共享配置',
          'x-component': 'Checkbox.Group',
          'x-component-props': {
            placeholder: '请输入共享配置'
          },
          'x-decorator': 'FormItem',
          enum: [
            { label: 'SYSLOG转发', value: 'SYSLOG转发' },
            { label: 'SFTP转发', value: 'SFTP转发' },
            { label: 'SNMP转发', value: 'SNMP转发' },
          ]
        },
      }
    },
  }
}
