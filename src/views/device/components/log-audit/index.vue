<!-- 日志审计 配置弹窗 -->

<template>
  <a-drawer
    v-model:visible="dialogVisible"
    placement="right"
    width="80%"
    :keyboard="false"
    :maskClosable="false"
    :onClose="handleClose"
  >
    <template #title>
      <a-tabs type="card" v-model:activeKey="activeKey">
        <a-tab-pane
          v-for="tab in LEAK_SCAN_TABS"
          :key="tab.componentName"
          :tab="tab.label"
        >
        </a-tab-pane>
      </a-tabs>
    </template>
    <a-spin :spinning="loading">
      <component :is="activeKey" />
    </a-spin>
  </a-drawer>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, provide, reactive, watch } from 'vue'
import { LEAK_SCAN_TABS } from './config';
import AuditAssetManage from './audit-asset-manage/index.vue';
import AuditAlarmManage from './audit-alarm-manage/index.vue';
import AuditPolicyManage from './audit-policy-manage/index.vue'


export default defineComponent({
  name: 'LeakScan',
  components: {
    AuditAssetManage,
    AuditAlarmManage,
    AuditPolicyManage
  },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    rowData: {
      type: Object,
       default: () => ({})
    }
  },
  emits: ['update:visible'],
  setup (props, ctx) {
    const { visible, rowData } = toRefs(props)
    const dialogVisible = computed(() => visible.value)

    provide('rowData', {
      rowData,
      safetyId: rowData.value.id,
    })

    const state = reactive({
      loading: false,
      activeKey: LEAK_SCAN_TABS[0].componentName
    })

    watch(
      () => visible.value,
      async (val) => {
        console.log('log-audit-drawer-visible: ', val)
      },
      { immediate: true }
    )

    const handleClose = async () => {
      ctx.emit('update:visible', false)
    }

    return {
      ...toRefs(state),
      LEAK_SCAN_TABS,
      dialogVisible,
      handleClose
    }
  }
})
</script>
