<template>
  <a-modal
    v-model:visible="dialogVisible"
    :title="title"
    width="800px"
    :keyboard="false"
    :maskClosable="false"
    @cancel="handleClose"
  >
    <a-spin :spinning="loading">
      <FormProvider :form="form">
        <SchemaField :schema="schema" :scope="formScope"></SchemaField>
      </FormProvider>
    </a-spin>
    <template #footer>
      <a-button
        v-if="!isView"
        type="primary"
        :loading="loading"
        @click="handleSave">保存</a-button>
      <a-button :type="isView ? 'primary' : 'default'" @click="handleClose">{{isView ? '关闭' : '取消'}}</a-button>
    </template>
  </a-modal>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, getCurrentInstance, inject, nextTick, shallowRef } from 'vue'
import { createForm } from '@formily/core'
import { cloneDeep, keyBy } from 'lodash'

import { getDeviceLogAuditCategoryList, getDeviceLogAuditCompanyList } from '@/request/api-device-log.js'

import { SCHEMA } from './config';

export default defineComponent({
props: {
  visible: {
    type: Boolean,
    default: false
  },
  mode: {
    type: String,
    default: 'add'
  },
  defaultData: {
    type: Object,
    default: () => ({})
  }
},
emits: ['update:visible', 'on-success'],
setup (props, ctx) {
  const { proxy } = getCurrentInstance()
  const { visible, mode, defaultData } = toRefs(props)
  // const isAdd = computed(() => mode.value === 'add')
  const isView = computed(() => mode.value === 'view')
  const dialogVisible = computed(() => visible.value)
  const title = computed(() => {
    const titleMap = {
      add: '新增',
      edit: '编辑',
      view: '查看'
    }
    return `${titleMap[mode.value]}资产`
  })

  const state = reactive({
    loading: false
  })

  const dataSourceMap = shallowRef({
    groupMap: {},
    companyMap: {}
  })

  const form = createForm()

  watch(
    () => visible.value,
    async (val) => {
      if (!val) return
      await nextTick()
      await initForm()
    }
  )

  /**
   * 格式化树形数据源
   * @param {Array} treeData 原始树形数据
   * @returns {Object} 返回格式化后的树形数据和映射
   */
  const formatTreeDataSource = (treeData = []) => {
    treeData = cloneDeep(treeData)
    if (!treeData.length) return {treeList: [], treeMap: {}}
    const treeMap = {}
    const recursion = (data = []) => {
      for (const item of data) {
        item.selectable = !item.children?.length
        item.label = item.node.name
        item.value = item.node.id
        if (item.children?.length) {
          recursion(item.children)
        }
        treeMap[item.id] = cloneDeep(item.node)
      }
      return data
    }
    return {
      treeList: recursion(treeData),
      treeMap
    }
  }

  /**
   * 获取分类数据源
   * @returns {Function} 返回获取数据源的函数
   */
  let groupReqPromise = null
  const getGroupDataSource = () => {
    return async (field) => {
      let data = []
      try {
        if (!groupReqPromise) groupReqPromise = getDeviceLogAuditCategoryList()
        const res = await groupReqPromise;
        const {treeList = [], treeMap = {}} = formatTreeDataSource(res.data ?? [])
        data = treeList
        dataSourceMap.value.groupMap = treeMap
      } finally {
        field.setDataSource(data ?? [])
      }
    }
  }

  /**
   * 获取厂商数据源
   * @returns {Function} 返回获取数据源的函数
   */
  let companyReqPromise = null
  const getCompanyDataSource = () => {
    return async (field) => {
      let data = []
      try {
        if (!companyReqPromise) companyReqPromise = getDeviceLogAuditCompanyList()
        const res = await companyReqPromise;
        data = res.data ?? []
        dataSourceMap.value.companyMap = keyBy(data, 'code')
      } finally {
        field.setDataSource(data?.map(({name, code}) => ({label: name, value: code})) ?? [])
      }
    }
  }

  /**
   * 初始化表单数据
   */
  const initForm = async () => {
    if (mode.value === 'add') return;
    const formData = cloneDeep(defaultData.value)
    form.setValues({...formData})
  }

  /**
   * 关闭弹窗处理
   * @param {boolean} hasError 是否有错误
   */
  const handleClose = async (hasError = false) => {
    state.loading = false
    if (hasError === true) return
    await form.setValues(cloneDeep(form.initialValues), 'overwrite')
    await form.reset()
    ctx.emit('update:visible', false)
  }

  /**
   * 保存表单数据
   */
  const handleSave = async () => {
    await form.validate()
    state.loading = true
    const params = cloneDeep(form.values)
    ctx.emit('on-success', {
      mode: mode.value,
      data: params,
      callback: handleClose
    })
  }

  return {
    title,
    isView,
    form,
    schema: SCHEMA,
    formScope: {
      getGroupDataSource,
      getCompanyDataSource
    },
    ...toRefs(state),
    dialogVisible,
    handleClose,
    handleSave
  }
}
})
</script>
