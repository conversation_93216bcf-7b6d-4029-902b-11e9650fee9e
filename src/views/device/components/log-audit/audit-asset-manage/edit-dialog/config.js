export const SCHEMA = {
  type: 'object',
  properties: {
    layout: {
      type: 'void',
      'x-component': 'FormLayout',
      'x-component-props': {
        labelWidth: 100
      },
      properties: {
        ip: {
          type: 'string',
          title: 'IP地址',
          'x-validator': [
            { required: true, message: '请输入IP地址' },
            {  format: 'ipv4' }
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入IP地址'
          },
        },
        ips: {
          type: 'string',
          title: '从IP地址',
          'x-decorator': 'FormItem',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入从IP地址'
          },
        },
        name: {
          type: 'string',
          title: '名称',
          'x-validator': [
            { required: true, message: '请输入名称' },
          ],
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入名称'
          },
        },
        type: {
          type: 'string',
          title: '类型',
          'x-validator': [
            { required: true, message: '请选择类型' },
          ],
          'x-component': 'TreeSelect',
          'x-component-props': {
            placeholder: '请选择类型',
            fieldNames: {children:'children', label:'label', key:'value', value: 'value'},
            treeDefaultExpandAll: true
          },
          'x-decorator': 'FormItem',
          'x-reactions': '{{getGroupDataSource()}}'
        },
        sn: {
          type: 'string',
          title: '序列号',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入序列号'
          },
          'x-decorator': 'FormItem',
        },
        // contact: {
        //   type: 'string',
        //   title: '联系人',
        //   'x-component': 'Select',
        //   'x-component-props': {
        //     placeholder: '请输入联系人'
        //   },
        //   'x-decorator': 'FormItem',
        //   'x-reactions': useAsyncDataSource(async () => {
        //     return []
        //   })
        // },
        mac: {
          type: 'string',
          title: 'MAC地址',
          'x-decorator': 'FormItem',
          'x-component': 'Input',
          'x-component-props': {
            placeholder: '请输入MAC地址'
          },
        },
        manufacturer: {
          type: 'string',
          title: '厂商',
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择厂商'
          },
          'x-decorator': 'FormItem',
          'x-reactions': '{{getCompanyDataSource()}}'
        },
        timePurchase: {
          type: 'string',
          title: '购置日期',
          'x-component': 'DatePicker',
          'x-component-props': {
            placeholder: '请输入购置日期',
            valueFormat: 'x',
          },
          'x-decorator': 'FormItem',
        },
        expiredDate: {
          type: 'string',
          title: '质保期限',
          'x-component': 'DatePicker',
          'x-component-props': {
            placeholder: '请输入质保期限',
            valueFormat: 'x'
          },
          'x-decorator': 'FormItem',
        },
        virtualType: {
          type: 'string',
          title: '设备形态',
          'x-validator': [
            { required: true, message: '请选择设备形态' },
          ],
          default: 0,
          'x-component': 'Select',
          'x-component-props': {
            placeholder: '请选择设备形态'
          },
          'x-decorator': 'FormItem',
          enum: [
            { label: '物理设备', value: 0 },
            { label: '虚拟设备', value: 2 }
          ],
        },
        description: {
          type: 'string',
          title: '描述',
          'x-decorator': 'FormItem',
          'x-component': 'Input.TextArea',
          'x-component-props': {
            placeholder: '请输入描述'
          },
        },
      }
    },
  }
}
