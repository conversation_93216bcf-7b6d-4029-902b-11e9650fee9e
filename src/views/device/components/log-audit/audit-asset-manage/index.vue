<template>
  <div class="asset-manage--wrapper">
    <div class="content-left">
      <a-input-search v-model:value="searchValue" placeholder="请输入关键字" />
      <a-tree
        ref="treeRef"
        class="tree-container"
        :tree-data="filteredTreeData"
        v-model:selected-keys="selectedKeys"
        v-model:expanded-keys="expandedKeys"
        :default-expand-all="true"
        :fieldNames="{ children: 'children', title: 'name', key: 'nodeId' }"
        @select="handleSelect"
      >
      <template #title="{ name, id, data }">
          <div class="node-item">
            <span class="node-item-label">{{ name }}</span>
            <span class="node-item-btn">
              <a-tooltip>
                <template #title>新增</template>
                <a-button type="link" @click.stop="handleOpenGroupDialog('add', data)">
                  <PlusSquareOutlined />
                </a-button>
              </a-tooltip>
              <template v-if="id !== ROOT_NODE.id">
                <a-tooltip>
                  <template #title>编辑</template>
                  <a-button type="link" @click.stop="handleOpenGroupDialog('edit', data)">
                    <FormOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip>
                  <template #title>删除</template>
                  <a-button type="link" danger @click.stop="handleDeleteGroup(data)">
                    <DeleteOutlined />
                  </a-button>
                </a-tooltip>
              </template>
            </span>
          </div>
        </template>
      </a-tree>
    </div>
    <div class="content-right">
      <a-space align="end" class="table-operate">
        <a-button type="primary" @click="handleOpenAssetDialog('add')">
          新增
        </a-button>
        <a-button type="primary" @click="handleDownload"> 导出 </a-button>
      </a-space>
      <CommonTable
        :rowSelection="rowSelection"
        :rowKey="(record) => record.id"
        ref="tableRef"
        :columns="columns"
        :fetchData="fetchTableData"
      >
        <template #operation="{ record }">
          <div>
            <a-button type="link" @click="handleOpenAssetDialog('view', record)"
              >查看</a-button
            >
            <a-button type="link" @click="handleOpenAssetDialog('edit', record)"
              >编辑</a-button
            >
            <a-button type="link" danger @click="handleDeleteAsset(record)"
              >删除</a-button
            >
          </div>
        </template>
      </CommonTable>
      <EditDialog
        v-model:visible="assetDialogConfig.visible"
        v-bind="assetDialogConfig"
        @on-success="handleSaveAsset" />
      <GroupDialog
        v-model:visible="groupDialogConfig.visible"
        v-bind="groupDialogConfig"
        @on-success="handleSaveGroup" />
    </div>
  </div>
</template>

<script>
import {
  getCurrentInstance,
  createVNode,
  reactive,
  toRefs,
  ref,
  watch,
  nextTick,
  computed,
  inject,
} from "vue";
import { lowerCase } from 'lodash'
import { ExclamationCircleOutlined, PlusSquareOutlined, FormOutlined, DeleteOutlined } from "@ant-design/icons-vue";

import { COLUMNS} from "./config";
import EditDialog from "./edit-dialog/index.vue";
import GroupDialog from './group-dialog/index.vue'
import CommonTable from "@/components/common-table/index.vue";
import {
  getDeviceLogAuditList,
  addDeviceLogAudit,
  updateDeviceLogAudit,
  deleteDeviceLogAudit,
  getDeviceLogAuditGroupList,
  addDeviceLogAuditGroup,
  updateDeviceLogAuditGroup,
  deleteDeviceLogAuditGroup
} from "@/request/api-device-log";
import { downloadFile } from "@/utils/util";

const ROOT_NODE = { id: 1, parentId: -1, name: '安全域', nodeId: 1 }

export default {
  components: {
    PlusSquareOutlined,
    FormOutlined,
    DeleteOutlined,
    EditDialog,
    CommonTable,
    GroupDialog
  },
  setup() {
    const { proxy } = getCurrentInstance();
    const safetyId = inject("deviceSafetyId");
    const tableRef = ref(null);

    const selectedTableKeys = ref([]);
    const rowSelection = computed(() => {
      return {
        selectedRowKeys: selectedTableKeys.value,
        onChange(selectedRowKeys, selectedRows) {
          selectedTableKeys.value = selectedRowKeys ?? [];
        },
      };
    });

    /**
     * 删除资产
     * @param {Object} record 资产记录
     */
    const handleDeleteAsset = async ({ id }) => {
      proxy.$confirm({
        title: "删除",
        content: "删除后信息将无法找回，是否进行删除?",
        icon: createVNode(ExclamationCircleOutlined),
        okText: "确定",
        okType: "warning",
        cancelText: "取消",
        async onOk() {
          await deleteDeviceLogAudit({ id });
          tableRef.value.refresh();
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    };

    /**
     * 获取表格数据
     * @param {Object} params 查询参数
     * @returns {Promise<Object>} 返回表格数据和总数
     */
    async function fetchTableData(params) {
      const { data = {} } = await getDeviceLogAuditList({
        ...params,
        groupId: treeState.selectedKeys[0],
      });
      return { items: data?.data ?? [], total: data?.total ?? 0 };
    }

    const assetDialogConfig = ref({
      visible: false,
      mode: 'add',
      defaultData: {}
    })

    /**
     * 打开资产编辑弹窗
     * @param {string} mode 操作模式
     * @param {Object} node 节点数据
     */
    const handleOpenAssetDialog = (mode, node) => {
      assetDialogConfig.value.visible = true
      assetDialogConfig.value.mode = mode
      assetDialogConfig.value.defaultData = node
    }

    /**
     * 保存资产数据
     * @param {Object} options 保存选项
     */
    const handleSaveAsset = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addDeviceLogAudit,
        edit: updateDeviceLogAudit
      }
      data.groupId ??= treeState.selectedKeys[0] ?? ROOT_NODE.id
      try {
        const res = await requestMethodEnum[mode]?.({...data, safetyId: safetyId.value})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        getTreeData()
      } catch (err) {
        // const errMsg = isAdd ? '新增失败！' : '更新失败！';
        // proxy.$message.error(err.message || errMsg)
        callback(true, err)
      }
    }

    const handleRefresh = () => {
      tableRef.value.refresh();
    };

    const treeState = reactive({
      treeData: [],
      searchValue: '',
      expandedKeys: [ROOT_NODE.id],
      selectedKeys: [],
      groupDialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    });

    /**
     * 获取树形数据
     */
    async function getTreeData() {
      const { data = [] } = await getDeviceLogAuditGroupList();
      treeState.treeData = [{...ROOT_NODE, children: data ?? []}];
      treeState.selectedKeys = [ROOT_NODE.id];
    }

    /**
     * 处理树节点选择
     * @param {Array} selectedKeys 选中的节点keys
     */
    function handleSelect(selectedKeys) {
      treeState.selectedKeys = selectedKeys
      handleRefresh()
    }

    /**
     * 导出数据
     */
    function handleDownload() {
      downloadFile(
        `/device-atomic/log/qm/export/exportAsset?safetyId=${
          safetyId.value
        }&id=${selectedTableKeys.value.join(",")}`
      );
    }
    getTreeData();

    /**
     * 打开分组编辑弹窗
     * @param {string} mode 操作模式
     * @param {Object} node 节点数据
     */
    const handleOpenGroupDialog = (mode, node) => {
      treeState.groupDialogConfig.visible = true
      treeState.groupDialogConfig.mode = mode
      treeState.groupDialogConfig.defaultData = node
    }

    /**
     * 保存分组数据
     * @param {Object} options 保存选项
     */
    const handleSaveGroup = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addDeviceLogAuditGroup,
        edit: updateDeviceLogAuditGroup
      }
      try {
        const res = await requestMethodEnum[mode]?.({...data, safetyId: safetyId.value})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        getTreeData()
      } catch (err) {
        // const errMsg = isAdd ? '新增失败！' : '更新失败！';
        // proxy.$message.error(err.message || errMsg)
        callback(true, err)
      }
    }

    /**
     * 删除分组
     * @param {Object} node 节点数据
     */
    const handleDeleteGroup = (node = {}) => {
      proxy.$confirm({
        title: "删除",
        content: "删除后信息将无法找回，是否进行删除?",
        icon: createVNode(ExclamationCircleOutlined),
        okText: "确定",
        okType: "warning",
        cancelText: "取消",
        async onOk() {
          await deleteDeviceLogAuditGroup({ safetyId: safetyId.value, id: node.nodeId });
          getTreeData();
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    }

    const columns = computed(() => {
      return COLUMNS.map(column => {
        if (column.customRender) {
          return {
            ...column,
            customRender: (args) => column.customRender(args, tableRef.value.pagination)
          };
        }
        return column;
      });
    });


    const filteredTreeData = computed(() => {
      if (!treeState.searchValue) return treeState.treeData;

      const filterTree = (nodes) => {
        return nodes
          .map(node => {
            const children = filterTree(node.children || []);
            if (lowerCase(node.name).includes(lowerCase(treeState.searchValue?.trim())) || children.length > 0) {
              return {
                ...node,
                children: children
              };
            }
            return null;
          })
          .filter(node => node !== null);
      };

      return filterTree(treeState.treeData);
    });

    return {
      ROOT_NODE,
      handleRefresh,
      columns,
      tableRef,
      fetchTableData,
      handleSelect,
      handleDownload,
      rowSelection,
      assetDialogConfig,
      handleOpenAssetDialog,
      handleDeleteAsset,
      handleSaveAsset,
      ...toRefs(treeState),
      handleOpenGroupDialog,
      handleDeleteGroup,
      handleSaveGroup,
      filteredTreeData
    };
  },
};
</script>

<style lang="less" scoped>
.table-operate {
  margin: 8px 0;
  display: flex;
  justify-content: flex-end;
}

.asset-manage--wrapper {
  display: flex;
  height: inherit;

  .content-left {
    width: 250px;
    max-height: calc(100vh - 135px);
    margin-right: 16px;
    padding: 16px;
    border: 1px solid #ccc;
    overflow: auto;

    ::v-deep .ant-tree-treenode {
      width: 100%;

      .ant-tree-node-content-wrapper {
        flex: 1;
      }
    }
  }

  .node-item {
    white-space: nowrap;
    padding-right: 72px;

    &-btn {
      display: none;

      .ant-btn {
        height: auto;
        padding: 0;
        margin-left: 8px;
      }
    }

    &:hover {
      .node-item-btn {
        display: inline-block;
      }
    }
  }

  .content-right {
    flex: 1;
    overflow: hidden;
  }
}
</style>
