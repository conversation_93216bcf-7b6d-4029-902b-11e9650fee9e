<template>
  <a-spin :spinning="loading">
    <div class="customer-manage--wrapper">
      <div class="content-left">
        <a-input v-model:value="searchValue" style="margin-bottom: 8px" placeholder="请输入" />
        <a-tree
          v-model:expanded-keys="expandedKeys"
          v-model:selected-keys="selectedKeys"
          :tree-data="treeData"
          :field-names="{title: 'name', key: 'id'}"
          :filterTreeNode="filterTreeNode"
          @select="handleSelectTreeNode"
        >
          <template #title="{ name, data }">
            <div class="node-item">
              <span class="node-item-label">{{ name }}</span>
              <span class="node-item-btn">
                <a-tooltip>
                  <template #title>新增</template>
                  <a-button type="link" @click.stop="handleOpenDialog('add', data)">
                    <PlusSquareOutlined />
                  </a-button>
                </a-tooltip>
                <a-tooltip v-if="['-1', -1].includes(data.parentId)">
                  <template #title>导出</template>
                  <a-button type="link" @click.stop="handleExportTree(data)">
                    <VerticalAlignBottomOutlined />
                  </a-button>
                </a-tooltip>
                <template v-else>
                  <a-tooltip>
                    <template #title>编辑</template>
                    <a-button type="link" @click.stop="handleOpenDialog('edit', data)">
                      <FormOutlined />
                    </a-button>
                  </a-tooltip>
                  <a-tooltip>
                    <template #title>删除</template>
                    <a-button type="link" danger @click.stop="handleDelete(data)">
                      <DeleteOutlined />
                    </a-button>
                  </a-tooltip>
                </template>
              </span>
            </div>
          </template>
        </a-tree>
      </div>
      <div class="content-right">
        <PolicyTable :selected-data="activeTreeNode" @refresh="fetchTreeData" />
      </div>
      <EditDialog
        v-model:visible="dialogConfig.visible"
        v-bind="dialogConfig"
        @on-success="handleSaveGroup" />
    </div>
  </a-spin>
</template>


<script lang="js">
import { defineComponent, createVNode, inject, toRefs, onMounted, getCurrentInstance, computed, reactive, watch } from 'vue'
import { PlusSquareOutlined, FormOutlined, DeleteOutlined, ExclamationCircleOutlined, VerticalAlignBottomOutlined } from '@ant-design/icons-vue'
import { find, lowerCase } from 'lodash'

import PolicyTable from './policy-table/index.vue';
import EditDialog from './edit-dialog.vue'
import { downloadFile } from '@/utils/util';

import {
  getDeviceLogAuditRuleGroupList,
  addDeviceLogAuditRuleGroup,
  updateDeviceLogAuditRuleGroup,
  deleteDeviceLogAuditRuleGroup
} from '@/request/api-device-log'

export default defineComponent({
  components: {
    PlusSquareOutlined,
    FormOutlined,
    DeleteOutlined,
    VerticalAlignBottomOutlined,
    PolicyTable,
    EditDialog,
  },
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { safetyId } = inject('rowData')
    const state = reactive({
      loading: false,
      searchValue: '',
      expandedKeys: [],
      treeData: [],
      selectedKeys: [],
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })

    const activeTreeNode = computed(() => {
      const selectedKeys = state.selectedKeys
      if (!selectedKeys?.length) return {}
      const allNodes = state.treeData?.[0]?.children ?? []
      return find(allNodes, ['id', selectedKeys[0]])
    })

    function traves(data) {
      if (!Array.isArray(data)) return []

      return data.filter(node => {
        if (node.children && node.children.length > 0) {
          node.children = traves(node.children)
          return true
        }
        return false
      })
    }

    const fetchTreeData = async () => {
      try {
        state.loading = true
        const { data = [] } = await getDeviceLogAuditRuleGroupList({safetyId})
        state.treeData = traves(data)
        let selectedKeys = data?.[0] ? [data[0]?.id] : []
        if (state.selectedKeys?.length && find(data, ['id', state.selectedKeys[0]])) {
          selectedKeys = state.selectedKeys
        }
        state.selectedKeys = selectedKeys
        state.expandedKeys = data?.[0]?.id ? [data?.[0]?.id] : []
      } finally {
        state.loading = false
      }
    }

    const filterTreeNode = (node = {}) => {
      if (!state.searchValue) return false
      return lowerCase(node.name)?.indexOf(lowerCase(state.searchValue)) > -1
    }

    const handleSelectTreeNode = (selectedKeys = []) => {
      state.selectedKeys = selectedKeys
    }

    const handleExportTree = (node = {}) => {
      downloadFile(`/device-atomic/log/qm/export/exportRuleGroup?safetyId=${safetyId}&id=${node.id}`)
    }

    const handleOpenDialog = (mode = 'add', row = {}) => {
      state.dialogConfig.visible = true
      state.dialogConfig.mode = mode
      state.dialogConfig.defaultData = row
    }

    const handleSaveGroup = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addDeviceLogAuditRuleGroup,
        edit: updateDeviceLogAuditRuleGroup
      }
      try {
        const res = await requestMethodEnum[mode]?.({...data, safetyId})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        fetchTreeData()
      } catch (err) {
        // const errMsg = isAdd ? '新增失败！' : '更新失败！';
        // proxy.$message.error(err.message || errMsg)
        callback(true, err)
      }
    }

    const handleDelete = async (node = {}) => {
      proxy.$confirm({
        title: '删除',
        content: `确认删除该条数据吗?`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteDeviceLogAuditRuleGroup({ id: node.groupId, safetyId })
            proxy.$message.success('删除成功！')
            fetchTreeData()
          } catch (err) {
            console.log(err)
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
    }

    onMounted(() => {
      fetchTreeData()
    })

    return {
      ...toRefs(state),
      filterTreeNode,
      activeTreeNode,
      fetchTreeData,
      handleExportTree,
      handleSelectTreeNode,
      handleOpenDialog,
      handleSaveGroup,
      handleDelete
    }
  }
})
</script>

<style lang="less" scoped>
.customer-manage--wrapper {
  display: flex;
  height: inherit;

  .content-left {
    width: 250px;
    max-height: calc(100vh - 135px);
    margin-right: 16px;
    padding: 16px;
    border: 1px solid #ccc;
    overflow: auto;

    ::v-deep .ant-tree-treenode {
      width: 100%;

      .ant-tree-node-content-wrapper {
        flex: 1;
      }
    }
  }

  .node-item {
    white-space: nowrap;
    padding-right: 72px;

    &-btn {
      display: none;

      .ant-btn {
        height: auto;
        padding: 0;
        margin-left: 8px;
      }
    }

    &:hover {
      .node-item-btn {
        display: inline-block;
      }
    }
  }

  .content-right {
    flex: 1;
    overflow: hidden;
  }
}
</style>

