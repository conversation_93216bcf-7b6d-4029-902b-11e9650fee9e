<template>
  <div class="policy-table--wrapper">
    <a-space align="end" class="drawer-table-operate">
      <a-button type="primary" @click="handleOpenDialog('add')">
        新增
      </a-button>
      <!-- <a-button type="danger" @click="handleDelete(selectedRowKeys)">
        删除
      </a-button> -->
      <a-button type="primary" @click="handleExport">
        导出
      </a-button>
    </a-space>
    <a-table
      row-key="id"
      :scroll="{x: 775, y: 'calc(100vh - 300px)'}"
      :data-source="dataSource"
      :columns="columns"
      :loading="loading"
      :pagination="pagination"
      :row-selection="{
        selectedRowKeys: selectedRowKeys,
        onChange: (...selection) => handleSelectionChange(...selection)
      }"
      @change="handleTableChange"
    >
      <template #bodyCell="{ column, record }">
        <template v-if="column.key === 'operation'">
          <a-button type="link" @click="handleOpenDialog('edit', record)">编辑</a-button>
          <a-button type="link" danger @click="handleDelete([record.id])">删除</a-button>
        </template>
      </template>
    </a-table>
    <EditDialog
      v-model:visible="dialogConfig.visible"
      v-bind="dialogConfig"
      @on-success="handleSave" />
  </div>
</template>
<script>
import { defineComponent, watch, inject, reactive, toRefs, computed, onMounted, getCurrentInstance, createVNode } from 'vue'
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { usePagination } from "vue-request";
import { cloneDeep } from 'lodash'

import {
  getDeviceLogAuditRuleList,
  addDeviceLogAuditRule,
  updateDeviceLogAuditRule,
  deleteDeviceLogAuditRule,
  startDeviceLogAuditRule,
  stopDeviceLogAuditRule,
  getDeviceLogAuditRuleDetail
} from '@/request/api-device-log'
import { getColumns } from './config';
import { downloadFile } from '@/utils/util';

import EditDialog from './edit-dialog.vue'

export default defineComponent({
  components: {
    EditDialog,
  },
  props: {
    selectedData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['refresh'],
  setup (props, ctx) {
    const { proxy } = getCurrentInstance()
    const { selectedData } = toRefs(props)
    const { safetyId } = inject('rowData')
    const state = reactive({
      selectedRowKeys: [],
      dialogConfig: {
        visible: false,
        mode: 'add',
        defaultData: {}
      }
    })

    const {
      data,
      run,
      loading,
      total,
      current,
      pageSize,
      reload
    } = usePagination(getDeviceLogAuditRuleList,{
      manual: true,
      defaultParams: {
        safetyId,
        ...state.searchParams
      },
      formatResult: ({data = {}}) => {
        return { items: data?.data ?? [], total: data?.total ?? 0 }
      },
      pagination: {
        currentKey: "page",
        pageSizeKey: "size"
      },
    });

    const pagination = computed(() => ({
      total: total.value,
      current: current.value,
      pageSize: pageSize.value,
      showQuickJumper: true,
      showSizeChanger: true,
      showTotal: (total) => `共 ${total} 条`,
    }));

    const dataSource = computed(() => {
      return data.value?.items || [];
    });

    const handleSelectionChange = (selectionKeys = []) => {
      state.selectedRowKeys = cloneDeep(selectionKeys) ?? []
    }

    const handleTableChange = (pag = { pageSize: 10, current: 1 }) => {
      const groupId = selectedData.value?.groupId ?? -1
      run({
        safetyId,
        groupId,
        size: pag.pageSize,
        page: pag?.current,
        ...state.searchParams
      })
    }

    const refreshTableData = (isReload = true) => {
      const groupId = selectedData.value?.groupId ?? -1
      run({
        safetyId,
        groupId,
        page: isReload ? 1 : pagination.value.current,
        size: pagination.value?.pageSize,
        ...state.searchParams
      })
    }

    const handleSave = async ({ mode, data, callback }) => {
      const isAdd = mode === 'add';
      const requestMethodEnum = {
        add: addDeviceLogAuditRule,
        edit: updateDeviceLogAuditRule
      }
      try {
        const res = await requestMethodEnum[mode]?.({...data, groupId: props.selectedData.groupId, safetyId})
        if (res.code !== '00000') throw new Error(res.msg)
        proxy.$message.success(isAdd ? '新增成功！' : '更新成功！')
        callback()
        refreshTableData()
      } catch (err) {
        console.log(err)
        // const errMsg = isAdd ? '新增失败！' : '更新失败！';
        // proxy.$message.error(err?.message || errMsg)
        callback(true, err)
      }
    }

    const handleDelete = async (ids = []) => {
      if (!ids.length) return proxy.$message.info('请先选择数据！')
      proxy.$confirm({
        title: '删除',
        content: `确认删除?`,
        icon: createVNode(ExclamationCircleOutlined),
        okText: '确定',
        okType: 'warning',
        cancelText: '取消',
        async onOk () {
          try {
            await deleteDeviceLogAuditRule({ id: ids.join(','), safetyId })
            proxy.$message.success('删除成功！')
            refreshTableData()
          } catch (err) {
            // proxy.$message.error('删除失败！')
          }
        },
        onCancel () {
          console.log('Cancel')
        }
      })
    }

    const handleOpenDialog = async (mode = 'add', row = {}) => {
      let payload = {};
      if(mode === 'edit'){
        const {data} = await getDeviceLogAuditRuleDetail({safetyId, id: row.id});
        if(data.ruleExpression.eventList && data.ruleExpression.eventList.length > 0){
          const logs = data.ruleExpression.eventList.map(item => item.name);
          // const eventList = data.ruleExpression.eventList.map(item => item.expression);
          payload = {
            ...row,
            ...data,
             ruleExpression: {
                ...data.ruleExpression,
                 logs,
             }
          }
        }
      }
      state.dialogConfig.defaultData = payload
      state.dialogConfig.visible = true
      state.dialogConfig.mode = mode
    }

    const handleChangeStatus = async (row = {}, operateType = 'enable') => {
      const apiMap = {
        enable: startDeviceLogAuditRule,
        disable: stopDeviceLogAuditRule
      }
      await apiMap[operateType]?.({id: row.id, safetyId});
    }

    const handleExport = () => {
      const ids = state.selectedRowKeys ?? [];
      if (!ids.length) return proxy.$message.info('请先选择数据！')
      downloadFile(`/device-atomic/log/qm/export/exportRule?safetyId=${safetyId}&id=${ids}`)
    }

    watch(
      () => selectedData.value,
      () => {
        console.log('selectedData.value:', selectedData.value)
        refreshTableData()
      }
    )

    return {
      ...toRefs(state),
      loading,
      pagination,
      dataSource,
      handleSelectionChange,
      handleTableChange,
      refreshTableData,
      handleOpenDialog,
      handleSave,
      handleDelete,
      handleExport,
      columns: getColumns({ handleChangeStatus, pagination })
    }
  }
})
</script>

<style lang="less" scoped>
.policy-table--wrapper {
  .drawer-table-operate {
    justify-content: end;
  }
}
</style>
