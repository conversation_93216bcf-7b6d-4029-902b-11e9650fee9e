<template>
  <div class="condition-container">
    <Select :options="conditionOptions" v-model="conditionState.operator" />
    <Select :options="dataSources" v-model="conditionState.leftAlias" />
    <Select :options="logFields" v-model="conditionState.leftFieldName" />
    <Select
      :options="operatorOptions"
      v-model="conditionState.functionOperate"
    />
    <Select :options="dataSources" v-model="conditionState.rightAlias" />
    <Select :options="logFields" v-model="conditionState.rightFieldName" />
    <a-button type="danger"  @click="handleDelete" style="margin-left: 8px; ">
      <DeleteOutlined />
     </a-button>
  </div>
</template>

<script setup>
import { reactive, ref, defineProps, computed, watch, defineEmits } from "vue";
import Select from "../components/select.vue";
import { isEqual } from "lodash";
import {
  getDeviceLogAuditRuleAttributeList,
  getDeviceLogAuditRuleOperatorList,
} from "@/request/api-device-log";
import { DeleteOutlined } from '@ant-design/icons-vue'


const emits = defineEmits(["change", 'delete']);

const props = defineProps({
  dataSources: {
    type: Array,
    default: () => [
      {
        label: "日志名称1",
        value: "logName1",
      },
      {
        label: "日志名称2",
        value: "logName2",
      },
    ],
  },
  condition: {
    type: Object,
    default: () => ({}),
  },
});

const conditionOptions = [
  { label: "满足条件", value: "BASE" },
  { label: "不满足条件", value: "NOT" },
];

const conditionState = reactive({
  operator: "BASE",
  leftAlias: "",
  leftFieldName: "",
  rightAlias: "",
  rightFieldName: "",
  functionOperate: "",
});

watch(
  () => props.condition,
  (val) => {
    if (!isEqual(val, conditionState)) {
      Object.assign(conditionState, val);
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => conditionState,
  (val) => {
    if (isEqual(val, props.condition)) {
      return;
    }
    emits("change", val);
  },
  { deep: true }
);

const logFields = ref([]);
const getCommonData = async () => {
    const [logRes, operatorRes] = await Promise.all([
      getDeviceLogAuditRuleAttributeList({}),
      getDeviceLogAuditRuleOperatorList({}),
    ]);
    logFields.value = logRes?.data?.map((item) => {
      let { displayName, fieldName, fieldType, supportOperate } = item;
      supportOperate = supportOperate.reduce((acc = [], opId) => {
        const ops = operatorRes.data?.filter((op) => op.id === opId);
        if (ops.length) {
          const operators = ops.map((i) => ({
            label: i.displayName,
            value: i.displayKey,
          }));
          acc = [...acc, ...operators];
        }
        return acc;
      }, []);
      return {
        label: displayName,
        value: fieldName,
        fieldType,
        supportOperate,
      };
    });

};

const operatorOptions = computed(() => {
  const item = logFields.value.find(
    (item) => item.value === conditionState.leftFieldName
  );
  return item?.supportOperate ?? [];
});

function handleDelete() {
  emits("delete");
}

getCommonData();
</script>

<style lang="less" scoped>
.condition-container {
  display: flex;
  margin-bottom: 16px;
}
</style>
