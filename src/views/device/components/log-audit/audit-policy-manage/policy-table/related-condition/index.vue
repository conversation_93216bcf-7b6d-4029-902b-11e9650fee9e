<template>
  <div class="related-condition">
    <div class="related-condition-header">
      <Select :options="options" v-model="relatedConditions.operator" />
      <el-button type="primary"  @click="addCondition">添加条件</el-button>
      <el-button type="primary" @click="addGroup">添加条件组</el-button>
     <a-button type="danger"  v-if="showDelete" style="margin-left: 8px;" @click="handleDelete">
      <DeleteOutlined />
     </a-button>
    </div>
    <div
      class="condition-wrapper"
      v-if="relatedConditions.children && relatedConditions.children.length"
    >
      <Condition
        v-for="(condition, index) in relatedConditions.children"
        :key="index"
        :dataSources="dataSources"
        :condition="condition"
        @change="(val) => handleConditionChange(index, val)"
        @delete="handleDeleteCondition(index)"
      />
    </div>
    <div
      class="group-wrapper"
      v-if="relatedConditions.groupChildren && relatedConditions.groupChildren.length"
    >
      <RelatedCondition
        v-for="(group, _index) in relatedConditions.groupChildren"
        :key="group.id"
        :dataSources="dataSources"
        :showDelete="true"
        :value="group"
        @delete="handleDeleteGroup(_index)"
      />
    </div>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import { ref, watch } from "vue";
import Select from "./components/select.vue";
import { isEqual, cloneDeep } from "lodash";
import Condition from "./condition/index.vue";
import { observer } from '@formily/reactive-vue';
import { useField } from "@formily/vue";
import { DeleteOutlined } from '@ant-design/icons-vue'


export default observer(defineComponent({
  name: "RelatedCondition",
  components: {
    Select,
    Condition,
    DeleteOutlined
  },
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    dataSources: {
      type: Array,
      default: () => [
        {
          label: "日志名称1",
          value: "logName1",
        },
        {
          label: "日志名称2",
          value: "logName2",
        },
      ],
    },
    showDelete: {
      type: Boolean,
      default: false,
    }
  },
  emits: ["change", 'delete'],
  setup(props, { emit }) {
    const options = [
      { label: "AND", value: "AND" },
      { label: "OR", value: "OR" },
    ];
    const relatedConditions = ref({
      children: [],
      groupChildren: [],
      operator: "AND",
    });

    watch(
      () => props.value,
      (val) => {
        console.log("value change", val);
        if (!isEqual(relatedConditions.value, val)) {
          relatedConditions.value = cloneDeep(val);
        }
      },
      { immediate: true, deep: true }
    );

    watch(
      () => relatedConditions.value,
      (val) => {
        if (!isEqual(val, props.value)) {
          emit("change", cloneDeep(val));
        }
      },
      { deep: true }
    );

    function addCondition() {
      if (!relatedConditions.value?.children)
        relatedConditions.value.children = [];

      relatedConditions.value.children.push({
        operator: "BASE",
        leftAlias: "",
        leftFieldName: "",
        rightFieldName: "",
        rightAlias: "",
        functionOperate: "",
      });
    }
    function addGroup() {
      if (!relatedConditions.value?.groupChildren) relatedConditions.value.groupChildren = [];
      relatedConditions.value.groupChildren.push({
        operator: "AND",
        children: [],
        groupChildren: [],
      });
    }

    function handleConditionChange(index, val) {
      relatedConditions.value.children[index] = val;
    }
    function handleDeleteCondition(index) {
      relatedConditions.value.children.splice(index, 1);
    }

  function handleDelete() {
      emit("delete");
    }

    function handleDeleteGroup(index) {
      relatedConditions.value.groupChildren.splice(index, 1);
    }


    return {
      options,
      relatedConditions,
      addCondition,
      addGroup,
      handleConditionChange,
      handleDeleteCondition,
      handleDeleteGroup,
      handleDelete
    };
  },
}));
</script>

<style lang="less" scoped>
.group-wrapper {
  margin-left: 24px;
}
.related-condition-header {
  margin-bottom: 10px;
}
</style>
