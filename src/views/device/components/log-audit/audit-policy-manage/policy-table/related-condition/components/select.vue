<template>
   <el-select
      v-model="modelValue"
      placeholder="Select"
      size="large"
    >
      <el-option
        v-for="item in options"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
</template>

<script setup>
import { defineProps, defineEmits, computed } from 'vue';
const props = defineProps({
  options: {
    type: Array,
    required: true, // 必填
  },
  value: {
    type: [String, Number],
    default: '',
  },
});
const emit = defineEmits(['update:modelValue']);

const modelValue = computed({
  get() {
    return props.value;
  },
  set(val) {
    emit('update:modelValue', val);
  },
});

</script>
