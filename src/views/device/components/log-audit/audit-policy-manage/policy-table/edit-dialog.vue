<template>
  <FormProvider :form="form">
    <a-modal
      v-model:visible="dialogVisible"
      :title="title"
      width="1200px"
      :keyboard="false"
      :maskClosable="false"
      @cancel="handleClose"
    >
      <a-spin :spinning="loading">
        <SchemaField
          :schema="schema"
          :scope="{ formStep }"
          :components="components"
        ></SchemaField>
      </a-spin>
      <template #footer>
        <FormConsumer>
          <a-space>
            <a-button
              type="primary"
              ghost
              :disabled="!formStep.allowBack"
              @click="() => formStep.back()"
            >
              上一步
            </a-button>
            <a-button
              type="primary"
              ghost
              :disabled="!formStep.allowNext"
              @click="() => handleNextStep()"
            >
              下一步
            </a-button>
            <a-button
              v-if="!isView"
              :disabled="formStep.allowNext"
              type="primary"
              :loading="loading"
              @click="handleSave"
              >保存</a-button
            >
            <a-button
              :type="isView ? 'primary' : 'default'"
              @click="handleClose"
            >
              {{ isView ? "关闭" : "取消" }}
            </a-button>
          </a-space>
        </FormConsumer>
      </template>
    </a-modal>
  </FormProvider>
</template>

<script lang="js">
import { defineComponent, toRefs, computed, reactive, watch, inject, getCurrentInstance, onMounted, nextTick, shallowRef } from 'vue'
import { createForm, onFieldReact, onFieldValueChange } from '@formily/core'
import { FormStep } from '@formily/antdv-x3'
import { cloneDeep, keyBy } from 'lodash';
import RelatedCondition from './related-condition/index.vue';
import FilterCondition from './filter-condition/index.vue'

import { getSchema } from './config';



export default defineComponent({
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    mode: {
      type: String,
      default: 'add'
    },
    defaultData: {
      type: Object,
      default: () => ({})
    }
  },
  emits: ['update:visible', 'on-success'],
  setup (props, ctx) {
    const { safetyId } = inject('rowData')

    const schema = shallowRef({});
    schema.value = getSchema(safetyId)

    const { proxy } = getCurrentInstance()
    const { visible, mode, defaultData } = toRefs(props)
    // const isAdd = computed(() => mode.value === 'add')
    const isView = computed(() => mode.value === 'view')
    const dialogVisible = computed(() => visible.value)
    const title = computed(() => {
      const titleMap = {
        add: '新增',
        edit: '编辑',
        view: '查看'
      }
      return `${titleMap[mode.value]}策略`
    })

    const state = reactive({
      loading: false
    })

    const form = createForm()

    const formStep = FormStep.createFormStep()

    const handleNextStep = () => {
      // if (!isView.value) {
      //   const selectedProtocol = form.values.protocolList.some(item => item.checked);
      //   if (!selectedProtocol) return proxy.$message.warning('请至少选择一项协议类型')
      // }
      formStep.next()
    }

    const components = {
      RelatedCondition,
      FilterCondition
    }

    watch(
      () => visible.value,
      async (val) => {
        if (!val) return
        await nextTick()
        await initForm()
      }
    )

    const initForm = async () => {
      const patternMap = {
        add: 'editable',
        view: 'readPretty',
        edit: 'editable'
      }
      form.setPattern(patternMap[mode.value])
      if (mode === 'add') return;
      const formData = cloneDeep(defaultData.value);
      await nextTick();
      console.log(formData,'++++++')
      form.setValues({...formData}, 'overwrite')
    }

    const handleClose = async (hasError = false) => {
      if (hasError === true) return;
      state.loading = false
      for (let i = 3; i >= 0; i--) { formStep.back() }
      await form.setValues(cloneDeep(form.initialValues), 'overwrite')
      await form.reset()
      ctx.emit('update:visible', false)
    }

    const transformNode = (node) => {
      const {children, groupChildren,} = node;
      if(children && children.length > 0){
        children.forEach(i => {
          if(!Array.isArray(i.rightValues)){
            i.rightValues = [i.rightValues]
          }
        })
      }
      if(groupChildren && groupChildren.length > 0){
        groupChildren.forEach(i => {
          transformNode(i)
        })
      }
    }

    const handleSave = async () => {
      await form.validate()
      const params = cloneDeep(form.values);
      const {eventList, logs} = params.ruleExpression;
      // params.ruleExpression.eventList = params.ruleExpression.eventList.map((expression, index) => {
      //   transformNode(expression)
      //   return {
      //    expression,
      //    name: logs[index],
      //    alias: `filter_${index}`
      //   }
      // })

      state.loading = true
      ctx.emit('on-success', {
        mode: mode.value,
        data: params,
        callback: handleClose
      })
    }

    onMounted(() => {
    })


    return {
      title,
      isView,
      form,
      formStep,
      schema,
      ...toRefs(state),
      dialogVisible,
      handleNextStep,
      handleClose,
      handleSave,
      components
    }
  }
})
</script>
