<template>
  <div class="related-condition">
    <div class="related-condition-header">
      <Select :options="options" v-model="relatedConditions.operator" />
      <el-button type="primary" @click="addCondition">添加条件</el-button>
      <el-button type="primary" @click="addGroup">添加条件组</el-button>
    </div>
    <div
      class="condition-wrapper"
      v-if="relatedConditions.children && relatedConditions.children.length"
    >
      <Condition
        v-for="(condition, index) in relatedConditions.children"
        :key="condition.id"
        :dataSources="dataSources"
        :logFields="logFields"
        :condition="condition"
        :ruleData="ruleData"
        :categoryData="categoryData"
        @change="(val) => handleConditionChange(index, val)"
      />
    </div>
    <div
      class="group-wrapper"
      v-if="
        relatedConditions.groupChildren &&
        relatedConditions.groupChildren.length
      "
    >
      <RelatedCondition
        v-for="(group, groupIndex) in relatedConditions.groupChildren"
        :key="group.id"
        :value="group"
        :dataSources="dataSources"
        :logFields="logFields"
        :ruleData="ruleData"
        :categoryData="categoryData"
        @change="(val) => handleConditionGroupChange(groupIndex, val)"
      />
    </div>
  </div>
</template>

<script>
import { defineComponent } from "vue";
import { ref, watch } from "vue";
import Select from "../components/select.vue";
import { isEqual, cloneDeep } from "lodash";
import Condition from "../condition/index.vue";

export default defineComponent({
  name: "RelatedCondition",
  components: {
    Select,
    Condition,
  },
  props: {
    value: {
      type: Object,
      default: () => ({}),
    },
    logFields: {
      type: Array,
      default: () => [],
    },
    ruleData: {
      type: Array,
      default: () => [],
    },
    categoryData: {
      type: Array,
      default: () => [],
    },
    dataSources: {
      type: Array,
      default: () => [
        {
          label: "日志名称1",
          value: "logName1",
        },
        {
          label: "日志名称2",
          value: "logName2",
        },
      ],
    },
  },
  emits: ["change"],
  setup(props, { emit }) {
    const options = [
      { label: "AND", value: "AND" },
      { label: "OR", value: "OR" },
    ];
    const relatedConditions = ref({
      operator: "AND",
      children: [],
      groupChildren: [],
    });

    watch(
      () => props.value,
      (val) => {
        console.log("group >>> val", val, relatedConditions.value);
        if (!isEqual(relatedConditions.value, val)) {
          relatedConditions.value = cloneDeep(val);
        }
      },
      {immediate: true, deep: true }
    );

    watch(
      () => relatedConditions.value,
      (val) => {
        console.log("group >emit>change> val", val, props.value);
        if (!isEqual(val, props.value)) {
          emit("change", cloneDeep(val));
        }
      },
      {deep: true}
    );

    function addCondition() {
      if (!relatedConditions.value?.children)
        relatedConditions.value.children = [];

      relatedConditions.value.children.push({
        operator: "BASE",
        function: "FunctionOperator",
        leftFieldName: "",
        functionOperate: "",
        rightValues: [],
      });
    }
    function addGroup() {
      if (!relatedConditions.value?.groupChildren)
        relatedConditions.value.groupChildren = [];
      relatedConditions.value.groupChildren.push({
        operator: "AND",
        children: [],
        groupChildren: [],
      });
    }

    function handleConditionChange(index, val) {
      relatedConditions.value.children[index] = val;
    }
    function handleConditionGroupChange(groupIndex, val) {
      relatedConditions.value.groupChildren[groupIndex] = val;
    }

    return {
      options,
      relatedConditions,
      addCondition,
      addGroup,
      handleConditionChange,
      handleConditionGroupChange
    };
  },
});
</script>

<style lang="less" scoped>
.group-wrapper {
  margin-left: 24px;
}
.related-condition-header {
  margin-bottom: 10px;
}
</style>
