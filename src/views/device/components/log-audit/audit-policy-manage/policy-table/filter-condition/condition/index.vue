<template>
  <div class="condition-container">
    <Select :options="conditionOptions" v-model="conditionState.operator" />
    <Select :options="FUNCTION_OPtIONS" v-model="conditionState.function" />
    <Select
      v-if="showFieldProperty"
      :options="fieldOptions"
      v-model="conditionState.leftFieldName"
    />
    <Select
      v-if="showOperator"
      :options="operatorOptions"
      v-model="conditionState.functionOperate"
    />
    <div>
      <a-input
        v-if="rightType === 'input'"
        v-model:value="conditionState.rightValues"
      ></a-input>
      <Select
        v-if="rightType === 'select'"
        :options="rightFieldOptions"
        :multiple="multiple"
        v-model="conditionState.rightValues"
      />
      <a-tree-select
        v-if="rightType === 'treeSelect'"
        style="min-width: 150px"
        tree-default-expand-all
        :fieldNames="{ label: 'name', value: 'id' }"
        :treeData="rightFieldOptions"
        v-model:value="conditionState.rightValues"
      />
    </div>
  </div>
</template>

<script setup>
import { reactive, ref, defineProps, computed, watch, defineEmits } from "vue";
import Select from "../components/select.vue";
import { isEqual } from "lodash";

const emits = defineEmits(["change"]);

const props = defineProps({
  dataSources: {
    type: Object,
    default: () => ({
      label: "日志名称1",
      value: "logName1",
    }),
  },
  condition: {
    type: Object,
    default: () => ({}),
  },
  logFields: {
    type: Array,
    default: () => [],
  },
  ruleData: {
    type: Array,
    default: () => [],
  },
  categoryData: {
    type: Array,
    default: () => [],
  },
});

const DEFAULT_OPERATORS = [
  { label: "等于", value: "FunctionEQ" },
  { label: "大于", value: "FunctionGT" },
  { label: "小于", value: "FunctionLT" },
  { label: "大于等于", value: "FunctionGE" },
  { label: "小于等于", value: "FunctionLE" },
];

const FUNCTION_OPtIONS = [
  { label: "条件属性", value: "FunctionOperator" },
  { label: "引用资产属性", value: "FunctionMatchAssetName" },
  { label: "引用规则", value: "FunctionMatchRule" },
  { label: "引用资产分类", value: "FunctionMatchAssetType" },
];

const conditionOptions = [
  { label: "满足条件", value: "BASE" },
  { label: "不满足条件", value: "NOT" },
];

const conditionState = reactive({
  operator: "BASE",
  function: "FunctionOperator",
  leftFieldName: "",
  functionOperate: "",
  rightValues: [],
});

const showOperator = computed(
  () =>
    !["FunctionMatchRule", "FunctionMatchAssetType"].includes(
      conditionState.function
    )
);

const fieldOptions = computed(() => {
  if (conditionState.function === "FunctionOperator") {
    return props.logFields;
  }
  if (conditionState.function === "FunctionMatchAssetName") {
    return [{ label: "资产名称", value: "assetName" }];
  }
  return [];
});

const operatorOptions = computed(() => {
  if (conditionState.function === "FunctionOperator") {
    const item = props.logFields.find(
      (item) => item.value === conditionState.leftFieldName
    );
    return item?.supportOperate ?? [];
  }
  return DEFAULT_OPERATORS;
});

const showFieldProperty = computed(() =>
  ["FunctionOperator", "FunctionMatchAssetName"].includes(
    conditionState.function
  )
);

const rightType = computed(() => {
  if (conditionState.function === "FunctionOperator") {
    const item = props.logFields.find(
      (item) => item.value === conditionState.leftFieldName
    );
    if (conditionState.functionOperate === "FunctionIsnull") {
      return "select";
    }
    return item?.fieldEnums ? "select" : "input";
  }
  if (
    ["FunctionMatchRule", "FunctionMatchAssetType"].includes(
      conditionState.function
    )
  ) {
    return "treeSelect";
  }
  return "input";
});
const multiple = computed(() => {
  if (
    conditionState.function === "FunctionOperator" &&
    [
      "FunctionIn",
      "FunctionInIgnore",
      "FunctionContains",
      "FunctionContainsIgnore",
    ].includes(conditionState.functionOperate)
  ) {
    return true;
  }
  return false;
});

watch(
  () => rightType.value,
  () => {
    const rightValues = conditionState.rightValues;
    if (!(rightType.value === "select" && multiple.value)) {
      console.log(rightValues, '>>>rightValues>>');
      conditionState.rightValues = Array.isArray(rightValues)
        ? rightValues?.[0]
        : rightValues;
    }
  }
);

const rightFieldOptions = computed(() => {
  if (conditionState.function === "FunctionOperator") {
    const item = props.logFields.find(
      (item) => item.value === conditionState.leftFieldName
    );
    if (conditionState.functionOperate === "FunctionIsnull") {
      return [
        { label: "是", value: "true" },
        { label: "是", value: "false" },
      ];
    }
    return item?.fieldEnums ?? [];
  }
  if ("FunctionMatchRule" === conditionState.function) {
    console.log(props.ruleData);
    return props.ruleData;
  }
  if ("FunctionMatchAssetType" === conditionState.function) {
    return props.categoryData;
  }
  return [];
});

watch(
  () => props.condition,
  (val) => {
    if (!isEqual(val, conditionState)) {
      const newValue = { ...val };
      Object.assign(conditionState, val);
    }
  },
  { deep: true, immediate: true }
);

watch(
  () => conditionState,
  (val) => {
    if (isEqual(val, props.condition)) {
      return;
    }
    emits("change", val);
  },
  { deep: true }
);
</script>

<style lang="less" scoped>
.condition-container {
  display: flex;
  margin-bottom: 16px;
}
</style>
