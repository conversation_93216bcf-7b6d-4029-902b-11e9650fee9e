<template>
  <div class="filter-condition">
    <el-input :value="state.name" disabled></el-input>
    <FilterGroup
      style="margin-top: 12px"
      :value="filterGroupData"
      :dataSources="dataSources"
      :logFields="logFields"
      :ruleData="ruleData"
      :categoryData="categoryData"
      @change="handleConditionChange"
    />
  </div>
</template>

<script>
import { defineComponent } from "vue";
import { ref, watch, reactive, computed } from "vue";
import Select from "./components/select.vue";
import { isEqual, cloneDeep } from "lodash";
import FilterGroup from "./group/index.vue";
import { observer } from "@formily/reactive-vue";

import {
  getDeviceLogAuditRuleAttributeList,
  getDeviceLogAuditRuleOperatorList,
  getDeviceLogAuditRuleGroupList,
  getDeviceLogAuditCategoryList,
} from "@/request/api-device-log";

export default observer(
  defineComponent({
    name: "RelatedCondition",
    components: {
      Select,
      FilterGroup,
    },
    props: {
      value: {
        type: Object,
        default: () => ({}),
      },
      dataSources: {
        type: Object,
        default: () => ({
          label: "日志名称1",
          value: "logName1",
        }),
      },
    },
    emits: ["change"],
    setup(props, { emit }) {
      const state = ref({})

      watch(
        () => props.value,
        (val) => {
          if (!isEqual(state.value, val)) {
            state.value = val;
          }
        },
        { immediate: true, deep: true }
      );

    const filterGroupData = computed({
      get(){
        return state.value.expression;
      },
      set(val){
        state.value = {...state.value, expression: val }
      }
    })

      watch(() => state.value,
        (val) => {
          console.log("state change", val);
          if (!isEqual(state.value, props.value)) {
            console.log("emit change", val);
            emit("change", cloneDeep(state.value));
          }
        },
        { deep: true }
      );

      function handleConditionChange(value) {
       filterGroupData.value = value;
      }

      const logFields = ref([]);
      const getLogFieldsData = async () => {
        const [logRes, operatorRes] = await Promise.all([
          getDeviceLogAuditRuleAttributeList({}),
          getDeviceLogAuditRuleOperatorList({}),
        ]);
        logFields.value = logRes?.data?.map((item) => {
          let {
            displayName,
            fieldName,
            fieldType,
            supportOperate,
            fieldEnums = null,
          } = item ?? {};
          if (fieldEnums) {
            fieldEnums = fieldEnums.map((enumItem) => {
              return {
                label: enumItem.text,
                value: enumItem.value,
              };
            });
          }
          supportOperate = supportOperate.reduce((acc = [], opId) => {
            const ops = operatorRes.data?.filter((op) => op.id === opId);
            if (ops.length) {
              const operators = ops.map((i) => ({
                label: i.displayName,
                value: i.displayKey,
              }));
              acc = [...acc, ...operators];
            }
            return acc;
          }, []);
          return {
            label: displayName,
            value: fieldName,
            fieldType,
            supportOperate,
            fieldEnums,
          };
        });
      };

      getLogFieldsData();
      function diffNode(root = {}) {
        let { children, node } = root;
        if (children && children.length > 0) {
          children = children.map(diffNode);
        }
        return { ...node, children };
      }
      const ruleData = ref([]);
      const categoryData = ref([]);
      const getData = async () => {
        const [categoryRes, groupRes] = await Promise.all([
          getDeviceLogAuditCategoryList({}),
          getDeviceLogAuditRuleGroupList({}),
        ]);
        categoryData.value = categoryRes?.data.map((i) => diffNode(i));
        ruleData.value = groupRes?.data.map((i) => {
          let { children, ...rest } = i;
          children = children.map((j) => ({ ...j, disabled: true }));
          return { ...rest, children, disabled: false };
        });
      };

      getData();

      return {
        handleConditionChange,
        state,
        logFields,
        ruleData,
        categoryData,
        filterGroupData
      };
    },
  })
);
</script>

<style lang="less" scoped>
.group-wrapper {
  margin-left: 24px;
}
.related-condition-header {
  margin-bottom: 10px;
}
</style>
