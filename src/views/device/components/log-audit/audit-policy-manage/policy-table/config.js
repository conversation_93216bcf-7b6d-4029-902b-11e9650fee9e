import { getDeviceLogAuditRuleGroupList, getDeviceLogAuditRuleAttributeList } from '@/request/api-device-log';
import { useAsyncDataSource } from '@/utils/util';
import dayjs from 'dayjs';

/** @type {*} 搜索配置 */
export const SEARCH_CONFIG = [
  {
    title: '名称',
    name: 'name',
    type: 'string',
    'x-component': 'Input',
    'x-component-props': {
      placeholder: '请输入名称',
    },
  },
]

function traves(data) {
  if (!Array.isArray(data)) return []

  return data.filter(node => {
    if (node.children && node.children.length > 0) {
      node.children = traves(node.children)
      return true
    }
    return false
  })
}

/**
 * 获取列表配置
 *
 * @export
 * @return {*}
 */
export function getColumns({ handleChangeStatus, pagination }) {
  return [
    {
      title: "序号",
      dataIndex: "num",
      width: 100,
      align: "center",
      fixed: 'left',
      customRender({ index }) {
        return pagination.value.pageSize * (pagination.value.current - 1) + index + 1;
      }
    },
    {
      title: '名称',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      fixed: 'left'
    },
    {
      title: '是否启用',
      dataIndex: 'is_enable',
      key: 'is_enable',
      width: 120,
      customRender: ({ record }) => {
        const handleClick = async () => {
          try {
            record.statusLoading = true;
            const state = record.is_enable === 1 ? 0 : 1;
            const operateType = state === 1 ? 'enable' : 'disable';
            await handleChangeStatus?.({ ...record, state }, operateType);
            record.is_enable = state;
          } finally {
            record.statusLoading = false;
          }
        }
        return <a-switch
          checked={record.is_enable}
          loading={!!record.statusLoading}
          checkedValue={1}
          unCheckedValue={0}
          onClick={handleClick}
        />
      }
    },
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      width: 120,
      customRender: ({ record }) => {
        return record.type === '1' ? 'IP地址' : '策略规则'
      }
    },
    {
      title: '创建时间',
      dataIndex: 'timeCreated',
      key: 'timeCreated',
      width: 165,
      customRender: ({ text }) => {
        return dayjs(text).format('YYYY-MM-DD HH:mm:ss')
      }
    },
    {
      title: '操作',
      dataIndex: 'operation',
      key: 'operation',
      fixed: 'right',
      width: 170
    }
  ]
}

/** @type {*} 表单schema */
export function getSchema(safetyId) {
  return {
    type: 'object',
    properties: {
      layout: {
        type: 'void',
        'x-component': 'FormLayout',
        'x-component-props': {
          labelWidth: 110
        },
        properties: {
          step: {
            type: 'void',
            'x-component': 'FormStep',
            'x-component-props': {
              formStep: '{{formStep}}'
            },
            properties: {
              fields: {
                type: 'void',
                'x-component': 'FormStep.StepPane',
                'x-component-props': {
                  title: '属性'
                },
                properties: {
                  name: {
                    type: 'string',
                    title: '名称',
                    'x-validator': [
                      { required: true, message: '请输入名称' }
                    ],
                    'x-decorator': 'FormItem',
                    'x-component': 'Input',
                    'x-component-props': {
                      placeholder: '请输入名称'
                    }
                  },
                  groupId: {
                    type: 'string',
                    'x-validator': [
                      { required: true, message: '请选择规则组类型' }
                    ],
                    title: '规则组类型',
                    'x-decorator': 'FormItem',
                    'x-component': 'TreeSelect',
                    'x-component-props': {
                      placeholder: '请选择规则组类型',
                      fieldNames: { label: 'name', value: 'groupId' },
                      treeDefaultExpandAll: true
                    },
                    'x-reactions': useAsyncDataSource(async () => {
                      const { data = [] } = await getDeviceLogAuditRuleGroupList({ safetyId });
                      return traves(data)
                    })
                  },
                  enable: {
                    type: 'number',
                    default: 0,
                    title: '启用',
                    'x-decorator': 'FormItem',
                    'x-component': 'Switch',
                    'x-component-props': {
                      checkedValue: 1,
                      unCheckedValue: 0
                    },
                  },
                  description: {
                    type: 'string',
                    'x-component': 'Input.TextArea',
                    'x-component-props': {
                      placeholder: '请输入描述'
                    },
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      label: '描述'
                    }
                  }
                }
              },
              filter: {
                type: 'void',
                'x-component': 'FormStep.StepPane',
                'x-component-props': {
                  title: '条件'
                },
                properties: {
                  ruleExpression: {
                    type: 'object',
                    properties: {
                      logs: {
                        type: 'array',
                        'x-component': 'ArrayItems',
                        'x-decorator': 'FormItem',
                        title: '日志',
                        'x-validator': [
                          { required: true, message: '请选择日志' }
                        ],
                        items: {
                          type: 'void',
                          'x-component': 'Space',
                          properties: {
                            input: {
                              type: 'string',
                              'x-decorator': 'FormItem',
                              'x-component': 'Input',
                            },
                            remove: {
                              type: 'void',
                              'x-decorator': 'FormItem',
                              'x-component': 'ArrayItems.Remove',
                            },
                          },
                        },
                        properties: {
                          add: {
                            type: 'void',
                            title: '添加条目',
                            'x-component': 'ArrayItems.Addition',
                          },
                        },
                        'x-reactions': field => {
                          if (!field.selfModified) return;
                          const dataSources = field.value?.map((log, index) => ({ label: log, value: `filter_${index + 1}` }));
                          const eventListField = field.query('..[].eventList')?.take();
                          eventListField.value = dataSources?.map((i, index) => ({ name: i.label, alias: i.value, expression: {} }))
                        }
                      },
                      relationExpression: {
                        type: 'object',
                        'x-component': 'RelatedCondition',
                        'x-decorator': 'FormItem',
                        title: '关联',
                        'x-validator': [
                          { required: true, message: '请添加关联条件' }
                        ],
                        'x-reactions': field => {
                          const logs = field.query('..[].logs')?.value() ?? [];
                          const dataSources = logs.map((log, index) => ({ label: log, value: `filter_${index + 1}` }));
                          field.setComponentProps({ dataSources });
                        }
                      },
                      eventList: {
                        type: 'array',
                        'x-component': 'ArrayItems',
                        'x-decorator': 'FormItem',
                        title: '过滤',
                        items: {
                          type: 'void',
                          properties: {
                            input: {
                              type: 'object',
                              'x-component': 'FilterCondition',
                              'x-decorator': 'FormItem',
                            }
                          }
                        },
                        'x-validator': [
                          { required: true, message: '请添加过滤条件' }
                        ],
                        'x-reactions': [
                          field => {
                            const logs = field.query('..[].logs')?.value() ?? [];
                            const dataSources = logs.map((log, index) => ({ label: log, value: `filter_${log}` }));
                            setTimeout(() => {
                              dataSources.forEach((dataSource, index) => {
                                const inputField = field.query(`..[].eventList[${index}].input`).take();
                                inputField?.setComponentProps({ dataSources: dataSource });
                              })
                            }, 500);
                          },
                          field => {
                            field.visible = field.value?.length > 0;
                          }
                        ]
                      }
                    }
                  },
                }
              },
              countExpression: {
                type: 'object',
                'x-component': 'FormStep.StepPane',
                'x-component-props': {
                  title: '次数'
                },
                properties: {
                  numConfig: {
                    type: 'void',
                    'x-component': 'FormLayout',
                    'x-component-props': {
                      layout: 'inline'
                    },
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      labelWidth: 145,
                      colon: false
                    },
                    title: '在',
                    properties: {
                      intervalValue: {
                        type: 'number',
                        title: '',
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          wrapperWidth: 170,
                        },
                        'x-component': 'InputNumber',
                        'x-component-props': {
                          placeholder: '请输入'
                        },
                        'x-validator': [
                          { required: true, message: '请输入' },
                          { format: 'integer' },
                          {
                            minimum: 0
                          },
                        ]
                      },
                      intervalType: {
                        type: 'string',
                        'x-validator': [
                          { required: true, message: '请选择' }
                        ],
                        title: '',
                        default: 'SECOND',
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          wrapperWidth: 80,
                          addonAfter: '时间范围内，日志发生'
                        },
                        'x-component': 'Select',
                        'x-component-props': {
                          placeholder: '请选择',
                        },
                        enum: [
                          { label: '秒', value: 'SECOND' },
                          { label: '分钟', value: 'MINUTE' },
                          { label: '小时', value: 'HOUR' },
                          { label: '天', value: 'DAY' },
                          { label: '周', value: 'WEEK' }
                        ]
                      },
                      logCount: {
                        type: 'number',
                        title: '',
                        'x-decorator': 'FormItem',
                        'x-component': 'InputNumber',
                        'x-component-props': {
                          placeholder: '请输入'
                        },
                        'x-decorator-props': {
                          wrapperWidth: 170,
                          addonAfter: '次'
                        },
                        'x-validator': [
                          { required: true, message: '请输入' },
                          { format: 'integer' },
                          {
                            minimum: 0
                          },
                        ]
                      },
                    }
                  },
                  ruleConfig: {
                    type: 'void',
                    'x-component': 'FormLayout',
                    'x-component-props': {
                      layout: 'inline'
                    },
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      labelWidth: 145,
                    },
                    title: '规则触发间隔',
                    properties: {
                      triggerIntegerValue: {
                        type: 'number',
                        title: '',
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          wrapperWidth: 170,
                        },
                        'x-component': 'InputNumber',
                        'x-component-props': {
                          placeholder: '请输入'
                        },
                        'x-validator': [
                          { required: true, message: '请输入' },
                          { format: 'integer' },
                          {
                            minimum: 0
                          },
                        ]
                      },
                      triggerIntervalType: {
                        type: 'string',
                        'x-validator': [
                          { required: true, message: '请选择' }
                        ],
                        title: '',
                        default: 'SECOND',
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          wrapperWidth: 80,
                        },
                        'x-component': 'Select',
                        'x-component-props': {
                          placeholder: '请选择',
                        },
                        enum: [
                          { label: '秒', value: 'SECOND' },
                          { label: '分钟', value: 'MINUTE' },
                          { label: '小时', value: 'HOUR' },
                          { label: '天', value: 'DAY' },
                          { label: '周', value: 'WEEK' }
                        ]
                      },
                    }
                  },
                  logSame: {
                    type: 'void',
                    'x-component': 'FormLayout',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      labelWidth: 145
                    },
                    title: '日志属性相同时计数',
                    properties: {
                      sameFields: {
                        type: 'array',
                        title: '',
                        default: [],
                        'x-decorator': 'FormItem',
                        'x-component': 'ArrayTable',
                        'x-component-props': {
                          pagination: { pageSize: 10 },
                          scroll: { x: '100%' },
                        },
                        items: {
                          type: 'object',
                          properties: {
                            column1: {
                              type: 'void',
                              'x-component': 'ArrayTable.Column',
                              'x-component-props': { title: '日志名称', key: 'logName', dataIndex: 'logName' },
                              ellipsis: true,
                              properties: {
                                alias: {
                                  type: 'string',
                                  'x-validator': [
                                    { required: true, message: '请选择日志名称' }
                                  ],
                                  'x-decorator': 'FormItem',
                                  'x-component': 'Select',
                                  'x-reactions': field => {
                                    const logs = field.query('ruleExpression.logs').value() ?? [];
                                    const dataSources = logs.map((log, index) => ({ label: log, value: `filter_${index + 1}` }));
                                    field.setDataSource(dataSources);
                                  }
                                }
                              }
                            },
                            column2: {
                              type: 'void',
                              'x-component': 'ArrayTable.Column',
                              'x-component-props': { title: '日志属性', key: 'logField', dataIndex: 'logField' },
                              ellipsis: true,
                              properties: {
                                fieldName: {
                                  type: 'string',
                                  'x-validator': [
                                    { required: true, message: '请选择日志属性' }
                                  ],
                                  'x-decorator': 'FormItem',
                                  'x-component': 'Select',
                                  'x-reactions': useAsyncDataSource(async () => {
                                    const { data = [] } = await getDeviceLogAuditRuleAttributeList({ safetyId });
                                    return data.map(i => ({ label: i.displayName, value: i.fieldName }));
                                  })
                                }
                              }
                            },
                            column3: {
                              type: 'void',
                              'x-component': 'ArrayTable.Column',
                              'x-component-props': {
                                title: '操作',
                                dataIndex: 'operations',
                                ellipsis: true,
                                width: 100,
                                fixed: 'right'
                              },
                              properties: {
                                item: {
                                  type: 'void',
                                  'x-component': 'FormItem',
                                  properties: {
                                    remove: {
                                      type: 'void',
                                      'x-component': 'ArrayTable.Remove'
                                    }
                                  }
                                }
                              }
                            }
                          }
                        },
                        properties: {
                          add: {
                            type: 'void',
                            'x-component': 'ArrayTable.Addition',
                            title: '添加'
                          }
                        }
                      },
                    }
                  },
                  logUnsame: {
                    type: 'void',
                    'x-component': 'FormLayout',
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      labelWidth: 145
                    },
                    title: '日志属性不同时计数',
                    properties: {
                      diffFields: {
                        type: 'array',
                        title: '',
                        default: [],
                        'x-decorator': 'FormItem',
                        'x-component': 'ArrayTable',
                        'x-component-props': {
                          pagination: { pageSize: 10 },
                          scroll: { x: '100%' },
                        },
                        items: {
                          type: 'object',
                          properties: {
                            column1: {
                              type: 'void',
                              'x-component': 'ArrayTable.Column',
                              'x-component-props': { title: '日志名称', key: 'logName', dataIndex: 'logName' },
                              ellipsis: true,
                              properties: {
                                alias: {
                                  type: 'string',
                                  'x-validator': [
                                    { required: true, message: '请选择日志名称' }
                                  ],
                                  'x-decorator': 'FormItem',
                                  'x-component': 'Select',
                                  'x-reactions': field => {
                                    const logs = field.query('ruleExpression.logs').value() ?? [];
                                    const dataSources = logs.map((log, index) => ({ label: log, value: `filter_${index + 1}` }));
                                    field.setDataSource(dataSources);
                                  }
                                }
                              }
                            },
                            column2: {
                              type: 'void',
                              'x-component': 'ArrayTable.Column',
                              'x-component-props': { title: '日志属性', key: 'logField', dataIndex: 'logField' },
                              ellipsis: true,
                              properties: {
                                fieldName: {
                                  type: 'string',
                                  'x-validator': [
                                    { required: true, message: '请选择日志属性' }
                                  ],
                                  'x-decorator': 'FormItem',
                                  'x-component': 'Select',
                                  'x-reactions': useAsyncDataSource(async () => {
                                    const { data = [] } = await getDeviceLogAuditRuleAttributeList({ safetyId });
                                    return data.map(i => ({ label: i.displayName, value: i.fieldName }));
                                  })
                                }
                              }
                            },
                            column3: {
                              type: 'void',
                              'x-component': 'ArrayTable.Column',
                              'x-component-props': {
                                title: '操作',
                                dataIndex: 'operations',
                                ellipsis: true,
                                width: 100,
                                fixed: 'right'
                              },
                              properties: {
                                item: {
                                  type: 'void',
                                  'x-component': 'FormItem',
                                  properties: {
                                    remove: {
                                      type: 'void',
                                      'x-component': 'ArrayTable.Remove'
                                    }
                                  }
                                }
                              }
                            }
                          }
                        },
                        properties: {
                          add: {
                            type: 'void',
                            'x-component': 'ArrayTable.Addition',
                            title: '添加'
                          }
                        }
                      },
                    }
                  },
                }
              },
              actionExpression: {
                type: 'object',
                'x-component': 'FormStep.StepPane',
                'x-component-props': {
                  title: '动作'
                },
                properties: {
                  alarmConfig: {
                    type: 'void',
                    'x-component': 'FormLayout',
                    'x-component-props': {
                      layout: 'inline'
                    },
                    'x-decorator': 'FormItem',
                    'x-decorator-props': {
                      labelWidth: 145,
                    },
                    title: '告警触发间隔',
                    properties: {
                      intervalValue: {
                        type: 'number',
                        title: '',
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          wrapperWidth: 170,
                        },
                        'x-component': 'InputNumber',
                        'x-component-props': {
                          placeholder: '请输入'
                        },
                        'x-validator': [
                          { required: true, message: '请选择告警触发间隔' },
                          { format: 'integer' },
                          {
                            minimum: 0
                          },
                        ]
                      },
                      intervalType: {
                        type: 'string',
                        'x-validator': [
                          { required: true, message: '请选择' }
                        ],
                        title: '',
                        default: 'SECOND',
                        'x-decorator': 'FormItem',
                        'x-decorator-props': {
                          wrapperWidth: 80,
                        },
                        'x-component': 'Select',
                        'x-component-props': {
                          placeholder: '请选择',
                        },
                        enum: [
                          { label: '秒', value: 'SECOND' },
                          { label: '分钟', value: 'MINUTE' },
                          { label: '小时', value: 'HOUR' },
                          { label: '天', value: 'DAY' },
                          { label: '周', value: 'WEEK' }
                        ]
                      },
                    }
                  },
                }
              }
            }
          }
        }
      }
    }
  }
}

