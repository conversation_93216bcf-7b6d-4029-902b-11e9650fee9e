import { get, post, postByFile, put, deleteMethod } from '../http';
import { getDeviceSafetyId } from '@/utils/device-type-id'
import { downloadFile } from '@/utils/util';



/**
 * 敏感数据-新增数据
 */
export const insertAssetSensitive = p => post('/device-atomic/db/assetSensitive/insert', p);

/**
 * 敏感数据-修改数据
 */
export const modifyAssetSensitive = p => put('/device-atomic/db/assetSensitive/modify', p);

/**
 * 敏感数据-删除数据
 */
export const deleteAssetSensitive = p => deleteMethod('/device-atomic/db/assetSensitive/delete', p);

/**
 * 敏感数据-分页查询数据
 */
export const getAssetSensitivePageData = p => post('/device-atomic/db/assetSensitive/pageData', p);

/**
 * 敏感数据-导出数据
 */
export const exportAssetSensitive = p => downloadFile('/device-atomic/db/assetSensitive/export', p);

/**
 * 敏感数据-导入数据
 */
export const importAssetSensitive = p => postByFile('/device-atomic/db/assetSensitive/import', p);

/**
 * 数据发现-新增数据
 */
export const insertAssetDiscovery = p => post('/device-atomic/db/assetDiscovery/insert', p);

/**
 * 数据发现-修改数据
 */
export const modifyAssetDiscovery = p => put('/device-atomic/db/assetDiscovery/modify', p);

/**
 * 数据发现-删除数据
 */
export const deleteAssetDiscovery = p => deleteMethod('/device-atomic/db/assetDiscovery/delete', p);

/**
 * 数据发现-分页查询数据
 */
export const getAssetDiscoveryPageData = p => post('/device-atomic/db/assetDiscovery/pageData', p);

/**
 * 数据发现-导出数据
 */
export const exportAssetDiscovery = p => downloadFile('/device-atomic/db/assetDiscovery/export', p);

/**
 * 数据发现-导入数据
 */
export const importAssetDiscovery = p => postByFile('/device-atomic/db/assetDiscovery/import', p);

/**
 * 敏感数据定义-新增数据
 */
export const insertAssetSensitiveDefine = p => post('/device-atomic/db/assetSensitiveDefine/insert', p);

/**
 * 敏感数据定义-修改数据
 */
export const modifyAssetSensitiveDefine = p => put('/device-atomic/db/assetSensitiveDefine/modify', p);

/**
 * 敏感数据定义-删除数据
 */
export const deleteAssetSensitiveDefine = p => deleteMethod('/device-atomic/db/assetSensitiveDefine/delete', p);

/**
 * 敏感数据定义-分页查询数据
 */
export const getAssetSensitiveDefinePageData = p => post('/device-atomic/db/assetSensitiveDefine/pageData', p);

/**
 * 敏感数据定义-导出数据
 */
export const exportAssetSensitiveDefine = p => downloadFile('/device-atomic/db/assetSensitiveDefine/export', p);

/**
 * 敏感数据定义-导入数据
 */
export const importAssetSensitiveDefine = p => postByFile('/device-atomic/db/assetSensitiveDefine/import', p);

/**
 * 存储过程-新增数据
 */
export const insertAssetProcedure = p => post('/device-atomic/db/assetProcedure/insert', p);

/**
 * 存储过程-修改数据
 */
export const modifyAssetProcedure = p => put('/device-atomic/db/assetProcedure/modify', p);

/**
 * 存储过程-删除数据
 */
export const deleteAssetProcedure = p => deleteMethod('/device-atomic/db/assetProcedure/delete', p);

/**
 * 存储过程-分页查询数据
 */
export const getAssetProcedurePageData = p => post('/device-atomic/db/assetProcedure/pageData', p);

/**
 * 存储过程-导出数据
 */
export const exportAssetProcedure = p => downloadFile('/device-atomic/db/assetProcedure/export', p);

/**
 * 存储过程-导入数据
 */
export const importAssetProcedure = p => postByFile('/device-atomic/db/assetProcedure/import', p);

/**
 * 网络审计-新增数据
 */
export const insertAuditNetwork = p => post('/device-atomic/db/auditNetwork/insert', p);

/**
 * 网络审计-修改数据
 */
export const modifyAuditNetwork = p => put('/device-atomic/db/auditNetwork/modify', p);

/**
 * 网络审计-删除数据
 */
export const deleteAuditNetwork = p => deleteMethod('/device-atomic/db/auditNetwork/delete', p);

/**
 * 网络审计-分页查询数据
 */
export const getAuditNetworkPageData = p => post('/device-atomic/db/auditNetwork/pageData', p);
