import { get, post, postByFile, put, deleteMethod } from '../http';
import { downloadFile } from '@/utils/util';

/**
 * 系统日志-删除数据
 */
export const deleteSystemLog = p => deleteMethod('/device-atomic/db/systemLog/delete', p);

/**
 * 系统日志-分页查询数据
 */
export const getSystemLogPageData = p => post('/device-atomic/db/systemLog/pageData', p);

/**
 * 系统日志-导出数据
 */
export const exportSystemLog = p => downloadFile('/device-atomic/db/systemLog/export', p);

// ================================
// 导流插件相关接口
// ================================

/**
 * 导流插件-新增数据
 */
export const insertSystemPlugin = p => post('/device-atomic/db/systemPlugin/insert', p);

/**
 * 导流插件-修改数据
 */
export const modifySystemPlugin = p => put('/device-atomic/db/systemPlugin/modify', p);

/**
 * 导流插件-删除数据
 */
export const deleteSystemPlugin = p => deleteMethod('/device-atomic/db/systemPlugin/delete', p);

/**
 * 导流插件-分页查询数据
 */
export const getSystemPluginPageData = p => post('/device-atomic/db/systemPlugin/pageData', p);

/**
 * 导流插件-导出数据
 */
export const exportSystemPlugin = p => downloadFile('/device-atomic/db/systemPlugin/export', p);

// ================================
// 多级配置相关接口
// ================================

/**
 * 多级配置-新增数据
 */
export const insertSystemCascade = p => post('/device-atomic/db/systemCascade/insert', p);

/**
 * 多级配置-修改数据
 */
export const modifySystemCascade = p => put('/device-atomic/db/systemCascade/modify', p);

/**
 * 多级配置-删除数据
 */
export const deleteSystemCascade = p => deleteMethod('/device-atomic/db/systemCascade/delete', p);

/**
 * 多级配置-分页查询数据
 */
export const getSystemCascadePageData = p => post('/device-atomic/db/systemCascade/pageData', p);

/**
 * 多级配置-导出数据
 */
export const exportSystemCascade = p => downloadFile('/device-atomic/db/systemCascade/export', p);

/**
 * 多级配置-导入数据
 */
export const importSystemCascade = p => postByFile('/device-atomic/db/systemCascade/import', p);

// ================================
// LVS配置相关接口
// ================================

/**
 * LVS配置-新增数据
 */
export const insertSystemLvs = p => post('/device-atomic/db/systemLvs/insert', p);

/**
 * LVS配置-修改数据
 */
export const modifySystemLvs = p => put('/device-atomic/db/systemLvs/modify', p);

/**
 * LVS配置-删除数据
 */
export const deleteSystemLvs = p => deleteMethod('/device-atomic/db/systemLvs/delete', p);

/**
 * LVS配置-分页查询数据
 */
export const getSystemLvsPageData = p => post('/device-atomic/db/systemLvs/pageData', p);

/**
 * LVS配置-导出数据
 */
export const exportSystemLvs = p => downloadFile('/device-atomic/db/systemLvs/export', p);

/**
 * LVS配置-导入数据
 */
export const importSystemLvs = p => postByFile('/device-atomic/db/systemLvs/import', p);

// ================================
// 抓包配置相关接口
// ================================

/**
 * 抓包配置-新增数据
 */
export const insertSystemPackage = p => post('/device-atomic/db/systemPackage/insert', p);

/**
 * 抓包配置-修改数据
 */
export const modifySystemPackage = p => put('/device-atomic/db/systemPackage/modify', p);

/**
 * 抓包配置-删除数据
 */
export const deleteSystemPackage = p => deleteMethod('/device-atomic/db/systemPackage/delete', p);

/**
 * 抓包配置-分页查询数据
 */
export const getSystemPackagePageData = p => post('/device-atomic/db/systemPackage/pageData', p);

// ================================
// 引擎配置相关接口
// ================================

/**
 * 引擎配置-新增数据
 */
export const insertSystemEngine = p => post('/device-atomic/db/systemEngine/insert', p);

/**
 * 引擎配置-修改数据
 */
export const modifySystemEngine = p => put('/device-atomic/db/systemEngine/modify', p);

/**
 * 引擎配置-删除数据
 */
export const deleteSystemEngine = p => deleteMethod('/device-atomic/db/systemEngine/delete', p);

/**
 * 引擎配置-分页查询数据
 */
export const getSystemEnginePageData = p => post('/device-atomic/db/systemEngine/pageData', p);

// ================================
// 联动处置-WAF相关接口
// ================================

/**
 * WAF联动配置-新增数据
 */
export const insertSystemDevlinkWaf = p => post('/device-atomic/db/systemDevlinkWaf/insert', p);

/**
 * WAF联动配置-修改数据
 */
export const modifySystemDevlinkWaf = p => put('/device-atomic/db/systemDevlinkWaf/modify', p);

/**
 * WAF联动配置-删除数据
 */
export const deleteSystemDevlinkWaf = p => deleteMethod('/device-atomic/db/systemDevlinkWaf/delete', p);

/**
 * WAF联动配置-分页查询数据
 */
export const getSystemDevlinkWafPageData = p => post('/device-atomic/db/systemDevlinkWaf/pageData', p);

/**
 * WAF联动配置-导出数据
 */
export const exportSystemDevlinkWaf = p => downloadFile('/device-atomic/db/systemDevlinkWaf/export', p);

// ================================
// 联动处置-APT相关接口
// ================================

/**
 * APT联动配置-新增数据
 */
export const insertSystemDevlinkApt = p => post('/device-atomic/db/systemDevlinkApt/insert', p);

/**
 * APT联动配置-修改数据
 */
export const modifySystemDevlinkApt = p => put('/device-atomic/db/systemDevlinkApt/modify', p);

/**
 * APT联动配置-删除数据
 */
export const deleteSystemDevlinkApt = p => deleteMethod('/device-atomic/db/systemDevlinkApt/delete', p);

/**
 * APT联动配置-分页查询数据
 */
export const getSystemDevlinkAptPageData = p => post('/device-atomic/db/systemDevlinkApt/pageData', p);

/**
 * APT联动配置-导出数据
 */
export const exportSystemDevlinkApt = p => downloadFile('/device-atomic/db/systemDevlinkApt/export', p);

// ================================
// 联动处置-DSMP相关接口
// ================================

/**
 * DSMP联动配置-新增数据
 */
export const insertSystemDevlinkDsmp = p => post('/device-atomic/db/systemDevlinkDsmp/insert', p);

/**
 * DSMP联动配置-修改数据
 */
export const modifySystemDevlinkDsmp = p => put('/device-atomic/db/systemDevlinkDsmp/modify', p);

/**
 * DSMP联动配置-删除数据
 */
export const deleteSystemDevlinkDsmp = p => deleteMethod('/device-atomic/db/systemDevlinkDsmp/delete', p);

/**
 * DSMP联动配置-分页查询数据
 */
export const getSystemDevlinkDsmpPageData = p => post('/device-atomic/db/systemDevlinkDsmp/pageData', p);

/**
 * DSMP联动配置-导出数据
 */
export const exportSystemDevlinkDsmp = p => downloadFile('/device-atomic/db/systemDevlinkDsmp/export', p);

// ================================
// 日志级别管理相关接口
// ================================

/**
 * 日志级别管理-新增数据
 */
export const insertSystemLogLevel = p => post('/device-atomic/db/systemLogLevel/insert', p);

/**
 * 日志级别管理-修改数据
 */
export const modifySystemLogLevel = p => put('/device-atomic/db/systemLogLevel/modify', p);

/**
 * 日志级别管理-删除数据
 */
export const deleteSystemLogLevel = p => deleteMethod('/device-atomic/db/systemLogLevel/delete', p);

/**
 * 日志级别管理-分页查询数据
 */
export const getSystemLogLevelPageData = p => post('/device-atomic/db/systemLogLevel/pageData', p);

/**
 * 日志级别管理-导出数据
 */
export const exportSystemLogLevel = p => downloadFile('/device-atomic/db/systemLogLevel/export', p);