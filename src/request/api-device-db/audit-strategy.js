import { get, post, postByFile, put, deleteMethod } from '../http';


/**
 * 网络审计-新增数据
 */
export const insertAuditNetwork = p => post('/device-atomic/db/auditNetwork/insert', p);

/**
 * 网络审计-修改数据
 */
export const modifyAuditNetwork = p => put('/device-atomic/db/auditNetwork/modify', p);

/**
 * 网络审计-删除数据
 */
export const deleteAuditNetwork = p => deleteMethod('/device-atomic/db/auditNetwork/delete', p);

/**
 * 网络审计-分页查询数据
 */
export const getAuditNetworkPageData = p => post('/device-atomic/db/auditNetwork/pageData', p);

/**
 * 审计白名单-新增数据
 */
export const insertAuditWhite = p => post('/device-atomic/db/auditWhite/insert', p);

/**
 * 审计白名单-修改数据
 */
export const modifyAuditWhite = p => put('/device-atomic/db/auditWhite/modify', p);

/**
 * 审计白名单-删除数据
 */
export const deleteAuditWhite = p => deleteMethod('/device-atomic/db/auditWhite/delete', p);

/**
 * 审计白名单-分页查询数据
 */
export const getAuditWhitePageData = p => post('/device-atomic/db/auditWhite/pageData', p);

/**
 * 互联网审计-新增数据
 */
export const insertInternetAuditSetting = p => post('/device-atomic/device-atomic/db/setting/insert', p);

/**
 * 互联网审计-修改数据
 */
export const modifyInternetAuditSetting = p => put('/device-atomic/device-atomic/db/setting/modify', p);

/**
 * 互联网审计-删除数据
 */
export const deleteInternetAuditSetting = p => deleteMethod('/device-atomic/db/setting/delete', p);

/**
 * 互联网审计-详情数据
 */
export const getInternetAuditSettingDetails = p => post('/device-atomic/db/setting/details', p);

/**
 * 互联网审计-新增数据
 */
export const insertAuditInternet = p => post('/device-atomic/db/auditInternet/insert', p);

/**
 * 互联网审计-修改数据
 */
export const modifyAuditInternet = p => put('/device-atomic/db/auditInternet/modify', p);

/**
 * 互联网审计-删除数据
 */
export const deleteAuditInternet = p => deleteMethod('/device-atomic/db/auditInternet/delete', p);

/**
 * 互联网审计-分页查询数据
 */
export const getAuditInternetPageData = p => post('/device-atomic/db/auditInternet/pageData', p);
