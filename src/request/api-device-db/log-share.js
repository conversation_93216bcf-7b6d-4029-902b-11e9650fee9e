import { get, post, postByFile, put, deleteMethod } from '../http';
import { downloadFile } from '@/utils/util';

// ================================
// 共享配置相关接口
// ================================

/**
 * 共享配置-新增数据
 */
export const insertShareConfig = p => post('/device-atomic/db/shareConfig/insert', p);

/**
 * 共享配置-修改数据
 */
export const modifyShareConfig = p => put('/device-atomic/db/shareConfig/modify', p);

/**
 * 共享配置-删除数据
 */
export const deleteShareConfig = p => deleteMethod('/device-atomic/db/shareConfig/delete', p);

/**
 * 共享配置-分页查询数据
 */
export const getShareConfigPageData = p => post('/device-atomic/db/shareConfig/pageData', p);

/**
 * 共享配置-导出数据
 */
export const exportShareConfig = p => downloadFile('/device-atomic/db/shareConfig/export', p);

/**
 * 共享配置-导入数据
 */
export const importShareConfig = p => postByFile('/device-atomic/db/shareConfig/import', p);

// ================================
// SYSLOG配置相关接口
// ================================

/**
 * SYSLOG配置-新增数据
 */
export const insertSyslogConfig = p => post('/device-atomic/db/shareSyslog/insert', p);

/**
 * SYSLOG配置-修改数据
 */
export const modifySyslogConfig = p => put('/device-atomic/db/shareSyslog/modify', p);

/**
 * SYSLOG配置-删除数据
 */
export const deleteSyslogConfig = p => deleteMethod('/device-atomic/db/shareSyslog/delete', p);

/**
 * SYSLOG配置-分页查询数据
 */
export const getSyslogConfigPageData = p => post('/device-atomic/db/shareSyslog/pageData', p);

/**
 * SYSLOG配置-导出数据
 */
export const exportSyslogConfig = p => downloadFile('/device-atomic/db/shareSyslog/export', p);

// ================================
// SFTP配置相关接口
// ================================

/**
 * SFTP配置-新增数据
 */
export const insertSftpConfig = p => post('/device-atomic/db/shareSftp/insert', p);

/**
 * SFTP配置-修改数据
 */
export const modifySftpConfig = p => put('/device-atomic/db/shareSftp/modify', p);

/**
 * SFTP配置-删除数据
 */
export const deleteSftpConfig = p => deleteMethod('/device-atomic/db/shareSftp/delete', p);

/**
 * SFTP配置-分页查询数据
 */
export const getSftpConfigPageData = p => post('/device-atomic/db/shareSftp/pageData', p);

/**
 * SFTP配置-导出数据
 */
export const exportSftpConfig = p => downloadFile('/device-atomic/db/shareSftp/export', p);

/**
 * SFTP配置-导入数据
 */
export const importSftpConfig = p => postByFile('/device-atomic/db/shareSftp/import', p);

// ================================
// SNMP配置相关接口
// ================================

/**
 * SNMP配置-新增数据
 */
export const insertSnmpConfig = p => post('/device-atomic/db/shareSnmp/insert', p);

/**
 * SNMP配置-修改数据
 */
export const modifySnmpConfig = p => put('/device-atomic/db/shareSnmp/modify', p);

/**
 * SNMP配置-删除数据
 */
export const deleteSnmpConfig = p => deleteMethod('/device-atomic/db/shareSnmp/delete', p);

/**
 * SNMP配置-分页查询数据
 */
export const getSnmpConfigPageData = p => post('/device-atomic/db/shareSnmp/pageData', p);

/**
 * SNMP配置-导出数据
 */
export const exportSnmpConfig = p => downloadFile('/device-atomic/db/shareSnmp/export', p);

// ================================
// KAFKA配置相关接口
// ================================

/**
 * KAFKA配置-新增数据
 */
export const insertKafkaConfig = p => post('/device-atomic/db/shareKafka/insert', p);

/**
 * KAFKA配置-修改数据
 */
export const modifyKafkaConfig = p => put('/device-atomic/db/shareKafka/modify', p);

/**
 * KAFKA配置-删除数据
 */
export const deleteKafkaConfig = p => deleteMethod('/device-atomic/db/shareKafka/delete', p);

/**
 * KAFKA配置-分页查询数据
 */
export const getKafkaConfigPageData = p => post('/device-atomic/db/shareKafka/pageData', p);

/**
 * KAFKA配置-导出数据
 */
export const exportKafkaConfig = p => downloadFile('/device-atomic/db/shareKafka/export', p);

/**
 * KAFKA配置-导入数据
 */
export const importKafkaConfig = p => postByFile('/device-atomic/db/shareKafka/import', p);
