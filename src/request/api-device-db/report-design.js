import { get, post, postByFile, put, deleteMethod } from '../http';
import { downloadFile } from '@/utils/util';

// ================================
// 设计管理相关接口
// ================================

/**
 * 设计管理-新增数据
 */
export const insertDesign = p => post('/device-atomic/db/reportDesign/insert', p);

/**
 * 设计管理-修改数据
 */
export const modifyDesign = p => put('/device-atomic/db/reportDesign/modify', p);

/**
 * 设计管理-删除数据
 */
export const deleteDesign = p => deleteMethod('/device-atomic/db/reportDesign/delete', p);

/**
 * 设计管理-分页查询数据
 */
export const getDesignPageData = p => post('/device-atomic/db/reportDesign/pageData', p);

/**
 * 设计管理-导出数据
 */
export const exportDesign = p => downloadFile('/device-atomic/db/reportDesign/export', p);

// ================================
// 统计任务相关接口
// ================================

/**
 * 统计任务-新增数据
 */
export const insertStatisticsTask = p => post('/device-atomic/db/reportTask/insert', p);

/**
 * 统计任务-修改数据
 */
export const modifyStatisticsTask = p => put('/device-atomic/db/reportTask/modify', p);

/**
 * 统计任务-删除数据
 */
export const deleteStatisticsTask = p => deleteMethod('/device-atomic/db/reportTask/delete', p);

/**
 * 统计任务-分页查询数据
 */
export const getStatisticsTaskPageData = p => post('/device-atomic/db/reportTask/pageData', p);

/**
 * 统计任务-导出数据
 */
export const exportStatisticsTask = p => downloadFile('/device-atomic/db/reportTask/export', p);
