import { get, post, postByFile, put, deleteMethod } from '../http';
import { downloadFile } from '@/utils/util';

// ================================
// 网络管理相关接口
// ================================

/**
 * 网络管理-新增数据
 */
export const insertBaseNetwork = p => post('/device-atomic/db/baseNetwork/insert', p);

/**
 * 网络管理-修改数据
 */
export const modifyBaseNetwork = p => put('/device-atomic/db/baseNetwork/modify', p);

/**
 * 网络管理-删除数据
 */
export const deleteBaseNetwork = p => deleteMethod('/device-atomic/db/baseNetwork/delete', p);

/**
 * 网络管理-分页查询数据
 */
export const getBaseNetworkPageData = p => post('/device-atomic/db/baseNetwork/pageData', p);

/**
 * 网络管理-导出数据
 */
export const exportBaseNetwork = p => downloadFile('/device-atomic/db/baseNetwork/export', p);

// ================================
// SNMP服务相关接口
// ================================

/**
 * SNMP服务-新增数据
 */
export const insertBaseSnmp = p => post('/device-atomic/db/baseSnmp/insert', p);

/**
 * SNMP服务-修改数据
 */
export const modifyBaseSnmp = p => put('/device-atomic/db/baseSnmp/modify', p);

/**
 * SNMP服务-删除数据
 */
export const deleteBaseSnmp = p => deleteMethod('/device-atomic/db/baseSnmp/delete', p);

/**
 * SNMP服务-分页查询数据
 */
export const getBaseSnmpPageData = p => post('/device-atomic/db/baseSnmp/pageData', p);

/**
 * SNMP服务-导出数据
 */
export const exportBaseSnmp = p => downloadFile('/device-atomic/db/baseSnmp/export', p);

// ================================
// EMAIL配置相关接口
// ================================

/**
 * EMAIL配置-新增数据
 */
export const insertBaseEmail = p => post('/device-atomic/db/baseEmail/insert', p);

/**
 * EMAIL配置-修改数据
 */
export const modifyBaseEmail = p => put('/device-atomic/db/baseEmail/modify', p);

/**
 * EMAIL配置-删除数据
 */
export const deleteBaseEmail = p => deleteMethod('/device-atomic/db/baseEmail/delete', p);

/**
 * EMAIL配置-分页查询数据
 */
export const getBaseEmailPageData = p => post('/device-atomic/db/baseEmail/pageData', p);

/**
 * EMAIL配置-导出数据
 */
export const exportBaseEmail = p => downloadFile('/device-atomic/db/baseEmail/export', p);

// ================================
// 短信配置相关接口
// ================================

/**
 * 短信配置-新增数据
 */
export const insertBaseSms = p => post('/device-atomic/db/baseSms/insert', p);

/**
 * 短信配置-修改数据
 */
export const modifyBaseSms = p => put('/device-atomic/db/baseSms/modify', p);

/**
 * 短信配置-删除数据
 */
export const deleteBaseSms = p => deleteMethod('/device-atomic/db/baseSms/delete', p);

/**
 * 短信配置-分页查询数据
 */
export const getBaseSmsPageData = p => post('/device-atomic/db/baseSms/pageData', p);

/**
 * 短信配置-导出数据
 */
export const exportBaseSms = p => downloadFile('/device-atomic/db/baseSms/export', p);
