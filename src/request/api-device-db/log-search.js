import { post, get, deleteMethod } from '@/request/http'
import { downloadFile } from '@/utils/util';

// ==================== 事件查询 ====================
/**
 * 事件查询-分页查询数据
 */
export const getLogEventPageData = p => post('/device-atomic/db/logEvent/pageData', p);

/**
 * 事件查询-删除数据
 */
export const deleteLogEvent = p => deleteMethod('/device-atomic/db/logEvent/delete', p);

/**
 * 事件查询-导出数据
 */
export const exportLogEvent = p => downloadFile('/device-atomic/db/logEvent/export', p);

// ==================== 源IP汇聚 ====================
/**
 * 源IP汇聚-分页查询数据
 */
export const getLogSipConvergePageData = p => post('/device-atomic/db/logSipConverge/pageData', p);

/**
 * 源IP汇聚-删除数据
 */
export const deleteLogSipConverge = p => deleteMethod('/device-atomic/db/logSipConverge/delete', p);

/**
 * 源IP汇聚-导出数据
 */
export const exportLogSipConverge = p => downloadFile('/device-atomic/db/logSipConverge/export', p);

// ==================== 会话管理 ====================
/**
 * 会话管理-分页查询数据
 */
export const getLogSessionPageData = p => post('/device-atomic/db/logSession/pageData', p);

/**
 * 会话管理-删除数据
 */
export const deleteLogSession = p => deleteMethod('/device-atomic/db/logSession/delete', p);

/**
 * 会话管理-导出数据
 */
export const exportLogSession = p => downloadFile('/device-atomic/db/logSession/export', p);

// ==================== 三层管理 ====================
/**
 * 三层管理-分页查询数据
 */
export const getLogThirdPageData = p => post('/device-atomic/db/logThird/pageData', p);

/**
 * 三层管理-删除数据
 */
export const deleteLogThird = p => deleteMethod('/device-atomic/db/logThird/delete', p);

/**
 * 三层管理-导出数据
 */
export const exportLogThird = p => downloadFile('/device-atomic/db/logThird/export', p);

// ==================== 异常行为 ====================
/**
 * 异常行为-分页查询数据
 */
export const getLogAbnormalPageData = p => post('/device-atomic/db/logAbnormal/pageData', p);

/**
 * 异常行为-删除数据
 */
export const deleteLogAbnormal = p => deleteMethod('/device-atomic/db/logAbnormal/delete', p);

/**
 * 异常行为-导出数据
 */
export const exportLogAbnormal = p => downloadFile('/device-atomic/db/logAbnormal/export', p);

// ==================== 文件查询 ====================
/**
 * 文件查询-分页查询数据
 */
export const getLogFilePageData = p => post('/device-atomic/db/logFile/pageData', p);

/**
 * 文件查询-删除数据
 */
export const deleteLogFile = p => deleteMethod('/device-atomic/db/logFile/delete', p);

/**
 * 文件查询-导出数据
 */
export const exportLogFile = p => downloadFile('/device-atomic/db/logFile/export', p);

// ==================== APT联动 ====================
/**
 * APT联动-分页查询数据
 */
export const getLogAptPageData = p => post('/device-atomic/db/logApt/pageData', p);

/**
 * APT联动-删除数据
 */
export const deleteLogApt = p => deleteMethod('/device-atomic/db/logApt/delete', p);

/**
 * APT联动-导出数据
 */
export const exportLogApt = p => downloadFile('/device-atomic/db/logApt/export', p);

// ==================== 互联网行为 ====================
/**
 * 互联网行为-分页查询数据
 */
export const getLogNetworkPageData = p => post('/device-atomic/db/logNetwork/pageData', p);

/**
 * 互联网行为-删除数据
 */
export const deleteLogNetwork = p => deleteMethod('/device-atomic/db/logNetwork/delete', p);

/**
 * 互联网行为-导出数据
 */
export const exportLogNetwork = p => downloadFile('/device-atomic/db/logNetwork/export', p);

// ==================== SYSLOG查询 ====================
/**
 * SYSLOG查询-分页查询数据
 */
export const getLogSyslogPageData = p => post('/device-atomic/db/logSyslog/pageData', p);

/**
 * SYSLOG查询-删除数据
 */
export const deleteLogSyslog = p => deleteMethod('/device-atomic/db/logSyslog/delete', p);

/**
 * SYSLOG查询-导出数据
 */
export const exportLogSyslog = p => downloadFile('/device-atomic/db/logSyslog/export', p);
