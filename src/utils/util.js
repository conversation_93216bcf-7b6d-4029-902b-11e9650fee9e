import { ref, onMounted, computed } from "vue";
import { message } from 'ant-design-vue'
import axios from 'axios'
import store from '@/store'

// tree结构换成一维数组结构
export const treeToList = (treedata) => {
  let listdata = [];
  treedata.map((item) => {
    listdata.push({ id: item.id, label: item.label });
    if (item.childern && item.childern.length) {
      listdata.push(...treeToList(item.childern))
    }
  })
  return listdata
}

// tree结构遍历--生成title
export const treeToTitle = (treedata) => {
  return treedata.map((item) => {
    item.title = item.meta.title;
    if (item.children && item.children.length) {
      item.children = treeToTitle(item.children);
    }
    return item;
  })
}

// 通过route生成menu
export const generateMenuList = (routeList, parentRoute) => {
  return routeList.map(route => {
    if (parentRoute) {
      route.path = parentRoute.path + '/' + route.path;
    }
    route.meta.key = route.path;
    if (route.children && route.children.length) {
      generateMenuList(route.children, route);
    }
    return route;
  });
}

/**
 * @description 一些像素是100px这样的字符串， 转换为100
 * @param {*} pxStr
 */
function pxStringToNumber(pxStr) {

  let lastI = pxStr.lastIndexOf('px');
  if (lastI == -1) {
    throw new Error("Please pass in a pixel string.")
  }
  if (lastI == 0) {
    return 0;
  }
  let num = Number(pxStr.substr(0, lastI))
  if (window.isNaN(num)) {
    throw new Error("Please pass in a pixel string.")
  }
  return num;

}

/**
* @description 这里的表格高度计算出来是以用户管理为基准，一些特殊的页面需要增加或减少其他元素的高度
*              如一个页面没有搜索栏的高度  那么需要给表格家长搜索栏的高度这样使用  useTableHeight(58) 或 useTableHeight('58px')
*              如指令管理页面需要给表格再减去tab组件的高度那么这样使用 useTableHeight(-56) 或  useTableHeight('-56px')
* @param {*} otherHeight
* @returns
*/
export default function useTableHeight(otherHeight = 0) {
  let px = typeof otherHeight == "string" ? pxStringToNumber(otherHeight) : otherHeight;
  let ohString = px <= 0 ? `- ${Math.abs(px)}px` : `+ ${px}px`
  return computed(() => {
    const navHeight = 64;
    return `calc(100vh - ${navHeight}px - 254px ${ohString})`
  })
}

/**
 * @description  流量字节转为较大的单位 B 转为 KB，MB，GB……
 * @param {*} arr 原始字节数据，可以是一维数组或者多维数组
 * @param {*} unit 预先设置的流量单位， 如果预先设置了单位，将会以改单位对数据进行单位转换
 * @param {*} promotionUnit 流量单位是否晋升一级 如 B -> KB , MB -> GB
 * @returns
 */
export function flowTransform(arr, unit, promotionUnit = true) {
  let k = 1024,
    sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  /**
   * 如果是数组 转化的结果就是数组 ，如果是单个值  转化的结构就是单个的值
   * @param {*} data 源数据
   * @param {*} scale 最小数据的格式化刻度
   * @returns
   */
  function formatNumber(data, scale) {
    let res, fixedLen = scale > 0 ? 3 : 0; // 保留的小数位数
    if (Array.isArray(data)) {
      res = []
      for (let i = 0, len = data.length; i < len; i++) {
        res.push(formatNumber(data[i], scale))
      }
    } else {
      res = data == 0 ? 0 : Number(parseFloat(data / Math.pow(k, scale)).toFixed(fixedLen))
    }
    return res;
  }

  if (!Array.isArray(arr)) {
    return arr;
  }
  // 寻找最小非零数字
  let minNonZero = arr.flat(Infinity).sort((a, b) => a - b).find(el => el > 0);

  if (minNonZero == null) { // 如果没有找到大于零的数字
    return {
      formatData: arr,
      unit: unit || "B" // 如果全部为0, 那么流量单位随便是什么都没有关系
    }
  }
  // 最小流量的格式化刻度
  let scale;

  // 如果预设了流量单位 ， 那么就去找到预设的流量单位的 1024 的指数
  // 否则就根据最小的大于零的流量数字去计算 1024 的指数
  let findIndex = sizes.findIndex(el => el == unit);
  if (unit && findIndex > -1) {
    scale = findIndex;
  } else {
    scale = Math.floor(Math.log(minNonZero) / Math.log(k));
    // 所有流量刻度 升级  然后保留三位小数
    if (promotionUnit) {
      scale = scale + 1;
    }
  }

  // 遍历格式化数据
  let formatData = arr.reduce((pre, cur) => {
    pre.push(formatNumber(cur, scale))
    return pre
  }, [])

  return {
    formatData,
    unit: sizes[scale]
  }
}

/**
 * @description 流量字节转为较大的单位 B 转为 KB，MB，GB……
 * @param { Number } bytes 原始字节
 * @param { String } unit 预先设置的流量单位，如果预先设置了单位，将会以改单位对数据进行单位转换
 * @param { Boolean } promotionUnit 流量单位是否晋升一级 如 B -> KB , MB -> GB
 * @returns 转换之后的流量数字和流量单位 [Number, String]
 */
export function bitFormat(bytes, unit, promotionUnit = true) {
  if (bytes == 0) {
    return [0, "B"]
  }
  var k = 1024,
    sizes = ["B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB"];

  let scale;

  let findIndex = sizes.findIndex(el => el == unit)
  if (unit && findIndex > -1) {
    scale = findIndex;
  } else {
    scale = Math.floor(Math.log(bytes) / Math.log(k));
    // 所有流量刻度 升级  然后保留三位小数
    // 所有流量刻度 升级  然后保留三位小数
    if (promotionUnit) {
      scale = scale + 1;
    }
  }

  let fixedLen = scale > 0 ? 3 : 0;

  let num = parseFloat(bytes / Math.pow(k, scale)).toFixed(fixedLen);
  return [num, sizes[scale]]
}

/**
 * 函数防抖
 */
export function debounce(fn, delay) {
  // 记录上一次的延时器
  var timer = null;
  var de = delay || 200;
  return function () {
    var args = arguments;
    var that = this;
    // 清除上一次延时器
    clearTimeout(timer)
    timer = setTimeout(function () {
      fn.apply(that, args)
    }, de);
  }
}

// 返回 年-月-日 : 时-分-秒
export const getDateAndTime = () => {
  let result = "";
  let date = new Date();
  result += date.getFullYear(); // 年
  let month = date.getMonth() + 1; // 月
  result += '-' + (month < 10 ? "0" + month : month); // 如果只有一位，则前面补零
  let day = date.getDate(); // 日
  result += '-' + (day < 10 ? "0" + day : day); // 如果只有一位，则前面补零
  let hour = date.getHours(); // 时
  result += ' : ' + (hour < 10 ? "0" + hour : hour); // 如果只有一位，则前面补零
  let minute = date.getMinutes(); // 分
  result += '-' + (minute < 10 ? "0" + minute : minute); // 如果只有一位，则前面补零
  let second = date.getSeconds(); // 秒
  result += '-' + (second < 10 ? "0" + second : second); // 如果只有一位，则前面补零
  return result;
}

export const operationConfirm = function (that, attr, message) {
  // const that = this
  return new Promise((resolve, reject) => {
    that.$confirm({
      title: `${attr}`,
      content: message,
      onOk() {
        resolve(true)
      },
      onCancel() {
        resolve(false)
      },
    })
  })
};

export const transNum = (num) => {
  let str = String(num);
  let len = str.length;
  let new_str = '';
  for (let i = len - 1, b = 0; i > -1; i--) {
    if (b < 3) {
      new_str = str[i] + new_str
    } else {
      new_str = str[i] + ',' + new_str
      b = 0
    }
    b++
  }
  return new_str
}

/**
 * @description 流量字节转为较大的单位 B 转为 KB，MB，GB……
 * @param { Number } bytes 原始字节
 * @param { String } unit 预先设置的流量单位，如果预先设置了单位，将会以改单位对数据进行单位转换
 * @param { Boolean } promotionUnit 流量单位是否晋升一级 如 B -> KB , MB -> GB
 * @returns 转换之后的流量数字和流量单位 [Number, String]
 */
export function tiaoFormat(bytes, unit, promotionUnit = true) {
  if (bytes == 0) {
    return [0, "条"]
  }
  var k = 1000,
    sizes = ["条", "k"];

  let scale;

  let findIndex = sizes.findIndex(el => el == unit)
  if (unit && findIndex > -1) {
    scale = findIndex;
  } else {
    scale = Math.floor(Math.log(bytes) / Math.log(k));
    // 所有流量刻度 升级  然后保留三位小数
  }

  if (promotionUnit) {
    scale = scale + 1;
  }

  let fixedLen = 0;

  let num = parseFloat(bytes / Math.pow(k, scale)).toFixed(fixedLen);
  return [num, sizes[scale]]
}

export function bus() {
  // 事件中心，用来存储所有事件
  const eventList = new Map()
  return {
    /**
     * 订阅事件
     * @param {string} key - 事件键值
     * @param {function} callback - 回调函数
     */
    on(key, callback) {
      // 如果该事件已经存在，则将回调函数添加到事件处理程序数组中
      const handlers = eventList.get(key)
      if (handlers) {
        handlers.push(callback)
      } else {
        // 否则，创建一个新数组并将回调函数添加到其中
        eventList.set(key, [callback])
      }
    },

    /**
     * 触发事件
     * @param {string} key - 事件键值
     * @param  args - 传递给回调函数的参数
     */
    emit(key, ...args) {
      // 获取事件处理程序数组
      const handlers = eventList.get(key)
      if (handlers) {
        // 遍历处理程序数组并调用每个处理程序，传递给定的参数
        handlers.slice().map((handler) => {
          handler(args)
        })
      }
    },

    /**
      * 取消订阅事件
      * @param {string} key - 事件键值
      * @param {function} callback - 要取消订阅的回调函数
      */
    off(key, callback) {
      // 获取事件处理程序数组
      let handlers = eventList.get(key)

      if (handlers) {
        // 如果处理程序数组中存在回调函数，则将其删除
        // const index = handlers.indexOf(callback)
        // if (index !== -1) {
        //   handlers.splice(index, 1)
        // } else {
        //   return new Error('未找到回调函数')
        // }
        // 清空处理程序数组以取消订阅所有回调函数
        handlers = [];
        // 删除指定的key
        eventList.delete(key);
      }
    }
  }
}

// function


// 遍历数组，去除数值占比低于0.01%的项，返回新数组
export function arrMap(arrList, total) {
  let newlist = [];
  let lowRateArr = [];
  let otherTotal = 0;
  if (total && arrList.length) {
    for (let item of arrList) {
      let rate = Math.round((item.value / total) * 10000) / 100;
      if (rate > 0.5) {
        newlist.push(item);
      } else {
        lowRateArr.push(item.value);
      }
    }
    otherTotal = lowRateArr.length ? lowRateArr.reduce((a, b) => a + b, 0) : 0;
    otherTotal && newlist.push({
      name: '其他',
      value: otherTotal,
    })
  }

  return newlist
}

export function useAsyncDataSource(service){
  return async field => {
    const dataSource =  await service();
    field.dataSource = dataSource;
  }
}

export function createUuid() {
  const lut = []
  for (let i = 0; i < 256; i++) {
    lut[i] = (i < 16 ? '0' : '') + i.toString(16)
  }
  const d0 = (Math.random() * 0xffffffff) | 0
  const d1 = (Math.random() * 0xffffffff) | 0
  const d2 = (Math.random() * 0xffffffff) | 0
  const d3 = (Math.random() * 0xffffffff) | 0
  return (
    lut[d0 & 0xff] +
    lut[(d0 >> 8) & 0xff] +
    lut[(d0 >> 16) & 0xff] +
    lut[(d0 >> 24) & 0xff] +
    lut[d1 & 0xff] +
    lut[(d1 >> 8) & 0xff] +
    '-' +
    lut[((d1 >> 16) & 0x0f) | 0x40] +
    lut[(d1 >> 24) & 0xff] +
    lut[(d2 & 0x3f) | 0x80] +
    lut[(d2 >> 8) & 0xff] +
    '-' +
    lut[(d2 >> 16) & 0xff] +
    lut[(d2 >> 24) & 0xff] +
    lut[d3 & 0xff] +
    lut[(d3 >> 8) & 0xff] +
    lut[(d3 >> 16) & 0xff] +
    lut[(d3 >> 24) & 0xff]
  )
}

/**
 * 文件下载 - 支持带token的安全下载
 *
 * @param {string} fileUrl - 文件下载URL
 * @param {object|string} params - 下载参数或目标窗口（向后兼容）
 * @param {string} filename - 自定义文件名（可选）
 */
export async function downloadFile(fileUrl, params = {}, filename = null) {
  // 向后兼容：如果第二个参数是字符串，则认为是target参数（旧版本用法）
  if (typeof params === 'string') {
    const target = params;
    const prefix = '/city/api'
    if (!fileUrl.startsWith(prefix)) fileUrl = `${prefix}${fileUrl}`

    const link = document.createElement('a');
    link.setAttribute('href', fileUrl)
    link.setAttribute('target', target || '_blank')
    link.setAttribute('rel', 'noopener')
    link.click()
    return;
  }

  // 新版本：使用axios发送带token的请求
  try {
    const prefix = '/city/api'
    if (!fileUrl.startsWith(prefix)) fileUrl = `${prefix}${fileUrl}`

    // 获取token
    const token = store.getters.token || localStorage.getItem('token');

    // 发送请求
    const response = await axios({
      method: 'GET',
      url: fileUrl,
      params: params,
      responseType: 'blob',
      headers: {
        'Authorization': token ? `Bearer ${token}` : '',
        'Content-Type': 'application/json'
      }
    });

    // 从响应头获取文件名
    let downloadFilename = filename;
    if (!downloadFilename) {
      const contentDisposition = response.headers['content-disposition'];
      if (contentDisposition) {
        const filenameMatch = contentDisposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
        if (filenameMatch && filenameMatch[1]) {
          downloadFilename = filenameMatch[1].replace(/['"]/g, '');
          // 处理UTF-8编码的文件名
          if (downloadFilename.includes('utf-8\'\'')) {
            downloadFilename = decodeURIComponent(downloadFilename.split('utf-8\'\'')[1]);
          }
        }
      }
    }

    // 如果还是没有文件名，使用默认名称
    if (!downloadFilename) {
      downloadFilename = 'download_' + new Date().getTime() + '.xlsx';
    }

    // 创建blob对象并下载
    const blob = new Blob([response.data]);
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.style.display = 'none';
    link.href = url;
    link.setAttribute('download', downloadFilename);
    document.body.appendChild(link);
    link.click();

    // 清理资源
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);

    return response;
  } catch (error) {
    console.error('文件下载失败:', error);
    message.error('文件下载失败');
    throw error;
  }
}


/**
 * 单独专门为拖拽上传的数据仅仅只保留fileId
 */
export function transformIncludeFileFormData(data){
  const resData = {...data};
  if( resData.lstFileId){
    resData.lstFileId =  resData.lstFileId.map(item => item.fileId)
  }
  return  resData;
}

/**
 * 下载文件
 */
export function resolveBlob(response) {
  const data = response.data
  if (!data) {
    return
  }
  const contentDisposition = response.headers['content-disposition']
  const fileName = decodeURIComponent(contentDisposition.substring(contentDisposition.indexOf('=') + 1))
  const url = window.URL.createObjectURL(new Blob([data]))
  const a = document.createElement('a')
  a.style.display = 'none'
  a.href = url
  a.setAttribute('download', fileName)
  document.body.appendChild(a)
  // 点击下载
  a.click()
  // 下载完成移除元素
  document.body.removeChild(a)
  // 释放掉blob对象
  window.URL.revokeObjectURL(url)
  message.success('下载成功')
}

/**
 * 字节B转化成KB，MB，GB
 */
export function formatByte(limit) {
  limit = parseInt(limit)
  let size = ''
  if (limit < 1024) {
    // 小于1KB, 则转化成B
    size = limit.toFixed(2) + 'B'
  } else if (limit < 1024 * 1024) {
    // 小于1MB, 则转化成KB
    size = (limit / 1024).toFixed(2) + 'KB'
  } else if (limit < 1024 * 1024 * 1024) {
    // 小于1GB, 则转化成MB
    size = (limit / (1024 * 1024)).toFixed(2) + 'MB'
  } else {
    // 其他转化成GB
    size = (limit / (1024 * 1024 * 1024)).toFixed(2) + 'GB'
  }

  const sizeStr = size + '' // 转成字符串
  const index = sizeStr.indexOf('.') // 获取小数点处的索引
  const dou = sizeStr.substr(index + 1, 2) // 获取小数点后两位的值
  if (dou === '00') {
    return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2)
  }
  return size
}

/**
 * 格式化日期
 * @param {string|Date} date 日期字符串或Date对象
 * @param {string} format 格式化模板，默认为 'YYYY-MM-DD HH:mm:ss'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD HH:mm:ss') {
  if (!date) return '';
  
  const d = new Date(date);
  if (isNaN(d.getTime())) return '';
  
  const year = d.getFullYear();
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const day = String(d.getDate()).padStart(2, '0');
  const hours = String(d.getHours()).padStart(2, '0');
  const minutes = String(d.getMinutes()).padStart(2, '0');
  const seconds = String(d.getSeconds()).padStart(2, '0');
  
  return format
    .replace('YYYY', year)
    .replace('MM', month)
    .replace('DD', day)
    .replace('HH', hours)
    .replace('mm', minutes)
    .replace('ss', seconds);
}